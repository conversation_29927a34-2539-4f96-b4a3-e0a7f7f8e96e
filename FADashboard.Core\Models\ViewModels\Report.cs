﻿using Libraries.CommonEnums;
using Microsoft.PowerBI.Api.Models;

namespace FADashboard.Core.Models.ViewModels;

public class Report
{
    public bool CanEmail { get; set; }
    public string Description { get; set; }
    public Guid EncryptionKey { get; set; }
    public string Header { get; set; }
    public int HeaderEnum { get; set; }
    public int ReportType { get; set; }
    public string Link { get; set; }
    public string Name { get; set; }
    public string Value { get; set; }
}

public class ReportStatus
{
    public long Id { get; set; }
    public bool ShouldRunLive { get; set; }
    public bool IsSuccess { get; set; }
    public bool IsDuplicate { get; set; }
    public EnumForReportAssembly? ReportType { get; set; }
}
