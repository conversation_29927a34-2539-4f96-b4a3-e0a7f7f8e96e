﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.PowerBI.Api.Models;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class PerfectEntityQualifiers
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public int KPIType { get; set; }

    public long KPIId { get; set; }

    public string? Target { get; set; }

    public int? ComparisionOperator { get; set; }

    public string? ParameterValues { get; set; }
    public string RuleName {get; set; }

}
