﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class PerfectCriteriaSlabDetail
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public float SlabStartPercentage { get; set; }

    public float? SlabEndPercentage { get; set; }

    public int Sequence { get; set; }

    public float SlabWeightage { get; set; }

    public long CriteriaId { get; set; }

    [ForeignKey(nameof(CriteriaId))]
    public PerfectEntityRuleCriteria PerfectEntityRuleCriteria { get; set; }

    public long RewardId { get; set; }

    public float RewardQuantity { get; set; }

    public SlabCalculationType SlabCalculationType { get; set; }
}
