﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IAPILogRepository
{
    Task<List<ApiLog>> GetAPILogs(long companyId, long startTimeEpoch, long endTimeEpoch, ApiType apiType, string requestId, string userName, int? statusCode);
    Task<string> GetInput(string reqId, ApiType? apiType);
    Task<string> GetOutput(string reqId, ApiType? apiType);
    Task<string> GetError(string reqId, ApiType? apiType);
    Task<string> GetCompleteRequest(string reqId, ApiType? apiType);
}
