﻿using AuditHelper;
using En<PERSON>tyHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class RouteOutletMappings : IAuditedEntity, IDeletable
{
    public long CompanyId { get; set; }
    [Audited]
    public bool Deleted { get; set; }
    public long Id { get; set; }
    public virtual Location Location { get; set; }
    public long LocationId { get; set; }
    public virtual Routes Route { get; set; }
    public long RouteId { get; set; }
    public DateTime CreatedAt { get; set; }   // asana: May 25,2024. reason: to store audit logs. link: https://app.asana.com/0/273175199711710/1206997621918824/f
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}

public class Routes : IAuditedEntity
{
    public long CompanyId { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public string Name { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public virtual ICollection<RoutePositionMappings> RoutePositionMapping { get; set; }
    public virtual ICollection<RouteOutletMappings> RouteOutletMappings { get; set; }
}
public class RoutePositionMappings : IAuditedEntity, IDeletable
{
    public long CompanyId { get; set; }
    [Audited]
    public bool Deleted { get; set; }
    public long RouteId { get; set; }
    public long Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long PositionCodeId { get; set; }
    public virtual Routes Route { get; set; }
    public virtual PositionCode PositionCode { get; set; }
}
