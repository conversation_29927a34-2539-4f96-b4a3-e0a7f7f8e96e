﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IOutletAdditionRequestRepository
{
    Task<RepositoryResponse> ApproveAdditionRequestByAdmin(OutletAdditionRequestInput request);

    Task<RepositoryResponse> ApproveAdditionRequestDoubleStep(OutletAdditionRequestInput request);

    Task<RepositoryResponse> DisapproveOutletAdditionRequest(long requestId, string reasonForRejection);

    Task<OutletAdditionRequestInput> GetOutletAdditionRequestById(long schemeId, long companyId);

    Task<OutletAdditionRequestInput> GetOutletAdditionRequestByIdForAdmin(long id, long companyId);

    Task<List<Requests>> GetOutletAdditionRequests(long userId, PortalUserRole userRole, long companyId, bool includeArchieved);
    Task<List<Requests>> GetOpenRequestsForOutletCreation(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<FAEventEntityMin> GetRequestIdForFAEventId(long companyId, long eventId);
}
