﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;
namespace FADashboard.Core.Services;
public class RewardRequestService(
    IRewardRequestRepository rewardRequestRepository,
    ICurrentUser currentUser,
    ICompanySettingsRepository companySettingsRepository,
    IAdminRepository adminRepository,
    IRequestsAndAlertsRepository requestsAndAlertsRepository,
    IPositionCodeRepository positionCodeRepository,
    AppConfigSettings appConfigSettings) : RepositoryResponse
{
    public async Task<List<EnhancedRewardRequestDto>> GetRewardRequestList(bool showArchived)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var rewardRequests = await rewardRequestRepository.GetRewardRequestList(showArchived, currentUser.CompanyId, currentUser.UserRole, companySettings.TypeofDistributorMapping);

        var groupedRequests = rewardRequests
            .GroupBy(r => new
            {
                DistributorName = r.DistributorName?.Trim() ?? string.Empty,
                r.EntityType,
                Date = r.CreatedAt.Date
            })
            .Select(g => new EnhancedRewardRequestDto
            {
                Id = g.First().Id,
                CompanyId = g.First().CompanyId,
                EntityType = g.First().EntityType,
                EntityId = g.First().EntityId,
                Status = g.First().Status,
                CreationContext = g.First().CreationContext,
                CreatedAt = g.First().CreatedAt,
                LastUpdatedAt = g.First().LastUpdatedAt,
                ExtraInfoJson = g.First().ExtraInfoJson,
                DistributorName = g.Key.DistributorName,
                DistributorIds = g.First().DistributorIds ?? new List<long>(),
                GroupedIds = g.Select(x => x.Id).ToList(),
                Achievement = g.Sum(x => x.Achievement ?? 0),
                Rewards = g.Sum(x => x.Rewards),
                RewardInfo = g.Select(x => new RewardInfo
                {
                    RuleName = x.RuleName,
                    CriteriaName = x.CriteriaName,
                    Achievement = x.Achievement ?? 0,
                    RewardPoints = x.Rewards
                }).ToList()
            })
            .ToList();

        return groupedRequests;
    }

    public async Task<List<RewardRequestDetailDto>> GetRewardRequestDetailsByDistributors(List<long> distributorIds)
    {
        if (distributorIds == null || distributorIds.Count == 0)
            return new List<RewardRequestDetailDto>();

        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var rewardRequests = await rewardRequestRepository.GetRewardRequestDetailsByDistributors(distributorIds, currentUser.CompanyId, companySettings.TypeofDistributorMapping);
        var groupedRequests = rewardRequests
            .GroupBy(r => r.EntityIdName?.Trim() ?? string.Empty)
            .Select(g =>
            {
                var groupedIds = g.Select(x => x.Id).ToList();
                return new RewardRequestDetailDto
                {
                    Id = g.First().Id,
                    Date = g.First().Date,
                    DistributorName = g.First().DistributorName,
                    DistributorERPId = g.First().DistributorERPId,
                    BeatIdName = g.First().BeatIdName,
                    EntityIdName = g.Key,
                    GroupedByIds = groupedIds,
                    TotalAchievement = g.Sum(x => x.TotalAchievement),
                    TotalRewardPoints = g.Sum(x => x.TotalRewardPoints),
                    RewardInfo = g.SelectMany(x => x.RewardInfo ?? new List<RewardInfo>()).ToList()
                };
            })
            .ToList();

        return groupedRequests;
    }

    private async Task ProcessForApprovalEngineQueue(long requestId, ApprovalEngineRequesType requestType, bool isApprovalRequest, bool IsRequestedByAdmin, string reasonForRejection = null, OutletAdditionRequestInput additionRequest = null, bool companyUsesHCCBUserFlows = false) //here
    {
        var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
        var attachedPositionIds = positions != null && positions.Count > 0 ? positions.Select(e => e.Id).ToList() : [];

        var newApprovalRequest = new ApprovalEngineModel
        {
            UserId = currentUser.LocalId,
            CompanyId = currentUser.CompanyId,
            RequestId = requestId,
            RequestType = requestType,
            IsApprovalRequest = isApprovalRequest,
            IsRequestedByAdmin = IsRequestedByAdmin,
            AttachedPositionsIds = attachedPositionIds,
            ReasonForRejection = reasonForRejection,
            UserRole = currentUser.UserRole,
            OutletAdditionRequestInput = additionRequest,
            CompanyUsesHCCBUserFlows = companyUsesHCCBUserFlows
        };

        var queueHandler = new QueueHandler<ApprovalEngineModel>(QueueType.NewApprovalAddUpdateQueue, appConfigSettings.StorageConnectionString);
        await queueHandler.AddToQueue(newApprovalRequest);
    }

    public async Task<RepositoryResponse> ApproveRejectRewardRequestsByIds(long[] requestIds, bool isApproved)
    {
        if (requestIds == null || !requestIds.Any())
        {
            return new RepositoryResponse
            {
                IsSuccess = false,
                Message = "No request IDs provided."
            };
        }
        try
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
            var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

            return await ProcessRequestApprovals(requestIds.Distinct().ToList(), isApproved, isRequestedByAdmin, companySettings);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse
            {
                IsSuccess = false,
                Message = "An unexpected error occurred while processing the request."
            };
        }
    }

    private async Task<RepositoryResponse> ProcessRequestApprovals(List<long> requestIds, bool isApproved, bool isRequestedByAdmin, CompanySettings companySettings)
    {
        var successCount = 0;
        var response = new RepositoryResponse { IsSuccess = true };

        foreach (var requestId in requestIds)
        {
            try
            {
                if (isRequestedByAdmin)
                {
                    await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, ApprovalEngineRequesType.Loyalty);
                }
                else
                {
                    var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                    var posIds = positions.Select(e => e.Id).ToList();
                    await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, ApprovalEngineRequesType.Loyalty);
                }

                await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.Loyalty, isApproved, isRequestedByAdmin, companyUsesHCCBUserFlows: companySettings.CompanyUsesHCCBUserFlows, additionRequest: null);

                successCount++;
            }
            catch (Exception ex)
            {
                if (response.IsSuccess)
                {
                    response.IsSuccess = false;
                    response.Message = $"Error processing request {requestId}";
                }
            }
        }
        if (response.IsSuccess)
        {
            response.Message = $"Successfully processed {successCount} reward request(s).";
        }
        else
        {
            response.Message = $"Processed {successCount} out of {requestIds.Count} requests with some errors. {response.Message}";
        }
        return response;
    }
}
