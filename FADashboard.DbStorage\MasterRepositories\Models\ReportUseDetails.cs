﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class ReportUseDetails : IEntity
{
    public long CompanyId { set; get; }
    public DateTime CreatedAt { get; set; }
    public bool DateManipulated { get; set; }
    public long Id { set; get; }
    public ViewPerspective? Perspective { get; set; }
    public PortalUserRole PortalRole { get; set; }
    public long ReportId { set; get; }
    public ReportSource ReportSource { set; get; }
    public ReportType ReportType { set; get; }
    public long UserId { set; get; }
}
