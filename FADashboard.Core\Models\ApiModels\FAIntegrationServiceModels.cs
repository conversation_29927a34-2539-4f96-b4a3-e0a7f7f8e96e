﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class FAISWorkflowsDetails
{
    public long? Id { get; set; }
    public string WorkflowName { get; set; }
    public int? Sequence { get; set; }
    public WorkFlowIntegrationType? IntegrationEntity { get; set; }
    public int? IntegrationType { get; set; }
    public string Configuration { get; set; }
    public string Cron { get; set; }
    public bool IsDeactive { get; set; }
}
public class FAISMappingsDetails
{
    public long? Id { get; set; }
    public long? WorkflowId { get; set; }
    public string Mappings { get; set; }
    public string ExtAPIEndpoint { get; set; }
    public string Conditions { get; set; }
}
