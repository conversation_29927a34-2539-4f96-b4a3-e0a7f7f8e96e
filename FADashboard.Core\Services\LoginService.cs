﻿using System.Net.Mail;
using System.Security.Cryptography;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Humanizer;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Libraries.Cryptography;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class LoginService(
    IEmployeeRepository employeeRepository,
    ICompanyRepository companyRepository,
    IIdsRepository idsRepository,
    IAdminRepository adminRepository,
    ICurrentUser currentUser,
    FAResilientHttpClient resilientHttpClient) : RepositoryResponse
{
    private async Task<UserModel> AddUpdateTokens(List<IDSLogin> users)
    {
        if (users is { Count: > 0 })
        {
            var allLogins = new List<TokenToReturn>();
            var companies = await companyRepository.GetAllCompanyDictionary();

            try
            {
                foreach (var user in users)
                {
                    var rng = RandomNumberGenerator.Create();
                    var tokenData = new byte[32];
                    rng.GetBytes(tokenData);
                    var token = Uri.EscapeDataString(Convert.ToBase64String(tokenData));

                    allLogins.Add(new TokenToReturn
                    {
                        Token = token,
                        UserId = user.LocalId,
                        UserRole = user.UserRole.ToString(),
                        UserName = user.Name,
                        CompanyName = companies[user.CompanyId].Name,
                        CompanyId = user.CompanyId,
                        ImageId = companies[user.CompanyId].ImageId,
                        UserSince = user.CreatedAt.Date.ToOrdinalWords(),
                        PhoneNo = user.PhoneNo,
                    });
                }
            }
            catch (Exception)
            {
                return null;
            }

            var firstToken = allLogins.FirstOrDefault();

            var returnDetails = new UserModel
            {
                Token = firstToken.Token,
                UserName = firstToken.UserName,
                UserRole = firstToken.UserRole,
                UserId = firstToken.UserId,
                CompanyName = firstToken.CompanyName,
                CompanyId = firstToken.CompanyId,
                UserSince = firstToken.UserSince,
                // Logo = companyImageBlobReader.GetPublicPath(firstToken.ImageId),
                PhoneNo = firstToken.PhoneNo,
                AllLogins = allLogins.Select(x => new TokenToReturn
                {
                    Token = x.Token,
                    UserName = x.UserName,
                    UserRole = x.UserRole.ToString(),
                    UserId = x.UserId,
                    CompanyName = x.CompanyName,
                    CompanyId = x.CompanyId,
                    UserSince = x.UserSince,
                    // Logo = companyImageBlobReader.GetPublicPath(x.ImageId),
                    PhoneNo = x.PhoneNo,
                }).ToList()
            };

            return returnDetails;
        }

        return null;
    }

    private async Task<List<IDSLogin>> CheckCredentials(string username, string password)
    {
        var baseUrl = new Uri("https://login.fieldassist.io/account/AuthenticateApi");
        var salt = "Ihu*()*(&ujk0009&^&h"; //don't change this... Used on IDS
        var data = Encryptor.CalculateMD5Hash($"{username}###{password}", salt);

        var urlParameters = $"?username={Uri.EscapeDataString(username)}&password={Uri.EscapeDataString(password)}&data={Uri.EscapeDataString(data)}";
        var api = $"{baseUrl}/{urlParameters}";
        var response = await resilientHttpClient.GetJsonAsync<Guid>(api, null, "application/json").ConfigureAwait(false);


        var users = await GetAllUsersLogins(response);
        var userRole = users.Select(d => d.UserRole).FirstOrDefault();
        if (adminRepository.UserHaveAdminPrivileges(userRole))
        {
            if (await adminRepository.IsCompanyAdminActive(users.Select(d => d.LocalId).FirstOrDefault()))
                return users;
        }
        else
        {
            if (await employeeRepository.IsActive(users.Select(d => d.LocalId).FirstOrDefault(), currentUser.CompanyId))
                return users;
        }

        return null;
    }

    private static bool IsValidEmail(string email)
    {
        var trimmedEmail = email.Trim();

        if (trimmedEmail.EndsWith(".", StringComparison.OrdinalIgnoreCase))
        {
            return false; // suggested by @TK-421
        }

        try
        {
            var addr = new MailAddress(email);
            return addr.Address == trimmedEmail;
        }
        catch
        {
            return false;
        }
    }

    private async Task<List<IDSLogin>> GetAllUsersLogins(Guid Id)
    {
        var logins = await idsRepository.GetAllUsersExceptDistributorsAndRegionalAdmins(Id, currentUser.CompanyId);
        if (logins is { Count: > 0 })
        {
            if (logins.Select(d => d.UserRole).FirstOrDefault() == PortalUserRole.GlobalAdmin)
            {
                var companies = await companyRepository.GetCompanyList();
                var login = logins.FirstOrDefault();
                return companies.Select(c => new IDSLogin
                    {
                        CompanyId = c.Id,
                        Id = login.Id,
                        EmailId = login.EmailId,
                        LocalId = login.LocalId,
                        Name = login.Name,
                        PhoneNo = login.PhoneNo,
                        IsDeactive = false,
                        UserRole = login.UserRole,
                        LoginGuid = login.LoginGuid,
                        CreatedAt = login.CreatedAt,
                    }
                ).ToList();
            }

            return logins;
        }

        return [];
    }

    public async Task<List<EntityMinWithCompany>> GetCompanyListUser(Guid loginGuid, PortalUserRole userRole)
    {
        if (userRole == PortalUserRole.GlobalAdmin)
            return await companyRepository.GetCompanyList();
        return await idsRepository.GetCompanyListByLoginGuid(loginGuid, currentUser.UserRole);
    }

    public async Task<IDSLogin> GetUserByLocalId(long localId)
    {
        var logins = await idsRepository.GetUserByLocalId(localId, currentUser.CompanyId);

        return logins;
    }
    public async Task<IDSLogin> GetUserByLoginGuid(Guid loginGuid)
    {
        var login = await idsRepository.GetUserByLoginGuid(loginGuid);
        return login;
    }

    public async Task<IDSLogin> GetUserForCompany(Guid Id, long companyId)
    {
        var logins = await idsRepository.GetAllUsersExceptDistributorsAndRegionalAdmins(Id, currentUser.CompanyId);
        if (logins is { Count: > 0 })
        {
            if (logins.Select(d => d.UserRole).FirstOrDefault() == PortalUserRole.GlobalAdmin)
            {
                var login = logins.FirstOrDefault();
                return new IDSLogin
                {
                    CompanyId = companyId,
                    Id = login.Id,
                    EmailId = login.EmailId,
                    LocalId = login.LocalId,
                    Name = login.Name,
                    PhoneNo = login.PhoneNo,
                    IsDeactive = false,
                    UserRole = login.UserRole,
                    LoginGuid = login.LoginGuid,
                    CreatedAt = login.CreatedAt,
                };
            }

            return logins.FirstOrDefault(l => l.CompanyId == companyId);
        }

        return new IDSLogin();
    }

    public async Task<UserModel> LoginByUserNameAndPassword(LogInModel loginModel)
    {
        if (loginModel.PasswordVersion == "v2")
        {
            loginModel.Password = Base64Helper.Decrypt(loginModel.Password);
        }

        var users = await CheckCredentials(loginModel.Username, loginModel.Password).ConfigureAwait(false);
        return await AddUpdateTokens(users).ConfigureAwait(false);
    }

    public async Task<HttpResponseMessage> PasswordReset(string username)
    {
        if (!IsValidEmail(username))
            _ = (await idsRepository.GetUserByPhoneNumber(username, currentUser.CompanyId)).EmailId;

        var baseUrl = "https://login.fieldassist.io/account/ForgotPasswordApi";
        var salt = "Ihu*()*(&ujk0009&^&h"; //don't change this... Used on IDS
        var data = Encryptor.CalculateMD5Hash($"{username}", salt);

        var urlParameters = $"?username={Uri.EscapeDataString(username)}&data={Uri.EscapeDataString(data)}";
        var api = $"{baseUrl}/{urlParameters}";
        var response = await resilientHttpClient.GetJsonAsync<HttpResponseMessage>(api, null).ConfigureAwait(false);
        return response;
    }

    public async Task<List<IDSLogin>> GetAllUsers(long companyId) => await idsRepository.GetAllUsers(companyId);
}
