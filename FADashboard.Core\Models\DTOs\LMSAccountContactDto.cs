using System;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountContactDto
    {
        public long Id { get; set; }
        public long AccountId { get; set; }
        public long CompanyId { get; set; }
        public string Name { get; set; }
        public string Designation { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string Photo { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class LMSAccountContactCreateInput
    {
        [Required]
        public long AccountId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(20)]
        [Phone]
        public string Mobile { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        public DateTime? DateOfBirth { get; set; }

        [StringLength(50)]
        public string Designation { get; set; }

        public string Description { get; set; }

        public string Photo { get; set; }
    }

    public class LMSAccountContactUpdateInput
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(20)]
        [Phone]
        public string Mobile { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        public DateTime? DateOfBirth { get; set; }

        [StringLength(50)]
        public string Designation { get; set; }

        public string Description { get; set; }

        public string Photo { get; set; }
    }
}
