﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Models.DTOs;

public class BIReportSubscriptions
{
    public long Id { get; set; }

    public string Name { set; get; }

    public Guid BIReportId { get; set; }
    public string BIReportName { get; set; }

    public PortalUserRole PortalUserRole { get; set; }
    public bool IsDeactive { get; set; }
    public SubscriptionType SubscriptionType { get; set; }
    public SubscribedScreen SubscriptionScreensType { get; set; }
}
