﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs;
public class DistributorAddressesDTO
{
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long CompanyId { get; set; }
    public long? RegionId { get; set; }
    public long? EntityId { get; set; }
    public string State { get; set; }
    public string City { get; set; }
    public string Address { get; set; }
    public string GSTIn { get; set; }
    public string ManagerName { get; set; }
    public string ManagerEmail { get; set; }
    public string ManagerPhoneNo { get; set; }
    public AddressType AddressType { get; set; }
    public string PinCode { get; set; }
    public AddressEntityType EntityType { get; set; }
    public string WareHouseName { get; set; }
    public string WarehouseERPID { get; set; }
    public string ERPID { get; set; }
}
