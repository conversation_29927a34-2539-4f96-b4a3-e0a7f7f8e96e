﻿using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class NewOutletFieldsAppMeta
{
    public long CompanyId { set; get; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public NewOutletFieldsForApp Field { set; get; }
    public long Id { set; get; }
    public bool IsMandatory { set; get; }
    public bool IsVisible { set; get; }
    public bool IsEditableForOutletEdit { set; get; }
    public DateTime LastUpdatedAt { get; set; }
    public bool TradeAppMandatory { get; set; }
    public bool TradeAppVisible { get; set; }
}
