﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IShopTypeRepository
{
    Task<RepositoryResponse> CreateShopType(ShopType shopType, long companyId);

    Task<RepositoryResponse> DeactivateShopType(long userId, long companyId);

    Task<Channel> GetOutletChannelById(long companyId, long id);
    Task<Channel> GetOutletChannelEnum(long companyId, OutletChannel? outletChannel, bool inludeDeactive);
    Task<List<Channel>> GetOutletChannels(long companyId, bool inludeDeactive);

    Task<ShopType> GetShopTypeById(long companyId, long id);

    Task<List<ShopType>> GetShopTypes(long companyId, bool inludeDeactive);

    Task<List<ShopType>> GetShopTypesByChannelId(long companyId, long channelId);

    Task<RepositoryResponse> UpdateChannels(List<Channel> channels, long companyId);

    Task<RepositoryResponse> UpdateShopType(ShopType shopType, long companyId);
    Task<SubshopType> GetSubshopTypeById(long subshopTypeId, long companyId, CancellationToken ct = default);
    Task<List<SubshopType>> GetSubShopTypes(long companyId, bool includeDeactive = false, CancellationToken ct = default);
    Task<RepositoryResponse> CreateUpdateSubShopType(SubshopType subshopType, long companyId, CancellationToken ct = default);
    Task<RepositoryResponse> DeactivateSubShopType(long subShopTypeId, long companyId, CancellationToken ct = default);

}
