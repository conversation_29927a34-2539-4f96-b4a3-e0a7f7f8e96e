﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FATradeUsers")]
public class FATradeUser
{
    public long Id { get; set; }

    [StringLength(500)] public string ShopName { get; set; }

    [StringLength(50)] public string City { get; set; }

    [StringLength(50)] public string State { get; set; }

    public string Address { get; set; }

    [StringLength(50)] public string OwnersName { get; set; } //TODO: Need to add first name and last name field in db table

    [StringLength(15)] public string PhoneNo { get; set; }


    public string GSTN { get; set; }


    public string AlternateImageId { set; get; }


    public string PhotoProofId { set; get; }


    public string TypeOfIdProof { get; set; }


    [StringLength(500)] public string Email { get; set; }

    [StringLength(100)] public string MarketName { set; get; }


    [StringLength(10)] public string PAN { get; set; }

    [StringLength(12)] public string Aadhar { get; set; }

    [StringLength(50)] public string ShopType { get; set; }


    [StringLength(6)] public string PinCode { get; set; }

    public Guid? ImageId { set; get; }


    [StringLength(1000)] public string Landmark { get; set; }

    [StringLength(50)] public string LandlineNumber { get; set; }

    [StringLength(500)] public string ModeOfDataCollection { get; set; }


    [StringLength(18)] public string BankAccountNumber { get; set; }

    public string AccountHoldersName { get; set; }

    [StringLength(11)] public string IFSCCode { get; set; }

    public string CreationContext { get; set; }

    public bool IsKYC { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public PortalUserRole UserRole { set; get; }
    public Guid? UniqueId { get; set; }
    public OutletChannel? OutletChannel { get; set; }
    public bool GSTRegistered { get; set; }
    public OutletSegmentation? Segmentation { set; get; }
}
[Table("FATradeUserCompanyMappings")]

public class FATradeUserCompanyMapping
{
    public long Id { get; set; }

    public long FATradeUserId { get; set; }

    public long ComapnyId { get; set; }

    public bool IsActive { get; set; }

    public bool IsApproved { get; set; }

    public long ApprovedBy { get; set; }
    public long LocationId { get; set; }
    public PortalUserRole ApprovedByRole { get; set; }

    public bool Deleted { get; set; }

    public virtual FATradeUser FATradeUser { get; set; }
}
