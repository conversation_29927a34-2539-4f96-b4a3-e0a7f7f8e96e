﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class CompanySetting
{
    [DisplayName("Any Possible Values")] public bool AnyPossibleValues { get; set; }

    [Required, DisplayName("Default Value")]
    public string DefaultValue { get; set; }

    [StringLength(2048), DisplayName("Description")]
    public string DescriptionText { get; set; }

    public long Id { get; set; }

    [Required, DisplayName("Minimum Version"), StringLength(32)]
    public string MinVersionSupported { get; set; }

    public int? Order { get; set; }

    [DisplayName("Possible Options")] public List<string> PossibleValues { get; set; }

    [StringLength(200)] public string Section { get; set; }

    [DisplayName("Send In App")] public bool SendInApp { get; set; }

    public long SettingId { set; get; }

    [DisplayName("Setting Key"), Required, StringLength(50, MinimumLength = 3)]
    public string SettingKey { get; set; }

    [DisplayName("Setting Key Text"), Required, StringLength(200)]
    public string SettingKeyText { get; set; }

    [DisplayName("Setting Type"), Required]
    public CompanySettingType SettingType { get; set; }

    [DisplayName("Setting Value"), StringLength(200)]
    public string SettingValue { get; set; }
}
