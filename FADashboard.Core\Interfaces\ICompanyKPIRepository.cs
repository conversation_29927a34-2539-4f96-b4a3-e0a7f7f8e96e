﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ICompanyKPIRepository
{
    Task<List<CompanyKPIsList>> GetCompanyKPIList(long companyId, bool showDeactive);
    Task<CompanyKPIs> GetCompanyKPIById(long companyId, long id);
    Task<RepositoryResponse> ActivateDeactivateCompanyKPI(long id, long companyId, bool action);
    Task<RepositoryResponse> CreateCompanyKPI(CompanyKPIRequestInput companyKPI, KPIRequestInput globalKPI, long companyId);
    Task<RepositoryResponse> UpdateCompanyKPI(CompanyKPIRequestInput companyKPI, long companyId);
}
