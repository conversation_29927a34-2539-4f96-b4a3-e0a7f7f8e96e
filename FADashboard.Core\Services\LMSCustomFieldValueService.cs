﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSCustomFieldValueService(ILMSCustomFieldValueRepository repository) : ILMSCustomFieldValueService
    {
        public async Task<LMSCustomFieldValueDto> CreateAsync(LMSCustomFieldValueDto customFieldValueDto, long createdByUserId)
        {
            customFieldValueDto.CreatedBy = createdByUserId;
            customFieldValueDto.CreatedAt = DateTime.UtcNow;
            return await repository.AddAsync(customFieldValueDto);
        }

        public async Task DeleteAsync(long id)
        {
            await repository.DeleteAsync(id);
        }

        public async Task DeleteByEntityAsync(int entityType, long entityId)
        {
            await repository.DeleteByEntityAsync(entityType, entityId);
        }

        public async Task<List<LMSCustomFieldValueDto>> GetByEntityAsync(int entityType, long entityId)
        {
            return await repository.GetByEntityAsync(entityType, entityId);
        }

        public async Task<LMSCustomFieldValueDto> GetByIdAsync(long id)
        {
            return await repository.GetByIdAsync(id);
        }

        public async Task UpdateAsync(long id, LMSCustomFieldValueDto customFieldValueDto, long updatedByUserId)
        {
            var existingValue = await GetByIdAsync(id);
            if (existingValue == null)
            {
                throw new Exception("Custom field value not found."); // Replace with custom exception
            }

            existingValue.Value = customFieldValueDto.Value;
            existingValue.UpdatedBy = updatedByUserId;
            existingValue.UpdatedAt = DateTime.UtcNow;

            await repository.UpdateAsync(existingValue);
        }

        public async Task UpsertRangeForEntityAsync(int entityType, long entityId, List<LMSCustomFieldValueDto> values, long userId)
        {
            foreach (var value in values)
            {
                value.EntityType = entityType;
                value.EntityId = entityId;
                if(value.Id == 0) // New value
                {
                    value.CreatedBy = userId;
                    value.CreatedAt = DateTime.UtcNow;
                }
                else // Existing value
                {
                    value.UpdatedBy = userId;
                    value.UpdatedAt = DateTime.UtcNow;
                }
            }
            await repository.UpsertRangeForEntityAsync(entityType, entityId, values);
        }
    }
}
