﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;
using Library.StorageWriter.Reader_Writer;
using Microsoft.AspNetCore.Http;

namespace FADashboard.Core.Services;

public class WeeklyOffService(
    IWeeklyOffRepository weeklyOffRepository,
    ICurrentUser currentUser,
    AppConfigSettings appConfigSettings,
    HCCBBlobWriter hccbBlobWriter) : RepositoryResponse
{
    private const string ContainerName = "weekly-off-upload";
    private readonly IWeeklyOffRepository weeklyOffRepository = weeklyOffRepository;
    private readonly ICurrentUser currentUser = currentUser;
    private readonly AppConfigSettings appConfigSettings = appConfigSettings;
    private readonly HCCBBlobWriter hccbBlobWriter = hccbBlobWriter;

    public async Task<List<WeeklyOffUploadDetail>> GetWeeklyOffDetails(CancellationToken ct = default)
    {
        var data = await weeklyOffRepository.GetWeeklyOffDetails(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);
        return data.Select(s => new WeeklyOffUploadDetail
        {
            Id = s.Id,
            InputFileName = s.InputFileName,
            InputFilePublicPath = hccbBlobWriter.GetHccbPublicPath(ContainerName, $"{currentUser.CompanyId}/{s.Id}/{s.InputFileName}"),
            ExecutedAt = s.ExecutedAt,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            StatusDetails = s.StatusDetails,
        }).OrderByDescending(s => s.Id).ToList();
    }

    public async Task<RepositoryResponse> CreateWeeklyOff(IFormFile file, CancellationToken ct = default)
    {
        var response = await weeklyOffRepository.SaveWeeklyOffDetails(currentUser.LocalId,
            currentUser.CompanyId, currentUser.UserRole, file.FileName, ct);

        if (response.Id != 0)
        {
            var connectionString = appConfigSettings.HCCBStorageConnectionString;
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            var blobPath = $"{currentUser.CompanyId}/{response.Id}/{file.FileName}";
            await hccbBlobWriter.UploadFileAsync(ContainerName, file, blobPath, contentType);
            var dataToQueue = new WeeklyOffUploadQueue { Id = response.Id.Value, FilePath = blobPath, CompanyId = currentUser.CompanyId };
            var queueHandler = new QueueHandler<WeeklyOffUploadQueue>(QueueType.HccbWeeklyOffUploadQueue, connectionString);
            await queueHandler.AddToQueue(dataToQueue);
            return response;
        }

        return response;
    }
}
