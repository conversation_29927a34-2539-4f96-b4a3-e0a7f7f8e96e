﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ICompanyTargetSubscriptionsRepository
{
    Task<List<CompanyTargetSubscriptionList>> GetAllCompanyTargetSubscriptions(long companyId, bool includeDeactive, CancellationToken ct);
    Task<CompanyTargetSubscriptionList> GetCompanyTargetSubscriptionById(long companyId, long id, CancellationToken ct);
    Task<TargetMasterView> GetTargetMasterById(long id, CancellationToken ct);
    Task<RepositoryResponse> ActivateDeactivateCompanyTargetSubscription(long companyId, long id, bool action, CancellationToken ct);
    Task<List<TargetMasterView>> GetAllTargetMasters(CancellationToken ct = default);
    Task<RepositoryResponse> CreateCompanyTargetSubscription(long companyId, CompanyTargetSubscriptionList cTSView, CancellationToken cts);
    Task<List<EntityMinWithStatus>> GetTargetSubscriptionExistStatus(long companyId, CancellationToken ct = default);
    Task<List<EntityMin>> GetCompanyTargetSubscriptions(long companyId, bool includeDeactive, CancellationToken ct = default);
    Task<RepositoryResponse> UpdateCompaniesTargetSubscriptionDetails(List<Criteria> criteria, long companyId);
    Task<RepositoryResponse> UpdateCompanyTargetSubscription(long companyId, CompanyTargetSubscriptionList companyTargetSubscriptionList, long id, CancellationToken cancellationToken);
}
