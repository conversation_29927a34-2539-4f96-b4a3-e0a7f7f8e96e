using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSAccountNoteService(ILMSAccountNoteRepository lmsAccountNoteRepository, ILMSAccountRepository lmsAccountRepository) : ILMSAccountNoteService
    {
        public async Task<LMSAccountNoteDto> GetAccountNoteByIdAsync(long id) =>
            await lmsAccountNoteRepository.GetByIdAsync(id);

        public async Task<PagedResult<LMSAccountNoteDto>> GetAccountNotesByAccountIdAsync(long accountId, LMSAccountNoteQueryParameters queryParameters) =>
            await lmsAccountNoteRepository.GetByAccountIdAsync(accountId, queryParameters);

        public async Task<LMSAccountNoteDto> CreateAccountNoteAsync(LMSAccountNoteCreateInput noteDto, long createdByUserId, long companyId)
        {
            ArgumentNullException.ThrowIfNull(noteDto);

            var account = await lmsAccountRepository.GetByIdAsync(noteDto.AccountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {noteDto.AccountId} not found.");
            }

            return await lmsAccountNoteRepository.CreateNoteAsync(noteDto, createdByUserId, companyId);
        }

        public async Task<LMSAccountNoteDto> UpdateAccountNoteAsync(long id, LMSAccountNoteUpdateInput noteDto, long updatedByUserId)
        {
            ArgumentNullException.ThrowIfNull(noteDto);
            return await lmsAccountNoteRepository.UpdateNoteAsync(id, noteDto, updatedByUserId);
        }

        public async Task<bool> DeleteAccountNoteAsync(long id, long deletedByUserId) =>
            await lmsAccountNoteRepository.DeleteNoteAsync(id, deletedByUserId);
    }
}
