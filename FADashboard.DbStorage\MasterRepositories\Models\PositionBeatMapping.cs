﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("PositionBeatMapping")]
public class PositionBeatMapping : IAuditedEntity
{
    [ForeignKey("LocationBeat")] public long BeatId { get; set; }

    public virtual Company Company { get; set; }

    [ForeignKey("Company")] public long CompanyId { get; set; }

    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    public string CreationContext { get; set; }
    public long Id { get; set; }

    public bool IsDeleted { get; set; }
    public bool IsSecondaryMapping { get; set; }

    public bool IsTemporaryAttachment { get; set; }

    public virtual LocationBeat LocationBeat { get; set; }

    public virtual PositionCode PositionCode { get; set; }

    [ForeignKey("PositionCode")] public long PositionId { get; set; }

    public virtual ProductDivision ProductDivision { get; set; }

    [ForeignKey("ProductDivision")] public long? ProductDivisionId { get; set; }
}
