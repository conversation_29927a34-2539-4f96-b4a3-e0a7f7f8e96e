﻿namespace FADashboard.Core.Models.ApiModels;
public class JourneyCycleModel
{
    public long Id { get; set; }
    public int MonthNumber { get; set; }
    public string MonthName { get; set; }
    public DateTime MonthStartDate { get; set; }
    public DateTime MonthEndDate { get; set; }
    public long JourneyCalendarId { get; set; }
    public long CompanyId { get; set; }
    public List<JourneyWeekModel> JourneyWeeks { get; set; }

}
public class JourneyWeekModel
{
    public long Id { get; set; }
    public long JourneyCycleId { get; set; }
    public int WeekForMonth { get; set; }
    public int QuarterNumber { get; set; }
    public int WeekForQuarter { get; set; }
    public int WeekForYear { get; set; }
    public DateTime WeekStartDate { get; set; }
    public DateTime WeekEndDate { get; set; }
}
public class JourneyCalendarModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public int ForMonths { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Year { get; set; }

    public string SelectedZones { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }

    public bool Deleted { get; set; }

    public bool IsDeactive { get; set; }

    public long CompanyId { get; set; }
    public List<JourneyCycleModel> JourneyCycles { get; set; }
}
