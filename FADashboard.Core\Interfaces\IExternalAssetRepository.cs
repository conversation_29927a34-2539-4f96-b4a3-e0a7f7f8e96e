﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IExternalAssetRepository
{
    Task<List<FACompanyExternalAssetList>> GetExternalAssetList(long companyId);
    Task<RepositoryResponse> CreateNewExternalAsset(ExternalAssetData data, long companyId, TimeSpan offset);
    Task<FACompanyExternalAssetList> GetExternalAssetDataById(long Id, long companyId);
    Task<RepositoryResponse> DeleteExternalAsset(long id);
    Task<RepositoryResponse> UpdateExternalAsset(ExternalAssetData data, long companyId, TimeSpan offset);
    Task<RetailerConnectPromotionConfigurationModel> GetRetailerConnectPromotionConfigurations(long companyId);
    Task<FACompanyExternalAssetList> GetCorrespondingExternalAsset(LocationInfoDto outletData);
}
