﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class TourPlanByIdDetails
{
    public long? EmployeeId { set; get; }
    public string EmployeeName { set; get; }
    public PortalUserRole? EmployeePortalRole { set; get; }
    public List<PositionCodeRecord> EmployeePositionCodes { get; set; }
    public DateTime EndDate { set; get; }
    public DateTime StartDate { set; get; }
    public List<TourPlanDetails> TourPlanItems { get; set; }
}

public class TourPlanDetails
{
    public long? BeatId { get; set; }
    public long? RouteId { get; set; }
    public string BeatName { get; set; }
    public string Date { get; set; }
    public string Day { get; set; }
    public bool Edited { get; set; }
    public long EmployeeTourPlanId { get; set; }
    public long Id { get; set; }
    public long? JWUserId { get; set; }

    // Used in approve so that we can know which items needs are updated
    public string JWUserName { get; set; }

    public string Reason { get; set; }
    public string ReasonCategory { get; set; }
    public JourneyPlanEntityType? EntityType { get; set; }
    public long? EntityId { set; get; }
}

public class TourPlanList
{
    public string ActionTakenBy { get; set; }
    public string EmployeeArea { get; set; }
    public long EmployeeId { set; get; }
    public string EmployeeName { set; get; }
    public DateTime EndDate { set; get; }
    public long Id { set; get; }
    public bool isApproved { get; set; }
    public string ManagerName { get; set; }
    public DateTime? OrderByTime { set; get; }
    public PortalUserRole PortalRole { get; set; }
    public DateTime RequestedOn { get; set; }

    public DateTime StartDate { set; get; }
}
