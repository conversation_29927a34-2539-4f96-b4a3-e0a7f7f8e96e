﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("PositionCodeEntityMappings")]
public class PositionCodeEntityMapping : IAuditedEntity, IDeactivatable
{
    public PositionCodeEntityMapping()
    {
        IsDeactive = false;
    }

    public ClientEmployee ClientEmployee { get; set; }
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }

    [ForeignKey("ClientEmployee")] public long EntityId { get; set; }

    public long Id { get; set; }

    [Column("IsDetached")] public bool IsDeactive { get; set; }

    public PositionCode PositionCode { get; set; }
    public long PositionCodeId { get; set; }
}
