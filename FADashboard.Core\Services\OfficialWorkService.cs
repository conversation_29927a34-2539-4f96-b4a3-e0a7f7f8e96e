﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;

namespace FADashboard.Core.Services;

public class OfficialWorkService(
    ICurrentUser currentUser,
    IOfficialWorkRepository officialWorkRepository
) : RepositoryResponse
{
    public async Task<OfficialWorkFlat> GetOfficialWorkById(long id) => await officialWorkRepository.GetOfficialWorkById(id, currentUser.CompanyId);
    public async Task<List<OfficialWorkFlat>> GetAllOfficialWorks(bool showInvalid) => await officialWorkRepository.GetAllOfficialWorks(showInvalid, currentUser.CompanyId);
    public async Task<RepositoryResponse> ActivateDeactivateOfficialWork(long id, bool action) => await officialWorkRepository.ActivateDeactivateOfficialWork(id, action, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateUpdateOfficialWork(OfficialWorkFlat officialWorkFlat)
    {
        if (officialWorkFlat.Id == 0)
        {
            return await officialWorkRepository.CreateOfficialWork(officialWorkFlat, currentUser.CompanyId);
        }

        return await officialWorkRepository.UpdateOfficialWork(officialWorkFlat, currentUser.CompanyId);
    }
}
