﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class QualifierSchemeSlab
{
    public long? BasketId { get; set; }
    public long Id { get; set; }
    public bool IsBasketMandatory { get; set; }
    public long? ProductId { get; set; }
    public long? Quantity { get; set; }
    public long SchemeSlabId { set; get; }
}

public class SchemeOutletConstraint
{
    [DisplayName("Select a channel")] public List<string> RequiredChannels { set; get; }

    public List<long> RequiredChannelsList { set; get; }

    [DisplayName("Is focused")] public string RequiredIsFocused { set; get; }

    public bool? RequiredIsFocusedBoolean { set; get; }

    [DisplayName("Select a segmentation")] public List<string> RequiredSegmentations { set; get; }

    public List<long> RequiredSegmentationsList { set; get; }

    [DisplayName("Select a shoptype")] public List<string> RequiredShopTypes { set; get; }

    public List<long> RequiredShopTypesList { set; get; }

    [DisplayName("Select a outlet Chains")]
    public List<string> OutLetChains { set; get; }

    public List<string> CustomTags { set; get; }

    public List<long> CustomTagsList { set; get; }

    public List<long> RequiredOutletChainsList { set; get; }
    public List<long> OutletIdsList { set; get; }
    public List<string> OutletIds { set; get; }
}

public class SchemeDistributorConstraint
{
    [DisplayName("Select a channel")] public List<string> DistributorRequiredChannels { set; get; }

    public List<long> DistributorRequiredChannelsList { set; get; }

    [DisplayName("Select a segmentation")] public List<string> DistributorRequiredSegmentations { set; get; }

    public List<long> DistributorRequiredSegmentationsList { set; get; }
}

public class SchemeUserConstraint
{
    public List<string> Cohorts { set; get; }
    public List<long> CohortsList { set; get; }
}

public class SchemeProductFilterConstraint
{
    // Changed from List<long> to handle decimal MRP values in JSON
    // asana: https://app.asana.***************************************************1210785770306791?focus=true
    public List<double> MRP { set; get; }

    public List<string> Attribute1 { set; get; }

    public List<string> Attribute2 { set; get; }

    public List<string> AltCategory { set; get; }
    public List<string> ManufacturingBatchNumbers { set; get; }
    public List<string> MasterBatchNumbers { set; get; }
}

public class SchemeOutletConstraintToBeSaved
{
    public List<string> RequiredChannels { set; get; }
    public bool? RequiredIsFocused { set; get; }
    public List<string> RequiredSegmentations { set; get; }
    public List<string> RequiredShopTypes { set; get; }
    public List<string> CustomTags { set; get; }
    public List<string> OutLetChains { set; get; }
    public List<string> OutletIds { set; get; }
    public List<string> ApplicableonOutletIDs { set; get; }
}

public class SchemeDistributorConstraintToBeSaved
{
    public List<string> DistributorRequiredChannels { set; get; }
    public List<string> DistributorRequiredSegmentations { set; get; }
}

public class SchemeProductFilterConstraintsToBeSaved
{
    public List<string> MRP { set; get; }
    public List<string> Attribute1 { set; get; }
    public List<string> Attribute2 { set; get; }
    public List<string> AltCategory { set; get; }
    public List<string> ManufacturingBatchNumbers { set; get; }
    public List<string> MasterBatchNumbers { set; get; }
}

public class SchemeUserConstraintToBeSaved
{
    public List<string> Cohorts { set; get; }
}

public class SchemesInput
{
    public SchemeCategorization Category { set; get; }
    public ConstraintType ConstraintType { set; get; }
    public string DiscountBlockArray { get; set; }
    public List<long> DiscountBlockArrayList { get; set; }
    public long? PreferredPayoutProduct { set; get; }
    public DiscountBlock DiscountOn { get; set; }
    public string DistributorIdBlock { set; get; }
    public List<long> DistributorIdBlockList { set; get; }

    public bool EligibleForBudget => PayoutType is PayoutType.Discount or PayoutType.Product
        or PayoutType.TopUpDiscount or PayoutType.PerUnitDiscount
        or PayoutType.Basket or PayoutType.FOC;

    [Column(TypeName = "datetime2")] public DateTime EndTime { get; set; }

    public string ErpId { get; set; }
    public int SchemeStep { get; set; }
    public int? SchemeSequence { set; get; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsMRPBilling { get; set; }
    public bool IsNew { set; get; }
    public bool IsOutletConstraint { set; get; }
    public bool IsDistributorConstraint { set; get; }
    public bool IsProductConstraint { set; get; }
    public bool IsUserConstraint { set; get; }
    public bool? MandatoryScheme { get; set; }
    public string Name { get; set; }
    public string AttributeText1 { get; set; }
    public SchemeOutletConstraint OutletConstraints { set; get; }
    public SchemeDistributorConstraint DistributorConstraints { set; get; }
    public SchemeUserConstraint UserConstraints { set; get; }
    public SchemeProductFilterConstraint ProductFilterConstraints { set; get; }
    public PayoutCalculationType PayoutCalculationType { set; get; }
    public DiscountBlock? PayoutOn { get; set; }
    public PayoutType PayoutType { set; get; }
    public PayoutIn? PayoutIn { get; set; }
    public List<long> RegionIds { get; set; }
    public double? SchemeBudget { get; set; }
    public BudgetType? SchemeBudgetType { get; set; }
    public ICollection<SchemeSlabs> SchemeSlabs { set; get; }
    public string SchemeSlabsStr { set; get; }
    public SchemeSubType? SchemeSubType { get; set; }
    public SchemeSubType? SchemeSubType2 { get; set; }
    public SchemeType? SchemeType { get; set; }
    public string SelectedProductBlockArray { get; set; }
    public List<long> SelectedProductBlockArrayList { get; set; }

    [Column(TypeName = "datetime2")] public DateTime StartTime { get; set; }

    public string StringDescription { get; set; }
    public List<long> ZoneIds { get; set; }
    public bool IsQPS { set; get; }
    public bool IsMarkup { get; set; }

    public bool? IsPreferred { set; get; }
    public bool? IsInclusive { set; get; }
    public bool IsIndividual { set; get; }
    public bool AllowOnNonTrackedSales { set; get; }
    public string States { get; set; }
    public List<string> StateIds { get; set; }
    public bool UserAllocation { get; set; }
    public long? AlternateScheme { get; set; }
    public CappingType? CappingType { get; set; }
    public double? CappingValue { get; set; }
    public bool IsDiscountCalculationOnPtr { get; set; }
    public bool? SingleProductUsesEntireBudget { get; set; }
    public bool? AllowExtraUnitIfBudgetLeft { get; set; }
    public bool CalculationOnCompleteNumber { get; set; }
}

public class SchemeSlabs
{
    public SchemeSlabs()
    {
        QualifierSchemeSlab = [];
    }

    [DisplayName("Basket Constraint Type")]
    public ConstraintType? BasketConstraintType { get; set; }

    [DisplayName("Invoice Value Payout Percentage")]
    public double? BasketInvoicePayout { get; set; }

    [DisplayName("Minimum constraint value")]
    public double? Constraint { set; get; }

    [DisplayName("Max Payout in Amount")] public double? MaxBasketPayout { get; set; }

    [DisplayName("MRP Payout")] public double? MRPPayout { get; set; }

    public double? Payout { set; get; }

    [DisplayName("Payout description")] public string PayoutDescription { get; set; }

    public int? Priority { set; get; }

    [DisplayName("Product")] public long? ProductId { set; get; }

    public QualifierPayoutType? QualifierPayoutType { get; set; }
    public IEnumerable<QualifierSchemeSlab> QualifierSchemeSlab { get; set; }
    public double? NominalPrice { set; get; }
    public int? SlabNumber { set; get; }
    public int? MultiplierStepSize { set; get; }
    public string BatchMasterNumber { set; get; }
    public List<string> BatchMasterNumbers { set; get; }
}

public class ReviewSchemeRequest
{
    public List<Guid> guids { get; set; }
    public int Action { get; set; }
    public string Reason { get; set; }
}
