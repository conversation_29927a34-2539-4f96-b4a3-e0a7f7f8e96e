﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class OutletSegmentationAttributes : IAuditedEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string DisplayName { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsValid { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public double MaxOutletOrderPotential { get; set; }
    public decimal MaxOutletPotential { get; set; }
    public decimal MinOutletPotential { get; set; }
    public OutletSegmentation Segmentation { get; set; }
    public int VisitsPerMonth { get; set; }
    public decimal MaxOutletPotentialInStdUnit { get; set; }
    public decimal MinOutletPotentialInStdUnit { get; set; }
    public decimal MinOrderValue { get; set; }
    public long? ProductDivisionId { get; set; }
}
