﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Watchlist : IAuditedEntity, ICreatedEntity, IUpdatableEntity
{
    public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public string EmployeeIds { get; set; }

    [Key] public long Id { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
}
