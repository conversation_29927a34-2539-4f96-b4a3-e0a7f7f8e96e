﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class UsersService(IUsersRepository usersRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<UserWithPositionFilter>> GetAllManagersUnderPositions(List<long> positionCodeIds)
    {
        var users = await usersRepository.GetAllManagersUnderPositions(currentUser.CompanyId, positionCodeIds);
        return users;
    }

    public async Task<List<UserWithPositionFilter>> GetAllManagersWithPositions()
    {
        var users = await usersRepository.GetAllManagersWithPositions(currentUser.CompanyId);
        return users;
    }

    public async Task<List<UserWithPositionFilter>> GetPositionLevelWiseUsers(PositionCodeLevel level)
    {
        var users = await usersRepository.GetPositionLevelWiseUsers(currentUser.CompanyId, level);
        return users;
    }

    public async Task<List<UserWithPositionFilter>> GetPositionLevelWiseUsersUnderPositions(List<long> positionCodeIds)
    {
        var users = await usersRepository.GetPositionLevelWiseUsersUnderPositions(currentUser.CompanyId, positionCodeIds);
        return users;
    }
}
