﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services.Retailer;

public class TradeCarouselBannerService(ITradeCarouselBannerRepository tradeCarouselBannerRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<RepositoryResponse> CreateUpdateTradeCarouselBanners(TradeBanners tradeBanner)
    {
        if (tradeBanner.Id == 0)
        {
            var data = await tradeCarouselBannerRepository.CreateTradeCarouselBanners(tradeBanner, currentUser.CompanyId);
            return data;
        }
        else
        {
            var data = await tradeCarouselBannerRepository.UpdateTradeCarouselBanners(tradeBanner, currentUser.CompanyId);
            return data;
        }
    }

    public async Task<List<TradeBanners>> GetTradeCarouselInfo(bool showInactive)
    {
        var data = await tradeCarouselBannerRepository.GetTradeCarouselBannersList(currentUser.CompanyId, showInactive);
        return data;
    }

    public async Task<RepositoryResponse> DeactivateTradeCarouselBanner(long bannerId)
    {
        var data = await tradeCarouselBannerRepository.DeactivateTradeCarouselBanner(bannerId, currentUser.CompanyId);
        return data;
    }

    public async Task<TradeBanners> GetTradeCarouselBannerById(long id)
    {
        var tradeCarouselBanner = await tradeCarouselBannerRepository.GetTradeCarouselBannerById(id, currentUser.CompanyId);
        return tradeCarouselBanner;
    }
}
