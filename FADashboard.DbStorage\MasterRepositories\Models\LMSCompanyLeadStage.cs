﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FADashboard.Core.Models;

namespace FADashboard.DbStorage.MasterRepositories.Models
{
    [Table("LMSCompanyLeadStage")]
    public class LMSCompanyLeadStage
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        public string StageName { get; set; }

        [Required]
        public LeadStageCategory StageCategory { get; set; }

        [Required]
        public decimal ClosurePercentage { get; set; }

        [Required]
        [ForeignKey("LMSCompanyLeadTemplate")]
        public long LeadTemplateId { get; set; }
        public virtual LMSCompanyLeadTemplate LMSCompanyLeadTemplate { get; set; }

        public int Sequence { get; set; }

        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
