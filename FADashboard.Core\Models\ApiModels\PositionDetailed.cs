﻿namespace FADashboard.Core.Models.ApiModels;
public class PositionDetailed
{
    public PositionDetailed() { }
    public long? Level1Id { get; set; }
    public long? Level2Id { get; set; }
    public long? Level3Id { get; set; }
    public long? Level4Id { get; set; }
    public long? Level5Id { get; set; }
    public long? Level6Id { get; set; }
    public long? Level7Id { get; set; }
    public long? Level8Id { get; set; }
    public string Level1 { get; set; }
    public string Level2 { get; set; }
    public string Level3 { get; set; }
    public string Level4 { get; set; }
    public string Level5 { get; set; }
    public string Level6 { get; set; }
    public string Level7 { get; set; }
    public string Level8 { get; set; }
    public string Level1Code { get; set; }
    public string Level2Code { get; set; }
    public string Level3Code { get; set; }
    public string Level4Code { get; set; }
    public string Level5Code { get; set; }
    public string Level6Code { get; set; }
    public string Level7Code { get; set; }
    public string Level8Code { get; set; }

    public PositionDetailed(PositionDetailed p)
    {
        Level1Id = p.Level1Id;
        Level1 = p.Level1;
        Level1Code = p.Level1Code;
        Level2Id = p.Level2Id;
        Level2 = p.Level2;
        Level2Code = p.Level2Code;
        Level3Id = p.Level3Id;
        Level3 = p.Level3;
        Level3Code = p.Level3Code;
        Level4Id = p.Level4Id;
        Level4 = p.Level4;
        Level4Code = p.Level4Code;
        Level5Id = p.Level5Id;
        Level5 = p.Level5;
        Level5Code = p.Level5Code;
        Level6Id = p.Level6Id;
        Level6 = p.Level6;
        Level6Code = p.Level6Code;
        Level7Id = p.Level7Id;
        Level7 = p.Level7;
        Level7Code = p.Level7Code;
        Level8Id = p.Level8Id;
        Level8 = p.Level8;
        Level8Code = p.Level8Code;
    }
}
