﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IOutletSegmentationAttributesRepository
{
    Task<List<OutletSegmentationAttribute>> GetAllOutletSegmentationAttributes(long companyId);
    Task<List<RepositoryResponse>> CreateDynamicSegmentations(List<OutletSegmentationInput> outletSegmentationInputs, long companyId);
    Task<List<RepositoryResponse>> UpdateDynamicSegmentations(List<OutletSegmentationInput> outletSegmentationInputs, long companyId);
}
