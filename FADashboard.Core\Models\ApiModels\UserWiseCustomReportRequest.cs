﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class CustomReportFilterInput
{
    public CategoryName CategoryName { get; set; }
    public string ReportColumnName { get; set; }
}

public class UserReportPreference
{
    public bool? ShowPositionsNextToUser { get; set; }
}

public class UserWiseCustomReportRequest
{
    public long CustomReportId { get; set; }
    public UserReportPreference OtherPreferences { get; set; }
    public List<CustomReportFilterInput> ReportFilters { get; set; }
}
