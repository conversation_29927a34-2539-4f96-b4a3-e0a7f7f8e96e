﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class GeographyService(
    ICurrentUser currentUser,
    IGeographyRepository geographyRepository,
    IZoneRepository zoneRepository,
    IRegionRepository regionRepository,
    ITerritoryRepository territoryRepository,
    IOutletRepository outletRepository,
    IPositionCodeRepository positionCodeRepository,
    INomenclatureRepository nomenclatureRepository,
    ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidGeography(GeographyInput geography)
    {
        var geographies = await GetAllGeographiesToHighestLevel(true);

        if (geography.Id != 0)
        {
            geographies = geographies.Where(p => p.Id != geography.Id).ToList();
        }

        var geographyNameList = geographies.Select(p => p.Name.NormalizeCaps().Trim()).ToList();
        if (geographyNameList.Contains(geography.Name.NormalizeCaps().Trim()))
        {
            return new RepositoryResponse
            {
                Id = geography.Id, ExceptionMessage = "Geography with this name already Exists!", Message = "Geography Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (!string.IsNullOrEmpty(geography.ErpId))
        {
            var geographyERPList = geographies.Select(p => p.ErpId.NormalizeCaps().Trim()).ToList();
            if (geographyERPList.Contains(geography.ErpId.NormalizeCaps().Trim()))
            {
                return new RepositoryResponse
                {
                    Id = geography.Id, ExceptionMessage = "Geography with this ERP Id already Exists!", Message = "Geography Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = geography.Id, Message = "Geography Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateGeography(GeographyLevel level, long geographyId, bool action)
    {
        switch (level)
        {
            case GeographyLevel.Territory:
                return await territoryRepository.ActivateDeactivateTerritory(geographyId, action, currentUser.CompanyId);

            case GeographyLevel.Region:
                return await regionRepository.ActivateDeactivateRegion(geographyId, action, currentUser.CompanyId);

            case GeographyLevel.Level4:
                return await zoneRepository.ActivateDeactivateZone(geographyId, action, currentUser.CompanyId);
            default:
                return await geographyRepository.ActivateDeactivateGeography(geographyId, action, currentUser.CompanyId);
        }
    }

    public async Task<RepositoryResponse> CreateGeographies(GeographyInput geography)
    {
        var checkValid = await IsValidGeography(geography);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        switch (geography.Level)
        {
            case GeographyLevel.Territory:
                var territoryInsert = new GeographiesListWithParent { ParentId = geography.ParentId, Name = geography.Name, ErpId = geography.ErpId };
                return await territoryRepository.CreateTerritory(territoryInsert, currentUser.CompanyId);

            case GeographyLevel.Region:
                var regionInsert = new GeographiesListWithParent
                {
                    ParentId = geography.ParentId,
                    Name = geography.Name,
                    ErpId = geography.ErpId,
                    RegionShortCode = geography.RegionShortCode,
                    ODSActive = geography.ODSActive
                };
                return await regionRepository.CreateRegion(regionInsert, currentUser.CompanyId);


            case GeographyLevel.Level4:
                var zoneInsert = new GeographiesListWithParent { ParentId = geography.ParentId, Name = geography.Name, ErpId = geography.ErpId };
                return await zoneRepository.CreateZone(zoneInsert, currentUser.CompanyId);

            case GeographyLevel.Level5:
                var level5insert = new GeographiesListWithParent { ParentId = geography.ParentId, Name = geography.Name, ErpId = geography.ErpId, Level = geography.Level };
                return await geographyRepository.CreateGeographies(level5insert, currentUser.CompanyId);

            case GeographyLevel.Level6:
                var level6insert = new GeographiesListWithParent { ParentId = geography.ParentId, Name = geography.Name, ErpId = geography.ErpId, Level = geography.Level };
                return await geographyRepository.CreateGeographies(level6insert, currentUser.CompanyId);
            default:
                var level7insert = new GeographiesListWithParent { Name = geography.Name, ErpId = geography.ErpId, Level = geography.Level };
                return await geographyRepository.CreateGeographies(level7insert, currentUser.CompanyId);
        }
    }

    public async Task<List<EntityMinIncludeParent>> GetAllGeographiesOfLevel(GeographyLevel level)
    {
        var geographies = new List<EntityMinIncludeParent>();
        switch (level)
        {
            case GeographyLevel.Territory:
                geographies = await territoryRepository.GetAllActiveTerritories(currentUser.CompanyId);
                break;

            case GeographyLevel.Region:
                geographies = await regionRepository.GetAllActiveRegions(currentUser.CompanyId);
                break;
            case GeographyLevel.location:
                break;
            case GeographyLevel.Beat:
                break;
            case GeographyLevel.Level4:
                geographies = await zoneRepository.GetAllActiveZones(currentUser.CompanyId);
                break;
            case GeographyLevel.Level5:
                break;
            case GeographyLevel.Level6:
                break;
            case GeographyLevel.Level7:
                break;
            default:
                geographies = await geographyRepository.GetAllActiveGeographiesOfLevel(currentUser.CompanyId, level);
                break;
        }

        return geographies;
    }

    public async Task<List<EntityMin>> GetAllGeographiesOfLevel(GeographyLevel level, List<long> ids)
    {
        var geographies = new List<EntityMin>();
        switch (level)
        {
            case GeographyLevel.Territory:
                geographies = await territoryRepository.GetAllActiveTerritories(currentUser.CompanyId, ids);
                break;

            case GeographyLevel.Region:
                geographies = await regionRepository.GetAllActiveRegions(currentUser.CompanyId, ids);
                break;
            case GeographyLevel.location:
                break;
            case GeographyLevel.Beat:
                break;
            case GeographyLevel.Level4:
                break;
            case GeographyLevel.Level5:
                break;
            case GeographyLevel.Level6:
                break;
            case GeographyLevel.Level7:
                break;
            default:
                geographies = await geographyRepository.GetAllActiveGeographiesOfLevel(currentUser.CompanyId, level, ids);
                break;
        }

        return geographies;
    }

    public async Task<List<GeographiesListWithParent>> GetAllGeographiesToHighestLevel(bool includeDeactivate, GeographyLevel? level = null, List<long> regionIds = null)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var highestLevel = companySettings.CompanyHighestGeography;
        var geographies = new List<GeographiesListWithParent>();

        if (level.HasValue)
        {
            switch (level.Value)
            {
                case GeographyLevel.Territory:
                    geographies.AddRange(await territoryRepository.GetAllTerritories(currentUser.CompanyId, includeDeactivate));
                    break;
                case GeographyLevel.Region:
                    geographies.AddRange(await regionRepository.GetAllRegions(currentUser.CompanyId, includeDeactivate));
                    break;
                case GeographyLevel.Level4:
                    geographies.AddRange(await zoneRepository.GetZones(currentUser.CompanyId, includeDeactivate));
                    break;
                case GeographyLevel.Level5:
                case GeographyLevel.Level6:
                case GeographyLevel.Level7:
                    var getGeographies = await geographyRepository.GetAllActiveGeographiesOfLevelWithParent(currentUser.CompanyId, highestLevel, includeDeactivate);
                    getGeographies = getGeographies.Where(p => p.Level == level.Value).ToList();
                    geographies.AddRange(getGeographies);
                    break;
                case GeographyLevel.location:
                    break;
                case GeographyLevel.Beat:
                    break;
            }

            if (regionIds != null && regionIds.Count > 0)
            {
                if (level.Value == GeographyLevel.Territory)
                    geographies = geographies.Where(p => p.ParentId.HasValue && regionIds.Contains(p.ParentId.Value)).ToList();
                else if (level.Value == GeographyLevel.Region)
                    geographies = geographies.Where(p => regionIds.Contains(p.Id)).ToList();
            }

            return geographies;
        }

        geographies.AddRange(await territoryRepository.GetAllTerritories(currentUser.CompanyId, includeDeactivate));
        geographies.AddRange(await regionRepository.GetAllRegions(currentUser.CompanyId, includeDeactivate));
        geographies.AddRange(await zoneRepository.GetZones(currentUser.CompanyId, includeDeactivate));
        geographies.AddRange(await geographyRepository.GetAllActiveGeographiesOfLevelWithParent(currentUser.CompanyId, highestLevel, includeDeactivate));
        return geographies;
    }

    public async Task<List<GeographiesListWithParent>> GetAllRegions(bool includeDeactivate)
    {
        var regions = await regionRepository.GetAllRegions(currentUser.CompanyId, includeDeactivate);
        return regions;
    }

    public async Task<List<GeographiesListWithParent>> GetAllTerritories(bool includeDeactivate)
    {
        var territories = await territoryRepository.GetAllTerritories(currentUser.CompanyId, includeDeactivate);
        return territories;
    }

    public async Task<List<EntityMinIncludeParent>> GetCompanyRegions()
    {
        var regions = await regionRepository.GetAllActiveRegions(currentUser.CompanyId);
        return regions.Select(z => new EntityMinIncludeParent { Id = z.Id, Name = z.Name, ParentId = z.ParentId, ParentName = z.ParentName }).ToList();
    }

    public async Task<List<GeographiesListWithParent>> GetCompanyZones(bool includeDeactivate)
    {
        var zones = await zoneRepository.GetAllZones(currentUser.CompanyId, includeDeactivate);
        return zones;
    }

    public async Task<List<EntityMinIncludeParent>> GetGeographiesOfLevelByParentId(GeographyLevel level, List<long> parentIds)
    {
        var geographies = new List<EntityMinIncludeParent>();
        switch (level)
        {
            case GeographyLevel.Region:
                geographies = await regionRepository.GetRegionsByZoneIds(currentUser.CompanyId, parentIds);
                break;

            case GeographyLevel.Territory:
                geographies = await territoryRepository.GetTerritoryByRegionIds(currentUser.CompanyId, parentIds);
                break;
            case GeographyLevel.location:
                break;
            case GeographyLevel.Beat:
                break;
            case GeographyLevel.Level4:
                break;
            case GeographyLevel.Level5:
                break;
            case GeographyLevel.Level6:
                break;
            case GeographyLevel.Level7:
                break;
        }

        return geographies;
    }

    public async Task<GeographyInput> GetGeographyById(GeographyLevel level, long geographyId)
    {
        switch (level)
        {
            case GeographyLevel.Territory:
                return await territoryRepository.GetTerritoryById(geographyId, currentUser.CompanyId);

            case GeographyLevel.Region:
                return await regionRepository.GetRegionById(geographyId, currentUser.CompanyId);

            case GeographyLevel.Level4:
                return await zoneRepository.GetZoneById(geographyId, currentUser.CompanyId);
            default:
                return await geographyRepository.GetGeographyById(geographyId, currentUser.CompanyId);
        }
    }

    public async Task<List<LocationDTO>> GetOutletsForGeoType(GeographyLevel type, List<long> ids)
    {
        var outlets = await outletRepository.GetAllActiveOutlets(currentUser.CompanyId, type, ids);
        return outlets;
    }

    public async Task<List<GeographiesListParent>> GetParentOfAllHierarchyLevels(GeographyLevel level)
    {
        List<GeographiesListParent> parentList;
        switch (level)
        {
            case GeographyLevel.Level4:
                parentList = await zoneRepository.GetZoneByParentId(currentUser.CompanyId);
                break;

            case GeographyLevel.Region:
                parentList = await regionRepository.GetZoneByRegion(currentUser.CompanyId);
                break;

            case GeographyLevel.Territory:
                parentList = await territoryRepository.GetRegionByTerritory(currentUser.CompanyId);
                break;
            default:
                parentList = await geographyRepository.GetParentByParentId(currentUser.CompanyId, level);
                break;
        }

        return parentList;
    }

    public async Task<RepositoryResponse> UpdateGeographies(GeographyInput geography)
    {
        var checkValid = await IsValidGeography(geography);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        switch (geography.Level)
        {
            case GeographyLevel.Territory:
                var territoryInsert = new GeographiesListWithParent { Id = geography.Id, ParentId = geography.ParentId, Name = geography.Name, ErpId = geography.ErpId };
                return await territoryRepository.UpdateTerritory(territoryInsert, currentUser.CompanyId);

            case GeographyLevel.Region:
                var regionInsert = new GeographiesListWithParent
                {
                    Id = geography.Id,
                    ParentId = geography.ParentId,
                    Name = geography.Name,
                    ErpId = geography.ErpId,
                    RegionShortCode = geography.RegionShortCode,
                    ODSActive = geography.ODSActive
                };
                return await regionRepository.UpdateRegion(regionInsert, currentUser.CompanyId);

            case GeographyLevel.Level4:
                var zoneInsert = new GeographiesListWithParent { Id = geography.Id, ParentId = geography.ParentId, Name = geography.Name, ErpId = geography.ErpId };
                return await zoneRepository.UpdateZone(zoneInsert, currentUser.CompanyId);

            case GeographyLevel.Level5:
                var level5insert = new GeographiesListWithParent
                {
                    Id = geography.Id,
                    ParentId = geography.ParentId,
                    Name = geography.Name,
                    ErpId = geography.ErpId,
                    Level = geography.Level
                };
                return await geographyRepository.UpdateGeographies(level5insert, currentUser.CompanyId);

            case GeographyLevel.Level6:
                var level6insert = new GeographiesListWithParent
                {
                    Id = geography.Id,
                    ParentId = geography.ParentId,
                    Name = geography.Name,
                    ErpId = geography.ErpId,
                    Level = geography.Level
                };
                return await geographyRepository.UpdateGeographies(level6insert, currentUser.CompanyId);
            default:
                var level7insert = new GeographiesListWithParent { Id = geography.Id, Name = geography.Name, ErpId = geography.ErpId, Level = geography.Level };
                return await geographyRepository.UpdateGeographies(level7insert, currentUser.CompanyId);
        }
    }

    public async Task<List<EntityMinIncludeParent>> GetTerritoriesBasedOnPosition(List<long> positionIds)
    {
        var childPosCodes = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionIds, currentUser.CompanyId);
        var regionIds = childPosCodes.Where(r => r.RegionId.HasValue).Select(p => p.RegionId!.Value).ToList();
        var territories = await GetGeographiesOfLevelByParentId(GeographyLevel.Territory, regionIds);
        return territories;
    }


    public async Task<List<EntityMin>> GetAllHierarchyLevel()
    {
        var geoNomKeys = new List<string>() { "Level5", "Level6", "Level7" };
        var nom = await nomenclatureRepository.GetCompanyNomenclature(currentUser.CompanyId, geoNomKeys);
        List<EntityMin> geographies = new();
        geographies.Add(new EntityMin { Id = (int)GeographyLevel.Territory, Name = GeographyLevel.Territory.ToString() });
        geographies.Add(new EntityMin { Id = (int)GeographyLevel.Region, Name = GeographyLevel.Region.ToString() });
        // No nomanclature for level 4
        geographies.Add(new EntityMin { Id = (int)GeographyLevel.Level4, Name = "Zone" });
        geographies.Add(new EntityMin
        {
            Id = (int)GeographyLevel.Level5,
            Name = nom.TryGetValue("Level5", out var l5) ? l5.DisplayName : "Level5"
        });
        geographies.Add(new EntityMin
        {
            Id = (int)GeographyLevel.Level6,
            Name = nom.TryGetValue("Level6", out var l6) ? l6.DisplayName : "Level6"
        });
        geographies.Add(new EntityMin
        {
            Id = (int)GeographyLevel.Level7,
            Name = nom.TryGetValue("Level7", out var l7) ? l7.DisplayName : "Level7"
        });

        return geographies;
    }

    public async Task<List<EntityMin>> GetRegionsBasedOnPosition(List<long> positionIds)
    {
        var childPosCodes = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionIds, currentUser.CompanyId);
        var regionIds = childPosCodes.Where(r => r.RegionId.HasValue).Select(p => p.RegionId!.Value).ToList();
        var regions = await regionRepository.GetRegionsMinByIds(currentUser.CompanyId, regionIds);
        return regions;
    }
}
