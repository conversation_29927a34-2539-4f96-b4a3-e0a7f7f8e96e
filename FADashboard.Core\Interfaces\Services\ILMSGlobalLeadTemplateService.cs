using FADashboard.Core.Models;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSGlobalLeadTemplateService
    {
        Task<LMSGlobalLeadTemplateDto> GetGlobalLeadTemplateByIdAsync(long id);
        Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetAllGlobalLeadTemplatesAsync();
        Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetGlobalLeadTemplatesByCompanyIdAsync(long companyId);
        Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetGlobalLeadTemplatesByCompanyIdAndTypeAsync(long companyId, LMSTemplateType templateType);
        Task<LMSGlobalLeadTemplateDto> CreateGlobalLeadTemplateAsync(LMSGlobalLeadTemplateDto templateDto, long createdByUserId);
        Task<LMSGlobalLeadTemplateDto> UpdateGlobalLeadTemplateAsync(long id, LMSGlobalLeadTemplateDto templateDto, long? updatedByUserId);
        Task<bool> DeleteGlobalLeadTemplateAsync(long id, long? updatedByUserId);
    }
}
