﻿using FADashboard.Core.Models;

namespace FADashboard.Core.Helper;

public static class EntityEndpointConfig
{
    public static (string Url, bool NeedsToken) GetEndpoint(OutputEntityType entityType)
    {
        return entityType switch
        {
            OutputEntityType.Distributors => ($"/api/master/distributorsV2", true),
            OutputEntityType.Routes => ($"/api/RoutePlan/getPlanForDate", false),
            OutputEntityType.Outlets => ($"/api/outlet/outlets", true),
            OutputEntityType.Products => ($"/api/Product/getSKU", true),
            _ => throw new ArgumentException($"Invalid entity type: {entityType}")
        };
    }
}

