﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ReportSubscriptionDetails
{
    public long ReportId { get; set; }
    public string ReportName { get; set; }
    public Guid EncryptionKey { get; set; }
}
public class ModuleScreenListView
{
    public long Id { get; set; }
    public string ModuleName { get; set; }
    public bool IsModulePremium { get; set; }
    public bool IsModuleDefault { get; set; }
    public string ModuleEnums { get; set; }
    public string Product { get; set; }
    public string Icon { get; set; }
    public List<ScreenLists> ScreenList { get; set; }
}

public class ScreenLists
{
    public long Id { get; set; }
    public string ScreenName { get; set; }
    public string ScreenEnum { get; set; }
    public long ModuleId { get; set; }
    public bool IsInternal { get; set; }
    public bool IsScreenPremium { get; set; }
    public bool IsScreenDefault { get; set; }
    public string Icon { get; set; }
    public string Route { get; set; }
    public string RedirectLink { get; set; }
    public long AccessType { get; set; }
}
public class CompanyConfigModules
{
    public string Product { get; set; }
    public Dictionary<string, List<string>> Modules { get; set; }
}
public class CompanyScreens
{
    public string ScreenEnum { get; set; }
}

public class ModulesView
{
    public long Id { get; set; }
    public string ModuleName { get; set; }
    public string ModuleEnum { get; set; }
    public string Product { get; set; }
    public bool IsModulePremium { get; set; }
    public bool IsModuleDefault { get; set; }
    public string Icon { get; set; }
}

public class RoleMastersView
{
    public long Id { get; set; }
    public string RoleName { get; set; }
    public string RoleDescription { get; set; }
    public Hierarchy Hierarchy { get; set; }
    public bool IsDeleted { get; set; }
}

public class RoleDetailsById
{
    public long Id { get; set; }
    public string RoleName { get; set; }
    public string RoleDescription { get; set; }
    public Hierarchy Hierarchy { get; set; }
    public bool IsDeleted { get; set; }
    public Dictionary<string, Dictionary<string, int>> Permissions { get; set; }
    public List<ReportSubscriptionDetails> ReportSubscriptionDetails { get; set; }
}
