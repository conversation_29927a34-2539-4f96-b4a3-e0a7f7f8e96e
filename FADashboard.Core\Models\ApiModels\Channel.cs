﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class Channel
{
    public string CustomName { get; set; }
    public string DefaultName { get; set; }
    public OutletChannel Enum { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsValid { get; set; }
    public virtual ICollection<ShopType> ShopTypes { get; set; }
}
