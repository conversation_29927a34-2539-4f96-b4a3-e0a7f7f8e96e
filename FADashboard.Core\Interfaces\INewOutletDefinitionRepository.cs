﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface INewOutletDefinitionRepository
{
    Task CreateFields(List<NewOutletFieldAppMeta> fields, ICurrentUser currentUser);

    Task<List<NewOutletFieldAppMeta>> GetFields(long companyId);

    Task UpdateFields(List<NewOutletFieldAppMeta> fields, ICurrentUser currentUser);
}
