﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IMarginSlabRepository
{
    Task<List<MarginSlab>> GetMarginSlabs(long companyId);
    Task<MarginSlabModel> GetMarginSlabById(long companyId, long id);

    Task<RepositoryResponse> CreateOutletMarginSlab(MarginSlabModel marginSlabModel, long companyId);
    Task<RepositoryResponse> UpdateOutletMarginSlab(MarginSlabModel marginSlabModel, long companyId);
    Task<RepositoryResponse> DeactivateOutletMarginSlab(long companyId, long id);
}
