﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class PositionCodeInput
{
    public string CodeId { get; set; }
    public long? EmployeeId { get; set; }
    public long Id { get; set; }
    public bool IsPositionVacant { get; set; }
    public bool IsReportingToAttached { get; set; }
    public PositionCodeLevel Level { get; set; }
    public string Name { get; set; }
    public long? ReportingTo { get; set; }
    public List<long> OutletIds { get; set; }
    public EmployeeType EmployeeType { get; set; }
}
