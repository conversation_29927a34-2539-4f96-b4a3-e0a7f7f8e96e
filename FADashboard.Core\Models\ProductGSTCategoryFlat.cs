﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models;

public class ProductGSTCategoryFlat
{
    public long Id { get; set; }

    [Required] public string Name { get; set; }

    public decimal IGST { get; set; }

    public decimal CGST { get; set; }

    public decimal SGST { get; set; }

    [Range(typeof(decimal), "0", "99", ErrorMessage = "VAT value must be in range 0 to 99")]
    public decimal VAT { get; set; }

    public bool Deleted { get; set; }
}
