﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Libraries.CommonEnums;
namespace FADashboard.Core.Models;
public class PerfectEntityRule
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    [Required(ErrorMessage = "Name is required.")]
    public string Name { get; set; }

    [Required(ErrorMessage = "RuleType is required.")]
    public RuleType RuleType { get; set; }

    [Required(ErrorMessage = "DisplayName is required.")]
    public string DisplayName { get; set; }

    public string Description { get; set; }

    public string? Visibility { get; set; }

    [Required(ErrorMessage = "StartDate is required")]
    public DateTime StartDate { get; set; }

    [Required(ErrorMessage = "EndDate is required")]
    public DateTime EndDate { get; set; }

    [Required(ErrorMessage = "Frequency is required")]
    public int Frequency { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;

    public string CreationContext { get; set; } = "Creatation Context";

    public bool IsDeactive { get; set; } = false;

    public bool Deleted { get; set; } = false;

    [Required(ErrorMessage = "FilterConstraintId is required")]
    public long FilterConstraintId { get; set; }

    public double Weightage { get; set; } = 100;

    public List<long?> ProductDivisionIds { get; set; }

    public bool IsQualifier { get; set; } = false;

    public string QualifierIds { get; set; }

    public string QualifierRelation { get; set; }

    public string CriteriaRelation { get; set; }

    public SubRuleType SubRuleType { get; set; }

}
