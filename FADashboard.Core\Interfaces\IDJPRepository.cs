﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IDJPRepository
{
    Task<List<DJPSequenceDetailDTO>> GetDJPSequenceDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<List<DJPIncrementalDetailDTO>> GetDJPIncrementalDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<RepositoryResponse> SaveDJPSequence(long userId, long companyId, PortalUserRole userRole,
            string fileName, string emailId, DateTime startDate, CancellationToken ct = default);
    Task<RepositoryResponse> SaveDJPIncremental(long userId, long companyId, PortalUserRole userRole,
        string fileName, string emailId, DateTime startDate, CancellationToken ct = default);
    Task UpdateDJPUploadStatus(long id, bool isIncremental, CancellationToken cancellationToken = default);
}
