﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class OutletService(IOutletRepository outletRepository) : RepositoryResponse
{
    public async Task<List<LocationModel>> GetLocationsOfPosition(long companyId, long positionId) => await outletRepository.GetOutletOfPosition(companyId, positionId);

    public async Task<List<LocationModel>> GetOutletBySearchAndCounter(long companyId, string searchString, int counter, long positionId, List<long> regionFilter = null)
    {
        const int batchSize = 10;
        if (regionFilter == null)
            return await outletRepository.GetOutletBySearch(companyId, searchString, counter * batchSize, batchSize, positionId);
        if (regionFilter.Count == 0)
            return [];
        return await outletRepository.GetOutletBySearchAndRegion(companyId, searchString, counter * batchSize, batchSize, regionFilter, positionId);
    }

    public async Task<long> GetOutletCountBySearchAndCounter(long companyId, string searchString, int counter, long positionId, List<long> regionFilter = null)
    {
        if (regionFilter == null)
            return await outletRepository.GetOutletCountBySearch(companyId, searchString, positionId);
        if (regionFilter.Count == 0)
            return 0;
        return await outletRepository.GetOutletCountBySearchRegion(companyId, searchString, regionFilter, positionId);
    }

    public async Task<List<LocationModel>> GetOutlets(long companyId, List<long> regionIds)
    {
        if (regionIds.Count > 0)
        {
            return await outletRepository.GetAllOutletByRegion(companyId, regionIds);
        }

        return await outletRepository.GetAllOutletWithRegion(companyId);
    }
}
