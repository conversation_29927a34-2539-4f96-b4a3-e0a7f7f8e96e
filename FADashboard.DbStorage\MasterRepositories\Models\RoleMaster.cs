﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class RoleMaster : IAuditedEntity
{
    public long Id { get; set; }
    public string RoleName { get; set; }
    public string RoleDescription { get; set; }
    public long CompanyId { get; set; }
    public string Permissions { get; set; }
    public Hierarchy Hierarchy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool IsDeleted { get; set; }
}
