﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("OfficialWorkTypes")]
public class OfficialWorkType : IAuditedEntity, ICreatedEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string CustomName { get; set; }
    public FAEventType Enum { get; set; }
    public long Id { get; set; }
    public bool IsInvalid { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public virtual ICollection<OfficialWorkTypeHierarchyMapping> OfficialWorkTypeHierarchyMapping { get; set; }
}
