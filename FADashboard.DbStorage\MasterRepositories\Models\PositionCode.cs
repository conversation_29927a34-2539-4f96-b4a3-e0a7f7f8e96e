﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("PositionCodes")]
public class PositionCode : IAuditedEntity, IDeletable
{
    public PositionCode()
    {
        PositionCodeEntityMappings = [];
        EmployeeId = null;
        LastEmployeeId = null;
    }

    public string CodeId { get; set; }
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public Guid GUID { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public PositionCodeLevel Level { get; set; }
    public string Name { get; set; }
    public PositionCode Parent { get; set; }
    public long? ParentId { get; set; }
    public EmployeeType PositionType { get; set; }
    public ICollection<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
    public long? EmployeeId { get; set; }

    public long? LastEmployeeId { get; set; }

    public virtual ClientEmployee Employee { get; set; }
    public virtual ClientEmployee LastEmployee { get; set; }
    public ICollection<PositionProductDivisionMapping> PositionProductDivisionMappings { get; set; }

    public PositionCode ShallowCopy() => (PositionCode)MemberwiseClone();
}
