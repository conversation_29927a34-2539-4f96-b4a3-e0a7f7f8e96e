﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class FAIService(ICurrentUser currentUser,
                        IFAISWorkflowRepository fAISWorkflowRepository,
                        IFAISMappingRepository fAISMappingRepository)
{
    public async Task<RepositoryResponse> CreateUpdateFAISWorkflow(FAISWorkflowsDetails fAISWorkflowInput)
    {
        if (fAISWorkflowInput.Id == 0 || fAISWorkflowInput.Id == null)
        {
            return await fAISWorkflowRepository.CreateFAISWorkflow(fAISWorkflowInput, currentUser.CompanyId);
        }
        else
        {
            return await fAISWorkflowRepository.UpdateFAISWorkflow(fAISWorkflowInput, currentUser.CompanyId);
        }
    }

    public async Task<RepositoryResponse> CreateUpdateFAISMapping(FAISMappingsDetails fAISMappingInput)
    {
        if (fAISMappingInput.Id == 0 || fAISMappingInput.Id == null)
        {
            return await fAISMappingRepository.CreateFAISMapping(fAISMappingInput);
        }
        else
        {
            return await fAISMappingRepository.UpdateFAISMapping(fAISMappingInput);
        }
    }
}
