using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.Enums;
using Libraries.CommonEnums;
using System.Text.Json;
using static MongoDB.Driver.WriteConcern;

namespace FADashboard.Core.Services
{
    public class LMSLeadService(ILMSLeadRepository lmsLeadRepository, ILMSLeadActivityService leadActivityService, ILMSCustomFieldValueService customFieldValueService) : ILMSLeadService
    {

        public Task<LMSLeadDto> GetByIdAsync(long lmsLeadId) => lmsLeadRepository.GetByIdAsync(lmsLeadId);

        public Task<IEnumerable<LMSLeadDto>> GetAllAsync() => lmsLeadRepository.GetAllAsync();

        public Task<IEnumerable<LMSLeadDto>> GetAllByCompanyIdAsync(long companyId) => lmsLeadRepository.GetAllByCompanyIdAsync(companyId);

        public Task<IEnumerable<LMSLeadDto>> GetByLMSAccountIdAsync(long lmsAccountId) => lmsLeadRepository.GetByLMSAccountIdAsync(lmsAccountId);

        public Task<PagedResult<LMSLeadDto>> GetByLMSAccountIdAsync(long lmsAccountId, LMSLeadQueryParameters queryParameters) => lmsLeadRepository.GetByLMSAccountIdAsync(lmsAccountId, queryParameters);

        public Task<IEnumerable<LMSLeadDto>> GetByAssignedToAsync(long assignedTo) => lmsLeadRepository.GetByAssignedToAsync(assignedTo);

        public Task<PagedResult<LMSLeadDto>> GetLeadsAsync(long companyId, LMSLeadQueryParameters queryParameters) => lmsLeadRepository.GetLeadsAsync(companyId, queryParameters);

        public async Task<LMSLeadDto> CreateAsync(LMSLeadCreateInput leadInput, long createdBy)
        {
            ArgumentNullException.ThrowIfNull(leadInput);

            var createdLead = await lmsLeadRepository.CreateAsync(leadInput, createdBy);

            if (leadInput.CustomFieldValues != null && leadInput.CustomFieldValues.Count != 0)
            {
                await customFieldValueService.UpsertRangeForEntityAsync((int)LMSCustomFieldsEntityType.Lead, createdLead.Id, leadInput.CustomFieldValues, createdBy);
            }

            //await leadActivityService.LogFieldUpdateActivityAsync(createdLead.Id, createdBy, "Lead", null, "Created");
            //await leadActivityService.LogStageChangeActivityAsync(createdLead.Id, createdBy, 0, createdLead.LeadStageId);

            return createdLead;
        }

        public async Task<LMSLeadDto> UpdateAsync(long leadId, LMSLeadUpdateInput leadInput, long updatedBy)
        {
            ArgumentNullException.ThrowIfNull(leadInput);

            var existingLead = await lmsLeadRepository.GetByIdAsync(leadId);
            if (existingLead == null)
            {
                throw new KeyNotFoundException($"LMSLead with ID {leadId} not found.");
            }

            var updatedLead = await lmsLeadRepository.UpdateAsync(leadId, leadInput, updatedBy);

            if (leadInput.CustomFieldValues != null)
            {
                await customFieldValueService.UpsertRangeForEntityAsync((int)LMSCustomFieldsEntityType.Lead, leadId, leadInput.CustomFieldValues, updatedBy);
            }

            var activitiesToLog = new List<LMSLeadActivityDTO>();

            // Log stage change
            if (leadInput.LeadStageId != existingLead.LeadStageId)
            {
                activitiesToLog.Add(new LMSLeadActivityDTO
                {
                    CompanyId = leadInput.CompanyId,
                    LeadId = leadId,
                    Title = "Stage update",
                    ActivityType = LMSActivityType.StageChange,
                    Source = LMSActivitySource.Web,
                    CreatedBy = updatedBy,
                    CreatedAt = DateTime.UtcNow,
                    AuditTrail = JsonSerializer.Serialize(new
                    {
                        FieldName = "LeadStageId",
                        OldValue = existingLead.LeadStageId.ToString(),
                        NewValue = leadInput.LeadStageId.ToString()
                    })
                });
            }

            // Log field changes
            var fieldChanges = GetLeadFieldChanges(existingLead, leadInput);
            if (fieldChanges.Count != 0)
            {
                activitiesToLog.Add(new LMSLeadActivityDTO
                {
                    CompanyId = leadInput.CompanyId,
                    LeadId = leadId,
                    Title = "Lead update",
                    ActivityType = LMSActivityType.LeadUpdate,
                    Source = LMSActivitySource.Web,
                    CreatedBy = updatedBy,
                    CreatedAt = DateTime.UtcNow,
                    AuditTrail = JsonSerializer.Serialize(fieldChanges)
                });
            }

            if (activitiesToLog.Count != 0)
            {
                await leadActivityService.CreateBulkActivitiesAsync(activitiesToLog);
            }

            return updatedLead;
        }

        private static List<object> GetLeadFieldChanges(LMSLeadDto existingLead, LMSLeadUpdateInput leadInput)
        {
            var fieldChanges = new List<object>();

            void AddChangeIfDifferent<T>(string fieldName, T oldValue, T newValue)
            {
                if (!EqualityComparer<T>.Default.Equals(oldValue, newValue))
                {
                    fieldChanges.Add(new { FieldName = fieldName, OldValue = oldValue.ToString(), NewValue = newValue.ToString() });
                }
            }

            AddChangeIfDifferent("LeadName", existingLead.LeadName, leadInput.LeadName);
            AddChangeIfDifferent("Description", existingLead.Description, leadInput.Description);
            AddChangeIfDifferent("Amount", existingLead.Amount, leadInput.Amount);
            AddChangeIfDifferent("LeadSourceId", existingLead.LeadSourceId, leadInput.LeadSourceId);
            AddChangeIfDifferent("City", existingLead.City, leadInput.City);
            AddChangeIfDifferent("State", existingLead.State, leadInput.State);
            AddChangeIfDifferent("Country", existingLead.Country, leadInput.Country);


            AddChangeIfDifferent("Latitude", existingLead.Latitude, leadInput.Latitude);
            AddChangeIfDifferent("Longitude", existingLead.Longitude, leadInput.Longitude);
            AddChangeIfDifferent("Pincode", existingLead.Pincode, leadInput.Pincode);
            AddChangeIfDifferent("Email", existingLead.Email, leadInput.Email);
            AddChangeIfDifferent("Website", existingLead.Website, leadInput.Website);
            AddChangeIfDifferent("Status", existingLead.Status, leadInput.Status);
            AddChangeIfDifferent("Priority", existingLead.Priority, leadInput.Priority);
            AddChangeIfDifferent("ClosingDate", existingLead.ClosingDate, leadInput.ClosingDate);
            AddChangeIfDifferent("FollowUpDate", existingLead.FollowUpDate, leadInput.FollowUpDate);
            AddChangeIfDifferent("AssignedTo", existingLead.AssignedTo, leadInput.AssignedTo);

            return fieldChanges;
        }

        public async Task<bool> DeleteAsync(long lmsLeadId, long updatedBy)
        {
            var existingLead = await lmsLeadRepository.GetByIdAsync(lmsLeadId);
            if (existingLead == null)
            {
                throw new KeyNotFoundException($"LMSLead with ID {lmsLeadId} not found.");
            }

            var result = await lmsLeadRepository.DeleteAsync(lmsLeadId, updatedBy);
            //if (result)
            //{
            //    await leadActivityService.LogFieldUpdateActivityAsync(lmsLeadId, updatedBy, "Lead", "Active", "Deleted");
            //}

            return result;
        }
    }
}
