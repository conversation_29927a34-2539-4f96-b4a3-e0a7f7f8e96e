﻿namespace FADashboard.Core.Interfaces;

public interface IManagerRepository
{
    Task<Dictionary<long, string>> GetAllPositionsManagerDictionary(long companyId);

    Task<List<long>> GetPositionIdsUnderManager(long managerId, long companyId);

    Task<Dictionary<long, string>> GetPositionManagerDictionary(long managerId, long companyId);
    Task<Dictionary<long, string>> GetPositionIdsManagerDictionary(List<long> posIds, long companyId);
}
