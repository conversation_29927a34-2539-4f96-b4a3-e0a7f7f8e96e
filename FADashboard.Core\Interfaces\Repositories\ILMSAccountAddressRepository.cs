﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSAccountAddressRepository
    {
        Task<LMSAccountAddressDto> GetByIdAsync(long id);
        Task<IEnumerable<LMSAccountAddressDto>> GetAllAsync();
        Task<IEnumerable<LMSAccountAddressDto>> GetByAccountIdAsync(long accountId);
        Task<LMSAccountAddressDto> AddAsync(LMSAccountAddressDto entity);
        Task<LMSAccountAddressDto> UpdateAsync(LMSAccountAddressDto entity);
        Task<bool> DeleteAsync(long id, long? updatedByUserId);
    }
}
