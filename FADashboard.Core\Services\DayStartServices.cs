﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.DateTimeHelpers;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class DayStartServicesReportApi(IDayStartRepository dayStartRepository, IPositionCodeRepository positionCodeRepository, ICurrentUser currentUser, FAResilientHttpClient resilientHttpClient, AppConfigSettings appConfigSettings)
    : RepositoryResponse
{
    public async Task<DayStartTypeSummary> GetDailyDayStartSummary(long companyId, long userId, PortalUserRole userRole, DateTime date)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/ds/dayUserSummary?companyId={companyId}&date={date:MM/dd/yyyy}&userId={userId}&userRole={userRole}";
        var data = await resilientHttpClient.GetJsonAsync<DayStartTypeSummary>(dataUrl, appConfigSettings.reportApiToken);
        return data;
    }

    public async Task<DayRecordsMinWithCount> GetDSforDay(DateTime today, List<long> positionCodeIds, PaginationFilter validFilter, List<int> positionCodeLvls, DayStartType? dayStartType, PortalUserRole userRole = PortalUserRole.GlobalAdmin, List<long> regionIds = null
        , bool includeDeactiveUsers = true)
    {
        var todayDateKey = today.GetDateKey_String();
        var positionData = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfoForDailySummary(positionCodeIds, currentUser.CompanyId, userRole, regionIds, includeDeactiveUsers);
        var usersDSData = await dayStartRepository.GetSearchedDayRecords(currentUser.CompanyId, validFilter, positionCodeLvls, dayStartType, positionData, userRole, todayDateKey);
        return usersDSData;
    }
}
