﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IDistributorWiseTaxIntegrationRepository
{
    Task<List<long?>> GetDeviceConfiguredDistributors(long companyId);
    Task<RepositoryResponse> CreateDistributorWiseTaxIntegration(TaxIntegrationInputModel inputModel, long companyId);
    Task<List<DeviceConfiguredDistributorsMeta>> GetAllTaxDevicesConfiguredDistributors(long companyId);
    Task<DeviceConfiguredDistributorsMeta> GetDeviceConfiguredDistributor(long id, long companyId);
    Task<RepositoryResponse> DeactivateTaxDeviceConfiguredDistributor(long id, long companyId);
    Task<RepositoryResponse> UpdateDistributorWiseTaxIntegration(TaxIntegrationInputModel inputModel, long companyId);


}
