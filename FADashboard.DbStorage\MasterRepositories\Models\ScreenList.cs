﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ScreenList
{
    public long Id { get; set; }
    public string ScreenName { get; set; }
    public string ScreenEnum { get; set; }
    [ForeignKey("Modules")]
    public long ModuleId { get; set; }
    public Module Module { get; set; }
    public bool IsInternal { get; set; }
    public bool IsPremium { get; set; }
    public bool IsDefault { get; set; }
    public string Icon { get; set; }
    public string Route { get; set; }
    public string RedirectLink { get; set; }
    public long AccessType { get; set; }
}
