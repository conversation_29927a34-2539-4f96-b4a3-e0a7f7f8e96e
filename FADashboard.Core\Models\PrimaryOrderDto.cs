﻿namespace FADashboard.Core.Models;
public class PrimaryOrderDto
{
    public Guid Guid { set; get; }
    public GeoLocation GeoLocation { get; set; }
    public bool IsUpdateGeoLocation { get; set; }
    public long DistributorId { set; get; }
    public string DistributorName { get; set; }
    public int StockistType { get; set; }
    public string DistributorAddress { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public long DayStartRecordId { get; set; }
    public long Time { get; set; }
    public string RemarksDistributor { set; get; }
    public string Remark { set; get; }
    public bool Productive { get; set; }
    public string NoSalesReason { get; set; }
    public decimal? OrderDiscount { get; set; }
    public ICollection<PrimaryOrderItem> PrimaryOrderItems { get; set; }
    public IEnumerable<PrimarySchemeSale> PrimarySchemes { get; set; }
    public string PrimaryOrderType { set; get; }
    public bool IsOVC { get; set; }
    public bool? IsTelephonic { get; set; }
    public long? PositionCodeId { get; set; }
    public decimal? CGST { get; set; }
    public decimal? SGST { get; set; }
    public decimal? IGST { get; set; }
    public decimal? GSTValueforsale { get; set; }
    public long? SuperstockistId { get; set; }
    public long? FactoryId { get; set; }
}

public class PrimaryOrderItem
{
    public long ProductId { get; set; }
    public string ProductName { get; set; }
    public double SaleValue { get; set; }

    public double? PTD { set; get; }

    public double? Discount { set; get; }

    public bool? IsPromoted { set; get; }
    public double? OriginalPTD { get; set; }
    public string MRP { get; set; }
    public decimal? CGST { get; set; }
    public decimal? SGST { get; set; }
    public decimal? IGST { get; set; }
    public decimal? GSTValueforsale { get; set; }
    public long? SuperstockistId { get; set; }
    public long? FactoryId { get; set; }
    public long? BasketId { get; set; }
}

public class PrimarySchemeSale
{
    public long SchemeId { get; set; }

    public long? SlabId { set; get; }

    public int TotalOrderQty { get; set; }

    public decimal TotalOrderValue { get; set; }

    public decimal TotalFOCValue { get; set; }

    public int TotalFOCQty { set; get; }

    public int TotalArticleQty { set; get; }

    public double? SchemeDiscount { set; get; }

    public ICollection<PrimarySchemeSaleitem> PrimarySchemeSaleItems { set; get; }
}

public class PrimarySchemeSaleitem
{
    public decimal OrderQty { get; set; }

    public decimal OrderValue { get; set; }

    public decimal FOCValue { get; set; }
    public long ProductId { get; set; }
    public decimal FOCQty { get; set; }
}

public class GeoLocation
{
    public string Captured_Address { get; set; }

    public bool HasValue => Longitude.HasValue && Latitude.HasValue;

    public bool IsGPSOff { get; set; }
    public decimal? Latitude { get; set; }
    public double? LocationAccuracy { get; set; }
    public decimal? Longitude { get; set; }
    public string Source { get; set; }
}
