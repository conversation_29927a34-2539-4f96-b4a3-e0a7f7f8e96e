﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IImageRecognitionLogicsRepository
{
    Task<List<ImageRecognitionLogicList>> GetAllImageRecognitionLogics(long companyId, bool includeDeactive);
    Task<ImageRecognitionLogicsView> GetImageRecognitionLogicById(long id, long companyId);
    Task<RepositoryResponse> ActivateDeactivateImageRecognitionLogic(long companyId, long id, bool action);
    Task<RepositoryResponse> CreateImageRecognitionLogic(long companyId, ImageRecognitionLogicsView iRLogic);
    Task<RepositoryResponse> UdpateImageRecognitionLogic(long companyId, ImageRecognitionLogicsView iRLogic);
}
