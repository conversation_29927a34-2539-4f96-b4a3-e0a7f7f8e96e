﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class ConfigurationService(
    ICurrentUser currentUser,
    ICompanyRepository companyRepository,
    IScreenListRepository screenListRepository,
    IRetailerConnectRepository retailerConnectRepository
) : RepositoryResponse
{

    public async Task<RepositoryResponse> SaveRetailerConnectPromotionalConfigurations(RetailerConnectPromotionalModel dataToSave)
    {
        dataToSave.CompanyId = currentUser.CompanyId;
        var data = await retailerConnectRepository.SaveRetailerConnectPromotionalConfigurations(dataToSave);
        return data;
    }

    public async Task<RetailerConnectPromotionalModel?> GetRetailerConnectPromotionalConfiguration()
    {
        var data =  await retailerConnectRepository.GetRetailerConnectPromotionalConfiguration(currentUser.CompanyId);
        if(data == null)
            return new RetailerConnectPromotionalModel();
        else return data;
    }

}



