﻿using FADashboard.Core.Interfaces.Cache;
using FADashboard.Core.Models;
using FADashboard.DbStorage.DbContexts;
using FADashboard.DbStorage.Helpers;
using FADashboard.DbStorage.MasterRepositories;
using FADashboard.DbStorage.MasterRepositories.Models;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.CachedRepositories;

public class CachedCompanySettingsRepository(ICacheProvider cacheProvider, MasterDbContext db, WritableMasterDbTransactionContext writableDb, AppConfigSettings appConfigSettings) : CompanySettingsRepository(db, writableDb)
{
    private readonly CacheHelper cacheHelper = new(cacheProvider);
    private string GetCacheKey(long companyId, string keyPrefix = "NewCompanySettings") => $"{appConfigSettings.deployments}:{keyPrefix}:{companyId}";

    public override async Task<Dictionary<string, object>> GetSettingsCompany(long companyId)
    {
        var cacheKey = GetCacheKey(companyId);
        const int expiresIn = 60 * 4; //minutes
        return await cacheHelper.GetResultAsync(cacheKey, expiresIn, () => base.GetSettingsCompany(companyId)).ConfigureAwait(false);
    }

    public override async Task<RepositoryResponse> UpdateSettingValues(List<CompanySettingsValues> vals, long companyId)
    {
        try
        {
            // Clear the cache before updating settings
            var cacheKey = GetCacheKey(companyId);
            await cacheHelper.ClearCache(cacheKey);

            // Retrieve existing values from the database
            var existingValuesDict = writableDb.CompanySettingValues
                .Where(v => v.CompanyId == companyId)
                .ToDictionary(v => v.SettingId, v => v);

            var valuesToBeAdded = new List<CompanySettingValue>();
            var valuesToBeUpdated = new List<CompanySettingValue>();

            foreach (var item in vals)
            {
                // Hack for setting type list string
                if (item.SettingType == CompanySettingType.TextList)
                {
                    item.SettingValue = JsonConvert.SerializeObject(item.SettingValues);
                }

                var val = new CompanySettingValue(companyId, item.SettingId) { SettingValue = item.SettingValue };

                if (existingValuesDict.TryGetValue(item.SettingId, out var existingVal))
                {
                    if (existingVal.SettingValue != val.SettingValue)
                    {
                        existingVal.SettingValue = val.SettingValue;
                        valuesToBeUpdated.Add(existingVal);
                    }
                }
                else
                {
                    valuesToBeAdded.Add(val);
                }
            }

            var company = await writableDb.Companies.SingleOrDefaultAsync(c => c.Id == companyId);
            company.LastUpdatedAt = DateTime.UtcNow;

            await using var transaction = await writableDb.Database.BeginTransactionAsync();
            if (valuesToBeAdded.Count > 0)
            {
                await writableDb.CompanySettingValues.AddRangeAsync(valuesToBeAdded);
            }

            if (valuesToBeUpdated.Count > 0)
            {
                writableDb.CompanySettingValues.UpdateRange(valuesToBeUpdated);
            }

            await writableDb.SaveChangesAsync();
            await transaction.CommitAsync();

            return new RepositoryResponse { Id = companyId, Message = $"Company Settings for CompanyId {companyId} Changed Successfully", IsSuccess = true, };
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = companyId, ExceptionMessage = ex.GetBaseException().Message, Message = "An Error Occurred while trying to save CompanySettings", IsSuccess = false };
        }
    }
}
