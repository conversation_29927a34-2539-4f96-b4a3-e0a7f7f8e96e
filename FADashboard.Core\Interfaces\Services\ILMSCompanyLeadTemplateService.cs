using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSCompanyLeadTemplateService
    {
        Task<LMSCompanyLeadTemplateDto> GetCompanyLeadTemplateByIdAsync(long id);
        Task<PagedResult<LMSCompanyLeadTemplateDto>> GetCompanyLeadTemplatesAsync(long companyId, LMSLeadTemplateQueryParameters queryParameters);
        Task<LMSCompanyLeadTemplateDto> CreateCompanyLeadTemplateAsync(long companyId, LMSCompanyLeadTemplateInput templateInput, long createdByUserId);
        Task<LMSCompanyLeadTemplateDto> UpdateCompanyLeadTemplateAsync(long id, long companyId, LMSCompanyLeadTemplateInput templateInput, long updatedByUserId);
        Task<bool> DeleteCompanyLeadTemplateAsync(long id, long companyId);
        Task<bool> SetDefaultAsync(long id, long companyId, long updatedByUserId);
        Task<bool> SetActiveStatusAsync(long id, long companyId, bool isActive, long updatedByUserId);
    }
}
