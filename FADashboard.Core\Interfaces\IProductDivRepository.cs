﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IProductDivRepository
{
    Task<RepositoryResponse> ActivateDeactivateProductDivision(long id, long companyId, bool action);

    Task<RepositoryResponse> CreateProductDivision(ProductCategoryDivision pd, long companyId);
    Task<List<ProductDivisionAttachedPositionInfo>> GetPositionProductDivisionInfo(List<long> positionIds);


    Task<Dictionary<long, List<long>>> GetEmployeeWiseProductDiv(long companyId, List<long> empIds);

    Task<Dictionary<long, string>> GetProdDivDict(long companyId, List<long> ids);

    Task<ProductCategoryDivision> GetProductDivisionById(long companyId, long id);

    Task<List<ProductCategoryDivision>> GetProductDivisionDetailedList(long companyId, bool includeInactive = false);

    Task<List<EntityMinWithErp>> GetProductDivisionMin(long companyId, bool includeInactive = false);

    Task<List<EntityMinWithErp>> GetProductDivisionList(long companyId, bool includeInactive = false);

    Task<List<EntityMinIds>> GetProductDivisionsforDistributors(List<long> distributorIds, long companyId);

    Task<List<long>> GetSkusForPositions(long companyId, List<long> userPositions);

    Task<List<long>> GetSkusInEmployeeProductDivisions(long companyId, long empId);

    Task<RepositoryResponse> UpdateProductDivision(ProductCategoryDivision pd, long companyId);

    Task<bool> UpdateProductDivisionMapping(EmployeeInput employee, long companyId);
    Task<bool> UpdateProductDivisionMappingToPosition(EmployeeInput employee, long companyId);
}
