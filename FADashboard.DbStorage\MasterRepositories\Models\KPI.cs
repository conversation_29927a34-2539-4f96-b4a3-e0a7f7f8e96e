﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class KPI : IAuditedEntity
{
    public KPI()
    {
        IsDeactivated = false;
    }
    public long Id { get; set; }
    public string CreationContext { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public KpiFrequency Frequency { get; set; }
    public Tier Tier { get; set; }
    public KpiUserType UserType { get; set; }
    public KpiObjective Objective { get; set; }
    public KpiCalculation Calculation { get; set; }
    public KpiMeasure Measure { get; set; }
    public bool IsQualifier { get; set; }
    public bool IsIncentive { get; set; }
    public string Description { get; set; }
    public KpiType KPIType { get; set; }
    public string SQLQuery { get; set; }
    public bool IsDeactivated { get; set; }
    public string Name { get; set; }
    public string UIName { get; set; }
    public int Sequence { get; set; }
    public Relation Relation { get; set; }
    public string MasterSQLQuery { get; set; }
    public string TargetSQLQuery { get; set; }
    public string TransactionSQLQuery { get; set; }
    public string DmsSQLQuery { get; set; }
    public string UnifySQLQuery { get; set; }
    public string ParameterReferences { get; set; }
    public DayOfWeek? WeekOfDay { get; set; }

    public AlertPlacement AlertPlacement { get; set; }
    public int? TimeAlertNumber { get; set; }
    public TimeSpan? TimeAlertWatch { get; set; }
    public int? ProgressInterval { get; set; }
    public int? ProgressStart { get; set; }
    public string TimeAlertMessage { get; set; }
    public string ProgressAlertMessage { get; set; }
    public AlertPlacementInApp AlertPlacementInApp { get; set; }
}
