﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IOrderBlockRepository
{
    Task<List<OrderBlockList>> GetAllBlockOrder();
    Task<OrderBlockModel> GetOrderBlockById(long id);
    Task<RepositoryResponse> CreateOrderBlock(OrderBlockModel newJourneyPlan);
    Task<RepositoryResponse> UpdateOrderBlock(OrderBlockModel newJourneyPlan);
    Task<RepositoryResponse> ActivateDeactivateOrderBlock(long id, bool action);
}
