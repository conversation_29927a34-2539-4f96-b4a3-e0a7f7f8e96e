﻿namespace FADashboard.Core.Models.ApiModels;

public class WhiteListedIPList
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public bool IsDeleted { get; set; }
    public string StartIPAddress { get; set; }
    public string EndIPAddress { get; set; }
}

public class IpAdress
{
    public string StartIPAddress { get; set; }
    public string EndIPAddress { get; set; }
}

public class CreateWhiteListedIPsRequest
{
    public string RuleName { get; set; }
    public List<IpAdress> IpAdresses { get; set; }
}
