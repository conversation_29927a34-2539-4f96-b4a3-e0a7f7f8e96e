﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class DistributorCreationRequestManagerEdit
{
    public long Id { get; set; }

    [StringLength(200)]
    public string Aadhar { get; set; }
    [StringLength(200)]
    public string AadharImage { get; set; }
    [StringLength(200)]
    public string AccountName { get; set; }
    public long AdditionRequestId { get; set; }
    public string Address { get; set; }
    [StringLength(200)]
    public string BankAccountNumber { get; set; }
    public string BeatId { get; set; }
    [StringLength(2000)]
    public string CityName { get; set; }
    public long CompanyId { get; set; }
    [StringLength(2000)]
    public string Country { get; set; }
    [StringLength(200)]
    public string DOB { get; set; }
    public double? DistanceFromSuperStockist { get; set; }
    [StringLength(2000)]
    public string District { get; set; }
    public long? EditedBy { get; set; }
    public PortalUserRole? EditedByUserRole { get; set; }
    public DateTime? EditedOn { get; set; }
    [StringLength(200)]
    public string GSTIN { get; set; }
    public Guid Guid { get; set; }
    [StringLength(200)]
    public string IFSCCode { get; set; }
    [StringLength(200)]
    public string Image { get; set; }
    public bool IsGSTRegistered { get; set; }
    public bool IsSubDistReplaced { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    [StringLength(2000)]
    public string Name { get; set; }
    [StringLength(2000)]
    public string OwnerName { get; set; }
    [StringLength(200)]
    public string OwnerNo { get; set; }
    [StringLength(200)]
    public string PAN { get; set; }
    [StringLength(200)]
    public string PinCode { get; set; }
    public int? RetailOutletCovered { get; set; }
    public int? SalesmanCount { get; set; }
    [StringLength(200)]
    public string SelfieWithOwner { get; set; }
    [StringLength(2000)]
    public string StateName { get; set; }
    [StringLength(2000)]
    public string SubCity { get; set; }
    public long? SuperStockistId { get; set; }
    public int? TownCovered { get; set; }
    public int? VehicleCount { get; set; }
    public int? WholeSaleOutletCovered { get; set; }
    public int? SubDistType { get; set; }
    [StringLength(128)]
    public string CurrentTurnOver { get; set; }
    public bool? DBorSuperstockist { get; set; }
    [StringLength(512)]
    public string GodownImage { get; set; }
    [StringLength(128)]
    public string InitialInvestment { get; set; }
    [StringLength(128)]
    public string OtherCompanyAgencies { get; set; }
    [StringLength(128)]
    public string Remark { get; set; }
    public int? YearsinAgencyBusiness { get; set; }
    public long? GeographicalMappingId { get; set; }
}
