﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class NotificationCreateInput
{
    public long Id { get; set; }

    [StringLength(128)]
    public string Name { get; set; }

    [StringLength(512)]
    public string Description { get; set; }

    public NotificationCondition NotificationType { get; set; }

    public bool Deleted { get; set; }

    public UserPlatform UserPlatform { get; set; }

    public List<long> CohortIds { get; set; }

    [StringLength(32)]
    public string Cron { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }
    public bool IsAggregated { get; set; }
    [StringLength(32)]
    public string AggregationCron { get; set; }
    public int? AggregationDelay { get; set; }
    public List<long> ManagerLevel { get; set; }
    public ICollection<KPITriggerCreateInput> KPITriggers { set; get; }
    public NotificationMessageInput NotificationMessages { set; get; }
    public NotificationMessageInput AggregatedMessages { set; get; }
}

public class KPITriggerCreateInput
{
    public long KpiId { get; set; }
    public ComparisonOperator ComparisonOperator { get; set; }
    public string Value { get; set; }
}

public class NotificationMessageInput
{
    public UserPlatform NotificationApp { get; set; }

    [StringLength(64)]
    public string Title { get; set; }

    public AppNotificationType? AppNotificationType { get; set; }

    public int? AppScreen { get; set; }

    public List<string> MessageText { get; set; }

    [StringLength(512)]
    public string Uri { get; set; }

    public int? OnClickScreen { get; set; }

    [StringLength(16)]
    public string Emoji { get; set; }

    public bool IsDeactive { get; set; }
    public bool ForWhatsapp { get; set; }
    public string TemplateID { get; set; }
    public List<string> WhatsappVariables { get; set; }
    public string Image { get; set; }

}
