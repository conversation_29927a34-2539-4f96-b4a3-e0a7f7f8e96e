﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class ProductPricingService(
    IProductPricingRepository productPricingRepository,
    ICurrentUser currentUser,
    IProductRepository productRepository,
    IGSTCategoryRepository gstCategoryRepository,
    ICESSCategoryRepository cessCategoryRepository) : RepositoryResponse
{
    public async Task<ProductPricingResponse> UpdateProductPricing(ProductPricingInput productPricingInput)
    {
        var gstTaxesDict = (await gstCategoryRepository.GetGSTCategories(currentUser.CompanyId)).ToDictionary(s => s.Id, s => s);
        var cessTaxesDict = (await cessCategoryRepository.GetCESSCategories(currentUser.CompanyId)).ToDictionary(s => s.Id, s => s);
        var productDict = (await productRepository.GetProducts(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s);

        switch (productPricingInput.PricingType)
        {
            case PricingMastertype.Default:
                return await productPricingRepository.UpdateProductsDefaultPricing(productPricingInput, currentUser.CompanyId, gstTaxesDict, cessTaxesDict, productDict);
            case PricingMastertype.Regional:
                return await productPricingRepository.UpdateProductsRegionalPricing(productPricingInput, currentUser.CompanyId, gstTaxesDict, cessTaxesDict, productDict);
            case PricingMastertype.Stockist:
                return await productPricingRepository.UpdateProductsStockistPricing(productPricingInput, currentUser.CompanyId, gstTaxesDict, cessTaxesDict, productDict);
            case PricingMastertype.StockistCategory:
                return await productPricingRepository.UpdateProductsStockistCategoryPricing(productPricingInput, currentUser.CompanyId, gstTaxesDict, cessTaxesDict, productDict);
            default:
                return new ProductPricingResponse { IsSuccess = false, ResponseStatus = ResponseStatus.Failure, Message = "Not a valid Pricing Type", };
        }
    }

    public async Task<List<decimal>> GetProductPricingMrp(List<long> productIds) => await productPricingRepository.GetProductPricingMrp(productIds, currentUser.CompanyId);

    public async Task<ProductPricingResponse> UpdateProductPrice(ProductPricingInput productPricingInput)
    {
        var productDict = (await productRepository.GetProducts(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s);

        switch (productPricingInput.PricingType)
        {
            case PricingMastertype.Default:
                return await productPricingRepository.UpdateProductsDefaultPrice(productPricingInput, currentUser.CompanyId, productDict);
            case PricingMastertype.Regional:
                return await productPricingRepository.UpdateProductsRegionalPrice(productPricingInput, currentUser.CompanyId, productDict);
            case PricingMastertype.Stockist:
                return await productPricingRepository.UpdateProductsStockistPrice(productPricingInput, currentUser.CompanyId, productDict);
            case PricingMastertype.StockistCategory:
                return await productPricingRepository.UpdateProductsStockistCategoryPrice(productPricingInput, currentUser.CompanyId, productDict);
            default:
                return new ProductPricingResponse { IsSuccess = false, ResponseStatus = ResponseStatus.Failure, Message = "Not a valid Pricing Type", };
        }
    }

    public async Task<PagedResponse<List<ProductPricingList>>> GetPricingMasterPricingTypeWise(PaginationFilter validFilter, PricingMastertype pricingMastertype)
    {
        var productPricingMaster = await productPricingRepository.GetPricingMasterPricingTypeWise(currentUser.CompanyId, validFilter, pricingMastertype);
        var totalRecords = productPricingMaster.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(productPricingMaster.ProductPricingRecords, validFilter, totalRecords);
        return pagedReponse;
    }
}
