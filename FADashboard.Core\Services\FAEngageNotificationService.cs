﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class FAEngageNotificationService(
    ICohortRepository cohortRepository,
    ICurrentUser currentUser,
    INotificationRepository notificationRepository) : RepositoryResponse
{
    public async Task<PagedResponse<List<CohortMin>>> GetAllCohort(PaginationFilter validFilter)
    {
        var cohorts = await cohortRepository.GetCohorts(currentUser.CompanyId, validFilter);
        var pagedReponse = PaginationHelper.CreatePagedReponse(cohorts.CohortMin, validFilter, cohorts.Count);
        return pagedReponse;
    }

    public async Task<CohortViewModel> GetCohort(long cohortId)
    {
        var cohorts = await cohortRepository.GetCohort(cohortId, currentUser.CompanyId);
        return cohorts;
    }

    public async Task<RepositoryResponse> CreateCohort(CohortDTO cohort) => await cohortRepository.CreateCohort(cohort, currentUser.CompanyId);

    public async Task<RepositoryResponse> UpdateCohort(CohortDTO cohort) => await cohortRepository.UpdateCohort(cohort, currentUser.CompanyId);

    public async Task<RepositoryResponse> ActivateDeactivateCohort(long cohortId, bool action) => await cohortRepository.ActivateDeactivateCohorts(cohortId, currentUser.CompanyId, action);

    public async Task<List<NotificationList>> GetNotifications(bool showDeactive)
    {
        var result = await notificationRepository.GetNotifications(currentUser.CompanyId, showDeactive);
        return result;
    }

    public async Task<NotificationDetails> GetNotificationById(long id)
    {
        var result = await notificationRepository.GetNotificationById(currentUser.CompanyId, id);
        return result;
    }

    public async Task<RepositoryResponse> ActivateDeactivateNotification(long Id, bool activateAction) => await notificationRepository.ActionNotifications(currentUser.CompanyId, Id, activateAction);

    public async Task<List<CohortViewModel>> GetCohorts(bool showDeactive)
    {
        var result = await cohortRepository.GetCohorts(currentUser.CompanyId, showDeactive);
        return result;
    }

    public async Task<RepositoryResponse> CreateNotification(NotificationCreateInput notification)
    {
        var maxStringLength = 512;
        if (notification.NotificationMessages is { MessageText.Count: > 0 })
        {
            if (notification.NotificationMessages.MessageText.Any(p => p.Length > maxStringLength))
            {
                return new RepositoryResponse { Id = 0, Message = $"Notification Message have a maximum limit of {maxStringLength} characters", IsSuccess = false };
            }
        }

        if (notification.IsAggregated && notification.AggregatedMessages is { MessageText.Count: > 0 })
        {
            if (notification.AggregatedMessages.MessageText.Any(p => p.Length > maxStringLength))
            {
                return new RepositoryResponse { Id = 0, Message = $"Aggregated Message have a maximum limit of {maxStringLength} characters", IsSuccess = false };
            }
        }

        return await notificationRepository.CreateNotification(notification, currentUser.CompanyId);
    }
}
