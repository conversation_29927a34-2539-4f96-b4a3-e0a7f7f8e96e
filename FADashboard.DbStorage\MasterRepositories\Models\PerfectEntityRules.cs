﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class PerfectEntityRule : IDeactivatable, IDeletable, ICompanyEntity, ICreatedEntity
{
    [Column("Id")]
    public long Id { get; set; }

    [Column("CompanyId")]
    public long CompanyId { get; set; }

    [Column("Name")]
    [StringLength(64)]
    public string Name { get; set; }

    [Column("RuleType")]
    public RuleType RuleType { get; set; }

    [Column("DisplayName")]
    [StringLength(64)]
    public string DisplayName { get; set; }

    [Column("Description")]
    [StringLength(128)]
    public string Description { get; set; }

    [Column("Visibility")]
    [StringLength(128)]
    public string Visibility { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    [Column("Frequency")]
    public int Frequency { get; set; }

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [Column("CreationContext")]
    public string CreationContext { get; set; }

    [Column("IsDeactive")]
    public bool IsDeactive { get; set; }

    [Column("Deleted")]
    public bool Deleted { get; set; } = false;

    [Column("FilterConstraintId")]
    public long FilterConstraintId { get; set; }

    [Column("Weightage")]
    public double Weightage { get; set; }

    [Column("ProductDivisionIds")]
    public List<long?> ProductDivisionIds { get; set; }

    [Column("IsQualifier")]
    public bool IsQualifier { get; set; }

    [Column("QualifierIds")]
    public List<long> QualifierIds { get; set; }

    [Column("QualifierRelation")]
    public string QualifierRelation { get; set; }

    [Column("CriteriaRelation")]
    public string CriteriaRelation { get; set; }
    [Column("SubRuleType")]
    public SubRuleType SubRuleType { get; set; }
    public List<PerfectEntityRuleCriteria> Criterias { get; set; } = new List<PerfectEntityRuleCriteria>();

}
