﻿using System.Net.Http.Headers;
using System.Text;

namespace FADashboard.Core.Services;

public class GTAppApiService(HttpClient httpClient)
{
    private readonly HttpClient httpClient = httpClient;

    public async Task<string> GetApiResponse(string endPoint, string token = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, endPoint);

        if (!string.IsNullOrEmpty(token))
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        var response = await httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }

    public async Task<string> PostApiResponse(string endPoint, string token, string jsonContent)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, endPoint);

        if (!string.IsNullOrEmpty(token))
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        var response = await httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }
}
