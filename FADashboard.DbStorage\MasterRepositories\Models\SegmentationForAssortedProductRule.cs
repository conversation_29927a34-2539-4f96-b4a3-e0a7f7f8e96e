﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("SegmentationForAssortedProductRules")]
public class SegmentationForAssortedProductRule
{
    public AssortedProductRule AssortedProductRule { get; set; }
    public long AssortedProductRuleId { get; set; }
    public long Id { get; set; }
    public OutletSegmentationAttributes OutletSegmentationAttribute { get; set; }
    public long OutletSegmentationAttributeId { get; set; }
}
