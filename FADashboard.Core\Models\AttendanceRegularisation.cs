﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class AttendanceRegulariseRequest
{
    public DateTime AttendanceDate { get; set; }
    public NormBasedAttendanceType AttendanceStatus { get; set; }
    public long CompanyId { set; get; }
    public long EmployeeId { set; get; }
    public string EmployeeName { get; set; }
    public long Id { set; get; }
    public List<AttendanceKraModel> KRAs { get; set; }
    public NormBasedAttendanceType RegularisedRequestStatus { get; set; }
    public string RegulariseReason { get; set; }
    public DateTime RequestDate { get; set; }
}
