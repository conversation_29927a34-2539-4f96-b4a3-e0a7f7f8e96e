﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class AssetAllocation
{
    public long Id { get; set; }
    public long LocationId { get; set; }
    public long EmployeeId { get; set; }
    public long? ApprovedBy { get; set; }
    public DateTime? ApprovedOn { get; set; }
    public PortalUserRole? ApprovalRole { get; set; }
    public long FAEventId { set; get; }
    public AssetManagementRequestStatus Status { get; set; }
    public long? EquipmentId { get; set; }
    public long? AssetTypeId { set; get; }
}
