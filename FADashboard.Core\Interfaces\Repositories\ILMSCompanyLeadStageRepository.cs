﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSCompanyLeadStageRepository
    {
        Task<LMSCompanyLeadStageDto> GetByIdAsync(long id);
        Task<List<LMSCompanyLeadStageDto>> GetByTemplateIdAsync(long leadTemplateId);
        Task<IEnumerable<LMSCompanyLeadStageDto>> GetAllByCompanyIdAsync(long companyId);
        Task<IEnumerable<LMSCompanyLeadStageDto>> BulkUpsertAsync(LMSCompanyLeadStageBulkInputDto inputDto, long companyId, long userId);
    }
}
