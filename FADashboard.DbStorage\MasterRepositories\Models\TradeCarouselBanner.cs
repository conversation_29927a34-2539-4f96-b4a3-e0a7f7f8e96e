﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FATradeCarouselBanners")]
public class FATradeCarouselBanner : IAuditedEntity
{
    public long Id { get; set; }
    [Required] [StringLength(100)] public string Title { get; set; }
    [StringLength(256)] public string Image { get; set; }
    [StringLength(256)] public string Link { get; set; }
    public long CompanyId { get; set; }
    public virtual Company Company { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { set; get; }
    [StringLength(64)] public string CreationContext { get; set; }
    public bool IsActive { get; set; }

    [StringLength(512)]
    [NotMapped]
    public List<long> RegionIds
    {
        get => string.IsNullOrEmpty(Regions) ? [] : JsonConvert.DeserializeObject<List<long>>(Regions);
        set => Regions = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use RegionIds Instead!")]
    public string Regions { get; set; }

    public bool CtaEnable { get; set; }
    [StringLength(256)] public string CtaUrl { get; set; }
    [StringLength(128)] public string CtaName { get; set; }
    public CtaType CtaType { get; set; }
    public ScrollType CtaScrollType { get; set; }
    public AppScreenType CtaAppScreenType { get; set; }
}

public enum CtaType
{
    NotApplicable = 0,
    URL = 1,
    Scroll = 2,
    AppScreen = 3,
}

public enum ScrollType
{
    NotApplicable = 0,
    NewLaunch = 1,
    PromotionalProducts = 2,
    BuyAgain = 3,
}

public enum AppScreenType
{
    NotApplicable = 0,
    OrderBooking = 1,
    OrderStatus = 2,
    ELearning = 3,
    QPSSchemes = 4,
    Loyalty = 5
}
