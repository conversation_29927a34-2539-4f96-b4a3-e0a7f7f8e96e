﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("RoutePlans")]
public class RoutePlan : ICreatedEntity, IDeletable, IDeactivatable
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public DateTime EffectiveDate { get; set; }
    public virtual ClientEmployee Employee { get; set; }
    public long EmployeeId { get; set; }
    public DateTime? EndDate { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public JourneyFrequency RepeatFrequency { get; set; }
    public virtual ICollection<RoutePlanItem> RoutePlanItems { get; set; }
    public DateTime StartDate { get; set; }
}

[Table("RoutePlanItems")]
public class RoutePlanItem
{
    public int DayNumber { get; set; }
    public long Id { get; set; }
    public virtual ClientEmployee JWFieldUser { get; set; }
    public long? JWFieldUserId { get; set; }

    [StringLength(1024)] public string Reason { get; set; }

    [StringLength(256)] public string ReasonCategory { get; set; }

    public virtual Routes Route { get; set; }
    public long? RouteId { get; set; }

    [NotMapped] public string RouteName { get; set; }

    public virtual RoutePlan RoutePlan { get; set; }
    public long RoutePlanId { get; set; }
}
