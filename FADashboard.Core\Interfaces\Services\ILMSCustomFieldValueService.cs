using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSCustomFieldValueService
    {
        Task<LMSCustomFieldValueDto> GetByIdAsync(long id);
        Task<List<LMSCustomFieldValueDto>> GetByEntityAsync(int entityType, long entityId);
        Task<LMSCustomFieldValueDto> CreateAsync(LMSCustomFieldValueDto customFieldValue, long createdByUserId);
        Task UpdateAsync(long id, LMSCustomFieldValueDto customFieldValue, long updatedByUserId);
        Task DeleteAsync(long id);
        Task UpsertRangeForEntityAsync(int entityType, long entityId, List<LMSCustomFieldValueDto> values, long userId);
        Task DeleteByEntityAsync(int entityType, long entityId);
    }
}
