﻿using Libraries.CommonEnums;
using static Libraries.CommonEnums.TypesUsedInReports;


namespace FADashboard.Core.Models.DTOs;

public class DumpRequestReportCreateExtraJson
{
    public int BasisPCLevel { get; set; }

    public string DistributorIds { get; set; }

    public long DistributorId { get; set; }
    public string EndMonth { get; set; }
    public int EndYear { get; set; }
    public FieldUserType FieldUserType { get; set; }
    public long? FromDateTimeStamp { get; set; }
    public GeographicalHierarchy GeoFilter { get; set; }
    public string GeoFilterIds { get; set; }
    public bool IsNewDashboard { get; set; }

    public string PCIds { get; set; }

    public string PCUserIds { get; set; }

    public int PCUserLevel { get; set; }

    public int PositionCodeLevel { get; set; }

    public List<long> ProductFilterIds { get; set; }

    public ProductHierarchy ProductFilterLevel { get; set; }

    public int ProductHierarchy { get; set; }

    public int Rank { get; set; }

    public string ReportName { get; set; }

    public bool ShowAllColumns { get; set; }

    public bool ShowActiveDataOnly { get; set; }

    public bool DateInvoiceOrder { get; set; }

    public PSODShowDataForEnum ShowDataFor { get; set; }

    public string StartMonth { get; set; }

    public int StartYear { get; set; }
    public string Month { get; set; }
    public int Year { get; set; }

    public long? ToDateTimeStamp { get; set; }

    // Extra Json parameters
    public long UserId { get; set; }

    public List<long> UserPositionCodeIds { get; set; }

    public PortalUserRole UserRole { get; set; }

    // for channel filter, stockists or sub stockists in super stockists

    public bool UsesGeographyMultiSelect { get; set; }
    public EnumForReportAssembly? ReportType { get; set; } // added only for report download of type master report
    public int? SurveyId { get; set; }

    public long? GameId { get; set; }
    public StockistType? StockistType { get; set; }
    public List<long> SchemeIds { get; set; }
    public OutletStatus? OutletStatus { get; set; }
    public CampaignTaskStatus? CampaignTaskStatus { get; set; }
    public string CampaignName { get; set; }
    public List<long> CampaignNames { get; set; }
    public bool IsIncludeAll { get; set; }
    public List<string> VariantName { get; set; }
    public string UserIds { get; set; }
    public long? BeatometerRuleId { get; set; }
    public bool ShowHierarchy { get; set; }
    public bool ShowPosHierarchy { get; set; }
    public bool ShowBlankForLowerHierarchy { get; set; }
    public bool ViewPositionHierarchy { get; set; }
    public bool ShowLiveData { get; set; }
}

public class DumpRequestReportCreateModel
{
    public DateTime EndDate { get; set; }

    // Extra Json parameters
    public DumpRequestReportCreateExtraJson ExtraJson { get; set; }

    // Required parameters
    public DateTime StartDate { get; set; }

    public Guid? SubscriptionKey { get; set; }
}
