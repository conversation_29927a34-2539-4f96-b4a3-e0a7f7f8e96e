using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using FADashboardApi.Configurations;
using Serilog;
using Serilog.Events;
using System.Reflection;

namespace FADashboardApi;

/// <summary>
/// Minimal hosting model implementation for the Dashboard API.
/// This replaces the traditional Startup.cs pattern with a more streamlined approach.
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point for the application using minimal hosting model.
    /// </summary>
    /// <param name="args">Command line arguments</param>
    public static async Task Main(string[] args)
    {
        // Create the web application builder
        var builder = WebApplication.CreateBuilder(args);
        
        // Configure logging early in the pipeline
        ConfigureLogging(builder);
        
        // Configure Azure Key Vault if not in development
        await ConfigureKeyVaultAsync(builder);
        
        // Configure services with conditional middleware
        ConfigureServices(builder);
        
        // Build the application
        var app = builder.Build();
        
        // Configure the middleware pipeline
        ConfigureMiddleware(app);
        
        // Log application startup information
        LogStartupInformation(app);
        
        // Run the application
        await app.RunAsync();
    }
    
    /// <summary>
    /// Configures Serilog logging with environment-specific settings.
    /// </summary>
    /// <param name="builder">Web application builder</param>
    private static void ConfigureLogging(WebApplicationBuilder builder)
    {
        // Get the minimum log level based on environment
        var minLogLevel = EnvironmentConfiguration.GetLogLevel(builder.Configuration);
        
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Is(ConvertToSerilogLevel(minLogLevel))
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", "FADashboardApi")
            .Enrich.WithProperty("Environment", EnvironmentConfiguration.GetEnvironmentName(builder.Configuration, builder.Environment))
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
            .WriteTo.File(
                path: "logs/dashboard-api-.log",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
            .CreateLogger();
        
        // Use Serilog as the logging provider
        builder.Host.UseSerilog();
    }
    
    /// <summary>
    /// Configures Azure Key Vault for production environments.
    /// </summary>
    /// <param name="builder">Web application builder</param>
    private static async Task ConfigureKeyVaultAsync(WebApplicationBuilder builder)
    {
        // Only configure Key Vault for non-development environments
        if (!EnvironmentConfiguration.IsDevelopmentEnvironment(builder.Configuration, builder.Environment))
        {
            try
            {
                var keyVaultUrl = builder.Configuration["KeyVaultUrl"];
                if (!string.IsNullOrEmpty(keyVaultUrl))
                {
                    var credential = new DefaultAzureCredential();
                    var client = new SecretClient(new Uri(keyVaultUrl), credential);
                    
                    builder.Configuration.AddAzureKeyVault(
                        client,
                        new AzureKeyVaultConfigurationOptions
                        {
                            ReloadInterval = TimeSpan.FromMinutes(30)
                        });
                    
                    Log.Information("Azure Key Vault configured successfully: {KeyVaultUrl}", keyVaultUrl);
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to configure Azure Key Vault. Continuing with local configuration.");
            }
        }
    }
    
    /// <summary>
    /// Configures services using the conditional middleware configuration.
    /// </summary>
    /// <param name="builder">Web application builder</param>
    private static void ConfigureServices(WebApplicationBuilder builder)
    {
        try
        {
            // Configure conditional services based on environment
            builder.Services.ConfigureConditionalServices(builder.Configuration, builder.Environment);
            
            // Configure dependencies (existing Dependencies.cs logic)
            builder.Services.ConfigureDependencies(builder.Configuration);
            
            Log.Information("Services configured successfully for environment: {Environment}", 
                EnvironmentConfiguration.GetEnvironmentName(builder.Configuration, builder.Environment));
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Failed to configure services");
            throw;
        }
    }
    
    /// <summary>
    /// Configures the middleware pipeline using conditional activation.
    /// </summary>
    /// <param name="app">Web application</param>
    private static void ConfigureMiddleware(WebApplication app)
    {
        try
        {
            // Configure conditional middleware pipeline
            app.ConfigureConditionalPipeline(app.Configuration, app.Environment);
            
            Log.Information("Middleware pipeline configured successfully for environment: {Environment}", 
                EnvironmentConfiguration.GetEnvironmentName(app.Configuration, app.Environment));
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Failed to configure middleware pipeline");
            throw;
        }
    }
    
    /// <summary>
    /// Logs application startup information.
    /// </summary>
    /// <param name="app">Web application</param>
    private static void LogStartupInformation(WebApplication app)
    {
        var environment = EnvironmentConfiguration.GetEnvironmentName(app.Configuration, app.Environment);
        var isProduction = EnvironmentConfiguration.IsProductionEnvironment(app.Configuration);
        var swaggerEnabled = EnvironmentConfiguration.ShouldEnableSwagger(app.Configuration, app.Environment);
        
        Log.Information("=== Dashboard API Startup Information ===");
        Log.Information("Environment: {Environment}", environment);
        Log.Information("Production Mode: {IsProduction}", isProduction);
        Log.Information("Swagger Documentation: {SwaggerEnabled}", swaggerEnabled ? "Enabled" : "Disabled");
        Log.Information("Application Version: {Version}", Assembly.GetExecutingAssembly().GetName().Version);
        Log.Information("Hosting Model: Minimal Hosting");
        
        if (swaggerEnabled)
        {
            Log.Information("Swagger UI available at: /vuedashboardapidocs/swagger");
            Log.Information("ReDoc available at: /vuedashboardapidocs/redock");
            Log.Information("RapiDoc available at: /vuedashboardapidocs/rapidoc");
        }
        
        Log.Information("=== Application Started Successfully ===");
    }
    
    /// <summary>
    /// Converts Microsoft.Extensions.Logging.LogLevel to Serilog.Events.LogEventLevel.
    /// </summary>
    /// <param name="logLevel">Microsoft log level</param>
    /// <returns>Serilog log event level</returns>
    private static LogEventLevel ConvertToSerilogLevel(LogLevel logLevel)
    {
        return logLevel switch
        {
            LogLevel.Trace => LogEventLevel.Verbose,
            LogLevel.Debug => LogEventLevel.Debug,
            LogLevel.Information => LogEventLevel.Information,
            LogLevel.Warning => LogEventLevel.Warning,
            LogLevel.Error => LogEventLevel.Error,
            LogLevel.Critical => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }
}