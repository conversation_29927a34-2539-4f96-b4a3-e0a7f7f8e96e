﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class MandaysCappingDto
{
    public string PostionUserName { get; set; }

    public string PositionCodeId { get; set; }

    public long PositionId { get; set; }
    public string Beat { get; set; }
    public string Route { get; set; }
    public long UpperLimit { get; set; }
    public long LowerLimit { get; set; }

    public long? BeatId { get; set; }
    public long? RouteId { get; set; }

    public long Id { get; set; }

    public string Town { get; set; }

    public string OfficialWork { get; set; }

    public FAEventType? OfficialWorkEnum { get; set; }

    public PortalUserRole? UserRole { get; set; }
}


public class MandaysCappingDtoTotal
{
    public int Total { get; set; }
    public List<MandaysCappingDto> MandaysCappingDtoRecords { get; set; }

}

