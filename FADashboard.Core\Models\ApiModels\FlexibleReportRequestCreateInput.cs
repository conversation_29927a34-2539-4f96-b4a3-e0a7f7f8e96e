﻿using Libraries.CommonEnums;
using static Libraries.CommonEnums.TypesUsedInReports;

namespace FADashboard.Core.Models.ApiModels;

public class FlexibleReportRequestCreateInput
{
    // Required parameters
    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }
    public List<long> PCUserIds { get; set; }
    public List<long> PCIds { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public PositionCodeLevel PCUserLevel { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }

    // Extra Json parameters
    public int Channel { get; set; }

    public StockistType? StockistType { get; set; }
    public DistributorChannel? DistributorChannel { get; set; }
    public List<long> DistributorIds { get; set; }
    public List<long> GeoLevel7Ids { get; set; }
    public List<long> GeoLevel6Ids { get; set; }
    public List<long> GeoLevel5Ids { get; set; }
    public List<long> ZoneIds { get; set; }
    public List<long> RegionIds { get; set; }
    public bool UsesGeographyMultiSelect { get; set; }
    public List<long> ProductFilterIds { get; set; }
    public ProductHierarchyEnum ProductFilterLevel { get; set; }
    public bool ShowAllColumns { get; set; }

    public Guid? SubscriptionKey { get; set; }

    public bool SaveFlexibleReport { get; set; }
    public GeographicalHierarchy GeoFilter { get; set; }
    public List<long> GeoFilterIds { get; set; }
}
