﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;
using Libraries.CommonModels.WeeklyRouteOptimizations;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class WeeklyRouteConfiguration(long companyId) : IAuditedEntity, ICompanyEntity, IDeactivatable
{
    public long Id { get; set; }

    public long CompanyId { get; set; } = companyId;

    public DateTime CreatedAt { get; set; }

    public string? CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string? AssociatedCohorts { get; set; }

    [NotMapped]
    public List<long>? AssociatedCohortsParsed => !string.IsNullOrEmpty(AssociatedCohorts) ? JsonConvert.DeserializeObject<List<long>>(AssociatedCohorts) : null;

    public string? LogicName { get; set; }

    public int RevenueConsiderationMonths { get; set; }

    public DateTime RouteStartDate { get; set; }

    public DayOfWeek WeeklyCycleStart { get; set; }

    public LocalSearchCoverageStrategy LocalSearchCoverageStrategy { get; set; }

    public bool NonWorkingDaysIncluded { get; set; }

    public double StandardOperatingDuration { get; set; } // Hours

    public double UpdatedOperatingDuration { get; set; } // Hours

    public ThresholdConsideration ThresholdConsiderationMode { get; set; }

    public double MaxTravelDistance { get; set; } // Kilometers

    public double UpdatedMaxTravelDistance { get; set; } // Kilometers

    public string? RequiredVisitsInput { get; set; }

    [NotMapped]
    public RequiredVisitsInput? RequiredVisitsInputParsed => !string.IsNullOrEmpty(RequiredVisitsInput) ? JsonConvert.DeserializeObject<RequiredVisitsInput>(RequiredVisitsInput) : null;

    public string? DefaultVisit { get; set; }

    [NotMapped]
    public DefaultVisit? DefaultVisitsParsed => !string.IsNullOrEmpty(DefaultVisit) ? JsonConvert.DeserializeObject<DefaultVisit>(DefaultVisit) : null;

    public RequiredRetailTime RequiredRetailTimeInput { get; set; }

    public string? RetailTimeDetails { get; set; }

    public PreferredDayOfVisitType PreferredDayOfVisitType { get; set; }

    public HomeLocationType HomeLocationType { get; set; }

    public double TravelSpeed { get; set; }

    public string? NonWorkingDays { get; set; }

    [NotMapped]
    public List<int> GetNonWorkingDays => !string.IsNullOrEmpty(NonWorkingDays)
        ? JsonConvert.DeserializeObject<List<int>>(NonWorkingDays) : [];

    public int MinDailyOutletVisits { get; set; }

    public int MaxDailyOutletVisits { get; set; }

    public bool IsDeactive { get; set; }
    public bool HolidayInclusion { get; set; }
    public double DefaultRetailTime { get; set;}
}
