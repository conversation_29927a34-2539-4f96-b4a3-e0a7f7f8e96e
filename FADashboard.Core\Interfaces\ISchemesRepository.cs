﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ISchemesRepository
{
    Task<RepositoryResponse> CreateScheme(SchemesInput scheme, long companyId);

    Task<RepositoryResponse> CreateSchemeBasket(SchemeBucketList scheme, long companyId);

    Task<RepositoryResponse> DeactivateScheme(Guid schemeGuid, bool isForceEnd, long companyId, DateTime? endDate = null);
    Task<List<RepositoryResponse>> DeactivateSchemes(List<Guid> schemeGuids, long companyId);

    Task<RepositoryResponse> DeactivateSchemeBasket(long id, long companyId);

    Task<SchemeBucketInput> GetSchemeBasketById(long id, long companyId);

    Task<SchemeBucketDetails> GetSchemeBasketDetails(long id, long companyId);

    Task<List<SchemeBucketList>> GetSchemeBaskets(long companyId, bool showActive);

    Task<SchemesInput> GetSchemeById(Guid schemeGuid, long companyId);

    Task<SchemesDetail> GetSchemeDetails(Guid schemeGuid, long companyId);

    Task<List<SchemesList>> GetSchemes(long companyId, DateTime getSchemesTill, bool includeDeactivate);
    Task<SchemeTotal> SearchSchemes(long companyId, PaginationFilter validFilter, PayoutType? payoutType, int? isSchemeApproved,
        PayoutCalculationType? payoutCalculationType, ConstraintType? constraintType, DateTime? getSchemesTill);
    Task<List<EntityMinWithErp>> GetSchemesMin(long companyId, bool includeDeactivate = false);
    Task<List<EntityMinWithErp>> GetAlternateSchemesMin(long companyId, bool includeDeactivate = false);
    Task<List<SchemesList>> GetSchemesWithFilters(long companyId, SchemeFilters schemeFilters);

    Task<List<SchemeMin>> GetSchemesAttachedToBasket(long basketId);

    Task<RepositoryResponse> UpdateScheme(SchemesInput scheme, long companyId);

    Task<RepositoryResponse> UpdateSchemeBasket(SchemeBucketList scheme, long companyId);
    Task<List<RepositoryResponse>> ReviewScheme(List<Guid> guids, int action, ICurrentUser currentUser, string reason);

}
