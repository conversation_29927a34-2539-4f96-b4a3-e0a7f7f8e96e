﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class GlobalOutletMetricService(IGlobalOutletMetricRepository globalOutletMetricRepository) : RepositoryResponse
{
    public async Task<List<GlobalOutletMetricList>> GetGlobalOutletMetrics(bool includeDeactive, bool perspectiveOutlet = false)
    {
        var products = await globalOutletMetricRepository.GetGlobalOutletMetrics(includeDeactive, perspectiveOutlet);
        return products;
    }


    private async Task<RepositoryResponse> IsValidGlobalOutletMetric(GlobalOutletMetricInput globalOutletMetric)
    {
        var allGlobalOutletMetrics = await globalOutletMetricRepository.GetGlobalOutletMetrics(true);
        if (globalOutletMetric.Id != 0)
        {
            allGlobalOutletMetrics = allGlobalOutletMetrics.Where(p => p.Id != globalOutletMetric.Id).ToList();
        }

        var globalOutletMetricNameList = allGlobalOutletMetrics.Select(p => p.Name.NormalizeCaps()).ToList();

        if (globalOutletMetricNameList.Contains(globalOutletMetric.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = globalOutletMetric.Id, ExceptionMessage = "global outlet metric name is not unique", Message = "global outlet metric creation/updation Failed!", IsSuccess = false,
            };
        }

        return GetSuccessResponse(globalOutletMetric.Id, "global outlet metric unique");
    }

    public async Task<RepositoryResponse> CreateUpdateGlobalOutletMetric(GlobalOutletMetricInput globalOutletMetric)
    {
        var checkValid = await IsValidGlobalOutletMetric(globalOutletMetric);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (globalOutletMetric.Id == 0)
        {
            return await globalOutletMetricRepository.CreateGlobalOutletMetric(globalOutletMetric);
        }

        return await globalOutletMetricRepository.UpdateGlobalOutletMetric(globalOutletMetric);
    }

    public async Task<RepositoryResponse> ActivateDeactivateGlobalOutletMetric(long globalOutletMetricId, bool action) => await globalOutletMetricRepository.ActivateDeactivateGlobalOutletMetric(globalOutletMetricId, action);

    public async Task<GlobalOutletMetricInput> GetGlobalOutletMetricById(long globalOutletMetricId)
    {
        var globalOutletMetric = await globalOutletMetricRepository.GetGlobalOutletMetricById(globalOutletMetricId);
        return globalOutletMetric;
    }
}
