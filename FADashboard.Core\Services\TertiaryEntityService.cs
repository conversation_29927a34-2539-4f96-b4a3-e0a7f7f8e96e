﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class TertiaryEntityService(
    ICurrentUser currentUser,
    ITertiaryEntityRepository tertiaryEntityRepository) : RepositoryResponse
{
    public async Task<PagedResponse<List<TertiaryEntityMasterList>>> GetTertiaryEntities(PaginationFilter validFilter, List<long> regionIds = null)
    {
        var tertiaryEntities = await tertiaryEntityRepository.GetTertiaryEntities(currentUser.CompanyId, validFilter, regionIds);
        var totalRecords = tertiaryEntities.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(tertiaryEntities.TertiaryEntityRecords, validFilter, totalRecords);

        return pagedReponse;
    }

    public async Task<RepositoryResponse> ActivateDeactivateTertiaryEntity(long tertiaryEntityId) => await tertiaryEntityRepository.DeactivateTertiaryEntity(currentUser.CompanyId, tertiaryEntityId);

    public async Task<RepositoryResponse> CreateUpdateTertiaryEntity(TertiaryEntityInput tertiaryEntity) => tertiaryEntity.Id == 0
            ? await tertiaryEntityRepository.CreateTertiaryEntity(tertiaryEntity)
            : await tertiaryEntityRepository.UpdateTertiaryEntityAndMappings(tertiaryEntity);

    public async Task<TertiaryEntityInput> GetTertiaryEntityById(long id) => await tertiaryEntityRepository.GetTertiaryEntityById(currentUser.CompanyId, id);
    public async Task<List<TertiaryEntityInput>> GetAllTertiaryEntities() => await tertiaryEntityRepository.GetAllTertiaryEntities(currentUser.CompanyId);
    public async Task<List<TertiaryEntityInput>> GetTertiaryEntitiesofOutletIds(List<long> outletIds) => await tertiaryEntityRepository.GetTertiaryEntitiesofOutletIds(currentUser.CompanyId, outletIds);
    public async Task<List<TertiaryEntityWithOutletId>> GetOutletIdsOfTertiaryEntities(List<long> tertiaryIds) => await tertiaryEntityRepository.GetOutletIdsOfTertiaryEntities(currentUser.CompanyId, tertiaryIds);
}
