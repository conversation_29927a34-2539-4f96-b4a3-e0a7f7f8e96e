﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;
public class RequestTimelineService(IRequestApprovalTimelineRepository approvalTimelineRepository,
        IOutletAdditionRequestRepository outletAdditionRequestRepository,
        IRequestsAndAlertsRepository requestsAndAlertsRepository,
        IBeatRepository beatRepository,
        IEmployeeRepository employeeRepository,
        IAdminRepository adminRepository,
        IPositionCodeRepository positionCodeRepository,
        ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<Requests>> GetOpenRequests(ApprovalEngineRequesType requestType, CancellationToken ct = default)
    {
        switch (requestType)
        {
            case ApprovalEngineRequesType.OutletUpdation:
                return await requestsAndAlertsRepository.GetOpenRequestsForOutletUpdation(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);

            case ApprovalEngineRequesType.OutletCreation:
                return await outletAdditionRequestRepository.GetOpenRequestsForOutletCreation(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);

            default:
                return [];
        }
    }

    public async Task<RequestApprovalUserTimelineDto> GetRequestTimelines(long requestId, ApprovalEngineRequesType requestType, CancellationToken ct = default)
    {
        var data = await approvalTimelineRepository.GetUserTimelineByRequestId(currentUser.CompanyId, requestId, requestType, ct);
        var response = new RequestApprovalUserTimelineDto();
        if (requestType == ApprovalEngineRequesType.OutletCreation)
        {
            var outlet = await outletAdditionRequestRepository.GetOutletAdditionRequestByIdForAdmin(requestId, currentUser.CompanyId);
            if (outlet != null)
            {
                response.OutletName = outlet.OutletName ?? string.Empty;
                var beat = await beatRepository.GetBeatId(outlet.BeatId, currentUser.CompanyId);
                if (beat != null)
                {
                    response.BeatName = beat.Name ?? string.Empty;
                }
            }
        }
        else if (requestType == ApprovalEngineRequesType.OutletUpdation)
        {
            var outlet = await requestsAndAlertsRepository.GetOutletUpdationRequestById(requestId, currentUser.CompanyId);
            if (outlet != null)
            {
                response.OutletName = outlet.newvalues.OutletName ?? string.Empty;
                response.BeatName = outlet.newvalues.BeatName ?? string.Empty;
            }
        }

        var approverUserIds = data.Where(x => x.ApproverUserId is not 0 and not null).Select(x => x.ApproverUserId.Value).Distinct().ToList();
        var actionTakenByUserIds = data.Where(x => x.ActionTakenById is not 0 and not null).Select(x => x.ActionTakenById.Value).Distinct().ToList();
        var userIds = approverUserIds.Concat(actionTakenByUserIds).Distinct().ToList();
        var requesterUserIds = data.Where(x => x.RequesterUserId is not 0).Select(x => x.RequesterUserId).Distinct().ToList();
        var approverEmployees = await employeeRepository.GetEmployeesForIds(currentUser.CompanyId, userIds);
        var requesterEmployees = await employeeRepository.GetEmployeesForIds(currentUser.CompanyId, requesterUserIds);
        var approverEmployeeDict = approverEmployees.ToDictionary(x => x.Id, y => y);
        var requesterEmployeeDict = requesterEmployees.ToDictionary(x => x.Id, y => y);
        var adminId = data.Where(x => x.RequestStatus is ApprovalEngineRequestStatus.ApprovedByAdmin or ApprovalEngineRequestStatus.RejectedByAdmin).Select(x => x.ActionTakenById).FirstOrDefault();
        var adminName = string.Empty;
        var adminNo = string.Empty;
        if (adminId != null)
        {
            var adminList = await adminRepository.GetUsersAndGlobalAdmins(currentUser.CompanyId, false);
            var admin = adminList.FirstOrDefault(e => e.Id == adminId);
            adminName = admin?.Name;
            adminNo = admin?.PhoneNo;
        }
        var posIds = data.Where(x => x.ApproverPositionId is not 0).Select(x => x.ApproverPositionId).Distinct().ToList();
        var posData = (await positionCodeRepository.GetPositionEntityMappings(posIds))
            .GroupBy(x => x.PositionId)
            .ToDictionary(x => x.Key, x => x.First());

        response.SequenceWiseTimelines = data.Select(x => new RequestApprovalTimelineDto
        {
            ApproverPositionId = x.ApproverPositionId,
            ApproverUserId = x.ApproverUserId,
            ApproverPositionLevel = x.ApproverPositionLevel,
            RequestStatus = x.RequestStatus,
            ActionTakenByUserRole = x.ActionTakenByUserRole,
            ActionTakenById = x.ActionTakenById,    
            ActionTakenByUser = x.RequestStatus is ApprovalEngineRequestStatus.ApprovedByAdmin or ApprovalEngineRequestStatus.RejectedByAdmin ?
                                        adminName :
                                        approverEmployeeDict.TryGetValue(x.ActionTakenById ?? 0, out var value3) ? value3.Name : string.Empty,
            Remarks = x.Remarks,
            CreatedAt = x.CreatedAt,
            LastUpdatedAt = x.LastUpdatedAt,
            Sequence = x.Sequence,
            RequesterUserId = x.RequesterUserId,
            RequesterUserName = requesterEmployeeDict.TryGetValue(x.RequesterUserId, out var val1) ? val1.Name : string.Empty,
            ApproverMobileNumber = x.RequestStatus is ApprovalEngineRequestStatus.ApprovedByAdmin or ApprovalEngineRequestStatus.RejectedByAdmin ?
                                        adminNo : posData.TryGetValue(x.ApproverPositionId, out var value1) ? value1.ClientEmployeeContactNo : string.Empty,
            ApproverUserName = x.RequestStatus is ApprovalEngineRequestStatus.ApprovedByAdmin or ApprovalEngineRequestStatus.RejectedByAdmin ?
                                        adminName : posData.TryGetValue(x.ApproverPositionId, out var value2) ? value2.ClientEmployeeName : string.Empty
        })
                                   .GroupBy(x => x.Sequence)
                                   .Select(g => new RequestApprovalSequenceWiseTimeline
                                   {
                                       Sequence = g.Key,
                                       Timeline = [.. g]
                                   })
                                   .ToList();
        return response;
    }
}
