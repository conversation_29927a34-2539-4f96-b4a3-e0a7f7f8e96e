﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class GameList
{
    public long CompanyId { get; set; }
    public DateTime EndDate { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public DateTime StartDate { get; set; }
}

public class CreateGame
{
    public long CompanyId { get; set; }
    public DateTime EndDate { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public DateTime StartDate { get; set; }
    public GameRewardType RewardType { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public List<CoinsforKpi> CoinsforKpi { get; set; }
    public List<TargetForTeams> TargetsforTeams { get; set; }

}

public class TargetForTeams
{
    [Key]
    public long Id { get; set; }
    public long GameId { get; set; }
    public long TeamId { get; set; }
    public long KpiId { get; set; }
    public string Target { get; set; }
    public bool IsQualifier { get; set; }
    public bool IsContinuous { get; set; }
    public bool IsActive { get; set; }
    public long Game { get; set; }
    public List<KPISlab> KPISlabs { get; set; }
}
public class CoinsforKpi
{
    [Key]
    public long Id { get; set; }
    public long GameId { get; set; }
    public long KpiId { get; set; }
    public long Coins { get; set; }
    public long Game { get; set; }
}

public class KPISlab
{
    public long Id { get; set; }
    public TargetForTeams TargetForTeam { get; set; }
    public long TargetForTeamId { get; set; }
    public string SlabTarget { get; set; }
    public long SlabCoins { get; set; }
    public long? SlabPayout { get; set; }
}
