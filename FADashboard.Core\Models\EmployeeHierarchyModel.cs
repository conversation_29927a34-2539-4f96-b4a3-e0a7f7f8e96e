﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class EmployeeGeoHierarchy
{
    public long? DesignationId { get; set; }
    public string Esm { get; set; }
    public string ESMContactNo { get; set; }
    public string EsmErpId { get; set; }
    public string EsmHQ { get; set; }
    public long EsmId { get; set; }
    public EmployeeRank EsmRank { get; set; }
    public string Region { get; set; }
    public string ReportingManager { get; set; }
    public long ReportingManagerId { get; set; }
    public UserActiveStatus UserStatus { get; set; }
    public string Zone { get; set; }
}

public class EmployeeHierarchyModel : ESMWithManagers
{
    public string AuthKey { get; internal set; }
    public long CompanyId { get; set; }
    public string ESMAppVersion { set; get; }
    public string EsmContactNumber { get; set; }
    public string ESMEmailId { get; set; }
    public string ESMErpId { get; set; }
    public Guid ESMGuid { get; set; }
    public string ESMIMEINumber { get; set; }
    public bool ESMIsDeactive { get; internal set; }
    public string ESMLocalName { get; set; }
    public string ESMSecondaryEmailId { get; set; }
    public string ESMZone { get; set; }
    public bool IsTrainingUser { get; set; }
    public string MobileNo { get; set; }
    public string Name { get; set; }
}

public class ESMWithManagers
{
    public long? ASMId { get; set; }
    public string ASMName { get; set; }
    public string ContactNo { get; set; }
    public DateTime DateOfCreation { get; private set; }
    public DateTime? DateOfJoining { get; private set; }
    public DateTime? DateOfLeaving { get; private set; }
    public long? DesignationId { get; set; }
    public string ErpId { get; set; }
    public string ESMName { get; set; }
    public long? GSMId { get; set; }
    public string GSMName { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public bool IsOrderBookingDisabled { get; set; }
    public string LocalName { get; set; }
    public long? NSMId { get; set; }
    public string NSMName { get; set; }
    public long? OldTableId { get; set; }
    public EmployeeRank Rank { get; set; }
    public string Region { get; set; }
    public string ReportingManager { get; set; }
    public long? RSMId { get; set; }
    public string RSMName { get; set; }
    public PortalUserRole UserRole { get; set; }
    public EmployeeType UserType { get; set; }
    public string Zone { get; set; }
    public long? ZSMId { get; set; }
    public string ZSMName { get; set; }

    internal static IEnumerable<ESMWithManagers> GetESMWithManagers(IEnumerable<UserWithManagers> userList) => userList.Select(GetESMWithManagers).ToList();

    internal static ESMWithManagers GetESMWithManagers(UserWithManagers userData)
    {
        switch (userData.UserRole)
        {
            case PortalUserRole.GlobalSalesManager:
                return new ESMWithManagers
                {
                    GSMId = userData.OldTableId,
                    GSMName = userData.Name,
                    Rank = EmployeeRank.GSM,
                    Id = userData.Id,
                    ESMName = userData.Name,
                    OldTableId = userData.OldTableId,
                    ContactNo = userData.ContactNo,
                    LocalName = userData.LocalName,
                    ErpId = userData.ErpId,
                    IsDeactive = userData.IsDeactive,
                    IsOrderBookingDisabled = userData.IsOrderBookingDisabled,
                    UserRole = userData.UserRole,
                    UserType = userData.UserType,
                    Zone = userData.Zone,
                    Region = userData.Region,
                    DateOfJoining = userData.DateOfJoining,
                    DateOfLeaving = userData.DateOfLeaving,
                    DateOfCreation = userData.DateOfCreation,
                    DesignationId = userData.DesignationId,
                };

            case PortalUserRole.NationalSalesManager:
                return new ESMWithManagers
                {
                    Id = userData.Id,
                    ESMName = userData.Name,
                    NSMId = userData.OldTableId,
                    NSMName = userData.Name,
                    GSMId = userData.ParentId,
                    OldTableId = userData.OldTableId,
                    GSMName = userData.ParentName,
                    Rank = EmployeeRank.NSM,
                    ContactNo = userData.ContactNo,
                    LocalName = userData.LocalName,
                    ErpId = userData.ErpId,
                    ReportingManager = userData.ParentName,
                    IsDeactive = userData.IsDeactive,
                    IsOrderBookingDisabled = userData.IsOrderBookingDisabled,
                    UserRole = userData.UserRole,
                    UserType = userData.UserType,
                    Zone = userData.Zone,
                    Region = userData.Region,
                    DateOfJoining = userData.DateOfJoining,
                    DateOfLeaving = userData.DateOfLeaving,
                    DateOfCreation = userData.DateOfCreation,
                    DesignationId = userData.DesignationId,
                };

            case PortalUserRole.ZonalSalesManager:
                return new ESMWithManagers
                {
                    Id = userData.Id,
                    ESMName = userData.Name,
                    ZSMId = userData.OldTableId,
                    ZSMName = userData.Name,
                    NSMId = userData.ParentId,
                    OldTableId = userData.OldTableId,
                    NSMName = userData.ParentName,
                    GSMId = userData.Parent2Id,
                    GSMName = userData.Parent2Name,
                    Rank = EmployeeRank.ZSM,
                    ContactNo = userData.ContactNo,
                    LocalName = userData.LocalName,
                    ErpId = userData.ErpId,
                    ReportingManager = userData.ParentName,
                    IsDeactive = userData.IsDeactive,
                    IsOrderBookingDisabled = userData.IsOrderBookingDisabled,
                    UserRole = userData.UserRole,
                    UserType = userData.UserType,
                    Zone = userData.Zone,
                    Region = userData.Region,
                    DateOfJoining = userData.DateOfJoining,
                    DateOfLeaving = userData.DateOfLeaving,
                    DateOfCreation = userData.DateOfCreation,
                    DesignationId = userData.DesignationId,
                };

            case PortalUserRole.RegionalSalesManager:
                return new ESMWithManagers
                {
                    Id = userData.Id,
                    ESMName = userData.Name,
                    RSMId = userData.OldTableId,
                    RSMName = userData.Name,
                    ZSMId = userData.ParentId,
                    ZSMName = userData.ParentName,
                    NSMId = userData.Parent2Id,
                    OldTableId = userData.OldTableId,
                    NSMName = userData.Parent2Name,
                    GSMId = userData.Parent3Id,
                    GSMName = userData.Parent3Name,
                    Rank = EmployeeRank.RSM,
                    ContactNo = userData.ContactNo,
                    LocalName = userData.LocalName,
                    ErpId = userData.ErpId,
                    ReportingManager = userData.ParentName,
                    IsDeactive = userData.IsDeactive,
                    IsOrderBookingDisabled = userData.IsOrderBookingDisabled,
                    UserRole = userData.UserRole,
                    UserType = userData.UserType,
                    Zone = userData.Zone,
                    Region = userData.Region,
                    DateOfJoining = userData.DateOfJoining,
                    DateOfLeaving = userData.DateOfLeaving,
                    DateOfCreation = userData.DateOfCreation,
                    DesignationId = userData.DesignationId,
                };

            case PortalUserRole.AreaSalesManager:
                return new ESMWithManagers
                {
                    Id = userData.Id,
                    ESMName = userData.Name,
                    ASMId = userData.OldTableId,
                    ASMName = userData.Name,
                    RSMId = userData.ParentId,
                    RSMName = userData.ParentName,
                    ZSMId = userData.Parent2Id,
                    ZSMName = userData.Parent2Name,
                    OldTableId = userData.OldTableId,
                    NSMId = userData.Parent3Id,
                    NSMName = userData.Parent3Name,
                    GSMId = userData.Parent4Id,
                    GSMName = userData.Parent4Name,
                    Rank = EmployeeRank.ASM,
                    ContactNo = userData.ContactNo,
                    LocalName = userData.LocalName,
                    ErpId = userData.ErpId,
                    ReportingManager = userData.ParentName,
                    IsDeactive = userData.IsDeactive,
                    IsOrderBookingDisabled = userData.IsOrderBookingDisabled,
                    UserRole = userData.UserRole,
                    UserType = userData.UserType,
                    Zone = userData.Zone,
                    Region = userData.Region,
                    DateOfJoining = userData.DateOfJoining,
                    DateOfLeaving = userData.DateOfLeaving,
                    DateOfCreation = userData.DateOfCreation,
                    DesignationId = userData.DesignationId,
                };

            case PortalUserRole.ClientEmployee:
                return new ESMWithManagers
                {
                    Id = userData.Id,
                    ESMName = userData.Name,
                    ASMId = userData.ParentId,
                    ASMName = userData.ParentName,
                    RSMId = userData.Parent2Id,
                    RSMName = userData.Parent2Name,
                    ZSMId = userData.Parent3Id,
                    ZSMName = userData.Parent3Name,
                    NSMId = userData.Parent4Id,
                    NSMName = userData.Parent4Name,
                    GSMId = userData.Parent5Id,
                    GSMName = userData.Parent5Name,
                    Rank = EmployeeRank.ESM,
                    ContactNo = userData.ContactNo,
                    LocalName = userData.LocalName,
                    ErpId = userData.ErpId,
                    ReportingManager = userData.ParentName,
                    IsDeactive = userData.IsDeactive,
                    IsOrderBookingDisabled = userData.IsOrderBookingDisabled,
                    UserRole = userData.UserRole,
                    UserType = userData.UserType,
                    Zone = userData.Zone,
                    OldTableId = userData.OldTableId,
                    Region = userData.Region,
                    DateOfJoining = userData.DateOfJoining,
                    DateOfLeaving = userData.DateOfLeaving,
                    DateOfCreation = userData.DateOfCreation,
                    DesignationId = userData.DesignationId,
                };
            default:
                return null;
        }
    }
}

public class UserWithManagers
{
    public long CompanyId { get; set; }
    public string ContactNo { get; set; }
    public DateTime DateOfCreation { get; set; }
    public DateTime? DateOfJoining { get; set; }
    public DateTime? DateOfLeaving { get; set; }
    public long? DesignationId { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public bool IsOrderBookingDisabled { get; set; }
    public string LocalName { get; set; }
    public string Name { get; set; }
    public long NewId { get; set; }
    public long? OldTableId { get; set; }
    public long? Parent2Id { get; set; }
    public string Parent2Name { get; set; }
    public long? Parent3Id { get; set; }
    public string Parent3Name { get; set; }
    public long? Parent4Id { get; set; }
    public string Parent4Name { get; set; }
    public long? Parent5Id { get; set; }
    public string Parent5Name { get; set; }
    public long? ParentId { get; set; }
    public string ParentName { get; set; }
    public EmployeeRank Rank { get; set; }
    public string Region { get; set; }
    public PortalUserRole UserRole { get; set; }
    public EmployeeType UserType { get; set; }
    public string Zone { get; set; }
}
