﻿namespace FADashboard.Core.Models;

public class FieldUserDailyAttendanceSummaryDataModel
{
    public int CAP { get; set; }
    public int? DistributorId { get; set; }
    public DateTimeOffset FirstCall { get; set; }
    public DateTimeOffset FirstPC { get; set; }
    public double LineWiseDiscount { get; set; }
    public int NewOutletsCreated { get; set; }
    public double OrderInRevenue { get; set; }
    public double OrderInStdUnits { get; set; }
    public int OVC { get; set; }
    public int OVT { get; set; }
    public int PC { get; set; }
    public int TC { get; set; }
    public int TelephonicOrders { get; set; }
}

public class FieldUserDailyDataModel
{
    public string AssignedBeat { get; set; }
    public string AssignedJointWorkingEmployee { get; set; }
    public string AssignedReason { get; set; }
    public string AssignedReasonCategory { get; set; }
    public string AssignedRoute { get; set; }
    public string DayStartLocation { get; set; }
    public string Distributor { get; set; }
    public string JointWorkingEmployee { get; set; }
    public string Login { get; set; }
    public string Rank { get; set; }
    public string Reason { get; set; }
    public string ReasonCategory { get; set; }
    public string ReportingManager { get; set; }
    public string SelectedBeat { get; set; }
    public string SelectedRoute { get; set; }
    public string User { get; set; }
    public string UserHQ { get; set; }
}
