﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.DbStorage.MasterRepositories.Models;

namespace FADashboard.Core.Services;

public class CESSCategoryService(
    ICESSCategoryRepository cessCategoryRepository,
    ICurrentUser currentUser
) : RepositoryResponse
{
    /// <summary>
    /// This particular is used to get CESS Categories from the tables - ProductCESSCategories and CESSCategoryTaxes
    /// </summary>
    /// <param name="companyId"></param>
    /// <param name="showDeactive"></param>
    /// <returns></returns>
    public async Task<List<ProductCESSCategoryFlat>> GetCESSCategories(bool showDeactive = false) => await cessCategoryRepository.GetCESSCategories(currentUser.CompanyId, showDeactive);

    /// <summary>
    /// This particular is used to create CESS Categories. The data is saved in the tables - ProductCESSCategories and CESSCategoryTaxes
    /// </summary>
    /// <param name="CESSCategoryInput"></param>
    /// <returns></returns>
    public async Task<RepositoryResponse> CreateCESSCategory(CESSCategoryInput CESSCategoryInput)
    {
        if (CESSCategoryInput.Id == 0)
        {
            return await cessCategoryRepository.CreateCESSCategory(CESSCategoryInput, currentUser.CompanyId);
        }

        return await cessCategoryRepository.UpdateCESSCategory(CESSCategoryInput, currentUser.CompanyId);
    }

    //Deactivate CESS Category
    public async Task<RepositoryResponse> DeactiveCESSCategory(long id) => await cessCategoryRepository.DeactiveCESSCategory(id, currentUser.CompanyId);
}
