﻿using System.ComponentModel.DataAnnotations.Schema;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class UserWiseCustomReport
{
    public UserWiseCustomReport()
    {
        UserWiseCustomReportItems = [];
    }

    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public virtual CustomReport CustomReport { get; set; }
    public long CustomReportId { get; set; }
    public long EmployeeId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [NotMapped]
    public UserReportPreference SelectedReportPreferences
    {
        get => string.IsNullOrEmpty(SelectedReportPreferencesJson) ? new UserReportPreference() : JsonConvert.DeserializeObject<UserReportPreference>(SelectedReportPreferencesJson);
        set => SelectedReportPreferencesJson = value == null ? "{}" : JsonConvert.SerializeObject(value);
    }

    [NotMapped]
    public Dictionary<string, object> SelectedReportPreferencesDictionary
    {
        get => string.IsNullOrEmpty(SelectedReportPreferencesJson) ? [] : JsonConvert.DeserializeObject<Dictionary<string, object>>(SelectedReportPreferencesJson);
        set => SelectedReportPreferencesJson = value == null ? "{}" : JsonConvert.SerializeObject(value);
    }

    public string SelectedReportPreferencesJson { get; set; }
    public PortalUserRole UserRole { get; set; }
    public ICollection<UserWiseCustomReportItem> UserWiseCustomReportItems { get; set; }
}

public class UserWiseCustomReportItem
{
    public DateTime CreatedAt { get; set; }
    public CustomReportItem CustomReportItem { get; set; }
    public long CustomReportItemId { get; set; }
    public long Id { get; set; }
    public bool IsSelected { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public UserWiseCustomReport UserWiseCustomReport { get; set; }
    public long UserWiseCustomReportId { get; set; }
}
