﻿using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class EmployeeList
{
    public int? AppVersionNumber { get; set; }
    public string ContactNo { get; set; }
    public string EmailId { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsBillable { set; get; }
    public bool IsFieldAppuser { get; set; }
    public bool IsOrderBookingDisabled { get; set; }
    public bool? IsVacant { set; get; }
    public Guid? LoginGuid { get; set; }
    public string Name { get; set; }
    public List<PositionMinParent> PositionsWithParent { get; set; }
    public EmployeeRank Rank { get; set; }
    public string RegionName { set; get; }
    public EmployeeType UserType { set; get; }
    public string ZoneName { get; set; }
    public int? MappedBeats { get; set; }
    public bool IsTrainingUser { get; set; }
    public AlertSource? AlertSource {  get; set; }
}

public class EmployeeListMin : EntityMinUser
{
    public string CodeId { get; set; }
    public string LevelName { get; set; }
}
