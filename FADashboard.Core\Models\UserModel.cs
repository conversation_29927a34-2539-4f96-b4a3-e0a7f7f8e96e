﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Models;

public class UserModel
{
    public List<TokenToReturn> AllLogins { get; set; }
    public long CompanyId { get; set; }
    public object CompanyName { get; set; }
    public string EmailId { get; set; }
    public string ImageId { get; set; }
    public Guid LoginGuid { get; set; }
    public string Logo { get; set; }
    public string PhoneNo { get; set; }
    public string Token { get; set; }
    public long UserId { get; set; }
    public string UserName { get; set; }
    public string UserRole { get; set; }
    public string UserSince { get; set; }
}
