﻿using EntityHelper;
using Libraries.CommonEnums;
using System.Text.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonModels;


namespace FADashboard.DbStorage.MasterRepositories.Models;
public class RequestApprovalRule : IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public ApprovalEngineRequesType RequestType { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string ConstraintType { get; set; }
    [NotMapped]
    public RuleConstraintType ConstraintTypes => JsonSerializer.Deserialize<RuleConstraintType>(ConstraintType);

    public int Priority { get; set; }
    public int ApprovalLevel { get; set; }
    public string LevelDefinition { get; set; }
    [NotMapped]
    public PositionLevelApprovalDefinition LevelDefinitions => JsonSerializer.Deserialize<PositionLevelApprovalDefinition>(LevelDefinition);
    public bool IsAdminRequired { get; set; }
    public int? AdminApprovalLevel { get; set; }
    public string? AdminLevelDefinition { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
}

