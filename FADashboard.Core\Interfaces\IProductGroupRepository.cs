﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Microsoft.AspNetCore.Http;


namespace FADashboard.Core.Interfaces;

public interface IProductGroupRepository
{
    Task<List<ProductInput>> GetActiveProductsGroupName(ICurrentUser currentUser);
}
