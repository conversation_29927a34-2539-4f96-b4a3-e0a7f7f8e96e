﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class VanDistributorMapping : IAuditedEntity
{
    public VanDistributorMapping()
    {
        IsVanUserMappingDeleted = false;
    }
    public long DistributorId { get; set; }
    public long Id { get; set; }
    public long VanId { get; set; }
    public long? EmployeeId { get; set; }
    public bool IsVanUserMappingDeleted { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string WarehouseErpId { get; set; }
    public string WarehouseName { get; set; }
    public string CreationContext { get; set; }
}

[Table("VanMaster")]
public class VanSales : IAuditedEntity, IDeletable
{
    public string ChassisNumber { get; set; }
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    [Column("IsDeleted")]
    public bool Deleted { get; set; }

    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public double VanCapacity { get; set; }
    public string Vaninvoiceprefix { get; internal set; }
    public string VanName { get; set; }
    public string VanRegistrationNumber { get; set; }
}
