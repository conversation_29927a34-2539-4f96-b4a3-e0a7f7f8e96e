﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class AppConfigView
{
    public long Id { get; set; }
    public UIType UIType { get; set; }
    public string CompanyLogo { get; set; }
    public ColourCode ColourCode { get; set; }
    public List<AppModules> AppModules { get; set; }
    public ConfigConstraints Constraints { get; set; }
    public UIConfigurations UIConfigurations { get; set; }
    public CTAsColor CTAsColor { get; set; }
    public ProductFilters ProductFilters { get; set; }
}

public class CTAsColor
{
    public DayStartCTA DayStartCTA { get; set; }
}
public class ProductFilters
{
    public string Filter1 { get; set; }
    public string Filter2 { get; set; }

    public string Filter3 { get; set; }

}

public class UIConfigurations
{
    public string OrderSummary { get; set; }
    public bool UsesProductCatalog { get; set; }
    public string ProductCard { get; set; }
    public bool InAppKeyboard { get; set; }
    public bool IsMRPVisible { get; set; }
    public bool ShowOVCPopUp { get; set; }
    public bool HideDistributorStock { get; set; }
    public bool ShowAllPrimaryCategoryOnFiltration { get; set; }
    public bool ExtendedCalendar { get; set; }
    public bool HideChannel { get; set; }
    public bool IsFilterSorted { get; set; }

}

public class ColourCode
{
    public string PrimaryColourHexCode { get; set; }
    public string SecondaryColourHexCode { get; set; }
    public ButtonGradients ButtonGradients { get; set; }

}

public class ButtonGradients
{
    public string Gradient1 { get; set; }
    public string Gradient2 { get; set; }
    public string Gradient3 { get; set; }

}

public class DayStartCTA
{
    public string RetailingHexCode { get; set; }
    public string JointWorkingHexCode { get; set; }
    public string OfficialWorkHexCode { get; set; }
    public string LeaveHexCode { get; set; }
    public string AbsentHexCode { get; set; }

}

public class AppModules
{
    public string ModuleName { get; set; }
    public bool IsEnabled { get; set; }
}

public class ConfigConstraints
{
    public string ConstraintType { get; set; }
    public List<long> ConstraintValues { get; set; }
}
