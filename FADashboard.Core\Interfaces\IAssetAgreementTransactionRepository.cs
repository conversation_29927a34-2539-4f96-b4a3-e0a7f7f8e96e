﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IAssetAgreementTransactionRepository
{
    Task<List<Requests>> GetAllAssetAgreementRequests(long userId, PortalUserRole userRole, long companyId, bool showArchived);
    Task<AssetAgreement> GetAssetAgreementRequestById(long id, long companyId);
    Task<RepositoryResponse> ApproveRejectAssetAgreementRequest(long requestId, bool isApproved);
}
