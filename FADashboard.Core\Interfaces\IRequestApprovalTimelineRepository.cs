﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IRequestApprovalTimelineRepository
{
    Task<List<long>> GetRequestIdsForRequestType(long companyId, bool userHaveAdminPrevileges, long userId,
        bool includeArchieved, ApprovalEngineRequesType requestType, List<long> positionIds = null);
    Task<long> GetRequestTimelinesCountForAdmin(long companyId, ApprovalEngineRequesType requestType);
    Task<List<long>> GetOpenRequestIds(long companyId, long userId,
       ApprovalEngineRequesType requestType, List<long> positionIds);
    Task<List<RequestApprovalTimelineDto>> GetUserTimelineByRequestId(long companyId, long requestId, ApprovalEngineRequesType requestType, CancellationToken ct = default);
    Task<List<RequestApprovalTimelineDto>> GetUserTimelineForUser(long companyId, List<long> userPositionCodes, DateTime startDate, DateTime endDate);
}
