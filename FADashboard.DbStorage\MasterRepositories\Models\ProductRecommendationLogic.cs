﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FAProductRecommendationLogics")]
public class ProductRecommendationLogic(long companyId) : IAuditedEntity, IDeactivatable
{
    public long Id { get; set; }
    public long CompanyId { get; set; } = companyId;
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool IsDeactive { get; set; }
    public string AsmId { get; set; }
    public string OutletConstraints { get; set; }
    public string PeerOutletConstraints { get; set; }
    public string ProductConstraints { get; set; }
    public double MinRadius { get; set; }
    public double RadiusIncreaseBy { get; set; }
    public int NumOfIterations { get; set; }
    public int IdealStrengthOfOutlets { get; set; }
    public double LiftFactor { get; set; }
    public int MonthsConsidered { get; set; }
    public string ManualRecommendedProductIds { get; set; }
    public DateTime ActiveFrom { get; set; }
    public DateTime? ActiveTill { get; set; }
    public string LogicRemark { get; set; }
    public double MinStockThreshold { get; set; }
    public double AvgOrderThreshold { get; set; }
    public double UpsellThreshold { get; set; }
    public int NumberOfRecommendationToBePushed { get; set; }
    public RecommendedProductCount RecommendedProductCount { get; set; }
}
