﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class CompanyRegistrationModel
{

    public long Id { get; set; }
    [StringLength(200), Display(Name = "Account Manager Email"), EmailAddress]
    public string AccountManagerEmail { set; get; }

    [StringLength(200), Required, Display(Name = "Account Manager Name")]
    public string AccountManagerName { set; get; }

    [Display(Name = "App Variant Number")] public string AppVariantName { get; set; } 

    [Display(Name = "App Version Number"),
     Required(AllowEmptyStrings = false),
     Range(1, int.MaxValue)]
    public int AppVersionNumber { get; set; }

    [StringLength(4, MinimumLength = 4, ErrorMessage = "Beat Short Name length should be 4"), Display(Name = "Beat Short Name")]
    public string BeatShortName { get; set; }

    [RegularExpression(@"^.{4}$", ErrorMessage = "Asset Short Name must be exactly 4 characters.")]
    [Display(Name = "Asset Short Name")]
    public string? AssetShortName { get; set; }


    [Required, Display(Name = "Billing Type")]
    public string BillingType { get; set; }

    [Display(Name = "Distributor Erp Incremental Number")]
    public int? DistributorErpIncrementalNumber { get; set; }

    [Display(Name = "Distributor Erp Length")]
    public int? DistributorErpLength { get; set; }

    [StringLength(4, MinimumLength = 4, ErrorMessage = "Distributor Short Name length should be 4"), Display(Name = "Distributor Short Name")]
    public string DistributorShortName { get; set; }

    [StringLength(4, MinimumLength = 4, ErrorMessage = "Employee Short Name length should be 4"), Display(Name = "Employee Short Name")]
    public string EmployeeShortName { get; set; }

    [StringLength(4, MinimumLength = 4, ErrorMessage = "Product Short Name length should be 4"), Display(Name = "Product Short Name")]
    public string ProductShortName { get; set; }

    [Display(Name = "Number of grace period days"),
     Required(AllowEmptyStrings = false)]
    public string GracePeriod { get; set; }

    [Required, Display(Name = "Company requires hardcopy Invoice")]
    public string HardCopy { get; set; }

    public string ImageId { get; set; }

    [StringLength(200), Display(Name = "Key Account Manager Email"), EmailAddress]
    public string KeyAccountManagerEmail { set; get; }

    [StringLength(200), Required, Display(Name = "Key Account Manager Name")]
    public string KeyAccountManagerName { set; get; }

    [Required(ErrorMessage = "Company Legal Name is required"),
     Display(Name = "Company Legal Name")]
    public string LegalName { get; set; }

    [Display(Name = "Minimum billing count"),
     Required(AllowEmptyStrings = false)]
    public string MinBillingUser { get; set; }

    [Display(Name = "Minimum app version number")]
    public int? MinRequiredAppVersion { set; get; }

    [Required(ErrorMessage = "Company Name is required")]
    public string Name { get; set; }

    [Required, Display(Name = "Payment Recovery Email IDs")]
    public List<string> PaymentRecoveryEmailIDs { get; set; }

    [Required, Display(Name = "Payment Recovery Mobile Number")]
    public List<string> PaymentRecoveryMobileNumber { get; set; }

    public string PreloaderBgImageId { get; set; }
    public string PreLoaderIconId { get; set; }

    [Display(Name = "Price Per User"),
     Required(AllowEmptyStrings = false)]
    public string PricePerUser { get; set; }

    [StringLength(200), Required, Display(Name = "Project Coordinator Email"), EmailAddress]
    public string ProjectCoordinatorEmail { set; get; }

    [StringLength(200), Required, Display(Name = "Project Coordinator Name")]
    public string ProjectCoordinatorName { set; get; }

    [StringLength(200), Required, Display(Name = "Project Manager Email"), EmailAddress]
    public string ProjectManagerEmail { set; get; }

    [StringLength(200), Required, Display(Name = "Project Manager Name")]
    public string ProjectManagerName { set; get; }

    [Required] public ProjectStage ProjectStage { set; get; }
    [Required] public ClientCategory? ClientCategory { set; get; }

    [StringLength(50), Display(Name = "Sector")]
    public string Sector { get; set; }

    [StringLength(4, MinimumLength = 4, ErrorMessage = "Outlet Short Name length should be 4"), Display(Name = "Outlet Short Name")]
    public string ShortName { get; set; }
    [StringLength(16, MinimumLength = 4, ErrorMessage = "Company Short Code length should be 4"), Display(Name = "Company Short Code")]
    public string CompanyShortCode { get; set; }
    public long? ClonedCompanyId { get; set; }
}
