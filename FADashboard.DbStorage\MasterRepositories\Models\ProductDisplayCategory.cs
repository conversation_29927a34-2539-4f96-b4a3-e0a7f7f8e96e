﻿using System.ComponentModel.DataAnnotations;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ProductDisplayCategory : IAuditedEntity, IDeactivatable, IDeletable, IERPEntity
{
    private ProductDisplayCategory()
    {
    }

    public ProductDisplayCategory(long companyId) : this()
    {
        CompanyId = companyId;
    }

    public virtual Company Company { get; private set; }

    public long CompanyId { get; private set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    [Audited]
    public bool Deleted { get; set; }

    public long Id { get; set; }

    [Audited]
    public bool? IsAssorted { get; set; }

    [Audited]
    public bool IsDeactive { get; set; }

    [Audited]
    public DateTime LastUpdatedAt { get; set; }

    [Required]
    [StringLength(128)]
    [Audited]
    public string Name { get; set; }

    public string ErpId { get; set; }

    public virtual ProductSecondaryCategory ProductSecondaryCategory { get; set; }
    public long ProductSecondaryCategoryId { get; set; }
}
