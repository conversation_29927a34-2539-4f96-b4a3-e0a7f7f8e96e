﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class JourneyPlanConfigurations : IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public JourneyType JourneyCreationSettingType { set; get; }
    public PositionCodeLevel PositionLevel { get; set; }
    public string EntityLevel { set; get; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
}
