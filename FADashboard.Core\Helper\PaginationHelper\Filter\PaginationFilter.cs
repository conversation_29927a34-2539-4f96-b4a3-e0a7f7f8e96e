﻿using System.Text.Json;

namespace FADashboard.Core.Helper.PaginationHelper.Filter;

public class PaginationFilter
{
    public PaginationFilter()
    {
        PageNumber = 1;
        PageSize = 10;
    }

    public PaginationFilter(PaginationFilter filter)
    {
        PageNumber = filter.PageNumber < 1 ? 1 : filter.PageNumber;
        PageSize = filter.PageSize < 10 ? 10 : filter.PageSize / 10 * 10;
        ShowDeactive = filter.ShowDeactive;
        ShowVacant = filter.ShowVacant;
        OrderBy = filter.OrderBy;

        if (!string.IsNullOrEmpty(filter.QuerySearch))
            QuerySearch = filter.QuerySearch.Trim().ToLower(System.Globalization.CultureInfo.CurrentCulture);
        if (!string.IsNullOrEmpty(filter.ColumnFilter))
            ColumnFilters = JsonSerializer.Deserialize<Dictionary<string, string>>(filter.ColumnFilter).Where(a => !string.IsNullOrEmpty(a.Value)).ToDictionary(b => b.Key, b => b.Value);
    }

    public string OrderBy { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public string QuerySearch { get; set; }
    public bool ShowDeactive { get; set; }
    public bool ShowVacant { get; set; }
    public string ColumnFilter { get; set; }
    public Dictionary<string, string> ColumnFilters { get; set; }
}
