﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class OutletMasterList
{
    public long BeatId { get; set; }
    public string BeatName { get; set; }
    public string City { set; get; }
    public string ContactNo { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string ShopName { get; set; }
    public string State { set; get; }
    public ISRAvailability? ISRAvailability { get; set; }
    public VerificationStatus? VerificationStatusEnum { get; set; }
    public string VerificationStatusString { set; get; }
    public string MarketName { set; get; }

    public string ShopType { set; get; }
}

public class OutletTotal
{
    public int Total { get; set; }
    public List<OutletMasterList> OutletRecords { get; set; }
}
