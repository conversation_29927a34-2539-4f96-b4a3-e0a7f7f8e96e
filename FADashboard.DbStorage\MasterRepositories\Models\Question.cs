﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("NewSurvey_QuestionChoice")]
public class Choice
{
    [StringLength(50)] public string Condition { get; set; }

    public bool? Deleted { set; get; }

    public long ID { get; set; }

    public bool IsSelected { get; set; }

    public long QuestionId { get; set; }

    [StringLength(50)] public string Text { get; set; }

    [StringLength(50)] public string Value { get; set; }

    //public virtual Question Question { get; set; }
}

public class Question
{
    public virtual List<Choice> Choices { get; set; }

    public bool Deactivated { get; set; }

    [StringLength(50)] public string DefaultValue { get; set; }

    public bool? Deleted { set; get; }

    [StringLength(500)] public string Description { get; set; }
    [StringLength(500)] public string DynamicDescription { get; set; }

    public int DisplayOrder { get; set; }

    public DisplayWidth DisplayWidth { get; set; }

    [StringLength(50)] public string Hint { get; set; }

    public long ID { get; set; }

    public string OutletMetadata { set; get; }

    public virtual QuestionGroup QuestionGroup { get; set; }

    public long QuestionGroupId { get; set; }

    public SurveyQuestionType QuestionType { get; set; }

    public bool Required { get; set; }

    [StringLength(500)] public string Title { get; set; }

    [StringLength(500)] public string ValidationErrorMessage { get; set; }

    [StringLength(500)] public string ValidationRule { get; set; }
}
