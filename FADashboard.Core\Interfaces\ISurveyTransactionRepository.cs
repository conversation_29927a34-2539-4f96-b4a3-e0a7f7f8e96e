﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ISurveyTransactionRepository
{
    Task<int> GetSurveyResponseCount(long formId, long companyId);

    Task<Dictionary<long, int>> GetSurveyResponseCountDict(List<long> formIds, long companyId);

    Task<List<SurveyResponses>> GetSurveyResponsesOnFaEventId(long faEventId, long companyId);
    Task<SurveyResponses> GetSurveyResponsesById(long surveyResponseId, long companyId);

}
