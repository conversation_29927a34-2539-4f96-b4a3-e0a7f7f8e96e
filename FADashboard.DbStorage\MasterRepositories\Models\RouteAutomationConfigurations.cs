﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class RouteAutomationConfiguration
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public RouteFrequency Frequency { get; set; }
    public long? CohortId { get; set; }
    public InputType InputType { get; set; }
    public VisitDefinitionType VisitDefinitionType { get; set; }
    public string? VisitDefinitionJson { get; set; }
    [NotMapped]
    public List<VisitDefinitionModel>? VisitDefinitionJsonList => !string.IsNullOrEmpty(VisitDefinitionJson) ?
        JsonSerializer.Deserialize<List<VisitDefinitionModel>>(VisitDefinitionJson) : null;
    public int? MaxDailyVisits { get; set; }
    public double? MaxDailyDistance { get; set; }
    public SpectralCoefficientType SpectralCoefficient { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public RoutePlaygroundStatus Status { get; set; }
    public string StatusRemark { get; set; }
    public long ActionTakenBy { get; set; }
    public string Email { get; set; }
    public PortalUserRole UserRole { get; set; }
    public long UserId { get; set; }
    public string InputFileName { get; set; }
    public DateTime? ExecutedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public OutlierAddition OutlierAddition { get; set; }
}

