﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IZoneRepository
{
    Task<RepositoryResponse> ActivateDeactivateZone(long zoneId, bool action, long companyId);

    Task<RepositoryResponse> CreateZone(GeographiesListWithParent zone, long companyId);

    Task<List<GeographiesListWithParent>> GetAllZones(long companyId, bool includeDeactivate);
    Task<List<EntityMinIncludeParent>> GetAllActiveZones(long companyId);

    Task<GeographyInput> GetZoneById(long zoneId, long companyId);

    Task<GeographyInput> GetZoneByName(string name, long companyId);

    Task<List<GeographiesListParent>> GetZoneByParentId(long companyId);

    Task<List<GeographiesListWithParent>> GetZones(long companyId, bool includeDeactivate);

    Task<List<EntityMin>> GetZonesMinByIds(long companyId, List<long> Ids);

    Task<RepositoryResponse> UpdateZone(GeographiesListWithParent zone, long companyId);
    Task<Dictionary<string, long>> GetZoneDict(long companyId);
}
