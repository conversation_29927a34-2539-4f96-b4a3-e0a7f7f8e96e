﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
namespace FADashboard.Core.Interfaces.Cache;
public interface IMandaysCappingRepository
{
    public Task<RepositoryResponse> UpdateMandaysDataLimit(MandaysCappingDto data, long companyId);

    public Task<RepositoryResponse> DeactivateJourneyPlanForMandays(long Id, long companyId);
    public Task<MandaysCappingDtoTotal> GetAllDataByFiltering(long CompanyId, PaginationFilter validFilter, PortalUserRole userRole);
}
