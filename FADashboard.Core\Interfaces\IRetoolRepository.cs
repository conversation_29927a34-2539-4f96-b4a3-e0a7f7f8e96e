﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces;

public interface IRetoolRepository
{
    Task<List<RetoolReport>> GetRetoolReports();
    Task<List<RetoolReportSubscriptions>> GetRetoolReportSubscriptions(long companyId, bool showDeactive);
    Task<RepositoryResponse> ActivateDeactivateRetoolReports(long id, long companyId, bool action);
    Task<RepositoryResponse> CreatRetoolReportSubscription(RetoolReportSubscriptionsInput retoolReport, long companyId, long userId);
    Task<RepositoryResponse> UpdateRetoolReportSubscription(RetoolReportSubscriptionsInput retoolReport, long companyId);
}
