﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IIdsRepository
{
    Task<List<IDSLogin>> GetAllUsersExceptDistributorsAndRegionalAdmins(Guid loginGuid, long companyId);
    Task<IDSLogin> GetUserByLoginGuid(Guid loginGuid);
    Task<List<EntityMinWithCompany>> GetCompanyListByLoginGuid(Guid loginGuid, PortalUserRole userRole);

    Task<IDSLogin> GetUserByLocalId(long localId, long companyId);

    Task<IDSLogin> GetUserByPhoneNumber(string phoneNumber, long companyId);

    Task<RepositoryResponse> RegisterOnIDSStatic(RegisterResponseModel loginAuth);
    Task<RepositoryResponse> RegisterViaPhoneOnIDSStatic(RegisterResponseModel loginAuth);
    Task<Dictionary<string, List<long>>> GetIDSWithRoleIds(long companyId);

    Task<List<long>> GetRoleIdsForUser(long userId, long companyId);
    Task<RepositoryResponse> UpdateRoleIds(List<long?> roleIds, long companyId, long localId);
    Task<List<IDSLogin>> GetAllUsers(long companyId);
}
