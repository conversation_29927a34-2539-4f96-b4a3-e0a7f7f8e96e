﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Interfaces.Cache;
using FADashboard.Core.Models.ApiModels;
namespace FADashboard.Core.Services;
public class MandaysCappingService(ICurrentUser currentUser, IMandaysCappingRepository mandayscapping)
{
    public async Task<PagedResponse<List<MandaysCappingDto>>> GetDataByFiltering(PaginationFilter validFilter)
    {
        var companyId = currentUser.CompanyId;
        var userRole = currentUser.UserRole;
        var result = await mandayscapping.GetAllDataByFiltering(companyId, validFilter,userRole);
        var totalRecords = result.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(result.MandaysCappingDtoRecords, validFilter, totalRecords);
        return pagedReponse;
    }
    public async Task<RepositoryResponse> MandaysCappingUpdateLimit(MandaysCappingDto mandaysdata)
    {
        var companyId = currentUser.CompanyId;
        var success = await mandayscapping.UpdateMandaysDataLimit(mandaysdata, companyId);
        return success;
    }
    public async Task<RepositoryResponse> MandaysCappingDeactiveJourneyPlan(long Id)
    {
        var companyId = currentUser.CompanyId;
        var success = await mandayscapping.DeactivateJourneyPlanForMandays(Id, companyId);
        return success;
    }

}
