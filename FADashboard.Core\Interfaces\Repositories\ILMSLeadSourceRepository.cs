using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSLeadSourceRepository
    {
        Task<LMSLeadSourceDto> GetByIdAsync(long id);
        Task<PagedResult<LMSLeadSourceDto>> GetAllByCompanyIdAsync(long companyId, LMSLeadSourceQueryParameters queryParameters);
        Task<LMSLeadSourceDto> AddAsync(LMSLeadSourceDto leadSourceDto);
        Task<LMSLeadSourceDto> UpdateAsync(LMSLeadSourceDto leadSourceDto);
        Task<bool> DeleteAsync(long id, long? updatedByUserId);
        Task<bool> SourceNameExistsAsync(long companyId, string sourceName, long? currentId = null);
    }
}
