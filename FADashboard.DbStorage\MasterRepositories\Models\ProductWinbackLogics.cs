﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ProductWinbackLogic(long companyId) : IAuditedEntity, IDeactivatable
{
    public long Id { get; set; }
    public long CompanyId { get; set; } = companyId;
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool IsDeactive { get; set; }
    public string AsmId { get; set; }
    public string OutletConstraints { get; set; }
    public DateTime ActiveFrom { get; set; }
    public DateTime ActiveTill { get; set; }
    public DateTime TrainFrom { get; set; }
    public DateTime TrainTill { get; set; }
    public string LogicRemark { get; set; }
    public double DeviationAllowed { get; set; }
    public int MaxRecordsToBeTagged { get; set; }
    public string ProductConstraints { get; set; }
}
