﻿namespace FADashboard.DbStorage.HCCBRepositories.Models
{
    public class CustomerMaster
    {
        public static HashSet<string> CustomerTypesForDistributors = new HashSet<string> { 
            "D",
            "C"
        };
        public long Id { get; set; }
        public string Email_id { get; set; }
        public DateTime Createddate { get; set; }
        public string Sap_customer_id { get; set; }
        public string Address_1 { get; set; }
        public string Address_2 { get; set; }
        public string Pin_code { get; set; }
        public string Latitude { get; set; }
        public string Address_3 { get; set; }
        public string Mobile_no { get; set; }
        public string Admin_entity_id { get; set; }
        public string Nka_customer_id { get; set; }
        public string Type { get; set; }
        public string Payer { get; set; }
        public string Open_market_discount { get; set; }
        public DateTime Sfa_processeddate { get; set; }
        public string Customer_category_id { get; set; }
        public string Town_id { get; set; }
        public string State_desc { get; set; }
        public string Town_class_desc { get; set; }
        public string Sfa_batch_id { get; set; }
        public string State_id { get; set; }
        public string Barcode { get; set; }
        public string Longitude { get; set; }
        public string Customertype { get; set; }
        public DateTime Last_modify_date { get; set; }
        public string Payment_mode { get; set; }
        public string Vpo_class_description { get; set; }
        public string Contact_person { get; set; }
        public string Region_id { get; set; }
        public string Active { get; set; }
        public string Telephone { get; set; }
        public string Entity_id { get; set; }
        public DateTime Customer_activated_date { get; set; }
        public string Rf_id { get; set; }
        public string Sfa_status { get; set; }
        public string Order_block { get; set; }
        public string Site_id { get; set; }
        public string Validation_remarks { get; set; }
        public string Credit_days { get; set; }
        public string Customer_name { get; set; }
        public string District_id { get; set; }
        public string Customer_class_id { get; set; }
        public string Sub_channel_id { get; set; }
        public string Account_photo_id { get; set; }
        public string Eb_mobile_barcode { get; set; }
        public string Channel_id { get; set; }
        public string Vpo_class_id { get; set; }
        public string gcc_id { get; set; }
        public long CompanyId { get; set; }
        public string CreationContext { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public string UniqueKey { get; set; }
    }
}
