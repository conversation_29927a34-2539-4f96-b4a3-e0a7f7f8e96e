﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class CompanyTargetSubscriptionList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public bool IsPremium { get; set; }
    public bool IsDeactive { get; set; }
    public string TargetDescription { get; set; }
    public bool IsForLandingScreen { get; set; }
    public bool IsTargetNeedToBreakDown { get; set; }
    public long? ParentId { get; set; }
    public long TargetMasterId { get; set; }
    public bool ShowInManagerApp { get; set; }
    public TargetFilters? DisplayHierarchy { get; set; }
    public TargetFilters? FilterHierarchy { get; set; }
    public AchievementReturnType? AchievementValueType { get; set; }
    public FlexibleTargetFrequency? TargetFrequency { get; set; }
    public TargetMasterView TargetMasterView { get; set; }
}

public class TargetMasterView
{
    public long Id { get; set; }
    public string TargetIdentifier { get; set; }
    public string TargetName { get; set; }
    public Heirarchy Hierarchy1 { get; set; }
    public Heirarchy? Hierarchy2 { get; set; }
    public Heirarchy? Hierarchy3 { get; set; }
    public string TargetOn { get; set; }
    public string Hierarchy1Query { get; set; }
    public string Hierarchy2Query { get; set; }
    public string Hierarchy3Query { get; set; }
    public string AchievementQuery { get; set; }
    public AchievementDb? AchievementDb { get; set; }
    public AchievementReturnType? AchievementReturnType { get; set; }
    public AppScreen? AppScreen { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public VisualizationType? VisualizationType { get; set; }
    public string TargetFilters { get; set; }
    public List<long?> TargetFiltersList { get; set; }
    public bool? IsForApp { get; set; }
    public TargetFilters? DisplayAxis { get; set; }
    public TargetType? TargetType { get; set; }
    public string AnalyticalQuery { get; set; }
    public string ReportDataQuery { get; set; }
    public string ReportDataDb { get; set; }
}
public class TargetMastersWithStatus
{
    public List<TargetMasterView> TargetMasters { get; set; }
    public Dictionary<long, bool> CompanyTargetSubscriptionsStatus { get; set; }
}
