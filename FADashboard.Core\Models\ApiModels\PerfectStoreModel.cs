﻿
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class PerfectStoreModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public string Description { get; set; }
    public string ERPId { get; set; }
    public string Visibility { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsRepeatable { get; set; }
    public int RepeatFrequencyInDays { get; set; }
    public bool IsDeactive { get; set; }
    public bool Deleted { get; set; }
    public string VisibilityInactive { get; set; }
    public long CohortId { get; set; }
    public List<long?> ProductDivisionIds { get; set; }
    public List<Criteria> Criteria { get; set; }
    public RuleType RuleType { get; set; }
}

public class PerfectStoreList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EffectiveEndDate { get; set; }
    public int RepeatFrequencyInDays { get; set; }
}
public class Criteria
{
    public long Id { get; set; }
    public string CriteriaName { get; set; }
    public TaskManagementFocusAreaType CriteriaType { get; set; }
    public long? TargetType { get; set; }
    public long? ProductTag { get; set; }
    public TargetSplitCriteria? TaskCalculationRule { get; set; }
    public TargetSplitRatio? DaysPerSet { get; set; }
    public int? Adherence { get; set; }
    public List<long?> SurveyIds { get; set; }
    public int? CallsConvertedPercentage { get; set; }
    public long? SurveyId { get; set; }
    public long? PerfectCallId { get; set; }
}
