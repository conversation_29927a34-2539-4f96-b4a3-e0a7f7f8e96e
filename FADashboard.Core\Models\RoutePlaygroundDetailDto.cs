﻿using Libraries.CommonEnums;
using Microsoft.AspNetCore.Http;

namespace FADashboard.Core.Models;
public class RoutePlaygroundDetailDto
{
    public long Id { get; set; }

    public string InputFileName { get; set; }

    public InputType InputType { get; set; }

    public RoutePlaygroundStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public string EmailId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string EstimatedTime { get; set; }
    public long CompanyId { get; set; }

}

public class RoutePlaygroundInputConstraints{
    public int MaximumDailyVisits { get; set; }
    public int MaximumDailyDistance { get; set; }
    public SpectralCoefficientType SpectralCoefficient { get; set; }
    public OutlierAddition OutlierAddition { get; set; }
    public RouteFrequency JourneyType { get; set; }
    public InputType InputType { get; set; }
    public long? CohortId { get; set; }
    public VisitDefinitionType VisitDefinitionType { get; set; }
    public List<VisitDefinitionModel> VisitDefinitionList { get; set; }

}

public class RouteOutletDataInputModel
{
    public long Id { get; set; }
    public List<string> EmployeeIds { get; set; }
    public List<string> DaysList { get; set; }
}

public class VisitDefinitionModel
{
    public required long Id { get; set; }
    public double? RequiredVisits { get; set; }
    public double? RetailTime { get; set; }
}

public class RoutePlaygroundFileModel
{
    public string inputConstraints { get; set; }
    public IFormFile file { get; set; }
}

public class RoutePlaygroundQueueModel
{
    public long Id { get; set; }
    public string FileName { get; set; }
    public int MaximumDailyVisits { get; set; }
    public int MaximumDailyDistance { get; set; }
    public string SpectralCoefficient { get; set; }
    public string OutlierAddition { get; set; }
    public string JourneyType { get; set; }
    public string Country { get; set; }
}

public class RoutePlaygroundOutletModel
{
    public string OutletErpId { get; set; }
    public decimal Longitude { get; set; }
    public decimal Latitude { get; set; }
    public string RouteId { get; set; }
    public int Sequence { get; set; }
    public string EmployeeId { get; set; }
    public decimal CumulativeDistance { get; set; }
    public string ShopName { get; set; }
    public string Region { get; set; }
}

public class RouteDataModel
{
    public List<string> EmployeeList { get; set; }
    public List<string> DaysList { get; set; }
}

public class RemovedRouteModel
{
    public string RouteId { get; set; }
    public string EmployeeId { get; set; }
}

public class RoutePlaygroundChangeQueueModel
{
    public long Id { get; set; }
    public string FileName { get; set; }
}

public class RoutePlaygroundInputModel
{
    public long Id { get; set; }
    public List<RoutePlaygroundOutletModel> Outlets { get; set; }
    public bool IsOriginal { get; set; }

    public List<RemovedRouteModel> RemovedRoutes { get; set; }
}

public class OptimizedRouteOutletModel
{
    public string OutletErpId { get; set; }
    public decimal Longitude { get; set; }
    public decimal Latitude { get; set; }
    public string ShopName { get; set; }
    public string Region { get; set; }
}

public class OptimizedRouteInputModel
{
    public List<OptimizedRouteOutletModel> Outlets { get; set; }
    public string Country { get; set; }
}

public class OptimizedRouteOutputModel
{
    public List<RoutePlaygroundOutletModel> optimized_sequence { get; set; }
}

