﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Models;

public class PositionCodeEntityMapping
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public long EntityId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public PositionCodeDTO PositionCode { get; set; }
    public long PositionCodeId { get; set; }
    public EmployeeDTO Employee { get; set; }
}
