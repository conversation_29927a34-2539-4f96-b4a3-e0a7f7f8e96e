﻿using System.Text.Json;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class ExternalAssetService(ICurrentUser currentUser, IExternalAssetRepository externalAssetRepository, ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    public async Task<RepositoryResponse> CreateNewExternalAsset(ExternalAssetData data)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        return await externalAssetRepository.CreateNewExternalAsset(data, currentUser.CompanyId, offset);
    }

    public async Task<RepositoryResponse> UpdateExternalAsset(ExternalAssetData data)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        return await externalAssetRepository.UpdateExternalAsset(data, currentUser.CompanyId, offset);
    }

    public async Task<RepositoryResponse> DeleteExternalAsset(long id) => await externalAssetRepository.DeleteExternalAsset(id);

    public async Task<ExternalAssetData> GetExternalAssetDataById(long id)
    {
        var data = await externalAssetRepository.GetExternalAssetDataById(id, currentUser.CompanyId);
        var dataToReturn = new ExternalAssetData()
        {
            AssetName = data.Name,
            AssetURL = data.Url,
            startDate = data.FACompanyExternalAssetFilter.StartDate,
            endDate = data.FACompanyExternalAssetFilter.EndDate,
            filterOn = data.FACompanyExternalAssetFilter.FilterOn,
            filterIds = JsonSerializer.Deserialize<List<long>>(data.FACompanyExternalAssetFilter.FilterIds, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
            channelIds = JsonSerializer.Deserialize<List<long>>(data.FACompanyExternalAssetFilter.Channel, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
            shopTypeIds = JsonSerializer.Deserialize<List<long>>(data.FACompanyExternalAssetFilter.ShopType, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
            segmentation = JsonSerializer.Deserialize<List<long>>(data.FACompanyExternalAssetFilter.Segmentation, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
            isFocused = data.FACompanyExternalAssetFilter.IsFocused,
            chainIds = JsonSerializer.Deserialize<List<long>>(data.FACompanyExternalAssetFilter.Chain, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
            customTag = JsonSerializer.Deserialize<List<long>>(data.FACompanyExternalAssetFilter.CustomTag, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
            isNoTag = data.FACompanyExternalAssetFilter.ApplyAssetsOnNoTagOutlets,
            Image = data.Image,
            UserPlatform = data.UserPlatforms,
        };
        return dataToReturn;
    }

    public async Task<List<CompanyExternalAssetModel>> GetExternalAssetList()
    {
        var data = await externalAssetRepository.GetExternalAssetList(currentUser.CompanyId);
        var dataToReturn = new List<CompanyExternalAssetModel>();
        data.ForEach(s =>
        {
            var isOutletConstraintPresent = !(s.FACompanyExternalAssetFilter.Channel == "null" && s.FACompanyExternalAssetFilter.IsFocused == null && s.FACompanyExternalAssetFilter.Chain == "null" &&
                                              s.FACompanyExternalAssetFilter.Segmentation == "null" && s.FACompanyExternalAssetFilter.ShopType == "null" && s.FACompanyExternalAssetFilter.CustomTag == "null" &&
                                              !s.FACompanyExternalAssetFilter.ApplyAssetsOnNoTagOutlets);
            dataToReturn.Add(new CompanyExternalAssetModel()
            {
                Id = s.Id,
                Name = s.Name,
                Url = s.Url,
                StartDate = s.FACompanyExternalAssetFilter.StartDate,
                EndDate = s.FACompanyExternalAssetFilter.EndDate,
                Applicability = isOutletConstraintPresent ? "Multiple" : s.FACompanyExternalAssetFilter.FilterOn.ToString(),
            });
        });
        return dataToReturn;
    }
}
