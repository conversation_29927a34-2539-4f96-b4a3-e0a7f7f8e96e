using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSAccountNoteService
    {
        Task<LMSAccountNoteDto> GetAccountNoteByIdAsync(long id);
        Task<PagedResult<LMSAccountNoteDto>> GetAccountNotesByAccountIdAsync(long accountId, LMSAccountNoteQueryParameters queryParameters);
        Task<LMSAccountNoteDto> CreateAccountNoteAsync(LMSAccountNoteCreateInput noteDto, long createdByUserId, long companyId);
        Task<LMSAccountNoteDto> UpdateAccountNoteAsync(long id, LMSAccountNoteUpdateInput noteDto, long updatedByUserId);
        Task<bool> DeleteAccountNoteAsync(long id, long deletedByUserId);
    }
}
