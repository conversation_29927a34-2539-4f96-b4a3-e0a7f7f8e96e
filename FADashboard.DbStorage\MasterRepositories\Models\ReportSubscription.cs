﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ReportSubscription : IAuditedEntity, IDeactivatable
{
    public ReportSubscription()
    {
        EncryptionKey = Guid.NewGuid();
    }

    public virtual Company Company { get; set; }

    [ForeignKey("Company")] public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public Guid EncryptionKey { get; private set; }
    public long Id { get; set; }

    [Audited] public bool IsDeactive { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string Link => $"DataVisualization/Home/ShowReport/{EncryptionKey}";

    [Audited] [StringLength(256)] public string Name { set; get; }

    [Audited] public PortalUserRole PortalUserRole { get; set; }

    public virtual Report Report { get; set; }

    [Audited] public long ReportId { get; set; }

    [Audited] public SubscriptionType SubscriptionType { get; set; }
}
