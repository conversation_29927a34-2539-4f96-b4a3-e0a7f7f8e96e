﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class ApprovalRequestDto
{
    public PrimaryOrderDto PrimaryOrderDetails { get; set; }
}

public class ApprovalRequestDetailsDto
{
    public long RequestId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public ApprovalEngineRequestStatus RequestStatus { get; set; }
    public string RequestorUser { get; set; }
    public long RequestorUserId { get; set; }
    public string Reason { get; set; }
    public string EntityName { get; set; }
    public long EntityId { get; set; }
}
