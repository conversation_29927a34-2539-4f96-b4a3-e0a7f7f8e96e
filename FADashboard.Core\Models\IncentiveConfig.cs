﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class IncentiveConfig
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsDeactive { get; set; }
    public int CriteriaCount { get; set; }
}
