﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("InternationalDiscounts")]
public class InternationalDiscounts
{
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    [Column("IsDeleted")]
    public bool Deleted { get; set; }
    public string DiscountName { get; set; }
    public DateTime EndDate { get; set; }
    public long? FirstLevelDiscount { get; set; }
    [Column("FirstLevelDiscount_AutoApplied")]
    public bool FirstLevelDiscountAutoApplied { get; set; }
    [Column("FirstLevelDiscount_IsEditable")]
    public bool FirstLevelDiscountIsEditable { get; set; }
    [Column("FOC_Applicable")]
    public bool FOCApplicable { get; set; }
    [Column("FOC_Mandatory")]
    public bool FOCMandatory { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long? SecondLevelDiscount { get; set; }
    [Column("SecondLevelDiscount_AutoApplied")]
    public bool SecondLevelDiscountAutoApplied { get; set; }
    [Column("SecondLevelDiscount_IsEditable")]
    public bool SecondLevelDiscountIsEditable { get; set; }
    public string Segmentation { get; set; }
    public DateTime StartDate { get; set; }
}
