﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ProductRecommendationList
{
    public long Id { get; set; }
    public string LogicRemark { get; set; }
    public DateTime ActiveFrom { get; set; }
    public bool IsDeactive { get; set; }
}
public class ProductRecommendationInput
{
    public long? Id { get; set; }
    public List<long> AsmIds { get; set; }
    public List<OutletConstraintsInput> OutletConstraints { get; set; }
    public List<PeerOutletConstraintsInput> PeerOutletContstraints { get; set; }
    public List<ProductConstraintsInput> ProductConstraints { get; set; }
    public double MinRadius { get; set; }
    public double RadiusIncreaseBy { get; set; }
    public int NumberOfIterations { get; set; }
    public int IdealStrengthOfOutlets { get; set; }
    public double LiftFactor { get; set; }
    public List<long> ManualRecommendedProductIds { get; set; }
    public DateTime ActiveFrom { get; set; }
    public DateTime? ActiveTill { get; set; }
    public string LogicRemarks { get; set; }
    public double MinimumStockThreshold { get; set; }
    public double AverageOrderThreshold { get; set; }
    public double UpsellThreshold { get; set; }
    public int NumberOfRecommendationToBePushed { get; set; }
    public RecommendedProductCount RecommendedProductCount { get; set; }
    public int MonthsConsidered { get; set; }
}
public class OutletConstraintsInput
{
    public string Property { get; set; }

    public List<string> PropertyValue { get; set; }

    public long? ProductDivisionId { get; set; }
}

public class PeerOutletConstraintsInput
{
    public string Property { get; set; }

    public long? ProductDivisionId { get; set; }
}

public class ProductConstraintsInput
{
    public string Property { get; set; }

    public List<long> PropertyValue { get; set; }

}
