﻿using Microsoft.EntityFrameworkCore;
using FADashboard.Core.Helper.ReportBuilderHelper;
using Libraries.CommonModels;

namespace FADashboard.DbStorage.DbContexts;

public class FlexibleReportBuilderDbContext(DbContextOptions<FlexibleReportBuilderDbContext> options, AppConfigSettings appConfigSettings) : DbContext(options)
{
    public DbSet<ReportDefinition> ReportDefinitions { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) => base.OnConfiguring(optionsBuilder);

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        var reportEntity = modelBuilder.Entity<ReportDefinition>();
        
        reportEntity
            .HasNoDiscriminator()
            .ToContainer("flexible-report")
            .HasPartitionKey(e => e.reportType);

        // Configure complex types as owned entities
        reportEntity.OwnsOne(p => p.DataSets, ds =>
        {
            ds.ToJsonProperty("dataSets");
            // Configure Selected as owned collection
            ds.OwnsMany(p => p.Selected, s =>
            {
                s.ToJsonProperty("selected");
                s.WithOwner();
                s.Property(p => p.Id).ToJsonProperty("id");
                s.Property(p => p.DataSourceId).ToJsonProperty("dataSourceId");
                s.Property(p => p.Name).ToJsonProperty("name");
            });
            
            // Configure Joins as owned collection
            ds.OwnsMany(p => p.Joins, j =>
            {
                j.ToJsonProperty("joins");
                j.WithOwner();
                j.Property(p => p.FirstDataSetId).ToJsonProperty("firstDataSetId");
                j.Property(p => p.FirstDataSetColumn).ToJsonProperty("firstDataSetColumn");
                j.Property(p => p.SecondDataSetId).ToJsonProperty("secondDataSetId");
                j.Property(p => p.SecondDataSetColumn).ToJsonProperty("secondDataSetColumn");
                
                // Configure nested Conditions collection
                j.OwnsMany(p => p.Conditions, c =>
                {
                    c.ToJsonProperty("conditions");
                    c.WithOwner();
                    c.Property(p => p.DataSetId).ToJsonProperty("dataSetId");
                    c.Property(p => p.Column).ToJsonProperty("column");
                    c.Property(p => p.Value).ToJsonProperty("value");
                });
            });
        });

        reportEntity.OwnsMany(p => p.DataSources, ds =>
        {
            ds.ToJsonProperty("dataSources");
            ds.WithOwner();
            ds.Property(p => p.Id).ToJsonProperty("id");
            ds.Property(p => p.Name).ToJsonProperty("name");
        });

        reportEntity.OwnsMany(p => p.Columns, c =>
        {
            c.ToJsonProperty("columns");
            c.WithOwner();
            c.Property(p => p.Id).ToJsonProperty("id");
            c.Property(p => p.Title).ToJsonProperty("title");
            c.Property(p => p.ElementId).ToJsonProperty("elementId");
            c.Property(p => p.AggregatorType).ToJsonProperty("aggregatorType");
            c.Property(p => p.DataType).ToJsonProperty("dataType");
            c.Property(p => p.DataSetId).ToJsonProperty("dataSetId");
            c.Property(p => p.Formula).ToJsonProperty("formula");
            c.Property(p => p.DerivedElementId).ToJsonProperty("derivedElementId");
            c.Property(p => p.Visible).ToJsonProperty("visible");
        });

        reportEntity.OwnsMany(p => p.Filters, f =>
        {
            f.ToJsonProperty("filters");
            f.WithOwner();
            f.Property(p => p.Id).ToJsonProperty("id");
            f.Property(p => p.Operator).ToJsonProperty("operator");
            f.Ignore(p => p.Value); // This property is marked with [NotMapped]
        });

        reportEntity.OwnsMany(p => p.Sorting, s =>
        {
            s.ToJsonProperty("sorting");
            s.WithOwner();
            s.Property(p => p.Id).ToJsonProperty("id");
            s.Property(p => p.SortDirection).ToJsonProperty("sortDirection");
        });

        reportEntity.OwnsOne(p => p.Metadata, m =>
        {
            m.ToJsonProperty("metadata");
            m.Property(p => p.ReportName).ToJsonProperty("reportName");
            m.Property(p => p.Description).ToJsonProperty("description");
            m.Property(p => p.IsTemplate).ToJsonProperty("isTemplate");
            m.Property(p => p.CompanyId).ToJsonProperty("companyId");
            m.Property(p => p.CreatedAt).ToJsonProperty("createdAt");
            m.Property(p => p.UpdatedAt).ToJsonProperty("updatedAt");
            m.Property(p => p.IsActive).ToJsonProperty("isActive");
        });

        reportEntity.OwnsOne(p => p.DynamicFilters, df =>
        {
            df.ToJsonProperty("dynamicFilters");
            df.Property(p => p.ShowUserHierarchyFilter).ToJsonProperty("showUserHierarchyFilter");
            df.Property(p => p.ShowPositionHierarchyFilter).ToJsonProperty("showPositionHierarchyFilter");
            df.Property(p => p.ShowChannelPartnerFilter).ToJsonProperty("showChannelPartnerFilter");
            df.Property(p => p.ShowDateRangeFilter).ToJsonProperty("showDateRangeFilter");
            df.Property(p => p.ShowDateFilter).ToJsonProperty("showDateFilter");
            df.Property(p => p.ShowProductHierarchyFilter).ToJsonProperty("showProductHierarchyFilter");
        });

        reportEntity.OwnsOne(p => p.DateOptions, do_ =>
        {
            do_.ToJsonProperty("dateOptions");
            do_.OwnsOne(d => d.DateRangeTypes, drt =>
            {
                drt.ToJsonProperty("dateRangeTypes");
                drt.Property(p => p.Daily).ToJsonProperty("daily");
                drt.Property(p => p.MonthToDate).ToJsonProperty("monthToDate");
                drt.Property(p => p.QuarterToDate).ToJsonProperty("quarterToDate");
                drt.Property(p => p.YearToDate).ToJsonProperty("yearToDate");
                drt.Property(p => p.CustomDateRange).ToJsonProperty("customDateRange");
                drt.Property(p => p.Today).ToJsonProperty("today");
                drt.Property(p => p.Yesterday).ToJsonProperty("yesterday");
            });
            do_.Property(d => d.DateColumnConfig).ToJsonProperty("dateColumnConfig");
        });

        reportEntity.Property(p => p.GroupBy).ToJsonProperty("groupBy");
        reportEntity.Property(p => p.id).ToJsonProperty("id");
        reportEntity.Property(p => p.reportType).ToJsonProperty("reportType");
    }
}
