using System;

namespace FADashboard.DbStorage.MasterRepositories.Models
{
    public class LMSCustomFieldValue
    {
        public long Id { get; set; }
        public int EntityType { get; set; }
        public long EntityId { get; set; }
        public long CustomFieldId { get; set; }
        public string Value { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public long? UpdatedBy { get; set; }
    }
}
