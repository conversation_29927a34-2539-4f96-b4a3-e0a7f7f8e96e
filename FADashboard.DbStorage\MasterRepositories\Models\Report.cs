﻿using System.ComponentModel.DataAnnotations;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Report : IAuditedEntity, IDeactivatable
{
    [Audited] public bool CanEmail { set; get; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    [Audited] [StringLength(2048)] public string Description { get; set; }

    [Audited] public DateTime EmailLocalDeliveryTime { get; set; }

    [Audited] public ReportFrequency Frequency { get; set; }

    public long Id { get; set; }

    [Audited] public bool IsDeactive { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string Link => $"DataVisualization/Home?enumForAssembly={ReportType}";

    [Audited] public string Name { get; set; }

    [Audited] public bool OnlyProductTeamCanAttach { set; get; }

    [Audited] public ReportCategory ReportCategory { get; set; }

    public virtual ReportSection ReportSection { get; set; }

    public long ReportSectionId { get; set; }

    public virtual ICollection<ReportSubscription> ReportSubscriptions { get; set; }

    [Audited] public EnumForReportAssembly ReportType { get; set; }
}

public class ReportSection
{
    public long Id { get; set; }
    public string Name { get; set; }
    public ReportSectionHeader ReportSectionEnum { get; set; }
}
