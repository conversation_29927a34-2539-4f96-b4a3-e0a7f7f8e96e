﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class PositionFilter
{
    public string PositionCodeId { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }
    public string PositionCodeName { get; set; }
    public long? PositionCodeParentId { get; set; }
    public long? PositionId { get; set; }
}

public class UserWithPosition
{
    public long? EmployeeId { get; set; }
    public string EmployeeLocalName { get; set; }
    public string EmployeeName { get; set; }
    public string EmployeeErpId { get; set; }
    public PortalUserRole? EmployeeRole { get; set; }
    public bool? IsFieldAppuser { get; set; }
    public string PositionCodeId { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }
    public string PositionCodeName { get; set; }
    public long? PositionCodeParentId { get; set; }
    public long PositionId { get; set; }
    public EmployeeType UserType { get; set; }
    public long? RegionId { get; set; }
}

public class UserWithPositionFilter
{
    public long? EmployeeId { get; set; }
    public string EmployeeName { get; set; }
    public string EmployeeErpId { get; set; }
    public PortalUserRole? EmployeeRole { get; set; }
    public bool? IsFieldAppuser { get; set; }
    public List<PositionFilter> Positions { get; set; }
}
