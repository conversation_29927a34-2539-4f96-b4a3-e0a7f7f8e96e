﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Models;

public class ApiLogInfo
{
    public static ApiLogInfo GetInfo(ApiType apiType, AppConfigSettings appConfigSettings) => new(apiType, appConfigSettings);

    public string ConnectionString { get; private set; }
    public string Container { get; private set; }

    private readonly string _extension;

    public string GetRequestPath(string requestId) => requestId + _extension;

    private ApiLogInfo(ApiType apiType, AppConfigSettings appConfigSettings)
    {
        switch (apiType)
        {
            case ApiType.GTAppAPI:
            case ApiType.HCCB:
            case ApiType.LDMSApp:
            case ApiType.NsManagerApp:
            case ApiType.DMSIntegration:
            case ApiType.DMSExternal:
            case ApiType.NSExtApi:
                Container = "appapilog";
                _extension = ".json";
                ConnectionString = appConfigSettings.StorageConnectionString;
                break;

            case ApiType.ExtApi:
                Container = "external-api-logs";
                _extension = ".txt";
                ConnectionString = appConfigSettings.MasterStorageConnectionString;

                break;

            case ApiType.ManagerApi:
                Container = "manager-app-api-logs";
                _extension = ".txt";
                ConnectionString = appConfigSettings.MasterStorageConnectionString;
                break;

            case ApiType.Unknown:
            case ApiType.FAApi:
            default:
                Container = "fa-api-logs";
                _extension = ".txt";
                ConnectionString = appConfigSettings.MasterStorageConnectionString;
                break;
        }
    }
}
