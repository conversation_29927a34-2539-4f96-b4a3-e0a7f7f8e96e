﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface ITertiaryEntityRepository
{
    Task<TertiaryEntityTotal> GetTertiaryEntities(long companyId, PaginationFilter validFilter, List<long> regionIds = null);
    Task<RepositoryResponse> DeactivateTertiaryEntity(long companyId, long tertiaryEntityId);
    Task<RepositoryResponse> CreateTertiaryEntity(TertiaryEntityInput tertiaryEntity);
    Task<RepositoryResponse> UpdateTertiaryEntityAndMappings(TertiaryEntityInput tertiaryEntity);
    Task<TertiaryEntityInput> GetTertiaryEntityById(long companyId, long id);
    Task<List<TertiaryEntityInput>> GetAllTertiaryEntities(long companyId);
    Task<List<TertiaryEntityInput>> GetTertiaryEntitiesofOutletIds(long companyId, List<long> outletIds);
    Task<List<TertiaryEntityWithOutletId>> GetOutletIdsOfTertiaryEntities(long companyId, List<long> outletIds);
}
