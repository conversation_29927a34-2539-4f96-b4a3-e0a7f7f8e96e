﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadMasterCallDto
    {
        public long Id { get; set; }
        public string Title { get; set; }
        public long LeadId { get; set; }
        public string LeadName { get; set; }
        public long? LeadContactId { get; set; }
        public string LeadContactName { get; set; }
        public string Description { get; set; }
        public LMSCallType? CallType { get; set; }
        public LMSTaskStatus? Status { get; set; }
        public long AssignedTo { get; set; }
        public string AssignedToName { get; set; }
        public DateTime? DueDate { get; set; }
        public long? Duration { get; set; }
        public DateTime CreatedAt { get; set; }
        public long CreatedBy { get; set; }
                public string CreatedByName { get; set; }
        public DateTime? StartTime { get; set; }
        public long? PositionCode { get; set; }
    }
}
