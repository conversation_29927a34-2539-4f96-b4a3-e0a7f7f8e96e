﻿using FADashboard.Core.Models;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IBeatOMeterRepository
{
    Task<List<BeatOMeterFlat>> GetAllBeatOMeterRules(long companyId, bool showDeactive);
    Task<BeatOMeterById> GetBeatOMeterById(long id, long companyId);
    Task<RepositoryResponse> DeactivateBeatOMeterRule(long id, long companyId);
    Task<RepositoryResponse> CreateBeatOMeterRule(BeatOMeterById beatOMeterData, long companyId);
    Task<List<EntityMin>> GetAllNewBeatoMeterRulesWithinDateRange(long companyId, DateTime startDate, DateTime endDate);
    Task<List<BeatOMeterKpiBasedFlat>> GetAllKpiBasedBeatOMeterRules(long companyId, bool showDeactive);
    Task<RepositoryResponse> DeactivateKpiBasedBeatOMeterRule(long id, long companyId);
    Task<KpiBasedBeatOMeterById> GetKpiBasedBeatOMeterById(long id, long companyId);
    Task<RepositoryResponse> CreateKpiBasedBeatOMeterRule(KpiBasedBeatOMeterById beatOMeterData, long companyId);
}
