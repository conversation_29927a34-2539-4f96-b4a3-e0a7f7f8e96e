﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class TaskManagementTask : IAuditedEntity, IDeactivatable, ICompanyEntity
{
    public long Id { get; set; }
    public long? TaskManagementFocusAreaId { get; set; }
    public TaskEntityType? TaskEntityType { get; set; }
    public long? TaskEntityId { get; set; }
    public long? TaskTarget { get; set; }
    public long? Achievement { get; set; }
    public long? Sequence { get; set; }
    public TasksProductHierarchy? ProductHierarchyLevel { get; set; }

    [StringLength(256)] public string ProductHierarchyIds { get; set; }

    [StringLength(256)] public string ProductSuggestedQtyList { get; set; }
    [StringLength(256)] public string SoldProductQtyList { get; set; }

    public TaskCalculationType? CalculationMeasure { get; set; }

    [StringLength(64)] public string Description { get; set; }

    public bool IsDeactive { get; set; }
    public DateTime? DeactivatedAt { get; set; }
    public DateTime CreatedAt { get; set; }

    [StringLength(32)] public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public long? RewardId { get; set; }
    public int? RewardQuantity { get; set; }
    public long CompanyId { get; set; }
    public TaskManagementFocusArea TaskManagementFocusArea { get; set; }
}
