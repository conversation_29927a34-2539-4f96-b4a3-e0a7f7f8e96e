﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class ProductCategoryDivision
{
    public long CompanyId { get; private set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { set; get; }
    public long Id { get; set; }
    public bool IsDeactive { set; get; }
    public DateTime LastUpdatedAt { get; set; }
    public ProductHierarchy Level { get; set; }
    public string LevelName { get; set; }
    public string Name { get; set; }
    public string ErpId { get; set; }
    public int OrderPosition { set; get; }
    public long? ProductDivisionId { get; set; }
    public string ProductDivisionName { get; set; }
    public long? ProductPrimaryCategoryId { get; set; }
    public string ProductPrimaryCategoryName { get; set; }
    public long? ProductSecondaryCategoryId { get; set; }
    public string ProductSecondaryCategoryName { get; set; }
    public bool? IsAssorted { set; get; }
    public string StandardUnit { get; set; }
    public Guid? Image { get; set; }
    public string SuperUnit { get; set; }
    public string ImageString { get; set; }
}
