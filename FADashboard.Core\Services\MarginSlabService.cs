﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class MarginSlabService(
    IMarginSlabRepository marginSlabRepository,
    ICurrentUser currentUser
) : RepositoryResponse
{
    //To get all the Margin Slabs
    public async Task<List<MarginSlab>> GetMarginSlabs() => await marginSlabRepository.GetMarginSlabs(currentUser.CompanyId);

    //To get  Margin Slab by Id
    public async Task<MarginSlabModel> GetMarginSlabById(long id) => await marginSlabRepository.GetMarginSlabById(currentUser.CompanyId, id);

    //To create or update Margin Slab
    public async Task<RepositoryResponse> CreateUpdateOutletMarginSlab(MarginSlabModel marginSlabModel)
    {
        if (marginSlabModel.Id == 0)
        {
            return await marginSlabRepository.CreateOutletMarginSlab(marginSlabModel, currentUser.CompanyId);
        }

        return await marginSlabRepository.UpdateOutletMarginSlab(marginSlabModel, currentUser.CompanyId);
    }

    //To deactivate Margin Slab
    public async Task<RepositoryResponse> DeactivateOutletMarginSlab(long id) => await marginSlabRepository.DeactivateOutletMarginSlab(currentUser.CompanyId, id);
}
