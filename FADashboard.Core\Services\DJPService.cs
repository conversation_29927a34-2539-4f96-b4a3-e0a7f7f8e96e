﻿using System.Collections.Concurrent;
using System.Text.Json;
using Azure.Storage.Blobs;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;
using Library.StorageWriter.Reader_Writer;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;


namespace FADashboard.Core.Services;

public class DJPService : RepositoryResponse
{
    private const string ContainerName = "djp-container";
    private const string DJPSeqFolder = "djp-sequencing";
    private const string DJPIncFolder = "djp-incremental";
    private const int EstimationTimeHrs = 1;
    private readonly IDJPRepository djpRepository;
    private readonly ICurrentUser currentUser;
    private readonly AppConfigSettings appConfigSettings;
    private readonly FaiDataLakeBlobWriter faiDataLakeBlobWriter;
    private readonly ICompanySettingsRepository companySettingsRepository;

    public DJPService(
        IDJPRepository djpRepository,
        ICurrentUser currentUser,
        AppConfigSettings appConfigSettings,
        FaiDataLakeBlobWriter faiDataLakeBlobWriter,
        ICompanySettingsRepository companySettingsRepository)
    {
        this.djpRepository = djpRepository;
        this.currentUser = currentUser;
        this.appConfigSettings = appConfigSettings;
        this.faiDataLakeBlobWriter = faiDataLakeBlobWriter;
        this.companySettingsRepository = companySettingsRepository;
        ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
    }

    public async Task<List<DJPSequenceDetailDTO>> GetDJPSequenceDetails(CancellationToken ct = default)
    {
        var data = await djpRepository.GetDJPSequenceDetails(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        return data.Select(s => new DJPSequenceDetailDTO
        {
            Id = s.Id,
            InputFileName = s.InputFileName,
            InputFilePublicPath = faiDataLakeBlobWriter.GetPublicPath(ContainerName, $"{DJPSeqFolder}/{s.Id}/input.xlsx"),
            OutputFilePublicPath = faiDataLakeBlobWriter.GetPublicPath(ContainerName, $"{DJPSeqFolder}/{s.Id}/output.xlsx"),
            ExecutedAt = s.ExecutedAt,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            StatusRemark = s.StatusRemark,
            EmailId = s.EmailId,
            EstimatedTime = (s.Status is DJPStatus.Executed or DJPStatus.UploadCompleted) ? s.ExecutedAt?.AddMinutes(10).ToString("HH:mm , MMM dd") : DateTime.UtcNow.Add(offset) >= s.CreatedAt.Add(offset).AddHours(EstimationTimeHrs)
                                ? DateTime.UtcNow.Add(offset).AddMinutes(30).ToString("HH:mm , MMM dd")
                                : s.CreatedAt.Add(offset).AddHours(EstimationTimeHrs).ToString("HH:mm , MMM dd")
        }).OrderByDescending(s => s.Id).ToList();
    }

    public async Task<List<DJPIncrementalDetailDTO>> GetDJPIncrementalDetails(CancellationToken ct = default)
    {
        var data = await djpRepository.GetDJPIncrementalDetails(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        return data.Select(s => new DJPIncrementalDetailDTO
        {
            Id = s.Id,
            InputFileName = s.InputFileName,
            InputFilePublicPath = faiDataLakeBlobWriter.GetPublicPath(ContainerName, $"{DJPIncFolder}/{s.Id}/input.xlsx"),
            OutputFilePublicPath = faiDataLakeBlobWriter.GetPublicPath(ContainerName, $"{DJPIncFolder}/{s.Id}/output.xlsx"),
            ExecutedAt = s.ExecutedAt,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            StatusRemark = s.StatusRemark,
            EmailId = s.EmailId,
            EstimatedTime = (s.Status is DJPStatus.UploadCompleted) ? s.ExecutedAt?.AddMinutes(10).ToString("HH:mm , MMM dd") : DateTime.UtcNow.Add(offset) >= s.CreatedAt.Add(offset).AddHours(1)
                                ? DateTime.UtcNow.Add(offset).AddMinutes(30).ToString("HH:mm , MMM dd")
                                : s.CreatedAt.Add(offset).AddHours(1).ToString("HH:mm , MMM dd")
        }).OrderByDescending(s => s.Id).ToList();
    }

    public async Task<RepositoryResponse> CreateDJPSequence(IFormFile file, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        var response = await djpRepository.SaveDJPSequence(currentUser.LocalId,
            currentUser.CompanyId, currentUser.UserRole, file.FileName, currentUser.EmailId, DateTime.UtcNow.Add(offset), ct);

        if (response.Id != 0)
        {
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            await faiDataLakeBlobWriter.UploadFileAsync(ContainerName, file, $"{DJPSeqFolder}/{response.Id}/input.xlsx", contentType);
            var deployment = Environment.GetEnvironmentVariable("AppSettings__Deployment") ?? "beta";
            var queueHandler = new QueueHandler<dynamic>(deployment.Equals("manage", StringComparison.OrdinalIgnoreCase) ? QueueType.DJPSequenceQueue : QueueType.DJPSequenceBetaQueue, appConfigSettings.FaiDataLakeStorageConnectionString);
            var payload = new
            {
                Msg = $"{response.Id.Value}/input.xlsx",
                CompanyId = currentUser.CompanyId
            };
            await queueHandler.AddToQueue(payload);
            return response;
        }

        return response;
    }

    public async Task<RepositoryResponse> CreateDJPIncremental(IFormFile file, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        var response = await djpRepository.SaveDJPIncremental(currentUser.LocalId,
            currentUser.CompanyId, currentUser.UserRole, file.FileName, currentUser.EmailId, DateTime.UtcNow.Add(offset), ct);

        if (response.Id != 0)
        {
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            await faiDataLakeBlobWriter.UploadFileAsync(ContainerName, file, $"{DJPIncFolder}/{response.Id}/input.xlsx", contentType);
            var deployment = Environment.GetEnvironmentVariable("AppSettings__Deployment") ?? "beta";
            var queueHandler = new QueueHandler<dynamic>(deployment.Equals("manage", StringComparison.OrdinalIgnoreCase) ? QueueType.DJPIncrementalQueue : QueueType.DJPIncrementalBetaQueue, appConfigSettings.FaiDataLakeStorageConnectionString);
            var payload = new
            {
                Msg = $"{response.Id.Value}/input.xlsx",
                CompanyId = currentUser.CompanyId
            };
            await queueHandler.AddToQueue(payload);
            return response;
        }

        return response;
    }
    public async Task<List<string>> GetEmployeesForVisualization(long id, bool isIncremental, CancellationToken ct)
    {
        var fileName = isIncremental ? $"{DJPIncFolder}/{id}/employee_list.json" : $"{DJPSeqFolder}/{id}/employee_list.json";
        var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync(ContainerName, fileName, ct);
        using var reader = new StreamReader(stream);
        var jsonString = await reader.ReadToEndAsync(cancellationToken: ct);
        return JsonSerializer.Deserialize<List<string>>(jsonString);
    }

    public async Task<List<EmployeeLocation>> GetEmployeeLocations(long id, List<string> employeeIds, bool isIncremental, CancellationToken ct = default)
    {
        var result = new ConcurrentBag<EmployeeLocation>();
        var maxDegreeOfParallelism = 5;

        await Parallel.ForEachAsync(employeeIds, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism,
            CancellationToken = ct
        },
        async (employee, token) =>
        {
            var fileName = isIncremental ? $"{DJPIncFolder}/{id}/visualization/{employee}/employee.json" : $"{DJPSeqFolder}/{id}/visualization/{employee}/employee.json";
            var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync(ContainerName, fileName, token);
            using var reader = new StreamReader(stream);
            var jsonString = await reader.ReadToEndAsync(cancellationToken: token);
            var employeeLocation = JsonSerializer.Deserialize<EmployeeLocation>(jsonString);

            if (employeeLocation != null)
            {
                result.Add(employeeLocation);
            }
        });

        return result.ToList();
    }

    public async Task<List<EmployeeDayLocation>> GetEmployeeDayLocations(long id, string employeeId, bool isIncremental, CancellationToken ct = default)
    {
        var employeeDayLocations = new ConcurrentBag<EmployeeDayLocation>();
        var days = Enumerable.Range(0, 8).ToList();

        await Parallel.ForEachAsync(days, new ParallelOptions
        {
            MaxDegreeOfParallelism = 5,
            CancellationToken = ct
        },
        async (day, token) =>
        {
            var fileName = isIncremental ? $"{DJPIncFolder}/{id}/visualization/{employeeId}/{day}/day.json" : $"{DJPSeqFolder}/{id}/visualization/{employeeId}/{day}/day.json";
            var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync(ContainerName, fileName, ct);
            if(stream.Length != 0)
            {
                using var reader = new StreamReader(stream);
                var jsonString = await reader.ReadToEndAsync(cancellationToken: ct);

                var employeeDayLocation = JsonSerializer.Deserialize<EmployeeDayLocation>(jsonString);

                if (employeeDayLocation != null)
                {
                    employeeDayLocations.Add(employeeDayLocation);
                }
            }
        });

        return employeeDayLocations.OrderBy(s => s.Day).ToList();
    }

    public async Task<List<OutletLocation>> GetEmployeeDayOutlets(long id, string employeeId, int day, bool isIncremental, CancellationToken ct = default)
    {
        var fileName = isIncremental ? $"{DJPIncFolder}/{id}/visualization/{employeeId}/{day}/day_outlets.json" : $"{DJPSeqFolder}/{id}/visualization/{employeeId}/{day}/day_outlets.json";
        var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync(ContainerName, fileName, ct);
        using var reader = new StreamReader(stream);
        var jsonString = await reader.ReadToEndAsync(cancellationToken: ct);
        var outlets = JsonSerializer.Deserialize<List<OutletLocation>>(jsonString);
        return outlets;
    }

    public async Task<EmployeeOutletDaysResponse> GetEmployeeDayOutletsByDays(long id, string employeeId, int[] days, bool isIncremental, CancellationToken ct = default)
    {
        if (string.IsNullOrEmpty(employeeId) || days == null || days.Length == 0)
        {
            return new EmployeeOutletDaysResponse
            {
                DayWiseOutlets = [],
                AggregateStats = new DayStats()
            };
        }

        double totalDistance = 0;
        double accumulatedTime = 0;
        var totalOutlets = 0;
        var daysWithOutlets = 0;

        var dayWiseOutlets = new ConcurrentDictionary<string, DayOutlets>();

        await Parallel.ForEachAsync(days, new ParallelOptions
        {
            MaxDegreeOfParallelism = 5,
            CancellationToken = ct
        },
        async (day, token) =>
        {
            var dayOutlets = await GetEmployeeDayOutlets(id, employeeId, day, isIncremental, ct);

            if (dayOutlets.Count != 0)
            {
                var lastOutlet = dayOutlets.Last();
                var dayDistance = lastOutlet.CumulativeDistance;
                var dayTime = lastOutlet.CumulativeTime;
                var dayOutletCount = dayOutlets.Count;

                var dayStats = new DayStats
                {
                    TotalDistance = Math.Round(dayDistance, 2),
                    TotalTime = dayTime,
                    OutletCount = dayOutletCount,
                    AvgDistance = Math.Round(dayDistance, 2),
                    AvgTime = dayTime
                };

                dayWiseOutlets[day.ToString()] = new DayOutlets
                {
                    Outlets = dayOutlets,
                    Stats = dayStats
                };

                lock (dayWiseOutlets) // this locks the dict to update the values of shared variables
                {
                    totalDistance += dayDistance;
                    accumulatedTime += double.Parse(dayTime);
                    totalOutlets += dayOutletCount;
                    daysWithOutlets++;
                }
            }
            else
            {
                dayWiseOutlets[day.ToString()] = new DayOutlets
                {
                    Outlets = [],
                    Stats = new DayStats()
                };
            }
        });

        var aggregateStats = new DayStats
        {
            TotalDistance = Math.Round(totalDistance, 2),
            TotalTime = accumulatedTime.ToString(),
            OutletCount = totalOutlets,
            AvgDistance = daysWithOutlets > 0 ? Math.Round(totalDistance / daysWithOutlets, 2) : 0,
            AvgTime = daysWithOutlets > 0 ? (accumulatedTime / daysWithOutlets).ToString() : "0"
        };

        return new EmployeeOutletDaysResponse
        {
            DayWiseOutlets = dayWiseOutlets.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
            AggregateStats = aggregateStats
        };
    }

    public async Task<bool> IntegrateAutomaticDJP(long id,bool isIncremental, CancellationToken ct = default)
    {
        await djpRepository.UpdateDJPUploadStatus(id,isIncremental, cancellationToken: ct);
        var path = isIncremental ? $"{DJPIncFolder}/{id}/output.xlsx" : $"{DJPSeqFolder}/{id}/output.xlsx";
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        var startDate = DateTime.UtcNow.Add(offset);
        var daysToAdd = (5 - (int)startDate.DayOfWeek + 7) % 7;
        startDate = startDate.AddDays(daysToAdd);
        var dataToQueue = new RoutePlanIntegrationQueueDto { Id = id, IsManual = false,IsDJP = true, IsDJPIncremental = isIncremental, FilePath = path, CompanyId = currentUser.CompanyId, StartDate = startDate.Date };
        var queueHandler = new QueueHandler<RoutePlanIntegrationQueueDto>(QueueType.RouteIntegrationTriggeredQueue, appConfigSettings.FaiDataLakeStorageConnectionString);
        await queueHandler.AddToQueue(dataToQueue);
        return true;
    }

    public async Task<Stream> DownloadMissingErpIds(long id, bool isIncremental, CancellationToken ct = default)
    {
        const string prefixEmpEprId = "MissingEmpErpIds";
        const string prefixOutletErpId = "MissingOutletErpIds";
        var outletStreams = new List<Stream>();
        var empStreams = new List<Stream>();
        var suggestiveRouteBlobServiceClient = new BlobServiceClient(appConfigSettings.FaiDataLakeStorageConnectionString);
        var suggestiveRouteContainerClient = suggestiveRouteBlobServiceClient.GetBlobContainerClient("suggestive-route");
        var path = isIncremental ? $"missingErpIds/djp-incremental/{id}/split-files" : $"missingErpIds/djp-sequence/{id}/split-files";

        // List blobs under the specified path
        await foreach (var blobItem in suggestiveRouteContainerClient.GetBlobsAsync(prefix: path, cancellationToken: ct))
        {
            if (blobItem.Name.Contains(prefixEmpEprId))
            {
                var blobClient = suggestiveRouteContainerClient.GetBlobClient(blobItem.Name);
                var stream = new MemoryStream();
                await blobClient.DownloadToAsync(stream, ct);
                stream.Position = 0;
                empStreams.Add(stream);
            }
            else if (blobItem.Name.Contains(prefixOutletErpId))
            {
                var blobClient = suggestiveRouteContainerClient.GetBlobClient(blobItem.Name);
                var stream = new MemoryStream();
                await blobClient.DownloadToAsync(stream, ct);
                stream.Position = 0;
                outletStreams.Add(stream);
            }
        }
        if (empStreams.Count == 0 && outletStreams.Count == 0)
        {
            var memoryStream = new MemoryStream();

            using (var package = new ExcelPackage(memoryStream))
            {
                var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                worksheet.Cells["A1"].Value = "No missing ERP IDs present.";

                package.Save();
            }

            memoryStream.Position = 0;
            return memoryStream;
        }
        else
        {
            var mergedStream = await MergeExcelFiles(empStreams, outletStreams);
            mergedStream.Position = 0;

            // Dispose all downloaded streams
            foreach (var stream in empStreams)
                await stream.DisposeAsync();
            foreach (var stream in outletStreams)
                await stream.DisposeAsync();

            return mergedStream;
        }
    }

    private static async Task<MemoryStream> MergeExcelFiles(List<Stream> empStreams, List<Stream> outletStreams)
    {
        var mergedStream = new MemoryStream();
        using var package = new ExcelPackage(mergedStream);

        // Merge Employee ERP IDs
        if (empStreams.Count > 0)
        {
            var employeeWorksheet = package.Workbook.Worksheets.Add("Employee ERP IDs");
            var row = 1;
            var isFirstStream = true;

            foreach (var stream in empStreams)
            {
                using var tempPackage = new ExcelPackage(stream);
                var tempWorksheet = tempPackage.Workbook.Worksheets[0];
                var tempRowCount = tempWorksheet.Dimension?.Rows ?? 0;
                var tempColumnCount = tempWorksheet.Dimension?.Columns ?? 0;

                var startRow = isFirstStream ? 1 : 2; // Skip header for subsequent streams
                if (tempRowCount == 0)
                    continue;

                for (var i = startRow; i <= tempRowCount; i++)
                {
                    for (var j = 1; j <= tempColumnCount; j++)
                    {
                        employeeWorksheet.Cells[row, j].Value = tempWorksheet.Cells[i, j].Value;
                    }
                    row++;
                }
                isFirstStream = false;
            }
        }

        // Merge Outlet ERP IDs
        if (outletStreams.Count > 0)
        {
            var outletWorksheet = package.Workbook.Worksheets.Add("Outlet ERP IDs");
            var row = 1;
            var isFirstStream = true;

            foreach (var stream in outletStreams)
            {
                using var tempPackage = new ExcelPackage(stream);
                var tempWorksheet = tempPackage.Workbook.Worksheets[0];
                var tempRowCount = tempWorksheet.Dimension?.Rows ?? 0;
                var tempColumnCount = tempWorksheet.Dimension?.Columns ?? 0;

                var startRow = isFirstStream ? 1 : 2; // Skip header for subsequent streams
                if (tempRowCount == 0)
                    continue;

                for (var i = startRow; i <= tempRowCount; i++)
                {
                    for (var j = 1; j <= tempColumnCount; j++)
                    {
                        outletWorksheet.Cells[row, j].Value = tempWorksheet.Cells[i, j].Value;
                    }
                    row++;
                }
                isFirstStream = false;
            }
        }

        await package.SaveAsync();
        mergedStream.Position = 0;
        return mergedStream;
    }
}
