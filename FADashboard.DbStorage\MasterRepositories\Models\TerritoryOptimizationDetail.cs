﻿using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class TerritoryOptimizationDetail
{
    public long Id { get; set; }

    public long UserId { get; set; }

    public long CompanyId { get; set; }

    public PortalUserRole UserRole { get; set; }

    public string InputFileName { get; set; }

    public DateTime CreatedAt { get; set; }

    public TerritoryOptimizationStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string Email { get; set; }
    public string InputConstraints { get; set; }
    public long ActionTakenBy { get; set; }
}
