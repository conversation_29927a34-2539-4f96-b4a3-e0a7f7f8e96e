﻿using Azure.Storage.Blobs;
using FADashboard.Core.Interfaces;

namespace FADashboard.Core.Helper;

//TODO shift methods and use library
public class UploadHandler(string connectionString) : IUploadHandler
{
    private readonly BlobServiceClient _service = new(connectionString);

    public async Task<bool> DeleteAsync(string containerName, string name)
    {
        var blobClient = _service.GetBlobContainerClient(containerName);
        var blob = blobClient.GetBlobClient(name);
        return await blob.DeleteIfExistsAsync().ConfigureAwait(true);
    }

    public async Task<byte[]> DownloadAsync(string containerName, string name)
    {
        var blobClient = _service.GetBlobContainerClient(containerName);
        var blob = blobClient.GetBlobClient(name);

        using (var stream = new MemoryStream())
        {
            await blob.DownloadToAsync(stream).ConfigureAwait(false);
            return stream.ToArray();
        }
    }
}
