using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSLeadService
    {
        Task<LMSLeadDto> GetByIdAsync(long lmsLeadId);
        Task<IEnumerable<LMSLeadDto>> GetAllAsync();
        Task<IEnumerable<LMSLeadDto>> GetAllByCompanyIdAsync(long companyId);
        Task<IEnumerable<LMSLeadDto>> GetByLMSAccountIdAsync(long lmsAccountId);
        Task<PagedResult<LMSLeadDto>> GetByLMSAccountIdAsync(long lmsAccountId, LMSLeadQueryParameters queryParameters);
        Task<IEnumerable<LMSLeadDto>> GetByAssignedToAsync(long assignedTo);
        Task<LMSLeadDto> CreateAsync(LMSLeadCreateInput leadInput, long createdBy);
        Task<LMSLeadDto> UpdateAsync(long leadId, LMSLeadUpdateInput leadInput, long updatedBy);
        Task<bool> DeleteAsync(long lmsLeadId, long updatedBy);
        Task<PagedResult<LMSLeadDto>> GetLeadsAsync(long companyId, LMSLeadQueryParameters queryParameters);
    }
}
