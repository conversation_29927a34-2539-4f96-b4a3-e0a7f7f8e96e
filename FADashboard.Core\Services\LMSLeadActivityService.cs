﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.Enums;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services
{
    public class LMSLeadActivityService(ILMSLeadActivityRepository activityRepository) : ILMSLeadActivityService
    {
        public async Task CreateBulkActivitiesAsync(List<LMSLeadActivityDTO> activities)
        {
            if (activities == null || activities.Count == 0)
            {
                return;
            }

            await activityRepository.AddBulkAsync(activities);
        }

        public async Task<Dictionary<string, List<LMSLeadActivityTimelineDto>>> GetLeadTimelineAsync(long leadId, LMSLeadActivityQueryParameters queryParameters)
        {
            var activities = await activityRepository.GetLeadTimelineAsync(leadId, queryParameters);

            var groupedActivities = activities
                .GroupBy(a => a.CreatedAt.ToString("MMMM yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            return groupedActivities;
        }

        public async Task<PagedResult<LMSLeadNoteDto>> GetLeadNotesAsync(long leadId, LMSLeadNoteQueryParameters queryParameters) =>
            await activityRepository.GetLeadNotesAsync(leadId, queryParameters);

        public async Task<PagedResult<LMSLeadCallDto>> GetLeadCallsAsync(long leadId, LMSLeadCallQueryParameters queryParameters) =>
            await activityRepository.GetLeadCallsAsync(leadId, queryParameters);

        public async Task<LMSLeadTaskDto> CreateLeadTaskAsync(CreateLMSLeadTaskDto taskDto, long companyId, long createdBy)
        {
            if (taskDto.Status != LMSTaskStatus.NotStarted)
            {
                throw new ArgumentException("On create, task status must be 'Not Started'.");
            }

            if (taskDto.DueDate.Date < DateTime.UtcNow.Date)
            {
                throw new ArgumentException("Due date cannot be in the past.");
            }

            var createdTask = await activityRepository.CreateLeadTaskAsync(taskDto, companyId, createdBy);

            return createdTask;
        }

        public async Task<LMSLeadMeetingDto> CreateLeadMeetingAsync(CreateLMSLeadMeetingDto meetingDto, long companyId, long createdBy)
        {
            if (meetingDto.Status != LMSTaskStatus.Scheduled)
            {
                throw new ArgumentException("On create, meeting status must be 'Scheduled'.");
            }

            if (meetingDto.MeetingEndTime <= meetingDto.MeetingStartTime)
            {
                throw new ArgumentException("'To' date and time must be after 'From' date and time.");
            }

            if (meetingDto.MeetingEndTime.Date != meetingDto.MeetingStartTime.Date)
            {
                throw new ArgumentException("'To' date must be the same as 'From' date.");
            }

            if (meetingDto.MeetingStartTime < DateTime.UtcNow)
            {
                throw new ArgumentException("'From' date and time cannot be in the past.");
            }

            return await activityRepository.CreateLeadMeetingAsync(meetingDto, companyId, createdBy);
        }

        public async Task<LMSLeadTaskDto> GetLeadTaskDetailsAsync(long taskId)
        {
            var task = await activityRepository.GetLeadTaskDetailsAsync(taskId);
            //if (task != null)
            //{
            //    var leadName = await activityRepository.GetLeadName(task.LeadId);
            //    task.LeadName = leadName;
            //}
            return task;
        }

        public async Task<LMSLeadMeetingDto> GetLeadMeetingDetailsAsync(long meetingId)
        {
            var meeting = await activityRepository.GetLeadMeetingDetailsAsync(meetingId);
            //if (meeting != null)
            //{
            //    var leadName = await activityRepository.GetLeadName(meeting.LeadId);
            //    meeting.LeadName = leadName;
            //}
            return meeting;
        }

        public async Task<LMSLeadCallDto> GetLeadCallDetailsAsync(long callId)
        {
            var call = await activityRepository.GetLeadCallDetailsAsync(callId);
            //if (call != null)
            //{
            //    var leadName = await activityRepository.GetLeadName(call.LeadId);
            //    call.LeadName = leadName;

            //    if (call.ContactId.HasValue)
            //    {
            //        var contactName = await activityRepository.GetContactName(call.ContactId.Value);
            //        call.ContactName = contactName;
            //    }
            //}
            return call;
        }

        public async Task<LMSLeadCallDto> CreateLeadCallAsync(CreateLMSLeadCallDto callDto, long companyId, long createdBy)
        {
            if (callDto.Status != LMSTaskStatus.Scheduled)
            {
                throw new ArgumentException("On create, call status must be 'Scheduled'.");
            }

            if (callDto.CallDuration.HasValue)
            {
                throw new ArgumentException("Call duration cannot be set when scheduling a call.");
            }

            if (callDto.StartTime < DateTime.UtcNow)
            {
                throw new ArgumentException("Start time cannot be in the past.");
            }

            return await activityRepository.CreateLeadCallAsync(callDto, companyId, createdBy);
        }

        public async Task<LMSLeadTaskDto> UpdateLeadTaskAsync(long taskId, UpdateLMSLeadTaskDto taskDto, long companyId, long updatedBy)
        {
            var existingTask = await activityRepository.GetTaskByIdAsync(taskId);
            if (existingTask == null)
            {
                throw new ArgumentException("Task not found.");
            }

            if (existingTask.Status == LMSTaskStatus.Completed || existingTask.Status == LMSTaskStatus.Cancelled)
            {
                throw new ArgumentException("A task cannot be updated once its status is 'Completed' or 'Cancelled'.");
            }

            if (taskDto.DueDate.Date < existingTask.CreatedAt.Date)
            {
                throw new ArgumentException("Due date cannot be before the task creation date.");
            }

            return await activityRepository.UpdateLeadTaskAsync(taskId, taskDto, companyId, updatedBy);
        }

        public async Task<LMSLeadMeetingDto> UpdateLeadMeetingAsync(long meetingId, UpdateLMSLeadMeetingDto meetingDto, long companyId, long updatedBy)
        {
            var existingMeeting = await activityRepository.GetMeetingByIdAsync(meetingId);

            if (existingMeeting == null)
            {
                throw new KeyNotFoundException("Meeting not found.");
            }

            if (existingMeeting.Status == LMSTaskStatus.Completed || existingMeeting.Status == LMSTaskStatus.Cancelled)
            {
                throw new ArgumentException("Cannot update a meeting that is already completed or cancelled.");
            }

            if (meetingDto.MeetingStartTime < existingMeeting.CreatedAt)
            {
                throw new ArgumentException("Meeting start time cannot be before the creation date.");
            }

            if (meetingDto.MeetingEndTime <= meetingDto.MeetingStartTime)
            {
                throw new ArgumentException("Meeting end time must be after the start time.");
            }

            return await activityRepository.UpdateLeadMeetingAsync(meetingId, meetingDto, companyId, updatedBy);
        }

        public async Task<LMSLeadCallDto> UpdateLeadCallAsync(long callId, UpdateLMSLeadCallDto callDto, long companyId, long updatedBy)
        {
            var existingCall = await activityRepository.GetCallByIdAsync(callId);

            if (existingCall == null)
            {
                throw new KeyNotFoundException("Call not found.");
            }

            if (existingCall.Status == LMSTaskStatus.Completed || existingCall.Status == LMSTaskStatus.Cancelled)
            {
                throw new ArgumentException("Cannot update a call that is already completed or cancelled.");
            }

            if (callDto.Status == LMSTaskStatus.Completed && !callDto.DurationInMinutes.HasValue)
            {
                throw new ArgumentException("Duration must be provided when completing a call.");
            }

            if (callDto.Status != LMSTaskStatus.Completed && callDto.DurationInMinutes.HasValue)
            {
                throw new ArgumentException("Duration can only be set when a call is completed.");
            }

            return await activityRepository.UpdateLeadCallAsync(callId, callDto, companyId, updatedBy);
        }

        public async Task DeleteLeadActivityAsync(long activityId, long companyId, long deletedBy)
        {
            var existingTask = await activityRepository.GetActivityByIdAsync(activityId);

            if (existingTask == null)
            {
                throw new KeyNotFoundException("Task not found.");
            }

            if (existingTask.Status != LMSTaskStatus.NotStarted)
            {
                throw new ArgumentException("Only tasks with status 'Not Started' can be deleted.");
            }

            await activityRepository.DeleteLeadActivityAsync(activityId, companyId, deletedBy);
        }

        public async Task<PagedResult<LMSLeadTaskDto>> GetLeadTasksAsync(long leadId, LMSLeadTaskQueryParameters queryParameters) =>
            await activityRepository.GetLeadTasksAsync(leadId, queryParameters);

        public async Task<PagedResult<LMSLeadMeetingDto>> GetLeadMeetingsAsync(long leadId, LMSLeadMeetingQueryParameters queryParameters) =>
            await activityRepository.GetLeadMeetingsAsync(leadId, queryParameters);

        public async Task<PagedResult<LMSLeadMasterTaskDto>> GetLeadMasterTasksAsync(long leadId, LMSLeadTaskQueryParameters queryParameters) =>
            await activityRepository.GetLeadMasterTasksAsync(leadId, queryParameters);

        public async Task<PagedResult<LMSLeadMasterMeetingDto>> GetLeadMasterMeetingsAsync(long leadId, LMSLeadMeetingQueryParameters queryParameters) =>
            await activityRepository.GetLeadMasterMeetingsAsync(leadId, queryParameters);

        public async Task<PagedResult<LMSLeadMasterCallDto>> GetLeadMasterCallsAsync(long leadId, LMSLeadCallQueryParameters queryParameters) =>
            await activityRepository.GetLeadMasterCallsAsync(leadId, queryParameters);

        public async Task<LMSLeadNoteDto> CreateLeadNoteAsync(CreateLMSLeadNoteDto noteDto, long companyId, long createdBy) =>
            await activityRepository.CreateLeadNoteAsync(noteDto, companyId, createdBy);
    }
}
