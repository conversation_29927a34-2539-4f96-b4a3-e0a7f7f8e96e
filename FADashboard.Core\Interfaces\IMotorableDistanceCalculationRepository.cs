﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IMotorableDistanceCalculationRepository
{
    Task<MotorableDistanceCalculationAPIsModel> GetMotorableDistanceCalculationAPIs(long companyId);

    Task<RepositoryResponse> CreateMotorableDistanceCalculationApis(MotorableDistanceCalculationAPIsModel motorableDistanceCalculationApi, long companyId);

    Task<RepositoryResponse> UpdateMotorableDistanceCalculationApis(MotorableDistanceCalculationAPIsModel motorableDistanceCalculationApi);
}
