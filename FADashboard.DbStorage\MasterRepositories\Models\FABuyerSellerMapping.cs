﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class FABuyerSellerMapping : ICreatedEntity, IDeletable, IUpdatableEntity
{
    public FABuyerSellerMapping()
    {
        Deleted = false;
    }
    public FABuyerSellerMapping(long companyId) : this()
    {
        CompanyId = companyId;
    }
    public long Id { get; set; }
    public long SellerId { get; set; }
    public long BuyerId { get; set; }
    public SellerType SellerType { get; set; }
    public BuyerType BuyerType { get; set; }
    public long CompanyId { get; set; }
    public bool Deleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool IsIDTBuyer { get; set; }
}
