﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FAProductPrimaryCategory")]
public class ProductPrimaryCategory : IAuditedEntity, IDeletable, IERPEntity
{
    public virtual Company Company { get; private set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { set; get; }
    public long Id { get; set; }
    public bool IsDeactive { set; get; }
    public DateTime LastUpdatedAt { get; set; }
    public string Name { get; set; }
    [Column("ERPId")]
    public string ErpId { get; set; }
    public int OrderPosition { set; get; }
    public virtual ProductDivision ProductDivision { get; set; }
    public long ProductDivisionId { get; set; }
    public virtual ICollection<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }
    public string StandardUnit { get; set; }
    public double StandardUnitConversionFactor { get; set; }
    public string SuperUnit { get; set; }
    public double SuperUnitConversionFactor { get; set; }
    public Guid? Image { get; set; } 
}
