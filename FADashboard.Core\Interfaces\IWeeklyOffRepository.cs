﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IWeeklyOffRepository
{
    Task<List<WeeklyOffUploadDetail>> GetWeeklyOffDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<RepositoryResponse> SaveWeeklyOffDetails(long userId, long companyId, PortalUserRole userRole, string fileName, CancellationToken ct = default);
}
