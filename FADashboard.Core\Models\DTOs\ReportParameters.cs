﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs;

public class ReportParameters
{
    public ReportParameters(ReportSubscription subscription)
    {
        ReportType = subscription.Report.ReportType;
    }

    public ReportParameters(EnumForReportAssembly reportType)
    {
        ReportType = reportType;
    }

    public bool IsNSReport
    {
        get
        {
            switch (ReportType)
            {
                case EnumForReportAssembly.AttendanceReportFromReportPerspective:
                case EnumForReportAssembly.AttendanceNormBasedReport:
                case EnumForReportAssembly.UserJourneyLocationReport:
                case EnumForReportAssembly.EmployeePerformanceReportFromReportsPerspective:
                case EnumForReportAssembly.FlatSalesFromReportPerspective:
                case EnumForReportAssembly.FlatSalesFromReportPerspectiveIsProductive:
                case EnumForReportAssembly.FlatSalesFromReportPerspectiveCreatedInPartsMechanism:
                case EnumForReportAssembly.TransactionDumpReport:
                case EnumForReportAssembly.GulfOutletApprovalStagesReport:
                case EnumForReportAssembly.PrimaryOrderValidationReport:
                case EnumForReportAssembly.SecondaryOrderValidationReportFromReportPerspective:
                case EnumForReportAssembly.SummarySheetFromPerspective:
                case EnumForReportAssembly.SummarySheetSCWiseFromPerspective:
                case EnumForReportAssembly.SummarySheetPDWiseFromPerspective:
                case EnumForReportAssembly.SummarySheetJockeyFromPerspective:
                case EnumForReportAssembly.SummarySheetSCWiseFromPerspectiveUsingNewIds:
                case EnumForReportAssembly.PJPAdherenceFromReportPerspective:
                case EnumForReportAssembly.PJPAdherenceFromReportPerspectiveAllSeg:
                case EnumForReportAssembly.SummaryReportVersion2:
                case EnumForReportAssembly.MustSellReportFromReportPerspective:
                case EnumForReportAssembly.KRAAdherenceReportFromReportPerspective:
                case EnumForReportAssembly.CategoryAnalysisReportFromPerspective:
                case EnumForReportAssembly.MonthOnMonthCategoryAnalysisFromPerspective:
                case EnumForReportAssembly.MonthlyDistributerStockReport:
                case EnumForReportAssembly.LiveDistributerStockReport:
                case EnumForReportAssembly.DateWiseDistributerStockReport:
                case EnumForReportAssembly.PrimaryOrdersReport:
                case EnumForReportAssembly.UserLoginActivity:
                case EnumForReportAssembly.PrimarySalesReport_FromMaster:
                case EnumForReportAssembly.NewOutletDetailedReport:
                case EnumForReportAssembly.NewOutletDetailedHybridReport:
                case EnumForReportAssembly.NewSubDDetailedReport:
                case EnumForReportAssembly.EmployeeProductivityReportFromPerspective:
                case EnumForReportAssembly.TourPlanAdherenceReportFromPerspective:
                case EnumForReportAssembly.TourPlanSubmission:
                case EnumForReportAssembly.LDMSClaimReports:
                case EnumForReportAssembly.LDMSFlatSales:
                case EnumForReportAssembly.LDMSInventory:
                case EnumForReportAssembly.BVCRFromPerspective:
                case EnumForReportAssembly.TimeLineReport:
                case EnumForReportAssembly.ImageAuditingReportTotalScore:
                case EnumForReportAssembly.ImageRecognitionReport:
                case EnumForReportAssembly.DailyLocationReportV4:
                case EnumForReportAssembly.FlexibleReport:
                case EnumForReportAssembly.OpeningClosingStockReportV4:
                case EnumForReportAssembly.FlatSalesLive:
                case EnumForReportAssembly.SummarySheetManagerWorkingFromPerspective:
                case EnumForReportAssembly.RoutePlanReport:
                case EnumForReportAssembly.VisitDumpReportFromPerspective:
                case EnumForReportAssembly.MTEmployeeWiseTargetVsAch:
                case EnumForReportAssembly.DailyStatsReportLiveV4:
                case EnumForReportAssembly.TransactionDumpReportLive:
                case EnumForReportAssembly.OutletReachReportV4:
                case EnumForReportAssembly.OutletWiseSalesReportV4:
                case EnumForReportAssembly.FlatSurveyReportFromV4:
                case EnumForReportAssembly.DeadOutletReport:
                case EnumForReportAssembly.FocusProductReportFromV4:
                case EnumForReportAssembly.DailyDistributorSalesReport:
                case EnumForReportAssembly.MTAttendanceReport:
                case EnumForReportAssembly.InvoiceReport:
                case EnumForReportAssembly.InvoiceReportForAccountManager:
                case EnumForReportAssembly.NoSalesReasonReport:
                case EnumForReportAssembly.OutletCategoryWiseSalesReport:
                case EnumForReportAssembly.EmployeeWiseTargetVsAch:
                case EnumForReportAssembly.PrimaryTargetVSAchievement:
                case EnumForReportAssembly.MTEmployeeTargetVsAch:
                case EnumForReportAssembly.LivePrimaryOrderReport:
                case EnumForReportAssembly.PaymentCollectionReport:
                case EnumForReportAssembly.PaymentCollectionReportVanSales:
                case EnumForReportAssembly.EmployeeDistributorPerformanceReport:
                case EnumForReportAssembly.PositionDistributorPerformanceReport:
                case EnumForReportAssembly.BattleGroundReport:
                case EnumForReportAssembly.FABattlegroundKPIAchievmentReport:
                case EnumForReportAssembly.MTSummaryReport:
                case EnumForReportAssembly.MTPerformanceAnalysisReport:
                case EnumForReportAssembly.FlatSchemePerformanceReport:
                case EnumForReportAssembly.FlatSchemePerformanceV4Report:
                case EnumForReportAssembly.OutletTargetReportV4:
                case EnumForReportAssembly.L3MEmpAnalysis:
                case EnumForReportAssembly.VanStockReport:
                case EnumForReportAssembly.TADAExpenseDump:
                case EnumForReportAssembly.OpeningClosingReportV4:
                case EnumForReportAssembly.TertiaryOfftake:
                case EnumForReportAssembly.TADAMonthlyExpense:
                case EnumForReportAssembly.TADAApprovedPendingExpenseDump:
                case EnumForReportAssembly.TADAExpenseDumpMonthly:
                case EnumForReportAssembly.NewTADAApprovedExpenseDump:
                case EnumForReportAssembly.DMSOneByOneReport:
                case EnumForReportAssembly.DMSOneByTwoReport:
                case EnumForReportAssembly.DMSInvoiceReport:
                case EnumForReportAssembly.DSMWiseTargetVsAch:
                case EnumForReportAssembly.StockLedgerReport:
                case EnumForReportAssembly.SecondaryOrderDumpWithLivePosition:
                case EnumForReportAssembly.DistributorSummaryReport:
                case EnumForReportAssembly.SecondaryOrderVsInvoiceDump:
                case EnumForReportAssembly.SecondaryOrderVsInvoiceSummary:
                case EnumForReportAssembly.FAEngageReport:
                case EnumForReportAssembly.OutletWiseSchemeUtiLizationReport:
                case EnumForReportAssembly.AssetManagementReport:
                case EnumForReportAssembly.OutletDumpGeoHierarchyV4:
                case EnumForReportAssembly.QPSSchemeReport:
                case EnumForReportAssembly.CurrentOpeningStockReport:
                case EnumForReportAssembly.ProductRecommendationDumpReport:
                case EnumForReportAssembly.GILSecondarySalesDump:
                case EnumForReportAssembly.GILMTDReport:
                case EnumForReportAssembly.MasterDataPositionDumpOutletCountReportV4:
                case EnumForReportAssembly.BTEmpProductivityReport:
                case EnumForReportAssembly.GamificationReport:
                case EnumForReportAssembly.ProductMasterReport:
                case EnumForReportAssembly.DailySalesTrackerCategoryWise:
                case EnumForReportAssembly.GILRePrimarySalesDump:
                case EnumForReportAssembly.GrowthPlanCustomReport:
                case EnumForReportAssembly.NewBeatometerReport:
                case EnumForReportAssembly.EmpRouteOutletDumpReport:
                case EnumForReportAssembly.OfftakeReportTHS:
                case EnumForReportAssembly.FinalDemandCustomReport:
                case EnumForReportAssembly.POCER:
                case EnumForReportAssembly.EOCER:
                case EnumForReportAssembly.LocationBulkUploadDumpV4:
                case EnumForReportAssembly.AttendanceOetkarReport:
                case EnumForReportAssembly.HccbTaskDumpReport:
                case EnumForReportAssembly.QPSSchemeUtilizationReport:
                case EnumForReportAssembly.RetailerStockReport:
                case EnumForReportAssembly.POSMReport:
                case EnumForReportAssembly.EOEReport:
                case EnumForReportAssembly.OcrReport:
                case EnumForReportAssembly.IRTransactionReport:
                    return true;
                default:
                    return false;
            }
        }
    }

    public bool IsMTReport
    {
        get
        {
            switch (ReportType)
            {
                case EnumForReportAssembly.MTLiveAttendance:
                case EnumForReportAssembly.CampaignTaskResponseDumpReport:
                //case EnumForReportAssembly.OpenCampaignResponseReport: //TODO : Update Common Enum for these new Reports
                //case EnumForReportAssembly.CompetitorSurveyReportVMT:
                case EnumForReportAssembly.MTShelfShareReport:
                case EnumForReportAssembly.MTCampaignStockReport:
                case EnumForReportAssembly.ModernTradeStoreStockVMT:
                case EnumForReportAssembly.MTCalculatedClosingReportVMT:
                case EnumForReportAssembly.ModernTradeClosingStockReportVMT:
                case EnumForReportAssembly.ISRSummaryReportVMT:
                case EnumForReportAssembly.StockInwardReportVMT:
                case EnumForReportAssembly.StockTertiaryReportVMT:
                case EnumForReportAssembly.MTPOandDispatchReportVMT:
                case EnumForReportAssembly.MTFocusProductTargetReportFromV4:
                case EnumForReportAssembly.MTISRSummaryReport:
                case EnumForReportAssembly.MTCampaignWiseIrReport:
                case EnumForReportAssembly.OpenCampaignResponseReport:
                case EnumForReportAssembly.MTSchemeUtilizationReport:
                case EnumForReportAssembly.MTOutletWiseTargetVsAch:
                    return true;

                default:
                    return false;
            }
        }
    }

    public bool IsHeavyReport
    {
        get
        {
            switch (ReportType)
            {
                case EnumForReportAssembly.POCER:
                case EnumForReportAssembly.OCERFromReport:
                    return true;

                default:
                    return false;
            }
        }
    }

    public bool IsMasterReport
    {
        get
        {
            switch (ReportType)
            {
                case EnumForReportAssembly.UserManagementNewDashboardReport:
                    return true;
                case EnumForReportAssembly.DailyStatsReportLiveV4:
                    return true;

                default:
                    return false;
            }
        }
    }

    public EnumForReportAssembly ReportType { get; set; }
}
