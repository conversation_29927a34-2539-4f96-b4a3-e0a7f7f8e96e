﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class AssetAgreement
{
    public long Id { get; set; }
    public long LocationId { get; set; }
    public long EmployeeId { get; set; }
    public long? ApprovedBy { get; set; }
    public DateTime? ApprovedOn { get; set; }
    public PortalUserRole? ApprovalRole { get; set; }
    public long FAEventId { set; get; }
    public Status Status { get; set; }
}
