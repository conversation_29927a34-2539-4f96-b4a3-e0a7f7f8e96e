﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class CreateLMSLeadMeetingDto
    {
        [Required]
        public string Title { get; set; }

        [Required]
        public long AssignedTo { get; set; }

        [Required]
        public long PositionCode { get; set; }

        [Required]
        public long LeadId { get; set; }

        [Required]
        public LMSMeetingLocation? MeetingLocation { get; set; }

        [Required]
        public DateTime MeetingStartTime { get; set; }

        [Required]
        public DateTime MeetingEndTime { get; set; }

        [Required]
        public LMSTaskStatus Status { get; set; }

        public string Description { get; set; }

        public List<string> Attachment { get; set; }
    }
}
