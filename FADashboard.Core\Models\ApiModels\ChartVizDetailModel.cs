﻿using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.Core.Models.ApiModels;

public class ChartVizDetailModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public long ViewId { get; set; }
    public ChartType ChartType { get; set; }
    public SortingType SortingType { get; set; }
    public List<string> Dimension { get; set; }
    public List<string> Measure { get; set; }
    public ViewPerspective ChartPerspective { get; set; }
    public bool IsPreview { get; set; }
    public int DrillDownLevel { get; set; }
    public List<ChartColorCoding> ColorCodingMeasures { get; set; }
    public bool IsDrillDownApplicable { get; set; }
    public string FilterName { get; set; }
    public string BaseFilterName { get; set; }
    public long BaseFilterId { get; set; }
    public long FilterId { get; set; }
    public string DrillDownDimension { get; set; }
    public string DrillDownDimensionDisplay { get; set; }
    public ComparisonTimePeriod ComparisonTimePeriod { get; set; }
    public AggregationType AggregationType { get; set; }
    public ChartSize ChartSize { get; set; }
    public long QueryViewId { get; set; }
    public ComparisonType ComparisonType { get; set; }
    public bool Deleted { get; set; }

    public long CompanyId { get; set; }
    public int PositionCodeLevel { get; set; }
    public IEnumerable<long> PositionIds { get; set; }
    public bool IsNewDashboard { get; set; }

    //   public List<ChartColorCoding> ColorCoding { get; set; }


    public List<string> XAxis
    {
        get => XaxisStr == null ? [] : JsonConvert.DeserializeObject<List<string>>(XaxisStr);
        set => XaxisStr = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    public string XaxisStr { get; set; }

    public List<string> YAxis
    {
        get => YaxisStr == null ? [] : JsonConvert.DeserializeObject<List<string>>(YaxisStr);
        set => YaxisStr = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    public string YaxisStr { get; set; }
}

public class ChartColorCoding
{
    public string Measure { get; set; }
    public double Defaulters { get; set; }
    public double Performers { get; set; }
}
