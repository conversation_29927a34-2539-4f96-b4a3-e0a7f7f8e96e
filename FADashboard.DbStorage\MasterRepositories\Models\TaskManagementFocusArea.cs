﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class TaskManagementFocusArea : IAuditedEntity, ICompanyEntity, IDeactivatable
{
    public long Id { get; set; }

    [StringLength(64)] public string Name { get; set; }
    public TaskManagementFocusAreaType? Type { get; set; }
    public long CompanyId { get; set; }
    public long? EntityId { get; set; }
    public bool? IsAutoSequenceRequired { get; set; }
    public int? SequencingType { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public ProcessingType ProcessingType { get; set; }
    public long CohortId { get; set; }
    public virtual Cohort Cohort { get; set; }
    [ForeignKey("PerfectStoreRule")]
    [Column("ParentId")] public long PerfectStoreRuleId { get; set; }
    public ParentType ParentType { get; set; }
    public TaskCalculationCriteria? TaskCalculationCriteria { get; set; }
    public int? CriteriaValue { get; set; }
    public TaskManagementUserFocusArea TaskManagementUserFocusArea { get; set; }
    public ICollection<TaskManagementTask> TaskManagementTasks { get; set; }
    public List<long?> EntityIds { get; set; }
}
