﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class CustomReport
{
    public DateTime CreatedAt { get; set; }
    public List<CustomReportItem> CustomReportItems { get; set; }
    public DateFiltersType DateFiltersType { get; set; }
    public FilterType FilterType { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsDownloadable { get; set; }
    public bool IsQueueable { get; set; }
    public bool IsQueueableDownload { get; set; }
    public bool IsRequestable { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public int MaxDaysForDownload { get; set; }
    public int MaxDaysForRequest { get; set; }
    public int MaxMonthsForDownload { get; set; }
    public int MaxMonthsForRequest { get; set; }
    public int MaxSelectableColumns { get; set; }
    public Report Report { get; set; }
    public long ReportId { get; set; }
    public string ReportName { get; set; }
    public List<ReportPreferences> ReportPreferences { get; set; }
}

public class CustomReportItem
{
    public CategoryName CategoryName { get; set; }
    public DateTime CreatedAt { get; set; }
    public CustomReport CustomReport { get; set; }
    public long CustomReportId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsNewColumn { get; set; }
    public bool IsSelected { get; set; }
    public bool IsStandardColumn { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string MappedColumnName { get; set; }
    public string MasterDbColumn { get; set; }
    public MasterDataTables MasterDbTable { get; set; }
    public string ReportColumnName { get; set; }
    public string ReportDbColumn { get; set; }

    public ReportDataTables ReportDbTable { get; set; }
    // added for using this model in user wise custom report result
}
