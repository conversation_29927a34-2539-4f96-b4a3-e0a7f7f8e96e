﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;
using Newtonsoft.Json;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class CompanyService(
    ICompanyRepository companyRepository,
    CompanySettingService companySettingService,
    NomenclatureService nomenclatureService,
    CompanyModuleService companyModuleService,
    IQueryViewRepository queryViewRepository,
    IScreenListRepository screenListRepository,
    ICurrentUser currentUser,
    AppConfigSettings appConfigSettings) : RepositoryResponse
{
    public static List<EntityNameValue> AMList =>
    [
        new EntityNameValue { Name = "Pankaj Sharma", Value = "<EMAIL>" },
        new EntityNameValue { Name = "If<PERSON><PERSON>", Value = "If<PERSON><PERSON>@fieldassist.in" },
        new EntityNameValue { Name = "Nithyana<PERSON>han", Value = "<EMAIL> " },
        new EntityNameValue { Name = "Mohit Singh", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Chitransh Jain", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Gyanendra Bharti", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Akshay Arora", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Aditya Singh", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Omkar Singh", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Sabhar Jain", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Prem Anand", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Sarang Nair", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Sankalp Singh ", Value = "<EMAIL>" },

        new EntityNameValue { Name = "Shrot", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Komal", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Amit", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Maaz", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Anand", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Ashutosh", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Prateek Jain", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Shiva", Value = "<EMAIL>" }
        //new EntityNameValue() { Name="Naina Pahuja",Value = "<EMAIL>"},
        //new EntityNameValue() { Name="Aisha ",Value = "<EMAIL>"},
    ];

    public static List<EntityNameValue> KAMList =>
    [
        new EntityNameValue { Name = "Nikhil Aggarwal", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Chitransh Jain", Value = "<EMAIL>" },
        new EntityNameValue { Name = "Shrot Srivastava", Value = "<EMAIL>" },
        //new EntityNameValue() { Name="Ankit Bansal",EmailId ="<EMAIL>"},
    ];

    public async Task<RepositoryResponse> ActionCompany(long companyId, bool action) => await companyRepository.ActionCompany(companyId, action);
    public async Task<RepositoryResponse> CreateCompany(CompanyRegistrationModel model)
    {
        if (model != null)
        {
            model.KeyAccountManagerEmail = KAMList.Where(s => string.Equals(s.Name, model.KeyAccountManagerName, StringComparison.OrdinalIgnoreCase)).Select(s => s.Value).FirstOrDefault();
            model.AccountManagerEmail = AMList.Where(s => string.Equals(s.Name, model.AccountManagerName, StringComparison.OrdinalIgnoreCase)).Select(s => s.Value).FirstOrDefault();
        }

        var appMeta = new CompanyAppMetaModel { AppVariantName = model.AppVariantName, AppVersionNumber = model.AppVersionNumber, MinRequiredAppVersion = model.MinRequiredAppVersion };
        if (await companyRepository.CompanyNameExists(model))
        {
            return GetRejectResponse("A Company with the same name already exists");
        }

        if (!string.IsNullOrEmpty(model.CompanyShortCode) && await companyRepository.CompanyShortCodeExists(model))
        {
            return GetRejectResponse("A Company with the same Company Short code already exists");
        }

        var returnModel = await companyRepository.CreateCompany(model, appMeta);
        if (!returnModel.IsSuccess)
        {
            return returnModel;
        }

        var errors = new List<string>();
        var listToReturn = await companySettingService.GetSettingForCreate();
        var allsettingvalue = new List<CompanySettingsValues>();
        var settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "BillingType").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.BillingType };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "PricePerUser").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.PricePerUser };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "MinimumBillingUser").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.MinBillingUser };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "IsInvoiceHardcopyRequired").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.HardCopy };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "GracePeriodCount").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.GracePeriod };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "PaymentRecoveryMobileNumber").Select(v => v.SettingId).SingleOrDefault())
        {
            SettingValue = JsonConvert.SerializeObject(model.PaymentRecoveryMobileNumber)
        };
        allsettingvalue.Add(settingsValues);
        if (model.ClientCategory == ClientCategory.SMB)
        {
            settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "LimitSalesHierarchyUserCount").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.MinBillingUser };
            allsettingvalue.Add(settingsValues);
            settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "UsesCappingonEmployeeCreation").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = "true" };
            allsettingvalue.Add(settingsValues);
        }
        var res = await companySettingService.CompanyNewSettingsSave(allsettingvalue, returnModel.Id.Value);
        if (!res.IsSuccess)
        {
            errors.Add($"Company added, but payment modules not set : {res.Message}. Please go to Company Settings page to set them manually.");
        }

        var resNom = await nomenclatureService.CreateDefaultNomenclatures(returnModel.Id.Value);
        if (!resNom.IsSuccess)
        {
            errors.Add($"Company added, but some nomenclatures not set : {resNom.Message}. Please go to nomenclature page to set them manually.");
        }

        var resMod = await companyModuleService.SetCompulsoryModulesForNewCompany(returnModel.Id.Value);
        if (!resMod.IsSuccess)
        {
            errors.Add($"Company added, but some compulsory modules not set : {resMod.Message}. Please go to modules page to set them manually.");
        }

        //TODO: hardcoded
        var chartIds = GetChartIds(appConfigSettings.deployments);
        foreach (var chartId in chartIds)
        {
            var resChar = await queryViewRepository.CloneChart(chartId, returnModel.Id.Value);
            if (!resChar.IsSuccess)
            {
                errors.Add($"Company added, but some charts not set : {resChar.Message}. Please set them manually.");
            }
        }

        if (returnModel.IsSuccess && returnModel.Id > 0)
        {
            var getDefaultCompanyConfig = await screenListRepository.GetDefaultModuleScreenLists();
            var createCompanyConfig = await companyRepository.CreateUpdateCompanyConfig(returnModel.Id.Value, getDefaultCompanyConfig);
        }
        //TODO: User Role Not working
        //if (User.PortalRole == PortalUserRole.AccountManager)
        //{
        //    var companyAdminService = new CompanyAdminService(dataContext, User.CompanyId);
        //    try
        //    {
        //        var res2 = companyAdminService.CloneManagerToNewCompany(User.Id, company.Id);
        //        if (!res2.IsSuccess)
        //        {
        //            errors.Add($"Company created, And Admin could not be added to new Company: {res2.Error}");
        //        }
        //        else
        //        {
        //            companyAdminService.Register(res2.Id.Value, User.PortalRole, User.UserEmail, HttpContext.Request.Url.Authority);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        ViewData.Add("Message", $"Company created, And Admin could not be added to new Company: {ex.Message}");
        //    }
        //}
        if (model.ClonedCompanyId != null && model.ClonedCompanyId > 0 && returnModel.Id > 0)
        {
            var cloningData = new ConfigCloningForNewCompany
            {
                FromCompanyId = model.ClonedCompanyId ?? 0,
                ToCompanyId = returnModel.Id ?? 0,
            };
            var queueHandler = new QueueHandler<ConfigCloningForNewCompany>(QueueType.FACloneConfigQueue,
                appConfigSettings.MasterStorageConnectionString);
        await queueHandler.AddToQueue(cloningData);
        }

        if (errors.Count > 0)
        {
            return GetRejectResponse(JsonConvert.SerializeObject(errors));
        }

        return GetSuccessResponse(returnModel.Id, "Information: Company created, default Login, nomenclatures and compulsory modules added.");
    }

    public async Task<RepositoryResponse> UpdateCompany(CompanyRegistrationModel model)
    {
        if (model != null)
        {
            model.KeyAccountManagerEmail = KAMList.Where(s => string.Equals(s.Name, model.KeyAccountManagerName, StringComparison.OrdinalIgnoreCase)).Select(s => s.Value).FirstOrDefault();
            model.AccountManagerEmail = AMList.Where(s => string.Equals(s.Name, model.AccountManagerName, StringComparison.OrdinalIgnoreCase)).Select(s => s.Value).FirstOrDefault();
        }

        var appMeta = new CompanyAppMetaModel { AppVariantName = model.AppVariantName, AppVersionNumber = model.AppVersionNumber, MinRequiredAppVersion = model.MinRequiredAppVersion };
        if (await companyRepository.CompanyNameExists(model))
        {
            return GetRejectResponse("A Company with the same name already exists");
        }
        if (!string.IsNullOrEmpty(model.CompanyShortCode) && await companyRepository.CompanyShortCodeExists(model))
        {
            return GetRejectResponse("A Company with the same Company Short code already exists");
        }

        var returnModel = await companyRepository.UpdateCompany(model, appMeta);
        if (!returnModel.IsSuccess)
        {
            return returnModel;
        }

        var errors = new List<string>();
        var listToReturn = await companySettingService.GetSettingForCreate();
        var allsettingvalue = new List<CompanySettingsValues>();
        var settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "BillingType").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.BillingType };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "PricePerUser").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.PricePerUser };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "MinimumBillingUser").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.MinBillingUser };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "IsInvoiceHardcopyRequired").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.HardCopy };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "GracePeriodCount").Select(v => v.SettingId).SingleOrDefault()) { SettingValue = model.GracePeriod };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "PaymentRecoveryMobileNumber").Select(v => v.SettingId).SingleOrDefault())
        {
            SettingValue = JsonConvert.SerializeObject(model.PaymentRecoveryMobileNumber)
        };
        allsettingvalue.Add(settingsValues);
        settingsValues = new CompanySettingsValues(returnModel.Id.Value, listToReturn.Where(v => v.SettingKey == "PaymentRecoveryEmailIDs").Select(v => v.SettingId).SingleOrDefault())
        {
            SettingValue = JsonConvert.SerializeObject(model.PaymentRecoveryEmailIDs)
        };
        allsettingvalue.Add(settingsValues);
        var res = await companySettingService.CompanyNewSettingsSave(allsettingvalue, returnModel.Id.Value);
        if (!res.IsSuccess)
        {
            errors.Add($"Company added, but payment modules not set : {res.Message}. Please go to Company Settings page to set them manually.");
        }

        if (errors.Count > 0)
        {
            return GetRejectResponse(JsonConvert.SerializeObject(errors));
        }

        return GetSuccessResponse(returnModel.Id, "Company Updated Successfully");
    }

    public async Task<Dictionary<long, CompanyNameAndLogo>> GetAllCompanyDictionary() => await companyRepository.GetAllCompanyDictionary();

    public static List<long> GetChartIds(string deployment)
    {
        if (deployment == "manage" || deployment == "beta")
            return [455, 905];
        return [414, 415];
    }

    public async Task<CompanyInfo> GetCompanyInfoMin() => await companyRepository.GetCompanyInfo(currentUser.CompanyId);
    public async Task<RepositoryResponse> SyncToFlo() => await companyRepository.SyncToFlo(currentUser.CompanyId);
    public async Task<RepositoryResponse> SyncToFloCompany() => await companyRepository.SyncToFloCompany(currentUser.CompanyId);
}
