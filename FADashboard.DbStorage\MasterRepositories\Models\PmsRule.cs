﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("PmsRules")]
public class PmsRule :  IDeletable
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Cron { get; set; }
    public string Message { get; set; }
    public long CompanyId { get; set; }
    public string CreationContext { get; set; } = "NewDashboard:Feature under research!!";
    public DateTime CreationAt { get; set; }
    public bool IsDeactive { get; set; }
    public bool Deleted { get; set; }
    public string ManagerMessage { get; set; }
    public string FieldUserMessage { get; set; }
    public string RuleVariables { get; set; }
    public virtual Company Company { get; set; }
  //public DateTime LastUpdatedAt { get; set; }
}
public class PmsRuleMetricConstraints : IDeletable
{
    public long Id { get; set; }
    public long PmsRuleId { get; set; }
    public long MetricId { get; set; }
    public string Target { get; set; } = string.Empty;
    public long CompanyId { get; set; }
    public string IndustryAverage { get; set; } = string.Empty;
    public string? ManagerMessage { get; set; }
    public string? FieldUserMessage { get; set; }
    public string? KPIMessage { get; set; }
    public string? MetricParameterValue { get; set; }
    public int MetricFrequency { get; set; }
    public string CreationContext { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsDeactive { get; set; }
    [Column("IsDeleted")]
    public bool Deleted { get; set; }
    public string? RuleKpiVariables { get; set; }
    public int ComparisionOperator { get; set; }

}
