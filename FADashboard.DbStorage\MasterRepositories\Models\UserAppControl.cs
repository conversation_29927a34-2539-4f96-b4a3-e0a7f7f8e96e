﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class UserAppControl : IAuditedEntity
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public string Description { get; set; }
    public string CohortIds { get; set; }
    public AlertSource PlatformType { get; set; }
    public bool IsDeactive { get; set; }
    public string CreationContext { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
