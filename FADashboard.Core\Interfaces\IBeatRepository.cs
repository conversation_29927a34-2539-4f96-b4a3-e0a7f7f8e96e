﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.HccbIntegrationLogs;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IBeatRepository
{
    Task<List<Beat>> GetBeatsMin(long companyId, bool includeDeactivate = false);

    Task<RepositoryResponse> ActivateDeactivateBeats(long beatId, long companyId, bool action, CompanySettings companySettings);

    Task<RepositoryResponse> CreateBeat(Beat beat, long companyId, CompanySettings companySettings, CancellationToken ct = default);

    Task<Beat> GetBeatId(long beatId, long companyId);

    Task<List<Beat>> GetBeatsList(long companyId, bool includeDeactivate);

    Task<BeatTotal> GetBeats(long companyId, PaginationFilter validFilter, List<long> regionIds, PortalUserRole role);
    Task<List<EntityMin>> GetBeatsMin(long companyId, List<long> beatIds);

    Task<List<BeatPositionMapInfo>> GetBeatsOfPosition(long companyId, long positionId);

    Task<List<Beat>> GetBeatsOfPositions(long companyId, List<long> positionIds);

    Task<RepositoryResponse> UpdateBeat(Beat beat, long companyId, CompanySettings companySettings, CancellationToken ct = default);

    Task<List<Beat>> GetBeatsUnderGeographyList(long companyId, string geographyName, List<long> geographyid);

    Task<long> GetBeatCount(long companyId, PaginationFilter validFilter);
    Task<Dictionary<long, BeatInfoDto>> GetBeatErpIdMapAsync(long companyId, List<long> beatIds);
}
