﻿using FADashboard.Core.Models;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IPositionDistributorRepository
{
    Task<RepositoryResponse> AttachDistributors(long companyId, long positionCodeId, List<long> distributorIds);

    Task<List<EntityMin>> GetDistributorForPosition(long positionId);

    Task<Dictionary<long, List<PositionDistributorMapping>>> GetPositionDistributorMappingDic(long companyId, List<long> positionIds);
}
