﻿using System.Collections.Concurrent;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.CommonModels;
using Library.CommonHelpers;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class DailySummaryService(
    ManagerAppPointersService managerAppPointersService,
    NumberSystemHelper numberSystemService,
    ICompanySettingsRepository companySettingsRepository,
    ICurrentUser currentUser,
    IAdvanceLeaveRepository advanceLeaveRepository,
    FAResilientHttpClient resilientHttpClient,
    AppConfigSettings appConfigSettings) : RepositoryResponse
{
    #region private members

    private async Task<List<AttendanceSummaryTransaction>> GetAttendanceSummary(DateTime? date, long employeeId, long companyId)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/timeline/usertimeline?date={date.Value:MM/dd/yyyy}&companyId={companyId}&userId={employeeId}&onlyVisits=true";
        var data = await resilientHttpClient.GetJsonAsync<List<AttendanceSummaryTransaction>>(dataUrl, appConfigSettings.reportApiToken);
        return data;
    }

    private async Task<List<AttendanceOutletDetails>> GetUserAttendanceSummary(DateTime? date, long employeeId, long locationId, long companyId)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/timeline/useroutlettimeline?date={date.Value:MM/dd/yyyy}&companyId={companyId}&userId={employeeId}&locationId={locationId}";
        var data = await resilientHttpClient.GetJsonAsync<List<AttendanceOutletDetails>>(dataUrl, appConfigSettings.reportApiToken);
        return data;
    }

    private async Task<List<FieldUserDailyDataPointersModel>> GetUserDailySummary(List<long> positionIds, DateTime date, SaleType saleType = SaleType.All)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/dailySummary/userDailySummaryUsingPositionCodes?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&saleType={saleType}";
        var result = await resilientHttpClient.PostJsonAsync<List<FieldUserDailyDataPointersModel>>(dataUrl, appConfigSettings.reportApiToken, positionIds);
        return result;
    }

    #endregion private members

    //TODO can be done better
    public async Task<TotalEmployeeSummaryDetails> GetEmployeeSummary(DateTime? date, long employeeId)
    {
        var attendanceSummary = await GetAttendanceSummary(date, employeeId, currentUser.CompanyId);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var CurrencySymbol = companySettings.CurrencySymbol;

        var returnModel = new TotalEmployeeSummaryDetails
        {
            // Please don't remove the _ from the properties name since the data for these is being retrieved from the reporting side, which could cause a key name mismatch.  
            TotalDiscountedSales = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.NetValue_double), CurrencySymbol, true),
            TotalOverallDiscount = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.Discount_double), CurrencySymbol, true),
            TotalStdUnitQuantiy = attendanceSummary
                                .Where(a => !string.IsNullOrEmpty(a.QtyStdUnit) && double.TryParse(a.QtyStdUnit, out _))
                                .Sum(a => double.TryParse(a.QtyStdUnit, out var qty) ? qty : 0)
                                .ToString(),
            TotalUnitQuantity = attendanceSummary.Where(a => int.TryParse(a.Qty, out _)).Sum(a => int.Parse(a.Qty)).ToString(),
            TotalSalesValues = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.Value_double
            ), CurrencySymbol, true),
            EmployeeSummaryDetails =
            [
                .. attendanceSummary.Select(d => new EmployeeSummaryDetails
                {
                    LocationId = d.LocationId,
                    OutletName = d.ShopName,
                    BeatName = d.BeatName,
                    Time = d.Time.ToString("hh:mm tt"),
                    OVT = d.IsOvt,
                    Telephonic = d.IsTelephonic,
                    SalesQuantity = d.Qty,
                    DiscountedSales = numberSystemService.GetFormattedValue(d.Value_double - d.Discount_double, CurrencySymbol, true),
                    Date = d.Time,
                    SalesValues = numberSystemService.GetFormattedValue(d.Value_double, CurrencySymbol, true),
                    SalesStdUnit = d.QtyStdUnit,
                    OverallDiscount = numberSystemService.GetFormattedValue(d.Discount_double, CurrencySymbol, true),
                    EmployeeId = employeeId
                }).OrderBy(d => d.Date)
            ]
        };
        return returnModel;
    }

    public async Task<List<dynamic>> GetFieldUserDailyData(DateTime date, List<DayRecordsMin> positionCode,
        DayStartType? dayStartType, SaleType saleType = SaleType.All)
    {
        var posLookup = positionCode.ToLookup(p => p.PositionCodeId);

        var dataPointerMappings = await managerAppPointersService.GetDashboardDataPointers(currentUser.CompanyId);
        if (dataPointerMappings == null)
        {
            return [];
        }

        var dyReturnObjectList = new ConcurrentBag<dynamic>();
        var dpDict = dataPointerMappings.ToDictionary(d => d.Name, d => d);
        // Dynamic Pointers data
        var UsersDailyData = await GetUserDailySummary(positionCode.Select(a => a.PositionCodeId).Distinct().ToList(), date, saleType: saleType);

        if (dayStartType.HasValue)
            UsersDailyData = UsersDailyData.Where(x => x.DayStartTypeEnum == dayStartType).ToList();

        var advanceLeaves = await advanceLeaveRepository.GetEmployeesAdvanceLeaveForDate(currentUser.CompanyId, date);

        Parallel.ForEach(UsersDailyData, data =>
        {
            var empId = data.EmployeeId;

            var pointers = new List<DynamicDataReturnModel>
            {
                new() { Name = "EmployeeId", Value = empId.ToString(), Order = 0 },
                new() { Name = DailyDataPointers.FieldUserName.GetDisplayName(), Value = posLookup.Contains(data.PositionId) ? posLookup[data.PositionId].FirstOrDefault().EmployeeName : string.Empty, Order = 1 },
                new() { Name = "Position Name", Value = posLookup.Contains(data.PositionId) ? posLookup[data.PositionId].FirstOrDefault().PositionName : string.Empty, Order = 2 },

                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.ReportingManager.GetDisplayName(), Value = managerName, Order = 3 });
                new() { Name = "Position Level", Value = posLookup.Contains(data.PositionId) ? posLookup[data.PositionId].FirstOrDefault().PositionLevel.ToString() : string.Empty, Order = 4 },
                new() { Name = "PositionId", Value = data.PositionId.ToString(), Order = 5 },
                new() { Name = "PositionCodeLevel", Value = posLookup.Contains(data.PositionId) ? ((long)posLookup[data.PositionId].FirstOrDefault().PositionLevel).ToString() : null, Order = 6},
                // pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.FieldUserRole.GetDisplayName(), Value = user.EmployeeRole.GetDisplayName(), Order = 4 });
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.FieldUserHQ.GetDisplayName(), Value = user.EmployeeLocalName, Order = 5 });
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.DayStartLocation.GetDisplayName(), Value = data.DayStartLocation, Order = 6 });
                //Removeed AssignedReasonCategory and AssignedReason calculation from reporting api
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.AssignedReasonCategory.GetDisplayName(), Value = data.AssignedReasonCategory = data.DayStartReasonCategory, Order = 7 });
                new()
                {
                    Name = DailyDataPointers.ReasonCategory.GetDisplayName(),
                    Value = string.IsNullOrEmpty(data.DayStartReasonCategory) ?
                    posLookup.Contains(data.PositionId) ? posLookup[data.PositionId].FirstOrDefault().ReasonCategory : DayStartType.None.GetDisplayName()
                    : data.DayStartReasonCategory,
                    Order = 8
                },
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.AssignedReason.GetDisplayName(), Value = data.AssignedReason, Order = 9 });
                new() { Name = DailyDataPointers.Reason.GetDisplayName(), Value = data.Reason, Order = 10 }, // This is Day Start Reason or Advance Leave Reason
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.AssignedBeat.GetDisplayName(), Value = data.AssignedBeat, Order = 11 });
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.AssignedRoute.GetDisplayName(), Value = data.AssignedRoute, Order = 13 });
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.AssignedJointWorkingEmployee.GetDisplayName(), Value = data.AssignedJointWorkingEmployee, Order = 15 });
                //pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.Distributor.GetDisplayName(), Value = data.Distributor, Order = 17 });
                //new DynamicDataReturnModel { Name = IsAmount ? DailyDataPointers.MTDvalue.GetDisplayName() : DailyDataPointers.MTDunit.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.MTDValue), Order = 18 }
            };

            foreach (var item in dpDict.Select(d => d.Key).ToList())
            {
                switch (item)
                {
                    case DailyDataPointers.Login:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = data.Login, Order = 19 });
                        pointers.Add(new DynamicDataReturnModel { Name = "Log Out", Value = data.Logout, Order = 19 });
                        pointers.Add(new DynamicDataReturnModel { Name = "Total Time", Value = data.TotalTime, Order = 19 });
                        var advanceLeave = advanceLeaves.FirstOrDefault(a => a.EmployeeId == empId);
                        if (advanceLeave != null)
                        {
                            pointers.Add(new DynamicDataReturnModel { Name = "HasAdvanceLeave", Value = "true", Order = 19 });
                            pointers.Add(new DynamicDataReturnModel { Name = "AdvanceLeaveReason", Value = advanceLeave.Reason, Order = 19 });
                        }
                        else
                        {
                            pointers.Add(new DynamicDataReturnModel { Name = "HasAdvanceLeave", Value = "false", Order = 19 });
                            pointers.Add(new DynamicDataReturnModel { Name = "AdvanceLeaveReason", Value = "", Order = 19 });
                        }

                        break;

                    case DailyDataPointers.FirstCallTime:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = data.FirstCallTime?.ToString("hh:mm tt") ?? "", Order = 20 });
                        pointers.Add(new DynamicDataReturnModel { Name = "Last Call", Value = data.LastCallTime?.ToString("hh:mm tt") ?? "", Order = 20 });
                        pointers.Add(new DynamicDataReturnModel { Name = "Retailing Time", Value = (data.LastCallTime - data.FirstCallTime)?.ToString("hh\\:mm") ?? "", Order = 20 });
                        break;

                    case DailyDataPointers.FirstPCTime:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = data.FirstPCTime, Order = 21 });
                        break;

                    case DailyDataPointers.SC:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.SC), Order = 22 });
                        break;

                    case DailyDataPointers.TC:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.TC), Order = 23 });
                        break;

                    case DailyDataPointers.PC:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.PC), Order = 24 });
                        break;

                    case DailyDataPointers.Productivity:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.Productivity), Order = 25 });
                        break;

                    case DailyDataPointers.SchProductivity:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.SchProductivity), Order = 26 });
                        break;

                    case DailyDataPointers.JointWorkingCalls:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.JointWorkingCalls), Order = 27 });
                        break;

                    case DailyDataPointers.PhycialCalls:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.PhycialCalls), Order = 28 });
                        break;

                    case DailyDataPointers.CAP:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.CAP), Order = 29 });
                        break;

                    case DailyDataPointers.OVC:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.PercentOVC, "%"), Order = 33 });
                        break;

                    case DailyDataPointers.OVT:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.PercentOVT, "%"), Order = 34 });
                        break;

                    case DailyDataPointers.NewOutletsCreated:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.NewOutletsCreated), Order = 35 });
                        break;

                    case DailyDataPointers.NewOutletSalesInRevenue:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.NewOutletSalesInRevenue), Order = 36 });
                        break;

                    case DailyDataPointers.TelephonicOrders:
                        // pointers.Add(new DynamicDataReturnModel { Name = "Telephonic Order Value", Value = data..ToString(), Order = 39 });
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.TelephonicOrders), Order = 37 });
                        break;

                    case DailyDataPointers.OrderInStdUnits:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.OrderInStdUnits), Order = 38 });
                        break;

                    case DailyDataPointers.NetValue:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.NetValue), Order = 39 });
                        break;

                    case DailyDataPointers.SelectedJourneyOutlet:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.SelectedJourneyOutlets), Order = 40 });
                        break;

                    case DailyDataPointers.TotalSchemeQty:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.TotalSchemeQty), Order = 41 });
                        break;

                    case DailyDataPointers.TelephonicOrdersValue:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.TelephonicOrders), Order = 42 });
                        break;

                    case DailyDataPointers.NoOfOtherActivities:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.NoOfOtherActivities), Order = 43 });
                        break;

                    case DailyDataPointers.FillratePer:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.FillratePer), Order = 23 });
                        break;

                    case DailyDataPointers.DC:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.DC), Order = 23 });
                        break;
                    case DailyDataPointers.LogOutTime:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = data.Logout, Order = 44 });
                        break;
                    case DailyDataPointers.CAPAdherancePer:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.DC), Order = 45 });
                        break;

                    case DailyDataPointers.TotalUsers:
                        break;

                    case DailyDataPointers.Retailing:
                        break;

                    case DailyDataPointers.OfficialWork:
                        break;

                    case DailyDataPointers.Leave:
                        break;

                    case DailyDataPointers.PlannedLeave:
                        break;

                    case DailyDataPointers.Absent:
                        break;

                    case DailyDataPointers.ReportingManager:
                        break;

                    case DailyDataPointers.FieldUserName:
                        break;

                    case DailyDataPointers.FieldUserHQ:
                        break;

                    case DailyDataPointers.FieldUserRole:
                        break;

                    case DailyDataPointers.AssignedReasonCategory:
                        break;

                    case DailyDataPointers.ReasonCategory:
                        break;

                    case DailyDataPointers.AssignedReason:
                        break;

                    case DailyDataPointers.Reason:
                        break;

                    case DailyDataPointers.AssignedJointWorkingEmployee:
                        break;

                    case DailyDataPointers.JointWorkingEmployee:
                        break;

                    case DailyDataPointers.AssignedBeat:
                        break;

                    case DailyDataPointers.SelectedBeat:
                        break;

                    case DailyDataPointers.AssignedRoute:
                        break;

                    case DailyDataPointers.SelectedRoute:
                        break;

                    case DailyDataPointers.Distributor:
                        break;

                    case DailyDataPointers.DayStartLocation:
                        break;

                    case DailyDataPointers.MTDValue:
                        break;

                    case DailyDataPointers.MTDUnit:
                        break;

                    case DailyDataPointers.DayStartType:
                        break;

                    case DailyDataPointers.DayStartReasonCategory:
                        break;

                    case DailyDataPointers.DayStartReasonDescription:
                        break;

                    case DailyDataPointers.TotalTime:
                        break;

                    case DailyDataPointers.IsJourneyViolated:
                        break;

                    case DailyDataPointers.OrderInSuperUnits:
                        pointers.Add(new DynamicDataReturnModel { Name = item.GetDisplayName(), Value = numberSystemService.GetFormattedValue(data.OrderInSuperUnits), Order = 47 });
                        break;

                    case DailyDataPointers.IsPositionNotUnderManager:
                        break;

                    case DailyDataPointers.UPC:
                        break;

                    case DailyDataPointers.UTC:
                        break;

                    case DailyDataPointers.JourneyDivertedDays:
                        break;

                    case DailyDataPointers.JWPC:
                        break;

                    case DailyDataPointers.JWOVC:
                        break;

                    case DailyDataPointers.JWOVT:
                        break;

                    case DailyDataPointers.JWSalesUnit:
                        break;

                    case DailyDataPointers.JWSalesStdUnit:
                        break;

                    case DailyDataPointers.JWSalesValue:
                        break;

                    case DailyDataPointers.LPC:
                        break;

                    case DailyDataPointers.ULC:
                        break;

                    case DailyDataPointers.PWScheduledVisits:
                        break;

                    case DailyDataPointers.TotalPWVisits:
                        break;

                    case DailyDataPointers.TotalPWProductiveVisits:
                        break;

                    case DailyDataPointers.AvgPLPC:
                        break;

                    case DailyDataPointers.PWOVCPercentage:
                        break;

                    case DailyDataPointers.PWAvgTimeSpent:
                        break;

                    case DailyDataPointers.PWJWSessions:
                        break;
                }
            }

            //To Be Sent At Last as per Product
            pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.SelectedBeat.GetDisplayName(), Value = data.SelectedBeat, Order = 44 });
            pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.SelectedRoute.GetDisplayName(), Value = data.SelectedRoute, Order = 45 });
            pointers.Add(new DynamicDataReturnModel { Name = DailyDataPointers.JointWorkingEmployee.GetDisplayName(), Value = data.JointWorkingEmployee, Order = 46 });
            var sortedPointers = pointers.OrderBy(o => o.Order).ToList();
            dyReturnObjectList.Add(sortedPointers);
        });
        return [.. dyReturnObjectList];
    }

    //TODO can be done better
    public async Task<TotalAttendanceOutletDetails> GetUserLocationsAndSales(DateTime? date, long employeeId, long locationId)
    {
        var attendanceSummary = await GetUserAttendanceSummary(date, employeeId, locationId, currentUser.CompanyId);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var currencySymbol = companySettings.CurrencySymbol;
        var returnModel = new TotalAttendanceOutletDetails
        {
            TotalDiscount = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.Discount), currencySymbol, true),
            TotalNetOrderInRevenue = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.NetOrderInRevenue), currencySymbol, true),
            TotalOrderInRevenue = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.OrderInRevenue), currencySymbol, true),
            TotalOrderInUnits = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.OrderInUnits)),
            TotalOrderInStdUnits = numberSystemService.GetFormattedValue(attendanceSummary.Sum(a => a.OrderInStdUnits)),
            AttendanceOutletDetails = attendanceSummary.Select(d => new AttendanceOutletDetails
            {
                PrimaryCategory = d.PrimaryCategory,
                SecondaryCategory = d.SecondaryCategory,
                Product = d.Product,
                OrderWithUnits = numberSystemService.GetFormattedValue(d.OrderInUnits, d.Unit),
                OrderWithStdUnits = numberSystemService.GetFormattedValue(d.OrderInStdUnits, d.StandardUnit),
                NetOrderInRevenueWithUnits = numberSystemService.GetFormattedValue(d.NetOrderInRevenue, currencySymbol, true),
                OrderInRevenueWithUnits = numberSystemService.GetFormattedValue(d.OrderInRevenue, currencySymbol, true),
                DiscountWithUnits = numberSystemService.GetFormattedValue(d.Discount, currencySymbol, true),
            }).ToList()
        };

        return returnModel;
    }
}
