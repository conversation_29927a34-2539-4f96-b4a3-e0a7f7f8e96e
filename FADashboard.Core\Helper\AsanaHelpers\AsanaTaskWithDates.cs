﻿using System.Globalization;
using Newtonsoft.Json;

namespace FADashboard.Core.Helper.AsanaHelpers;
public class AsanaTaskWithDates
{
    public static AsanaTaskWithDates UpdateDueDate(string severity) => new()
    {
        start_at = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ", CultureInfo.InvariantCulture),
        due_at = GetDueDate(DateTime.UtcNow, severity).ToString("yyyy-MM-ddTHH:mm:ssZ", CultureInfo.InvariantCulture),
    };

    [JsonProperty("start_at")]
    public string start_at { get; set; }

    [JsonProperty("due_at")]
    public string due_at { get; set; }

    public static DateTime GetDueDate(DateTime startDate, string severity)
    {
        switch (severity)
        {
            case "x1":

            case "x2":
                return SLARespondBy(startDate, 16 * 8);

            case "x4":
                return SLARespondBy(startDate, 8 * 8);

            case "x8":
                return SLARespondBy(startDate, 4 * 8);

            case "x16":
                return SLARespondBy(startDate, 16, true);

            case "x24":
                return SLARespondBy(startDate, 8, true);

            case "x32":
                return SLARespondBy(startDate, 3, true);

            default:
                return startDate.AddDays(16 * 8);
        }
    }

    public static DateTime SLARespondBy(DateTime startDate, int slaHours, bool highPriority = false)
    {
        //start hour utc = start time India - India offset
        const double startHour = 10 - 5.5;
        const double endHour = 18 - 5.5;
        var slaTimeSpanHours = TimeSpan.FromHours(slaHours);
        while (true)
        {
            startDate = Max(startDate, startDate.Date.AddHours(startHour));
            DateTime x = Min(startDate + slaTimeSpanHours, startDate.Date.AddHours(endHour));
            slaTimeSpanHours -= x - startDate;
            startDate = x;
            if (slaTimeSpanHours == TimeSpan.Zero)
            { return startDate; }
            do
            { startDate = startDate.Date.AddDays(1); } while (IsWeekendDay(startDate, highPriority));
        }
    }

    private static bool IsWeekendDay(DateTime dt, bool highPriority)
    {
        return highPriority ? dt.DayOfWeek == DayOfWeek.Sunday : dt.DayOfWeek == DayOfWeek.Saturday
            || dt.DayOfWeek == DayOfWeek.Sunday;
    }

    private static DateTime Max(DateTime a, DateTime b)
    {
        return new DateTime(Math.Max(a.Ticks, b.Ticks));
    }

    private static DateTime Min(DateTime a, DateTime b)
    {
        return new DateTime(Math.Min(a.Ticks, b.Ticks));
    }
}
