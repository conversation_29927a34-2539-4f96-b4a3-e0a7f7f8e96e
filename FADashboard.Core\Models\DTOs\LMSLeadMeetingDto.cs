﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadMeetingDto
    {
        public long Id { get; set; }
        public string Title { get; set; }
        public long LeadId { get; set; }
        public string LeadName { get; set; }
        public string Description { get; set; }
        public LMSTaskStatus? Status { get; set; }
        public long AssignedTo { get; set; }
        public string AssignedToName { get; set; }
        public LMSMeetingLocation? MeetingLocation { get; set; }
        public DateTime? MeetingStartTime { get; set; }
        public DateTime? MeetingEndTime { get; set; }
        public DateTime CreatedAt { get; set; }
        public long CreatedBy { get; set; }
        public string CreatedByName { get; set; }
    }
}
