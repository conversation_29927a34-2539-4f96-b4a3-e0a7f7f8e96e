﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ViewModels;

public class GeographiesList
{
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public GeographyLevel Level { get; set; }
    public string LevelName { get; set; }
    public string Name { get; set; }
    public string RegionShortCode { get; set; }
    public bool? ODSActive { get; set; }
}

public class GeographiesListParent
{
    public long? Id { get; set; }
    public string Name { get; set; }
}

public class GeographiesListWithParent : GeographiesList
{
    public long? ParentId { get; set; }
    public string ParentName { get; set; }
}
