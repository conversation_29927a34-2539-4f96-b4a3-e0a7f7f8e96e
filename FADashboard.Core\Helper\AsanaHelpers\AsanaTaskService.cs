﻿using System.Net.Http.Headers;
using System.Text.Json;
using System.Text;
using Newtonsoft.Json;

namespace FADashboard.Core.Helper.AsanaHelpers;
public class AsanaTaskService : IAsyncDisposable
{
    private readonly HttpClient client;

    public AsanaTaskService()
    {
        client = new HttpClient
        {
            BaseAddress = new Uri("https://app.asana.com/api/1.0/")
        };
        client.DefaultRequestHeaders.Accept.Clear();
        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AsanaCredentials.GetCredentialsForUser("AnandSamuel").AsanaToken);
    }

    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
    };

    public async Task<AsanaTask> CreateNewTask(AsanaNewTask task, string emailId, AsanaProjectOwner owner = null)
    {
        try
        {
            //verify whether the current user has an associated asana account
            var asanaDomain = "flick2know.com"; //asana workspace has flick2know.com domain
            if (!emailId.EndsWith($"@{asanaDomain}"))
            {
                emailId = string.Concat(emailId.AsSpan(0, emailId.LastIndexOf('@') + 1), asanaDomain);
            }
            var encodedEmail = Uri.EscapeDataString(emailId);
            var url = $"users/{encodedEmail}";
            var checkCurrentUser = await client.GetAsync(url);
            if (!checkCurrentUser.IsSuccessStatusCode)
            {
                return new AsanaTask { Id = "0", Message = "No asana account associated to your email found", IsSuccess = false };
            }
            var result = await checkCurrentUser.Content.ReadAsStringAsync();
            var user = JsonConvert.DeserializeObject<dynamic>(result);
            if (user?.data?.gid == null)
            {
                return new AsanaTask();
            }
            string userGid = user.data.gid;
            task.Followers.Add(userGid);

            var jsonContent = System.Text.Json.JsonSerializer.Serialize(new AsanaWrapper<AsanaNewTask>(task), JsonSerializerOptions);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await client.PostAsync("tasks", content);
            response.EnsureSuccessStatusCode();
            var res = await response.Content.ReadAsStringAsync();

            //task has been created; now we need to add it to the appropriate project
            if (!string.IsNullOrWhiteSpace(res) && owner != null)
            {
                var d = JsonConvert.DeserializeObject<dynamic>(res);
                string id = d.data.gid;
                string asanaLink = d.data.permalink_url;
                //Tasks will be added to projects with respective project owner's credentials as the end user may not have permission for the same
                if (owner.ProjectIds?.Count > 0)
                {
                    var updateTaskModel = AsanaTaskWithDates.UpdateDueDate(owner.ImportanceScale);
                    var updateJsonContent = System.Text.Json.JsonSerializer.Serialize(new AsanaWrapper<AsanaTaskWithDates>(updateTaskModel), JsonSerializerOptions);
                    var updateContent = new StringContent(updateJsonContent, Encoding.UTF8, "application/json");

                    var rew = await client.PutAsync($"tasks/{id}", updateContent);
                    rew.EnsureSuccessStatusCode();
                    foreach (var p in owner.ProjectIds)
                    {
                        var req = new AsanaTaskWithProject(p);
                        var reqJsonContent = System.Text.Json.JsonSerializer.Serialize(new AsanaWrapper<AsanaTaskWithProject>(req), JsonSerializerOptions);
                        var reqContent = new StringContent(reqJsonContent, Encoding.UTF8, "application/json");

                        var r = await client.PostAsync($"tasks/{id}/addProject", reqContent);
                        r.EnsureSuccessStatusCode();
                    }
                }
                else if (!string.IsNullOrWhiteSpace(owner.ProjectId))
                {
                    var req = new AsanaTaskWithProject(owner.ProjectId);
                    var reqJsonContent = System.Text.Json.JsonSerializer.Serialize(new AsanaWrapper<AsanaTaskWithProject>(req), JsonSerializerOptions);
                    var reqContent = new StringContent(reqJsonContent, Encoding.UTF8, "application/json");

                    var r = await client.PostAsync($"tasks/{id}/addProject", reqContent);
                    r.EnsureSuccessStatusCode();
                }

                return new AsanaTask() { Id = id, AsanaLink = asanaLink };
            }
            return null;
        }
        catch (Exception ex)
        {
            return new AsanaTask { Id = "0", ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }

    public async Task<string> AttachFile(byte[] file, string fileName, string id, AsanaCredentials AsanaToken = null)
    {
        //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userAuth);
        client.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "multipart/form-data");

        var form = new MultipartFormDataContent();

        var fileCont = new ByteArrayContent(file, 0, file.Length);
        fileCont.Headers.Add("Content-Disposition", $"form-data; name=\"file\"; filename=\"{fileName}\"");
        form.Add(fileCont);

        var response = await client.PostAsync($@"https://app.asana.com/api/1.0/tasks/{id}/attachments", form);
        var responseData = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
            return "file was uploaded successfully";

        throw new ArgumentException($"Error code: {response.StatusCode}; {responseData}");
    }
    public ValueTask DisposeAsync()
    {
        if (client != null)
        {
            client.Dispose();
        }
        GC.SuppressFinalize(this);
        return ValueTask.CompletedTask;
    }
}
