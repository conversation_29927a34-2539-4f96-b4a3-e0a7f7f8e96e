﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IAdminRepository
{
    Task<RepositoryResponse> ActivateUser(long userId, long companyId, string portalUrl, bool twoFactorEnabled, string userName);

    Task<RepositoryResponse> CreateUser(Admin user, long companyId, string portalUrl);

    Task<RepositoryResponse> DeactivateUser(long userId, long companyId);

    Task<Admin> GetUserById(long userId, long companyId);

    Task<List<Admin>> GetUsers(long companyId, bool includeDeactivate);

    Task<List<Admin>> GetUsersAndGlobalAdmins(long companyId, bool includeDeactivate);

    Task<List<Admin>> GetFinanceHeads(long companyId, bool includeDeactivate);
    Task<bool> IsCompanyAdminActive(long Id);

    Task<RepositoryResponse> UpdateUser(Admin user, long companyId, string portalUrl);

    bool UserHaveAdminPrivileges(PortalUserRole userRole);

    Task<RegionalAdminDTO> GetRegionalAdminDetails(long id, long companyId);

    Task<RegionalAdminDTO> GetRegionalAdminAndItsChildPositions(long id, long companyId);
    Task<List<CompanyFactories>> GetCompanyFactoriesToAttach(long? adminId, long companyId);

    Task<RepositoryResponse> RegisterAdmin(long adminId, bool action, long companyId, string redirectUrl, bool twoFactorEnable,string userName, List<long> roleIds);

}
