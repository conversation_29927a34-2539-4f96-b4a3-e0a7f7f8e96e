﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IEmployeeTourPlanRepository
{
    Task<List<NewJourneyPlan>> GetNewJourneyPlans();
    Task<RepositoryResponse> CreateNewJourney(NewJourneyPlan newJourneyPlan);
    Task<NewJourneyPlan> GetNewJourneyPlanById(long id);
    Task<RepositoryResponse> ForceEndNewJourneyPlan(long id);
    Task<RepositoryResponse> UpdateNewJourney(NewJourneyPlan newJourneyPlan);
    Task<RepositoryResponse> PlanAlreadyExist(NewJourneyPlan newJourneyPlan, bool companyCreatesPlanOnPositionCodes);
    Task<RepositoryResponse> UpdateJourneyPlanConfiguration(List<NewJourneyPlanConfig> newJourneyConfigs);
    Task<List<NewJourneyPlanConfig>> GetJourneyPlanConfiguration();
    Task<RepositoryResponse> GetJourneyPlanId(long employeeId);
    Task<List<NewJourneyPlanEntityConfig>> GetJourneyPlanEntityConfigurations();
    Task<RepositoryResponse> UpdateJourneyPlanEntityConfiguration(List<NewJourneyPlanEntityConfig> newConfigs);
}
