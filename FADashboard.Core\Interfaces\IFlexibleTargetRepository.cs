﻿
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IFlexibleTargetRepository
{
    Task<List<FlexibleTargetList>> GetFlexibleTargetList();

    Task<FlexibleTargetModel> GetFlexibleTargetById(long id);

    Task<RepositoryResponse> CreateFlexibleTarget(FlexibleTargetModel flexibleTarget);

    Task<RepositoryResponse> UpdateFlexibleTarget(FlexibleTargetModel flexibleTarget);
}
