﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IDesignationRepository
{
    Task<RepositoryResponse> ActivateDeactivateDesignation(long companyId, long id, bool action);

    Task<RepositoryResponse> CreateDesignation(Designation designation, long companyId);

    Task<List<Designation>> GetDesignation(long companyId, bool includeDeactivate = false);

    Task<int> GetEmployeeCountForDesignation(long companyId, long id);

    Task<RepositoryResponse> UpdateDesignation(Designation designation, long companyId);
}
