using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadDto
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long AccountId { get; set; }
        public string AccountName { get; set; }
        public long LeadTemplateId { get; set; }
        public string LeadTemplateName { get; set; }
        public long LeadStageId { get; set; }
        public string LeadStageName { get; set; }
        public string StageCategory { get; set; }
        public DateTime? LastActivityDate { get; set; }
        public string LeadName { get; set; }
        public string LeadImage { get; set; }
        public string Description { get; set; }

        public List<LMSCustomFieldValueDto> CustomFieldValues { get; set; }
        public decimal? Amount { get; set; }
        public long LeadSourceId { get; set; }
        public string LeadSourceName { get; set; }
        public string Email { get; set; }
        public string Website { get; set; }
        public string Street { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string Pincode { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public LMSLeadStatus Status { get; set; }
        public LMSPriority? Priority { get; set; }
        public DateTime? ClosingDate { get; set; }
        public DateTime? FollowUpDate { get; set; }
        public long AssignedTo { get; set; }
        public long PositionCode { get; set; }
        public string AssignedToName { get; set; }
        public bool IsDeleted { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CreatedByName { get; set; }
        public string UpdatedByName { get; set; }
    }

    public class LMSLeadCreateInput
    {
        [Required]
        public long CompanyId { get; set; }

        [Required]
        public long AccountId { get; set; }

        [Required]
        public long LeadTemplateId { get; set; }

        [Required]
        public long LeadStageId { get; set; }

        [Required]
        [StringLength(255)]
        public string LeadName { get; set; }

        [StringLength(500)]
        public string LeadImage { get; set; }

        public string Description { get; set; }

        public List<LMSCustomFieldValueDto> CustomFieldValues { get; set; }

        public decimal? Amount { get; set; }

        [Required]
        public long LeadSourceId { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        [StringLength(255)]
        public string Website { get; set; }

        [StringLength(500)]
        public string Street { get; set; }
        [StringLength(2000)]
        public string City { get; set; }
        [StringLength(100)]
        public string State { get; set; }

        [StringLength(100)]
        public string Country { get; set; }
        [StringLength(200)]
        public string Pincode { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        [Required]
        public LMSLeadStatus Status { get; set; }

        public LMSPriority? Priority { get; set; }

        public DateTime? ClosingDate { get; set; }

        public DateTime? FollowUpDate { get; set; }

        [Required]
        public long AssignedTo { get; set; }
        [Required]
        public long PositionCode { get; set; }
    }

    public class LMSLeadUpdateInput : LMSLeadCreateInput
    {

    }
}
