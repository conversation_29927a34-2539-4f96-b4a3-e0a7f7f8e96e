﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IRequestsAndAlertsRepository
{
    Task<RepositoryResponse> ApproveOutletUpdationRequest(long requestId);

    Task<RepositoryResponse> ApproveTourPlanRequest(long requestId, List<TourPlanDetails> tourPlanDetails, DateTime today);

    Task<RepositoryResponse> DisapproveOutletUpdationRequest(long requestId, string reasonForRejection);

    Task<RepositoryResponse> DisapproveTourPlanRequest(long requestId);

    Task<List<string>> GetOfficialWorkTypesForUserRole(PortalUserRole portalUserRole, long companyId);

    Task<OutletUpdationRequest> GetOutletUpdationRequestById(long requestId, long companyId);

    Task<List<Requests>> GetOutletUpdationRequests(long userId, PortalUserRole userRole, long companyId, bool includeArchieved);

    Task<TourPlanByIdDetails> GetTourPlanDetails(long tourPlanId, long companyId);

    Task<List<TourPlanList>> GetTourPlanRequests(long userId, PortalUserRole userRole, long companyId, bool includeApproved);

    Task UpdateManagerAlert(long requestId, long companyId, AlertType alertType, AlertAction action);
    Task<RepositoryResponse> ApproveRejectJourneyDiversionRequest(long requestId, bool isApproved);
    Task<JourneyDiversionRequest> GetJourneyDiversionRequestById(long requestId, long companyId);
    Task<List<JourneyDiversionList>> GetJourneyDiversionRequests(long userId, PortalUserRole userRole, long companyId, bool showArchived);
    Task<List<OnboardingRequest>> GetOnboardingRequestsForUser(bool showArchived = false, CancellationToken ct = default);
    Task<List<OnboardingRequest>> GetAllOnboardingRequests(bool showArchived = false, CancellationToken ct = default);
    Task<OnboardingRequestInput> GetOnboardingRequestById(long requestId, CancellationToken ct = default);
    Task<RepositoryResponse> DisapproveOnboardingRequest(long requestId, CancellationToken ct = default);
    Task<RepositoryResponse> ApproveOnboardingRequest(OnboardingRequestInput onboardingReq, CancellationToken ct = default);
    Task<RepositoryResponse> ApproveOnboardingRequestWhileOutletCreation(long requestId, CancellationToken ct = default);
    Task<List<Requests>> GetOpenRequestsForOutletUpdation(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<ApprovalRequestDto> GetApprovalRequestById(long requestId, ApprovalEngineRequesType requestType, long companyId);
    Task<List<ApprovalRequestDetailsDto>> GetApprovalRequests(bool showArchieved, ApprovalEngineRequesType requestType, long companyId);
    Task<List<ApprovalRequestDetailsDto>> GetRequestDetailbyIds(long companyId, List<long> requestIds);
    Task<Dictionary<long, string>> GetEntityDetailsForRequest(List<long> entityIds, long companyId, ApprovalEngineRequesType requestType);
    Task<List<ApprovalRequestDetailsDto>> GetOpenApprovalRequests(ApprovalEngineRequesType requestType, long companyId);
    Task UpdateRequestToPendingAtProcessor(long companyId, long requestId, List<long> approverPositionIds, ApprovalEngineRequesType requestType);
    Task UpdateRequestToPendingAtProcessorForAdmin(long companyId, long requestId, ApprovalEngineRequesType requestType);
}
