﻿using FADashboard.Core.Interfaces.Authentication;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.DateTimeHelpers;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Helper;

public class MTDDateHelper(ICurrentUser currentUser, FAResilientHttpClient resilientHttpClient, AppConfigSettings appConfigSettings)
{
    public async Task<MTD_LMTD> GetMTDDateData(DateTime date, int? yearStartMonth = null, bool includeToday = false)
    {
        var yearStartMonthParameter = yearStartMonth == null ? "" : $"&yearStartMonth={yearStartMonth.Value}";
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/MTDLMTDDate/GetMTDLMTD?companyId={currentUser.CompanyId}&today={date:MM/dd/yyyy}&includeToday={includeToday}{yearStartMonthParameter}";
        var data = await resilientHttpClient.GetJsonAsync<MTD_LMTD>(dataUrl, appConfigSettings.reportApiToken);
        return data;
    }

    public async Task<double> GetMTDValue(long companyId, long userId, PortalUserRole userRole, DateTime date)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/MTDSalesData/MTDValueForUserAndDateUsingAllData/?companyId={companyId}&userId={userId}&userRole={userRole}&date={date:MM/dd/yyyy}";
        var result = await resilientHttpClient.GetJsonAsync<double>(dataUrl, appConfigSettings.reportApiToken);
        return result;
    }
}
