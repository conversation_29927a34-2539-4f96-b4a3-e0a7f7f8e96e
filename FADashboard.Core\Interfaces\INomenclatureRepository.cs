﻿using FADashboard.Core.Models;

namespace FADashboard.Core.Interfaces;

public interface INomenclatureRepository
{
    Task<RepositoryResponse> CreateDefaultNomenclatures(long companyId);

    Task<List<CompanyNomenclature>> GetAllNomenclature(long companyId);

    Task<Dictionary<string, CompanyNomenclature>> GetCompanyNomenclature(long companyId, List<string> nomenclatures);

    Task<RepositoryResponse> UpdateNomenclatureValues(List<CompanyNomenclature> vals, long companyId);
}
