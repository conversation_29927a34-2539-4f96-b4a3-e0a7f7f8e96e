﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class RequestApprovalUserTimelineDto
{
    public string OutletName { get; set; }

    public string BeatName { get; set; }

    public List<RequestApprovalSequenceWiseTimeline> SequenceWiseTimelines { get; set; }
}

public class RequestApprovalSequenceWiseTimeline
{
    public int Sequence { get; set; }

    public List<RequestApprovalTimelineDto> Timeline { get; set; }
}

public class RequestApprovalTimelineDto
{
    public long ApproverPositionId { get; set; }

    public long? ApproverUserId { get; set; }

    public PositionCodeLevel ApproverPositionLevel { get; set; }

    public ApprovalEngineRequestStatus RequestStatus { get; set; }
    public ApprovalEngineRequesType RequestType { get; set; }
    public long RequestId { get; set; }

    public PortalUserRole? ActionTakenByUserRole { get; set; }

    public long? ActionTakenById { get; set; }

    public string ActionTakenByUser { get; set; }

    public string Remarks { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public int Sequence { get; set; }

    public string ApproverMobileNumber { get; set; }

    public string ApproverUserName { get; set; }
    public long RequesterUserId { get; set; }
    public string RequesterUserName { get; set; }
}
