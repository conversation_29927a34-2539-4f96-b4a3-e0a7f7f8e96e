﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IHolidayRepository
{
    Task<List<RegionalHolidays>> GetAllHolidays(long companyId, bool showInactive = false);
    Task<RegionalHolidays> GetHolidaysForRegion(long regionId, int year, long companyId);
    Task<RepositoryResponse> DeactivateAllHolidaysForRegion(long regionId, int year, long companyId);
    Task<RepositoryResponse> CreateRegionalHolidays(RegionalHolidayCreateInput holiday, long companyId);
    Task<RepositoryResponse> UpdateRegionalHolidays(RegionalHolidayUpdateInput holiday, long companyId);
}
