﻿namespace FADashboard.DbStorage.MasterRepositories.Models;
public class UserTrainingProgress
{
    public int Id { get; set; }
    public long UserId { get; set; }
    public long CompanyId { get; set; }
    public long TrainingId { get; set; }
    public DateTime? VideoCompletedAt { get; set; }
    public DateTime? TaskCompletedAt { get; set; }
    public bool? VideoStatus { get; set; }
    public bool? TaskStatus { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string? CreationContext { get; set; }
}

public class UserTrainingTasksMaster
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public string VideoUrl { get; set; }
    public string ActionType { get; set; }
    public string TriggerType { get; set; }
    public bool Isdeactive { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string? CreationContext { get; set; }
}
