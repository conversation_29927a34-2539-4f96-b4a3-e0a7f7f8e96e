﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class ApiLog
{
    public ApiLog()
    {
        RequestId = Guid.NewGuid().ToString();
    }
    public string Id { get; set; }
    public string UserName { get; set; }
    public DateTime RequestTime { get; set; }
    public long RequestTimestamp { get; set; }
    public string RequestPath { get; set; }
    public string Status { get; set; }
    public int StatusCode { get; set; }
    public ApiType ApiType { get; set; }
    public string Description { get; set; }
    public string RequestId { get; set; }
    public TimeSpan Duration { get; set; }

}
