﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class NewJourneyPlan
{
    public long Id { get; set; }
    public DateTime StartDate { set; get; }
    public DateTime EndDate { set; get; }
    public bool IsRepeatable { get; set; }
    public int ForDays { get; set; }
    public long EmployeeId { set; get; }
    public string EmployeeName { set; get; }
    public string EmployeeErpId { get; set; }
    public ApprovedStatus ReviewedStatus { set; get; }
    public long? PositionCodeId { get; set; }
    public string PositionName { set; get; }
    public string PositionCodeLevel { set; get; }
    public string JourneyPlanEntity { set; get; }
    public List<NewJourneyPlanItem> NewJourneyPlanItems { set; get; }
}

public class NewJourneyPlanItem
{
    public long Id { get; set; }
    public DateTime ItemDate { set; get; }
    public long? BeatId { set; get; }
    public string BeatName { set; get; }
    public long? RouteId { set; get; }
    public string RouteName { set; get; }
    public long? JWFieldUserId { set; get; }
    public string JWFieldUserName { set; get; }
    public long? JWPositionCodeId { get; set; }
    public string JWPositionCodeName { set; get; }
    public string ReasonCategory { set; get; }
    public string Reason { set; get; }
    public JourneyPlanEntityType? EntityType { set; get; }
    public long? EntityId { set; get; }
    public List<NewJourneyPlanItemSecondary> NewJourneyPlanItemSecondary { set; get; }
}
public class NewJourneyPlanItemSecondary
{
    public long Id { get; set; }
    public DateTime ItemDate { set; get; }
    public long? BeatId { set; get; }
    public string BeatName { set; get; }
    public long? RouteId { set; get; }
    public string RouteName { set; get; }
    public long? JWFieldUserId { set; get; }
    public string JWFieldUserName { set; get; }
    public long? JWPositionCodeId { get; set; }
    public string JWPositionName { set; get; }
    public string ReasonCategory { set; get; }
    public string Reason { set; get; }
    public int Sequence { set; get; }
    public bool IsDeactive { get; set; }
}
public class NewJourneyPlanConfig
{
    public long Id { set; get; }
    public PositionCodeLevel PositionLevel { set; get; }
    public JourneyType JourneyCreationSettingType { set; get; }
}
public class NewJourneyPlanEntityConfig
{
    public long Id { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
    public List<JourneyPlanEntityType> EntityLevels { get; set; }
}

