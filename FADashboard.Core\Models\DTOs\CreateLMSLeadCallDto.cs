﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class CreateLMSLeadCallDto
    {
        [Required]
        public string Title { get; set; }

        [Required]
        public long AssignedTo { get; set; }

        [Required]
        public long PositionCode { get; set; }

        [Required]
        public long LeadId { get; set; }

        [Required]
        public long ContactId { get; set; }

        [Required]
        public LMSCallType CallType { get; set; }

        [Required]
        public LMSTaskStatus Status { get; set; }

        [Required]
        public DateTime? DueDate { get; set; }

        [Required]
        public DateTime StartTime { get; set; }

        public int? CallDuration { get; set; }

        public string Description { get; set; }
    }
}
