﻿namespace FADashboard.Core.Models;

public enum HCCBConsoleRequestType
{
    OUTLET_BEAT_VALIDATION,
    ORDER_VALIDATION,
    ORDER_STATUS_VALIDATION,
    OUTLET_ASSET_MAPPINGS,
    OUTLET_BEAT_DISTRIBUTOR_EMPLOYEE,
    TODAY_OUTLETS,
    CURRENT_ROUTE_PLAN_OUTLETS
}

public class HCCBConsoleRequest
{
    public HCCBConsoleRequestType RequestType { get; set; }
    public List<string> Inputs { get; set; } = new();
}

public class HCCBConsoleResponse
{
    public HCCBConsoleRequestType RequestType { get; set; }
    public List<HCCBValidationResult> Results { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

public class HCCBValidationResult
{
    public string Category { get; set; }
    public string Input { get; set; }
    public bool Success { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}
