﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IVanSalesRepository
{
    Task<RepositoryResponse> ActivateDeactivateVan(long vanId, long companyId, bool action);

    Task<RepositoryResponse> CreateVan(VanInput van, long companyId);

    Task<VanInput> GetVanById(long vanId, long companyId);

    Task<List<VanSales>> GetVans(long companyId, bool includeDeactivate);

    Task<RepositoryResponse> UpdateVan(VanInput van, long companyId);
    Task<RepositoryResponse> CreateVanDistMappingWithEmployee(List<long> oldEmployeeIds, long newEmployeeId, long companyId);
    Task<List<long>> GetEmployeesMappedToVans(long companyId);
    Task<List<VanSales>> GetVansForDistributors(long companyId, List<long> distributors);
    Task<List<long>> GetEmployeesForPositionIds(long companyId, List<long> positionId);

}
