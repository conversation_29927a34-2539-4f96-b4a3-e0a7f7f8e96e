﻿using FADashboard.Core.Interfaces;

namespace FADashboard.Core.Services.Console;

public class OTPService(ISendOtpRepository sendOtpRepository) : RepositoryResponse
{
    public async Task<RepositoryResponse> SendOtp() => await sendOtpRepository.SendOtp();

    public async Task<RepositoryResponse> ValidateOtp(Guid otpSeed, string otp, string reason) => await sendOtpRepository.ValidateOtp(otpSeed, otp, reason);
}
