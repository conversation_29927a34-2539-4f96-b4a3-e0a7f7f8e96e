﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class FlexibleReportRequestCreate
{
    public DistributorChannel? DistributorChannel { get; set; }
    public List<long> Stockists { get; set; }
    public List<string> AdditionalMeasures { get; set; }
    public string ChildRow { get; internal set; }
    public List<string> Columns { get; set; }
    public long CompanyId { get; set; }
    public DateRangePreset DateRangePreset { get; set; }
    public List<string> Dimensions { get; set; }
    public DateTime? EndDate { get; set; }
    public string FileName { get; set; }
    public Dictionary<string, PerspectiveMeasure> MeasurePersDictionary { get; set; }
    public List<string> Measures { get; set; }
    public string ParentRow { get; internal set; }
    public ViewPerspective PerspectiveType { get; set; }
    public string PivotColumnName { get; set; }
    public List<string> PivotValues { get; set; }
    public string QueryId { get; set; }
    public string ReportName { get; set; }
    public bool SaveFlexibleReport { get; set; }
    public string SecondaryPivot { get; set; }
    public bool ShowSubtotals { get; internal set; }
    public DateTime? StartDate { get; set; }
    public StockistType? StockistType { get; set; }

    /// <summary>
    /// Subscribed User Roles in Comma separated integer format
    /// </summary>
    public string SubscribedUserRoles { get; internal set; }

    public bool UseAttendancePivot { get; set; }
    public bool UseGrouping { get; internal set; }
    public long UserId { get; set; }
    public List<long> UserIds { get; set; }
    public List<long> UserPositionIds { get; set; }
    public int UserPositionLevel { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string ViewName { get; set; }
}
