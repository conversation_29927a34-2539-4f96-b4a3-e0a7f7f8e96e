﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IRouteOptimizationRepository
{
    Task<List<RouteOptimizationList>> GetRouteOptimization(long companyId, bool includeDeactivate);
    Task<RepositoryResponse> ActivateDeactivateRouteOptimization(long id, long companyId, bool action);
    Task<RouteOptimizationInput> GetRouteOptimizationById(long id, long companyId);
    Task<RepositoryResponse> CreateRouteOptimization(RouteOptimizationInput inputLogic, long companyId);
    Task<RepositoryResponse> UpdateRouteOptimization(RouteOptimizationInput inputLogic, long companyId);
    Task<List<WeeklyRouteOptimizationList>> GetWeeklyRouteOptimization(long companyId, bool includeDeactivate = false);
    Task<RepositoryResponse> ActivateDeactivateWeeklyRouteOptimization(long id, long companyId, bool action);
    Task<RepositoryResponse> CreateWeeklyRouteOptimization(WeeklyRouteOptimizationInput inputLogic, long companyId, RetailTimeDetailsModel retailTimeDetailsModel,VisitSegmentationModel requiredVisitInput);
}
