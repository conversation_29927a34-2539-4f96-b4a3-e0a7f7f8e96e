﻿using System.IO.Compression;
using Microsoft.AspNetCore.Http;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StorageWriter;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class ProductCategoryDivisionService(
    IPrimaryCategoryRepository primaryCategoryRepository,
    ISecondaryCategoryRepository secondaryCategoryRepository,
    ICompanySettingsRepository companySettingsRepository,
    DistributorService distributorService,
    EmployeeService employeeService,
    IProductDivRepository productDivRepository,
    BlobWriterUnknown blobWriterUnknown,
    IProductDisplayCategoryRepositiory productDisplayCategoryRepository,
    ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<RepositoryResponse> ActivateDeactivateProductCategoryDivision(long id, ProductHierarchy level, bool action)
    {
        //action = false means deactivate
        if (!action)
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var DistUsesProdDiv = companySettings.UsesProductDivision;

            switch (level)
            {
                case ProductHierarchy.Division:

                    var errorList = new ErrorList
                    {
                        Count1 = (await GetPrimaryCategoryForDivisionId(id))?.Count ?? 0,
                        Count2 = DistUsesProdDiv ? (await distributorService.GetDistributorsForProductDivision(id))?.Count ?? 0 : 0,
                        Count3 = (await employeeService.GetEmployeesForProductDivision(id, currentUser.CompanyId))?.Count ?? 0,
                    };

                    if (errorList.Count1 != 0 || errorList.Count2 != 0 || errorList.Count3 != 0)
                    {
                        return new RepositoryResponse
                        {
                            Id = id,
                            Message = $"{errorList.Count1} Primary Categories attached , {errorList.Count2} Distributors attached, {errorList.Count3} Users attached under this Product Division, Please Detach before Deactivating",
                            IsSuccess = false
                        };
                    }

                    break;

                case ProductHierarchy.PrimaryCategory:
                    var secCatCount = (await GetSecondaryCategoryForPrimaryCatId(id))?.Count ?? 0;
                    if (secCatCount != 0)
                    {
                        return new RepositoryResponse { Id = id, Message = $"{secCatCount} Secondary Categories attached under this Primary Category, Please Detach SecondaryCategory before Deactivating", IsSuccess = false };
                    }

                    break;

                case ProductHierarchy.SecondaryCategory:
                    var prodCount = (await GetProductsForSecCatId(id))?.Count ?? 0;
                    if (prodCount != 0)
                    {
                        return new RepositoryResponse { Id = id, Message = $"{prodCount} Products attached under this Secondary Category, Please Detach Products before Deactivating", IsSuccess = false };
                    }

                    break;
            }
        }

        switch (level)
        {
            case ProductHierarchy.Division:
                return await productDivRepository.ActivateDeactivateProductDivision(id, currentUser.CompanyId, action);

            case ProductHierarchy.PrimaryCategory:
                return await primaryCategoryRepository.ActivateDeactivatePrimaryCategory(id, currentUser.CompanyId, action);

            case ProductHierarchy.SecondaryCategory:
                return await secondaryCategoryRepository.ActivateDeactivateSecondaryCategory(id, currentUser.CompanyId, action);

            case ProductHierarchy.DisplayCategory:
                return await productDisplayCategoryRepository.ActivateDeactivateProductDisplayCategory(id, currentUser.CompanyId, action);

            default:
                return new RepositoryResponse { Id = 0, Message = "An Error Occurred", IsSuccess = false };
        }
    }

    private static bool IsImageFile(ZipArchiveEntry entry)
    {
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
        return allowedExtensions.Any(ext => Path.GetExtension(entry.FullName).ToLower() == ext);
    }

    private async Task ProcessImageAsync(Stream inputStream, string containerName, Guid imageGuid, string contentType)
    {
        using var memoryStream = new MemoryStream();
        await inputStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0; // Reset position

        IFormFile formFile = new FormFile(memoryStream, 0, memoryStream.Length, null, Path.GetFileName(Path.GetRandomFileName()));
        await blobWriterUnknown.UploadFileAsync(containerName, formFile, imageGuid.ToString("N"), contentType);
    }

    public async Task<ApiResponse> BulkUploadSecondaryCatImages(string containerName, IFormFile file)
    {
        using var zipStream = file.OpenReadStream();
        using var archive = new ZipArchive(zipStream, ZipArchiveMode.Read);
        var apiResponse = new ApiResponse("ProductImagesStatus");
        var maxImageSize = 204800; // Maximum image size in bytes
        var productErpCodes = archive.Entries.Select(p => Path.GetFileNameWithoutExtension(p.Name)).ToList();
        var dbProduct = await secondaryCategoryRepository.GetProductSecondaryCategoriesByErpCodes(currentUser.CompanyId, productErpCodes);
        foreach (var entry in archive.Entries)
        {
            var productErpCode = Path.GetFileNameWithoutExtension(entry.Name);
            var dbProductErp = dbProduct.FirstOrDefault(p => p.ErpId == productErpCode);
            if (dbProductErp != null && IsImageFile(entry))
            {
                if (entry.Length > maxImageSize)
                {
                    apiResponse.ResponseList.Add(
                        new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image Should be less than 200 kb", ResponseStatus = ResponseStatus.Failure });
                    apiResponse.ResponseStatusCount.Failed++;
                }
                else
                {
                    var imageGuid = System.Guid.NewGuid();
                    try
                    {
                        await ProcessImageAsync(entry.Open(), containerName, imageGuid, "image/jpg");
                        dbProductErp.Image = imageGuid;
                        apiResponse.ResponseList.Add(
                            new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image Uploaded Successfully", ResponseStatus = ResponseStatus.Success });
                        apiResponse.ResponseStatusCount.Updated++;
                        await secondaryCategoryRepository.UpdateSecondaryCatImages(dbProduct, currentUser.CompanyId);
                    }
                    catch
                    {
                        apiResponse.ResponseList.Add(
                            new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image Upload Failed", ResponseStatus = ResponseStatus.Failure });
                        apiResponse.ResponseStatusCount.Failed++;
                    }
                }
            }
            else
            {
                apiResponse.ResponseList.Add(
                    new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image with Wrong Extension or Invalid Name", ResponseStatus = ResponseStatus.Failure });
                apiResponse.ResponseStatusCount.Failed++;
            }
        }
        return apiResponse;
    }

    public async Task<ApiResponse> BulkUploadPrimaryCatImages(string containerName, IFormFile file)
    {
        using var zipStream = file.OpenReadStream();
        using var archive = new ZipArchive(zipStream, ZipArchiveMode.Read);
        var apiResponse = new ApiResponse("ProductImagesStatus");
        var maxImageSize = 204800; // Maximum image size in bytes
        var productErpCodes = archive.Entries.Select(p => Path.GetFileNameWithoutExtension(p.Name)).ToList();
        var dbProduct = await primaryCategoryRepository.GetProductPrimaryCategoriesByErpCodes(currentUser.CompanyId, productErpCodes);
        foreach (var entry in archive.Entries)
        {
            var productErpCode = Path.GetFileNameWithoutExtension(entry.Name);
            var dbProductErp = dbProduct.FirstOrDefault(p => p.ErpId == productErpCode);
            if (dbProductErp != null && IsImageFile(entry))
            {
                if (entry.Length > maxImageSize)
                {
                    apiResponse.ResponseList.Add(
                        new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image Should be less than 200 kb", ResponseStatus = ResponseStatus.Failure });
                    apiResponse.ResponseStatusCount.Failed++;
                }
                else
                {
                    var imageGuid = System.Guid.NewGuid();
                    try
                    {
                        await ProcessImageAsync(entry.Open(), containerName, imageGuid, "image/jpg");
                        dbProductErp.Image = imageGuid;
                        apiResponse.ResponseList.Add(
                            new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image Uploaded Successfully", ResponseStatus = ResponseStatus.Success });
                        apiResponse.ResponseStatusCount.Updated++;
                        await primaryCategoryRepository.UpdatePrimaryCatImages(dbProduct, currentUser.CompanyId);
                    }
                    catch
                    {
                        apiResponse.ResponseList.Add(
                            new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image Upload Failed", ResponseStatus = ResponseStatus.Failure });
                        apiResponse.ResponseStatusCount.Failed++;
                    }
                }
            }
            else
            {
                apiResponse.ResponseList.Add(
                    new ApiResponseMessage { ERPId = productErpCode, Message = "Product Image with Wrong Extension or Invalid Name", ResponseStatus = ResponseStatus.Failure });
                apiResponse.ResponseStatusCount.Failed++;
            }
        }
        return apiResponse;
    }

    public async Task<RepositoryResponse> CreateUpdateProductCategoryDivision(ProductCategoryDivision pcd)
    {
        if (!string.IsNullOrWhiteSpace(pcd.ErpId))
        {
            if (pcd.ProductPrimaryCategoryId is not 0 and not null)
            {
                var activeERPs = (await secondaryCategoryRepository.GetSecCategoriesErpMin(currentUser.CompanyId, true)).Where(p => p.Id != pcd.Id).Select(p => p.ErpId.NormalizeCaps()).ToList();

                if (activeERPs.Contains(pcd.ErpId, StringComparer.OrdinalIgnoreCase))

                {
                    return GetRejectResponse(pcd.Id, "Please enter a unique ERP Id for the Secondary Category");
                }
            }

            if (pcd.ProductDivisionId is not 0 and not null)
            {
                if (!string.IsNullOrWhiteSpace(pcd.ErpId))
                {
                    var activeERPs = (await primaryCategoryRepository.GetPrimaryCategoriesErpMin(currentUser.CompanyId, true)).Where(p => p.Id != pcd.Id).Select(p => p.ErpId.NormalizeCaps()).ToList();

                    if (activeERPs.Contains(pcd.ErpId, StringComparer.OrdinalIgnoreCase))

                    {
                        return GetRejectResponse(0, "Please enter a unique ERP Id for the Primary Category");
                    }
                }
            }

            if (pcd.ProductSecondaryCategoryId is not 0 and not null)
            {
                if (!string.IsNullOrWhiteSpace(pcd.ErpId))
                {
                    var activeERPs = (await productDisplayCategoryRepository.GetProductDisplayCategoriesMin(currentUser.CompanyId, true)).Where(p => p.Id != pcd.Id).Select(p => p.ErpId.NormalizeCaps()).ToList();

                    if (activeERPs.Contains(pcd.ErpId, StringComparer.OrdinalIgnoreCase))

                    {
                        return GetRejectResponse(0, "Please enter a unique ERP Id for the Display Category");
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(pcd.ErpId))
            {
                var activeERPs = (await productDivRepository.GetProductDivisionMin(currentUser.CompanyId, true)).Where(p => p.Id != pcd.Id).Select(p => p.ErpId.NormalizeCaps()).ToList();

                if (activeERPs.Contains(pcd.ErpId, StringComparer.OrdinalIgnoreCase))

                {
                    return GetRejectResponse(0, "Please enter a unique ERP Id for the Product Division");
                }
            }
        }

        if (pcd.Id == 0)
        {
            if (pcd.ProductPrimaryCategoryId is not 0 and not null)
            {
                return await secondaryCategoryRepository.CreateSecondaryCat(pcd, currentUser.CompanyId);
            }

            if (pcd.ProductDivisionId is not 0 and not null)
            {
                return await primaryCategoryRepository.CreatePrimaryCat(pcd, currentUser.CompanyId);
            }

            if (pcd.ProductSecondaryCategoryId is not 0 and not null)
            {
                return await productDisplayCategoryRepository.CreateProductDisplayCategory(pcd, currentUser.CompanyId);
            }

            return await productDivRepository.CreateProductDivision(pcd, currentUser.CompanyId);
        }

        if (pcd.ProductPrimaryCategoryId is not 0 and not null)
        {
            return await secondaryCategoryRepository.UpdateSecondaryCat(pcd, currentUser.CompanyId);
        }

        if (pcd.ProductDivisionId is not 0 and not null)
        {
            return await primaryCategoryRepository.UpdatePrimaryCat(pcd, currentUser.CompanyId);
        }

        if (pcd.ProductSecondaryCategoryId is not 0 and not null)
        {
            return await productDisplayCategoryRepository.UpdateProductDisplayCategory(pcd, currentUser.CompanyId);
        }

        return await productDivRepository.UpdateProductDivision(pcd, currentUser.CompanyId);
    }

    public async Task<List<ProductCategoryDivision>> GetDetailedPrimaryCategory(bool inludeDeactive = false)
    {
        var list = await primaryCategoryRepository.GetDetailedPrimaryCategories(currentUser.CompanyId, inludeDeactive);
        return list;
    }

    public async Task<List<ProductCategoryDivision>> GetDetailedProductCategoryDivisions(bool inludeDeactive)
    {
        var divisions = await productDivRepository.GetProductDivisionDetailedList(currentUser.CompanyId, inludeDeactive);
        var primaryCategoryDivisions = await primaryCategoryRepository.GetDetailedPrimaryCategories(currentUser.CompanyId, inludeDeactive);
        var secondaryCategoryDivisions = await secondaryCategoryRepository.GetDetailedSecondaryCategories(currentUser.CompanyId, inludeDeactive);
        var productDisplayCategoryDivisions = await productDisplayCategoryRepository.GetDetailedProductDisplayCategories(currentUser.CompanyId, inludeDeactive);

        var list = new List<ProductCategoryDivision>();
        list.AddRange(divisions);
        list.AddRange(primaryCategoryDivisions);
        list.AddRange(secondaryCategoryDivisions);
        list.AddRange(productDisplayCategoryDivisions);
        return list;
    }

    public async Task<List<ProductCategoryDivision>> GetDetailedSecondaryCategory(bool inludeDeactive = false)
    {
        var list = await secondaryCategoryRepository.GetDetailedSecondaryCategories(currentUser.CompanyId, inludeDeactive);
        return list;
    }

    public async Task<ProductCategoryDivision> GetPrimaryCategoryById(long id)
    {
        var primaryCategoryDivisions = await primaryCategoryRepository.GetPrimaryCategoryById(currentUser.CompanyId, id);
        return primaryCategoryDivisions;
    }

    public async Task<List<ProductCategoryDivision>> GetPrimaryCategoryForDivisionId(long divId)
    {
        var divisions = await primaryCategoryRepository.GetPrimaryCategoryForDivisionId(currentUser.CompanyId, divId);
        return divisions;
    }

    public async Task<ProductCategoryDivision> GetProductDivisionById(long id)
    {
        var divisions = await productDivRepository.GetProductDivisionById(currentUser.CompanyId, id);
        return divisions;
    }

    public async Task<List<ProductCategoryDivision>> GetProductsForSecCatId(long secCatId)
    {
        var divisions = await primaryCategoryRepository.GetProductsForSecCatId(currentUser.CompanyId, secCatId);
        return divisions;
    }

    public async Task<ProductCategoryDivision> GetSecondaryCategoryById(long id)
    {
        var secondaryCategoryDivisions = await secondaryCategoryRepository.GetSecondaryCategoryById(currentUser.CompanyId, id);
        return secondaryCategoryDivisions;
    }

    public async Task<ProductCategoryDivision> GetProductDisplayCategoryById(long id)
    {
        var displayCategoryDivisions = await productDisplayCategoryRepository.GetProductDisplayCategoryById(currentUser.CompanyId, id);
        return displayCategoryDivisions;
    }

    public async Task<List<ProductCategoryDivision>> GetSecondaryCategoryForPrimaryCatId(long primCatId)
    {
        var divisions = await primaryCategoryRepository.GetSecondaryCategoryForPrimaryCatId(currentUser.CompanyId, primCatId);
        return divisions;
    }
}
