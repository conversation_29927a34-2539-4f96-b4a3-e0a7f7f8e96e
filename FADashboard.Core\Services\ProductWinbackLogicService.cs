﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class ProductWinbackLogicService(IProductWinbackLogicRepository productWinbackRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<ProductWinbackList>> GetAllProductWinbackLogics(bool showDeactive) => await productWinbackRepository.GetAllProductWinbackLogics(currentUser.CompanyId, showDeactive);

    public async Task<ProductWinbackInput> GetProductWinbackLogicById(long id) => await productWinbackRepository.GetProductWinbackLogicById(currentUser.CompanyId, id);

    public async Task<RepositoryResponse> ActivateDeactivateWinbackLogic(long Id, bool action) => await productWinbackRepository.ActivateDeactivateWinbackLogic(Id, action, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateUpdateProductWinbackLogic(ProductWinbackInput productWinbackInput)
    {
        if (productWinbackInput.Id > 0)
        {
            return await productWinbackRepository.UpdateProductWinbackLogic(productWinbackInput, currentUser.CompanyId);
        }

        return await productWinbackRepository.CreateProductWinbackLogic(productWinbackInput, currentUser.CompanyId);
    }
}
