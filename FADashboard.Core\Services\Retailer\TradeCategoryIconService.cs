﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Services.Retailer;

public class TradeCategoryIconService(ITradeCategoryIconRepository tradeCategoryIconRepository, ICurrentUser currentUser)
{
    public async Task<List<EntityMin>> GetProductPrimaryCategoryMin()
    {
        var data = await tradeCategoryIconRepository.GetProductPrimaryCategoryMin(currentUser.CompanyId);
        return data;
    }

    public async Task<RepositoryResponse> UpsertTradeCategory(TradeCategoryIcon tradeCategoryIcon)
    {
        if (tradeCategoryIcon.Id == 0)
        {
            var data = await tradeCategoryIconRepository.CreateTradeCategory(tradeCategoryIcon, currentUser.CompanyId);
            return data;
        }
        else
        {
            var data = await tradeCategoryIconRepository.UpdateTradeCategory(tradeCategoryIcon, currentUser.CompanyId);
            return data;
        }
    }

    public async Task<RepositoryResponse> DeactivateTradeCategory(long categoryId)
    {
        var data = await tradeCategoryIconRepository.DeactivateTradeCategory(categoryId, currentUser.CompanyId);
        return data;
    }

    public async Task<TradeCategoryIcon> GetTradeCategoryById(long id)
    {
        var data = await tradeCategoryIconRepository.GetTradeCategoryById(id, currentUser.CompanyId);
        return data;
    }

    public async Task<List<TradeCategoryIcon>> GetTradeCategoryList()
    {
        var data = await tradeCategoryIconRepository.GetTradeCategoryList(currentUser.CompanyId);
        return data;
    }
}
