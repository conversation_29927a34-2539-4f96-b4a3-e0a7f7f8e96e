﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

// important model.Change to be done carefully
public class ProductPricingInput
{
    public PricingMastertype PricingType { get; set; }
    public List<long> RegionIds { get; set; }
    public StockistType? DistributorType { get; set; }
    public List<long> DistributorIds { get; set; } // Based on Distributor Type
    public long? DistributorSegmentationId { get; set; }
    public long? DistributorChannelId { get; set; }
    [Required]
    public List<long> ProductIds { get; set; }
    public decimal? PTRMargin { get; set; }   // Retailer margin
    public decimal? PTDMargin { get; set; }  // stockist margin
    public decimal? PTSSMargin { get; set; } // super stockist margin
    public decimal? PTSubMargin { get; set; } // sub stockist margin
    public bool IsSuperUnits { get; set; }
    public bool IsPTRIncrease { get; set; }
    public bool IsPTRAmount { get; set; }
    public decimal? PTRValue { get; set; }   // Retailer
    public bool IsPTDIncrease { get; set; }
    public bool IsPTDAmount { get; set; }
    public decimal? PTDValue { get; set; }  // stockist
    public bool IsPTSSIncrease { get; set; }
    public bool IsPTSSAmount { get; set; }
    public decimal? PTSSValue { get; set; } // super stockist
    public bool IsPTSubIncrease { get; set; }
    public bool IsPTSubAmount { get; set; }
    public decimal? PTSubValue { get; set; } // sub stockist
}
