﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ExternalAssetData
{
    public long Id { get; set; }
    public string AssetName { get; set; }
    public string AssetURL { get; set; }
    public DateTime? startDate { get; set; }
    public DateTime? endDate { get; set; }
    public ExternalAssetGeography filterOn { get; set; }
    public List<long> filterIds { get; set; }
    public List<long> channelIds { get; set; }
    public List<long> shopTypeIds { get; set; }
    public List<long> segmentation { get; set; }
    public bool? isFocused { get; set; }
    public List<long> chainIds { get; set; }
    public List<long> customTag { get; set; }
    public bool? isNoTag { get; set; }
    public string Image { get; set; }
    public List<string> UserPlatform { get; set; }
}
