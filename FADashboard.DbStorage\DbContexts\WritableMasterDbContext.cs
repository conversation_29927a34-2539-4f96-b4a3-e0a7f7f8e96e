﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.DbStorage.MasterRepositories.Models;
using FADashboard.DbStorage.Models;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.DbContexts;

public class WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options, ICurrentUser identity) : DbContextAbstract(options, identity)
{
    public DbSet<AdminFactoryMappings> AdminFactoryMappings { get; set; }
    public DbSet<MasterRepositories.Models.AdvanceLeaveSubmission> AdvanceLeaveSubmission { get; set; }
    public DbSet<AppConfig> AppConfig { get; set; }
    public DbSet<AssetDefinition> AssetDefinitions { get; set; }
    public DbSet<AssetOutletMappings> AssetOutletMappings { get; set; }
    public DbSet<AssetRuleDefinition> AssetRuleDefinitions { get; set; }
    public DbSet<AssortedProductRule> AssortedProductRules { get; set; }
    public DbSet<AssortedProductRulePositionCodeMapping> AssortedProductRulePositionCodeMappings { get; set; }
    public DbSet<AssortedProductSKUDetails> AssortedProductSKUDetails { get; set; }
    public DbSet<AttendanceNorm> AttendanceNorms { get; set; }
    public DbSet<AttendanceNormPolicies> AttendanceNormPolicies { get; set; }
    public DbSet<AttendanceTargetForTeams> AttendanceTargetForTeams { get; set; }
    public DbSet<BeatOMeterRule> BeatOMeterRules { get; set; }
    public DbSet<BeatOMeterRuleForSegmentation> BeatOMeterRuleForSegmentations { get; set; }
    public DbSet<BIReportSubscriptions> BIReportSubscriptions { get; set; }
    public DbSet<CarouselBanners> CarouselBanners { get; set; }
    public DbSet<DynamicCarouselBanner> DynamicCarouselBanners { get; set; }
    public DbSet<CESSCategoryTax> CESSCategoryTaxes { get; set; }
    public DbSet<Channels> Channels { get; set; }
    public DbSet<ChartsUsageLog> ChartsUsageLogs { get; set; }
    public DbSet<ChartVizDetails> ChartVizDetails { get; set; }
    public DbSet<ClientEmployee> ClientEmployees { get; set; }
    public DbSet<Cohort> Cohorts { get; set; }
    public DbSet<CoinsforKpi> CoinsforKpi { get; set; }
    public DbSet<Company> Companies { get; set; }
    public DbSet<CompanyAdmin> CompanyAdmins { get; set; }
    public DbSet<CompanyAppMeta> CompanyAppMetas { get; set; }
    public DbSet<CompanyExternalMetrices> CompanyExternalMetrices { get; set; }
    public DbSet<CompanyFactory> CompanyFactories { get; set; }
    public DbSet<CompanyKPI> CompanyKPIs { get; set; }
    public DbSet<CompanyModules> CompanyModules { get; set; }
    public DbSet<CompanyModulesMapping> CompanyModulesMapping { get; set; }
    public DbSet<CompanyNomenclature> CompanyNomenclatures { get; set; }
    public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMappings { get; set; }
    public DbSet<CompanyProduct> Products { get; set; }
    public DbSet<CompanySetting> CompanySettings { get; set; }
    public DbSet<CompanySettingValue> CompanySettingValues { get; set; }
    public DbSet<CouponMaster> CouponMasters { get; set; }
    public DbSet<CueCardsMaster> CueCards { get; set; }
    public DbSet<CustomReport> CustomReports { get; set; }
    public DbSet<CustomReportItem> CustomReportItems { get; set; }
    public DbSet<DerivedKPIs> DerivedKpis { get; set; }
    public DbSet<MasterRepositories.Models.Designation> Designation { get; set; }
    public DbSet<DeviceConnection> DeviceConnections { get; set; }
    public DbSet<Distributor> Distributors { get; set; }
    public DbSet<DistributorAddress> DistributorAddresses { get; set; }
    public DbSet<DistributorBeatMapping> DistributorBeatMappings { get; set; }
    public DbSet<DistributorChannels> DistributorsChannels { get; set; }
    public DbSet<DistributorFactoryMappings> DistributorFactoryMappings { get; set; }
    public DbSet<DistributorKycDocument> DistributorKycDocuments { get; set; }
    public DbSet<DistributorProductDivisionMapping> DistributorProductDivisionMapping { get; set; }
    public DbSet<DistributorSegmentations> DistributorsSegmentations { get; set; }
    public DbSet<DMSNotification> DMSNotifications { get; set; }
    public DbSet<DumpReportRequest> DumpReportRequests { get; set; }
    public DbSet<EmailSchedule> EmailSchedules { get; set; }
    public DbSet<EmployeeAdvanceLeaves> AdvanceLeaves { get; set; }
    public DbSet<EmployeeProductDivisionMapping> EmployeeProductDivisionMapping { get; set; }
    public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }
    public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }
    public DbSet<EmployeeTourPlanItemsSecondary> EmployeeTourPlanItemsSecondary { get; set; }
    public DbSet<EntityMarginSlab> EntityMarginSlabs { get; set; }
    public DbSet<EquipmentMaster> EquipmentMaster { get; set; }
    public DbSet<FACompanyExternalAsset> FACompanyExternalAssets { get; set; }
    public DbSet<FACompanyExternalAssetFilter> FACompanyExternalAssetFilters { get; set; }
    public DbSet<FAISMapping> FAISMappings { get; set; }
    public DbSet<FAISWorkflow> FAISWorkflows { get; set; }
    public DbSet<FAOutletUpdationRequest> OutletUpdationRequests { set; get; }
    public DbSet<FATradeCarouselBanner> TradeCarouselBanners { get; set; }
    public DbSet<FATradeCategoryIcon> TradeCategoryIcons { get; set; }
    public DbSet<FATradeUser> FATradeUsers { get; set; }
    public DbSet<FlexibleReport> FlexibleReports { get; set; }
    public DbSet<FocusedProductRule> FocusedProductRules { get; set; }
    public DbSet<FocusedProductRulePositionCodeMapping> FocusedProductRulePositionCodeMappings { get; set; }
    public DbSet<Game> Games { get; set; }
    public DbSet<Geographies> Geographies { get; set; }
    public DbSet<GlobalOutletMetric> GlobalOutletMetrices { get; set; }
    public DbSet<GSTCategoryTax> GSTCategoryTaxes { get; set; }
    public DbSet<Holidays> Holidays { get; set; }
    public DbSet<IdsLogins> IdsLogins { get; set; }
    public DbSet<ImageRecognitionLogic> ImageRecognitionLogics { get; set; }
    public DbSet<InternationalDiscounts> InternationalDiscounts { get; set; }
    public DbSet<ISROutletMapping> ISROutletMappings { get; set; }
    public DbSet<JourneyCalendar> JourneyCalendars { get; set; }
    public DbSet<JourneyCycle> JourneyCycles { get; set; }
    public DbSet<JourneyPlanConfigurations> JourneyPlanConfigurations { get; set; }
    public DbSet<JourneyWeek> JourneyWeeks { get; set; }
    public DbSet<LMSAccount> LMSAccounts { get; set; }
    public DbSet<LMSAccountContact> LMSAccountContacts { get; set; }
    public DbSet<LMSAccountAddress> LMSAccountAddresses { get; set; }
    public DbSet<LMSAccountTemplate> LMSAccountTemplates { get; set; }
    public DbSet<LMSAccountNote> LMSAccountNotes { get; set; }
    public DbSet<LMSCompanyLeadStage> LMSCompanyLeadStages { get; set; }
    public DbSet<LMSCompanyLeadTemplate> LMSCompanyLeadTemplates { get; set; }
    public DbSet<LMSGlobalLeadStage> LMSGlobalLeadStages { get; set; }
    public DbSet<LMSGlobalLeadTemplate> LMSGlobalLeadTemplates { get; set; }
    public DbSet<LMSLead> LMSLeads { get; set; }
    public DbSet<LMSLeadContact> LMSLeadContacts { get; set; }
    public DbSet<LMSLeadSource> LMSLeadSources { get; set; }
    public DbSet<LMSCustomField> LMSCustomFields { get; set; }
    public DbSet<LMSCustomFieldValue> LMSCustomFieldValues { get; set; }
    public DbSet<KPI> KPIs { get; set; }
    public DbSet<KPISlab> KPISlabs { get; set; }
    public DbSet<KPITriggers> FaEngageKPITriggers { get; set; }
    public DbSet<Location> Locations { get; set; }
    public DbSet<LocationBeat> LocationBeats { get; set; }
    public DbSet<ManagerAlerts> ManagerAlerts { get; set; }
    public DbSet<ManagerAppPointerMappings> ManagerAppPointerMappings { get; set; }
    public DbSet<MotorableDistanceCalculationAPIs> MotorableDistanceCalculationAPIs { get; set; }
    public DbSet<MTOutletFields> MTOutletFields { get; set; }
    public DbSet<NewOutletFieldsAppMeta> NewOutletFieldsAppMetas { get; set; }
    public DbSet<NotificationMaster> FaEngageNotifications { get; set; }
    public DbSet<NotificationMessage> FaEngageNotificationMessages { get; set; }
    public DbSet<OfficialWorkType> OfficialWorkTypes { get; set; }
    public DbSet<OfficialWorkTypeHierarchyMapping> OfficialWorkTypeHierarchyMappings { get; set; }
    public DbSet<OrderBlock> OrderBlock { get; set; }
    public DbSet<OTPSent> OTPSent { get; set; }
    public DbSet<OutletMetric> OutletMetrices { get; set; }
    public DbSet<OutletSegmentationAttributes> OutletSegmentationAttributes { get; set; }
    public DbSet<OutletTags> OutletTags { get; set; }
    public DbSet<PerfectEntityRule> PerfectEntityRules { get; set; }
    public DbSet<PositionBeatMapping> PositionBeatMappings { get; set; }
    public DbSet<PositionCode> PositionCodes { get; set; }
    public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
    public DbSet<PositionDistributorMapping> PositionDistributorMappings { get; set; }
    public DbSet<PositionOutletMapping> PositionOutletMappings { get; set; }
    public DbSet<PositionProductDivisionMapping> PositionProductDivisionMappings { get; set; }
    public DbSet<ProductCESSCategory> ProductCESSCategories { get; set; }
    public DbSet<ProductChannelMapping> ProductChannelMapping { get; set; }
    public DbSet<ProductDisplayCategory> ProductDisplayCategory { get; set; }
    public DbSet<ProductDivision> ProductDivision { get; set; }
    public DbSet<ProductGroup> ProductGroups { get; set; }
    public DbSet<ProductGSTCategory> ProductGSTCategories { get; set; }
    public DbSet<ProductPricingMaster> ProductPricingMaster { get; set; }
    public DbSet<ProductPrimaryCategory> ProductPrimaryCategories { get; set; }
    public DbSet<ProductRecommendationLogic> ProductRecommendationLogics { get; set; }
    public DbSet<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }
    public DbSet<ProductTagMaster> ProductTagMasters { get; set; }
    public DbSet<ProductTagSuggestion> ProductTagSuggestions { get; set; }
    public DbSet<ProductWinbackLogic> ProductWinbackLogics { get; set; }
    public DbSet<QueryView> QueryViews { get; set; }
    public DbSet<MasterRepositories.Models.Regions> Regions { get; set; }
    public DbSet<RegionWiseODS> RegionWiseODS { get; set; }
    public DbSet<ReportUseDetails> ReportUseDetails { get; set; }
    public DbSet<RetoolReportSubscriptions> RetoolReportSubscriptions { get; set; }
    public DbSet<RoleBasedReportSubscription> RoleBasedReportSubscriptions { get; set; }
    public DbSet<RoleMaster> RoleMasters { get; set; }
    public DbSet<RouteOptimizationLogic> RouteOptimizationLogic { get; set; }
    public DbSet<WeeklyRouteConfiguration> WeeklyRouteConfigurations { get; set; }
    public DbSet<RouteOutletMappings> RouteOutletMappings { get; set; }
    public DbSet<RoutePositionMappings> RoutePositionMappings { get; set; }
    public DbSet<Routes> Routes { get; set; }
    public DbSet<Scheme> Scheme { get; set; }
    public DbSet<SchemeBucket> SchemeBuckets { get; set; }
    public DbSet<SchemeSlab> SchemeSlab { get; set; }
    public DbSet<SecondaryOrderSummaryPdfConfigs> SecondaryOrderSummaryPdfConfigs { get; set; }
    public DbSet<VanOrderPdfConfigs> VanOrderPdfConfigs { get; set; }
    public DbSet<DistributorOrderPdfConfigs> DistributorOrderPdfConfigs { get; set; }
    public DbSet<PrimaryOrderPdfConfigs> PrimaryOrderPdfConfigs { get; set; }
    public DbSet<SegmentationForAssortedProductRule> SegmentationForAssortedProductRule { get; set; }
    public DbSet<ShopTypes> ShopTypes { get; set; }
    public DbSet<ShopTypesForAssortedProductRule> ShopTypesForAssortedProductRule { get; set; }
    public DbSet<Survey> SurveyForms { get; set; }
    public DbSet<SurveyToCompanyZoneMap> SurveyToCompanyZoneMaps { get; set; }
    public DbSet<TargetForTeams> TargetForTeams { get; set; }
    public DbSet<TaskManagementFocusArea> TaskManagementFocusAreas { get; set; }
    public DbSet<TaskManagementTask> TaskManagementTasks { get; set; }
    public DbSet<TaskManagementUserFocusArea> TaskManagementUserFocusAreas { get; set; }
    public DbSet<Teams> Teams { get; set; }
    public DbSet<TeamUserMappings> TeamUserMappings { get; set; }
    public DbSet<Territory> Territories { get; set; }
    public DbSet<TradeNewLaunchProduct> TradeNewLaunchProducts { get; set; }
    public DbSet<UserLoginActivity> UserLoginActivities { get; set; }
    public DbSet<UserWiseCustomReport> UserWiseCustomReports { get; set; }
    public DbSet<UserWiseCustomReportItem> UserWiseCustomReportItems { get; set; }
    public DbSet<VanDistributorMapping> VanDistributorMapping { get; set; }
    public DbSet<VanSales> VanSales { get; set; }
    public DbSet<PmsRule> PmsRules { get; set; }
    public DbSet<UserAppControl> UserAppControls { get; set; }
    public DbSet<Watchlist> Watchlists { get; set; }
    public DbSet<Zone> Zones { get; set; }
    public DbSet<CompanyFactoryStock> CompanyFactoryStocks { get; set; }
    public DbSet<ProductToEmptyMapping> ProductToEmptyMapping { get; set; }
    public DbSet<EmptyMaster> EmptyMaster { get; set; }
    public DbSet<Catalog> Catalogs { get; set; }
    public DbSet<ProductFactoryDistributorMapping> ProductFactoryDistributorMappings { get; set; }
    public DbSet<UserModuleConfiguration> UserModuleConfigurations { get; set; }
    public DbSet<CallToAction> CallToActions { get; set; }
    public DbSet<CompanyTargetSubscription> CompanyTargetSubscriptions { get; set; }
    public DbSet<TargetMaster> TargetMaster { get; set; }
    public DbSet<OutletOnboardingDetails> OutletOnboardingDetails { get; set; }
    public DbSet<DMSClaim> DMSClaims { get; set; }
    public DbSet<BeatometerRule> BeatometerRule { get; set; }
    public DbSet<BeatometerRuleDetails> BeatometerRuleDetails { get; set; }
    public DbSet<WhiteListedIP> WhiteListedIPs { get; set; }
    public DbSet<RequestApprovalRule> RequestApprovalRules { get; set; }
    public DbSet<RequestApprovalTimeline> RequestApprovalTimelines { get; set; }
    public DbSet<FAISExecutionLog> FAISExecutionLogs { get; set; }
    public DbSet<MasterRepositories.Models.AsanaBugs> AsanaBugs { get; set; }
    public DbSet<CompanyAppMetadata> CompanyAppMetadata { get; set; }
   // public DbSet<FATradeLocationTracking> FATradeLocationTrackings { get; set; }
    public DbSet<AssetType> AssetTypes { get; set; }
    public DbSet<TertiaryEntity> TertiaryEntities { get; set; }
    public DbSet<TertiaryEntityOutletMapping> TertiaryEntityOutletMappings { get; set; }
    public DbSet<RoutePlanAutomationDetail> RoutePlanAutomationDetails { get; set; }
    public DbSet<TerritoryOptimizationDetail> TerritoryOptimizationDetails { get; set; }
    public DbSet<RouteAutomationConfiguration> RouteAutomationConfigurations { get; set; }
    public DbSet<DJPSequenceDetails> DJPSequenceDetails { get; set; }
    public DbSet<DJPIncrementalDetails> DJPIncrementalDetails { get; set; }
    public DbSet<RoutePlanManualDJPDetail> RoutePlanManualDJPDetails { get; set; }
    public DbSet<ProductVisibilityRule> ProductVisibilityRules { get; set; }
    public DbSet<EffectivePcKpiRules> EffectivePcKpiRules { get; set; }
    public DbSet<RetailerConnectPromotionConfiguration> RetailerConnectPromotionConfigurations { get; set; }
    public DbSet<FilterConstraintDetails> FilterConstraintDetail { get; set; }
    public DbSet<DistributorWiseTaxIntegration> DistributorWiseTaxIntegration { get; set; }
    public DbSet<PerfectEntityRuleCriteria> PerfectEntityRuleCriterias { get; set; }
    public DbSet<ApprovalEntities> ApprovalEntities { get; set; }
    public DbSet<ApprovalEntityConfig> ApprovalEntityConfig { get; set; }

    public DbSet<FABuyerSellerMapping> FABuyerSellerMappings { get; set; }


    public DbSet<BeatCapping> BeatCappings { get; set; }
    public DbSet<SubShopType> SubShopTypes { get; set; }
    public DbSet<PerfectCriteriaSlabDetail> PerfectCriteriaSlabDetails { get; set; }
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AdminFactoryMappings>().HasOne(n => n.CompanyAdmins).WithMany(m => m.AdminFactoryMappings).HasForeignKey(n => n.AdminId);
        modelBuilder.Entity<AdminFactoryMappings>().HasOne(n => n.CompanyFactories).WithMany(m => m.AdminFactoryMappings).HasForeignKey(n => n.FactoryId);
        modelBuilder.Entity<BeatOMeterRule>().ToTable("OutletReachRules");
        modelBuilder.Entity<BeatOMeterRuleForSegmentation>().ToTable("OutletReachRuleForSegmentations");
        modelBuilder.Entity<BeatPlan>().ToTable("FABeatPlans");
        modelBuilder.Entity<BeatPlanItem>().ToTable("FABeatPlanItems");
        modelBuilder.Entity<ClientEmployee>().Property(e => e.CompanyId).HasColumnName("Company");
        modelBuilder.Entity<ClientEmployee>().Property(e => e.Guid).HasColumnName("GUID");
        modelBuilder.Entity<ClientEmployee>().Property(e => e.PhoneNo).HasColumnName("ContactNo");
        modelBuilder.Entity<CompanyNomenclature>().ToTable("FACompanyNomenclatures");
        modelBuilder.Entity<CompanyNomenclatureMapping>().HasOne(n => n.CompanyNomenclature).WithMany(m => m.CompanyNomenclatureMappings).HasForeignKey(n => n.NomenclatureId);
        modelBuilder.Entity<CompanyNomenclatureMapping>().ToTable("FACompanyNomenclaturesMapping");
        modelBuilder.Entity<CompanyProduct>().ToTable("FACompanyProducts");
        modelBuilder.Entity<CompanyProduct>().ToTable(tb => tb.HasTrigger("FACompanyProductsAfterUpdate"));
        modelBuilder.Entity<CustomReportItem>().HasOne(p => p.CustomReport).WithMany(e => e.CustomReportItems).HasForeignKey(p => p.CustomReportId);
        modelBuilder.Entity<Devices>().ToTable("Devices");
        modelBuilder.Entity<Distributor>().ToTable("FADistributors");
        modelBuilder.Entity<Distributor>().ToTable(tb => tb.HasTrigger("FADistributorsAfterUpdate"));
        modelBuilder.Entity<DistributorBeatMapping>().ToTable(tb => tb.HasTrigger("DistributorBeatMappingsAfterUpdate"));
        modelBuilder.Entity<EntityMarginSlab>().ToTable("EntityMarginSlabs");
        modelBuilder.Entity<Location>().Property(e => e.Latitude).HasPrecision(18, 10);
        modelBuilder.Entity<Location>().Property(e => e.Longitude).HasPrecision(18, 10);
        modelBuilder.Entity<Location>().Property(p => p.CompanyId).HasColumnName("Company");
        modelBuilder.Entity<Location>().ToTable("F2KLocations");
        modelBuilder.Entity<Location>().ToTable(tb => tb.HasTrigger("F2KLocationsAfterUpdate"));
        modelBuilder.Entity<OrderBlockItems>().HasOne(n => n.OrderBlock).WithMany(m => m.OrderBlockItems).HasForeignKey(n => n.OrderBlockId);
        modelBuilder.Entity<PositionCodeEntityMapping>().HasOne(p => p.PositionCode).WithMany(e => e.PositionCodeEntityMappings).HasForeignKey(p => p.PositionCodeId);
        modelBuilder.Entity<ProductDisplayCategory>().ToTable("ProductDisplayCategories");
        modelBuilder.Entity<ProductDivision>().ToTable("FAProductDivision");
        modelBuilder.Entity<ProductWinbackLogic>().ToTable("FAProductWinbackLogics");
        modelBuilder.Entity<Question>().ToTable("NewSurvey_Question");
        modelBuilder.Entity<QuestionGroup>().ToTable("NewSurvey_QuestionGroup");
        modelBuilder.Entity<SurveyToCompanyZoneMap>().ToTable("FASurveyToCompanyZoneMaps");
        modelBuilder.Entity<UserWiseCustomReportItem>().HasOne(p => p.UserWiseCustomReport).WithMany(e => e.UserWiseCustomReportItems).HasForeignKey(p => p.UserWiseCustomReportId);
        modelBuilder.Entity<Zone>().ToTable("FACompanyZones");
        modelBuilder.Entity<DynamicCarouselBanner>().ToTable("DynamicCarouselBanners");
        modelBuilder.Entity<CallToAction>().ToTable("FACallToActions");
        modelBuilder.Entity<OutletOnboardingDetails>().Property(p => p.Status).HasDefaultValue(Status.Pending);
        modelBuilder.Entity<OutletOnboardingDetails>().Property(p => p.GSTRegistered).HasDefaultValue(false);
        modelBuilder.Entity<EffectivePcKpiRules>().ToTable("EffectivePcKpiRules");
        modelBuilder.Entity<RequestApprovalTimeline>().ToTable("RequestApprovalTimeline");
        modelBuilder.Entity<SubShopType>().ToTable("SubShopTypes");
        modelBuilder.Entity<CompanyProduct>().Property(x => x.PTD).HasPrecision(18, 10);
    }

    public void RejectChanges()
    {
        var entries = ChangeTracker.Entries().ToList();
        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                case EntityState.Deleted:
                    entry.State = EntityState.Modified; //Revert changes made to deleted entity.
                    entry.State = EntityState.Unchanged;
                    break;

                case EntityState.Added:
                    entry.State = EntityState.Detached;
                    break;

                case EntityState.Detached:
                    break;

                case EntityState.Unchanged:
                    break;

                default:
                    break;
            }
        }
    }

    public override int SaveChanges(bool acceptAllChangesOnSuccess) => throw new InvalidOperationException("use save changes async instead");

    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<WritableMasterDbContext>
    {
        public WritableMasterDbContext CreateDbContext(string[] args)
        {
            var builder = new DbContextOptionsBuilder<WritableMasterDbContext>();
            var connectionString = "";
            builder.UseSqlServer(connectionString);
            return new WritableMasterDbContext(builder.Options, default);
        }
    }
}
