﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class RouteOptimizationList
{
    public long Id { get; set; }
    public string LogicName { get; set; }
    public DateTime StartDateForRoute { get; set; }
    public DateTime EndDateForRoute { get; set; }
    public bool IsDeactive { get; set; }
}

public class WeeklyRouteOptimizationList
{
    public long Id { get; set; }
    public string LogicName { get; set; }
    public DateTime StartDateForRoute { get; set; }
    public DateTime EndDateForRoute { get; set; }
    public bool IsDeactive { get; set; }
}

public class RouteOptimizationInput
{
    public long? Id { get; set; }
    public string LogicName { get; set; }
    public List<long> AsmIds { get; set; }
    public List<OutletConstraints>? OutletConstraints { get; set; }
    public int MonthsToConsider { get; set; }
    public int MonthlyAvgOrderRevenueType { get; set; }
    public int VisitCountType { get; set; }
    public int RetailTimeType { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime StartDateForRoute { get; set; }
    public DateTime EndDateForRoute { get; set; }
    public int WorkingHrsInDay { get; set; }
    public long DistanceLimit { get; set; }
    public int StartingPointOfEmployeeType { get; set; }
    public int WeeklyOff { get; set; }
    public double? PrescribedRetailTime { get; set; }
    public bool IsProcessed { get; set; }
    public double? DistancePercentageThreshold { get; set; }
    public double? TimePercentageThreshold { get; set; }
    public bool? IsHomeToHomeEvaluation { get; set; }
    public long? MaxStores { get; set; }
    public long? MinStores { get; set; }
    public double? WorkUtilization { get; set; }
    public int? PreferredDayOfVisitType { get; set; }
    public int? Bufferbetweenvisit { get; set; }
}

public class OutletConstraints
{
    public string Property { get; set; }
    public List<string> PropertyValue { get; set; }
    public long? ProductDivisionId { get; set; }
}

public class WeeklyRouteOptimizationInput
{
    public long? Id { get; set; }
    public string LogicName { get; set; }
    public List<long> CohortIds { get; set; }

    public DateTime StartDateForRoute { get; set; }

    public List<TaskOutletConstraints> OutletConstraints { get; set; }

    public PreferredDayOfVisitType PreferredDayOfVisit { get; set; }

    public double? DefaultRetailTime { get; set; }

    public VisitConstraintModel DefaultVisits { get; set; }
    public int WorkingHours { get; set; }

    public double? RevisedTimeLimit { get; set; }
    public double MaxDistance { get; set; }
    public double? RevisedDistanceLimit { get; set; }
    public DayOfWeek WeekStartDay { get; set; }

    public List<int>? WeeklyOff { get; set; }

    public int IsRevenueCalculated { get; set; }

    public double TravelSpeed { get; set; }

    public int MaxStores { get; set; }

    public int MinStores { get; set; }
    public HomeLocationType HomeLocation { get; set; }

    public bool ConditionType { get; set; }

    public LocalSearchCoverageStrategy CoveragePriority { get; set; }

    public bool IsHolidayIncluded { get; set; }
    public RequiredRetailTime RetailTimeConfiguration { get; set; }
}

public class VisitConstraintModel
{
    public int MaxVisits { get; set; }

    public int MinVisits { get; set; }

    public int IdealVisits { get; set; }
}

public class TaskOutletConstraints
{
    public OutletSegmentation? Segmentation { get; set; }
    public VisitConstraintModel Visit { get; set; }
    public int? RetailTime { get; set; }
    public List<TaskConstraintModel>? Tasks { get; set; }
}

public class TaskConstraintModel
{
    public int? TaskId { get; set; }
    public double? RetailTime { get; set; }
    public List<RetailTimeConstraintModel>? AssetRetailTime { get; set; }
}

public class RetailTimeConstraintModel
{
    public double? MinVol { get; set; }
    public double? MaxVol { get; set; }
    public double? RetailTime { get; set; }
}

public class RetailTimeDetailsModel
{
    public List<SegmentationTimeModel> Segmentations { get; set; }
}

public class SegmentationTimeModel
{
    public int Segmentation { get; set; }
    public List<FocusAreaDetails> FocusAreas { get; set; }
}

public class FocusAreaDetails
{
    public long Id { get; set; }
    public double Time { get; set; }
    public List<AssetTimeDetails> Asset { get; set; }
}

public class AssetTimeDetails
{
    public List<long> Ids { get; set; }
    public double Time { get; set; }
}

public class VisitSegmentationModel
{
    public string EntityType { get; set; } = "Segmentation";
    public List<VisitModel> Entity { get; set; }
}

public class VisitBaseModel
{
    public int Max { get; set; }
    public int Min { get; set; }
    public int Avg { get; set; }
}
public class VisitModel: VisitBaseModel
{
    public int EntityValue { get; set; }
}
