﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class PositionDistributorMapping : IAuditedEntity, ICreatedEntity
{
    public virtual Company Company { get; set; }

    [ForeignKey("Company")] public long CompanyId { get; set; }

    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }
    public virtual Distributor Distributor { get; set; }

    [ForeignKey("Distributor")] public long DistributorId { get; set; }

    public long Id { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public virtual PositionCode PositionCode { get; set; }

    [ForeignKey("PositionCode")] public long PositionId { get; set; }
}
