﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class CueCardService(ICueCardRepository cueCardRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<CueCardsList>> GetCueCards(bool includeDeactive)
    {
        var cueCards = await cueCardRepository.GetCueCards(currentUser.CompanyId, includeDeactive);
        return cueCards;
    }

    public async Task<RepositoryResponse> CreateUpdateCueCard(CueCardInput input)
    {
        if (input.Id == 0)
        {
            return await cueCardRepository.CreateCueCard(currentUser.CompanyId, input);
        }

        return await cueCardRepository.UpdateCueCard(currentUser.CompanyId, input);
    }

    public async Task<CueCardInput> GetCueCardById(long cueCardID) => await cueCardRepository.GetCueCardById(cueCardID, currentUser.CompanyId);

    public async Task<RepositoryResponse> ActivateDeactivateCueCard(long cueCardID, bool action) => await cueCardRepository.ActivateDeactivateCueCard(cueCardID, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> UpdateCueCardSequences(List<CueCardSequence> cueCardSequences) => await cueCardRepository.UpdateCueCardSequences(currentUser.CompanyId, cueCardSequences);
}
