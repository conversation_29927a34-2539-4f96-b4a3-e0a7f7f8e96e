﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;
public class IncentiveConfigService(IincentiveConfig iincentiveConfig, ICurrentUser currentUser): RepositoryResponse
{
    private async Task<RepositoryResponse> IsRuleNameValid(CreateIncentiveConfig rule)
    {
        var rules = await iincentiveConfig.GetRules(currentUser.CompanyId, true);
        if (rule.Id != 0)
        {
            rules = rules.Where(r => r.Id != rule.Id).ToList();
        }
        var activeRuleNames = rules
            .Where(r => !r.IsDeactive)
            .Select(r => r.RuleName.Trim().ToLower())
            .ToList();

        var inactiveRuleNames = rules
            .Where(r => r.IsDeactive)
            .Select(r => r.RuleName.Trim().ToLower())
            .ToList();

        if (activeRuleNames.Contains(rule.Name.Trim().ToLower()))
        {
            return new RepositoryResponse
            {
                Id = rule.Id,
                ExceptionMessage = "Rule name is not unique. An active rule with the same name already exists.",
                Message = "Rule Creation/Updation Failed!",
                IsSuccess = false
            };
        }
        if (inactiveRuleNames.Contains(rule.Name.Trim().ToLower()))
        {
            return new RepositoryResponse
            {
                Id = rule.Id,
                Message = "Rule name is valid (duplicate of an inactive rule).",
                IsSuccess = true
            };
        }
        return new RepositoryResponse
        {
            Id = rule.Id,
            Message = "Rule name is unique.",
            IsSuccess = true
        };
    }
    public async Task<List<IncentiveConfig>> GetRules(bool includeDeactivate, CancellationToken ct)
    {
        var rules = await iincentiveConfig.GetRules(currentUser.CompanyId, includeDeactivate, ct);
        return rules;
    }

    public async Task<List<Cohorts>> GetCohorts(CancellationToken ct)
    {
        var data = await iincentiveConfig.GetCohorts(currentUser.CompanyId, ct);
        return data;
    }
    public async Task<List<QualifiersDetails>> GetQualifiersDetails(CancellationToken ct)
    {
        var data = await iincentiveConfig.GetQualifierDetails(currentUser.CompanyId, ct);
        return data;
    }

    public async Task<List<FaRewardsModel>> GetRewards(CancellationToken ct)
    {
        var data = await iincentiveConfig.GetRewards(currentUser.CompanyId, ct);
        return data;
    }
    public async Task<RepositoryResponse> CreateUpdateRule(CreateIncentiveConfig rule, CancellationToken ct)
    {
        var checkValid = await IsRuleNameValid(rule);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }
        if (rule.Id == 0)
        {
            return await iincentiveConfig.CreateRule(rule, currentUser.CompanyId, ct);
        }
        throw new NotImplementedException("UpdateRule functionality will be implemented in the next sprint.");

    }

}
