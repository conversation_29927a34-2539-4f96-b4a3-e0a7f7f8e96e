﻿namespace FADashboard.DbStorage.HCCBRepositories.Models
{
    public class CustomerMasterSalesArea
    {
        public long Id { get; set; }
        public string Asm_name { get; set; }
        public string Sap_customer_id { get; set; }
        public string Sm_id { get; set; }
        public string Active { get; set; }
        public string Customer_order_block { get; set; }
        public string Se_id { get; set; }
        public string Sm_name { get; set; }
        public string Customer_direct_indirect_flag { get; set; }
        public string Se_name { get; set; }
        public string Type { get; set; }
        public string Pricelist_code { get; set; }
        public string Marketarea_name { get; set; }
        public DateTime Sfa_processeddate { get; set; }
        public string Asm_id { get; set; }
        public string Sfa_status { get; set; }
        public string Distribution_channel { get; set; }
        public string Site_id { get; set; }
        public string Sales_org { get; set; }
        public string Parent_sap_customer_site_id { get; set; }
        public string Marketarea_id { get; set; }
        public string Division_code { get; set; }
        public long CompanyId { get; set; }
        public string CreationContext { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsProcessed { get; set; }
        public string UniqueKey { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public bool IsPositionProcessed { get; set; }
        public bool IsUserPositionProcessed { get; set; }
        public bool IsPositionBeatProcessed { get; set; }
        public bool IsGeographyProcessed { get; set; }
        public bool IsDistributorProcessed { get; set; }
        public bool IsOutletProcessed { get; set; }
        public bool IsDistributorBeatMappingProcessed { get; set; }
    }
}
