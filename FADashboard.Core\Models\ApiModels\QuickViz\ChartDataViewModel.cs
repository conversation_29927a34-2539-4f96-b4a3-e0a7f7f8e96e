﻿using Libraries.CommonEnums;
using Library.NumberSystem;

namespace FADashboard.Core.Models.ApiModels.QuickViz;

public class ChartDataViewModel : ChartErrors
{
    public ChartDataViewModel()
    {
        CardSpan = 1;
    }

    public long Id { get; set; }
    public string Name { get; set; }
    public long QueryId { get; set; }
    public string QueryName { get; set; }
    public ChartType ChartType { get; set; }
    public string Dimension { get; set; }
    public string Measure { get; set; }
    public string Measure1 { get; set; }
    public string Measure2 { get; set; }
    public string Measure3 { get; set; }
    public string Measure4 { get; set; }
    public List<string> Measures { get; set; }
    public List<string> MonthlyMeasures { get; set; }
    public List<string> Dimensions { get; set; }
    public string Dimension1 { get; set; }
    public int CardSpan { get; set; }
    public string PresetTimePeriod { get; set; }
    public List<Dictionary<string, FormattedData>> ChartData { get; set; }
    public string DimensionNomenclature { get; set; }
    public string MeasureNomenclature { get; set; }
    public string MeasureNomenclature1 { get; set; }
    public string MeasureNomenclature2 { get; set; }
    public string MeasureNomenclature3 { get; set; }
    public string MeasureNomenclature4 { get; set; }
    public List<string> MeasureNomenclatures { get; set; }
    public List<string> DimensionNomenclatures { get; set; }
    public string Aggregation { get; set; }
    public string Aggregation1 { get; set; }
    public string Aggregation2 { get; set; }
    public string Aggregation3 { get; set; }
    public string Aggregation4 { get; set; }
    public List<string> Aggregations { get; set; }
    public string ComparisonType { get; set; }
    public string ComparisonTimePeriod { get; set; }
    public List<string> SortedColumns { get; set; }
    public List<string> SortedColumnsNomenclature { get; set; }
    public List<string> SortedRows { get; set; }
    public Dictionary<string, TotalValues> TotalDictionary { get; set; }
    public List<FormattedData> TotalValuesList { get; set; }
    public Dictionary<string, string> EmpHierarchyFilters { get; set; }
    public Dictionary<string, string> GeoHierarchyFilters { get; set; }
    public Dictionary<string, string> MTHierarchyFilters { get; set; }
    public bool InAscendingOrder { get; set; }
    public bool IsFavorite { get; set; }
    public bool IsDrillDownValid { get; set; }
    public ViewPerspective ChartPerspective { get; set; }
    public int DrillDownLevel { get; set; }
    public bool IsNewDashboard { get; set; }
}

public class ChartErrors
{
    public bool IsError { get; set; }
    public string ErrorMgs { get; set; }
}

public class TotalValues
{
    public FormattedData Total1 { get; set; }
    public FormattedData Total2 { get; set; }
    public FormattedData Total3 { get; set; }
    public FormattedData Total4 { get; set; }
    public FormattedData Total5 { get; set; }
}
