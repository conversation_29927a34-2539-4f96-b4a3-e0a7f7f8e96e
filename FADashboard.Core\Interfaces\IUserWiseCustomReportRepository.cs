﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IUserWiseCustomReportRepository
{
    Task<UserWiseCustomReport> GetUserWiseCustomReport(long companyId, long employeeId, long customReportId);

    Task<UserWiseCustomReport> GetUserWiseCustomReportItems(long customReportId, long companyId, long employeeId, PortalUserRole userRole);

    Task SaveUserWiseCustomReportRequest(long companyId, long employeeId, PortalUserRole userRole, UserWiseCustomReportRequest request, List<CustomReportItem> customReportItems);
}
