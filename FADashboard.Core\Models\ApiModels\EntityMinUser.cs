﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Models.ApiModels;

public class EntityMinUser
{
    public long UserId { get; set; }
    public string UserName { get; set; }
    public PortalUserRole UserRole { get; set; }
}

public class EntityMinUserWithStatus : EntityMinUser
{
    public bool IsActive { get; set; }
}

public class EntityMinGuidWithErp : EntityMinWithErp
{
    public Guid? Image { get; set; }
}
