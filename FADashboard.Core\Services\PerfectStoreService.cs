﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class PerfectStoreService(IPerfectStoreRepository perfectStoreRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<PerfectStoreList>> GetPerfectStoreList(bool showDeactive)
    {
        var perfectStoreList = await perfectStoreRepository.GetPerfectStoreList(currentUser.CompanyId, showDeactive);
        return perfectStoreList;
    }

    public async Task<PerfectStoreModel> GetPerfectStoreById(long id)
    {
        var flexibleTarget = await perfectStoreRepository.GetPerfectStoreById(currentUser.CompanyId, id);
        return flexibleTarget;
    }

    public async Task<RepositoryResponse> ActivateDeactivatePerfectStore(long id, bool action)
    {
        var companyKPI = await perfectStoreRepository.ActivateDeactivatePerfectStore(id, currentUser.CompanyId, action);
        return companyKPI;
    }

    public async Task<RepositoryResponse> CreateUpdatePerfectStore(PerfectStoreModel perfectStore) => await perfectStoreRepository.CreatePerfectStore(perfectStore, currentUser.CompanyId);

    public async Task<List<PerfectStoreList>> GetPerfectCallList(bool showDeactive)
    {
        var perfectCallList = await perfectStoreRepository.GetPerfectCallList(currentUser.CompanyId, showDeactive);
        return perfectCallList;
    }
}
