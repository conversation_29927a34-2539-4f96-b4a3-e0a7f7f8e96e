﻿using System.Collections.Concurrent;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;
using Library.StorageWriter.Reader_Writer;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;
using Newtonsoft.Json;


namespace FADashboard.Core.Services;

public class TerritoryOptimizationService : RepositoryResponse
{
    private const string ContainerName = "territory-optimization";
    private const int EstimationTimeHrs = 1;
    private readonly ITerritoryOptimizationRepository territoryOptimizationRepository;
    private readonly ICurrentUser currentUser;
    private readonly AppConfigSettings appConfigSettings;
    private readonly FaiDataLakeBlobWriter faiDataLakeBlobWriter;
    private readonly ICompanySettingsRepository companySettingsRepository;

    public TerritoryOptimizationService(
        ITerritoryOptimizationRepository territoryOptimizationRepository,
        ICurrentUser currentUser,
        AppConfigSettings appConfigSettings,
        FaiDataLakeBlobWriter faiDataLakeBlobWriter,
        ICompanySettingsRepository companySettingsRepository)
    {
        this.territoryOptimizationRepository = territoryOptimizationRepository;
        this.currentUser = currentUser;
        this.appConfigSettings = appConfigSettings;
        this.faiDataLakeBlobWriter = faiDataLakeBlobWriter;
        this.companySettingsRepository = companySettingsRepository;
        ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
        ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
    }

    public async Task<List<TerritoryOptimizationDetailDto>> GetTerritoryOptimizationDetails(CancellationToken ct = default)
    {
        var data = await territoryOptimizationRepository.GetTerritoryOptimizationDetails(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        return data.Select(s => new TerritoryOptimizationDetailDto
        {
            Id = s.Id,
            InputFileName = s.InputFileName,
            ExecutedAt = s.ExecutedAt,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            StatusRemark = s.StatusRemark,
            EmailId = s.EmailId,
            EstimatedTime = (s.Status is TerritoryOptimizationStatus.Executed || s.Status is TerritoryOptimizationStatus.ErrorDuringExecution) ? s.CreatedAt.AddMinutes(100).ToString("HH:mm , MMM dd") : DateTime.UtcNow.Add(offset) >= s.CreatedAt.Add(offset).AddHours(EstimationTimeHrs)
                                ? DateTime.UtcNow.Add(offset).AddMinutes(30).ToString("HH:mm , MMM dd")
                                : s.CreatedAt.Add(offset).AddHours(EstimationTimeHrs).ToString("HH:mm , MMM dd")
        }).OrderByDescending(s => s.Id).ToList();
    }

    public async Task<RepositoryResponse> CreateTerritoryOptimizationDetail(IFormFile file, TerritoryInputConstraints inputConstraints, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var country = companySettings.GetCountryName.ToLower();
        var response = await territoryOptimizationRepository.SaveTerritoryOptimizationDetail(currentUser.LocalId,
            currentUser.CompanyId, currentUser.UserRole, file.FileName, currentUser.EmailId, inputConstraints, ct);

        if (response.Id != 0)
        {
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            await faiDataLakeBlobWriter.UploadFileAsync(ContainerName, file, $"{response.Id}/{file.FileName}", contentType);
            var queueHandler = new QueueHandler<TerritoryOptimizationQueueModel>(QueueType.TerritoryOptimizationCreationQueue, appConfigSettings.StorageConnectionString);
            var queueObj = new TerritoryOptimizationQueueModel
            {
                Id = response.Id ?? 0,
                FileName = file.FileName,
                NumberOfIterations = inputConstraints.NumberOfIterations,
                NumberOfTerritories = inputConstraints.NumberOfTerritories,
                FeasibleDeviation = inputConstraints.FeasibleDeviation,
                IdealOutletCount = inputConstraints.IdealOutletCount,
                Country = country
            };
            await queueHandler.AddToQueue(queueObj);
            return response;
        }

        return response;
    }

    public async Task<Stream> DownloadFile(string filePath, CancellationToken ct = default)
    {
        var stream = await faiDataLakeBlobWriter.OpenFileReadStreamAsync(ContainerName, filePath, ct);
        return stream;
    }

    public async Task<TerritoryDataReponseModel> GetTerritoryData(long id, CancellationToken ct)
    {
        var requestDetail = await territoryOptimizationRepository.GetTerritoryOptimizationDetails(id, ct);
        if (requestDetail.Count > 0 && requestDetail.First().CompanyId == currentUser.CompanyId && requestDetail.First().Status == TerritoryOptimizationStatus.Executed)
        {
            var territoryBoundaryFilePath = $"{id}/territory_boundaries.json";
            var subTerritoryBoundaryFilePath = $"{id}/subterritory_boundaries.json";
            var territoryBoundaryTask = GetDataFromBlob<Dictionary<string, TerritoryBoundaryModel>>(territoryBoundaryFilePath, ct);
            var subTerritoryBoundaryTask = GetDataFromBlob<Dictionary<string, List<List<double>>>>(subTerritoryBoundaryFilePath, ct);

            // Wait for all tasks to complete
            await Task.WhenAll(territoryBoundaryTask);
            var territoryBoundaries = await territoryBoundaryTask;
            return new TerritoryDataReponseModel
            {
                TerritoryList = territoryBoundaries.Keys.ToList(),
                TerritoryBoundaries = await territoryBoundaryTask,
                SubterritoryBoundaries = await subTerritoryBoundaryTask
            };
        }
        else
        {
            throw new BadHttpRequestException("You are not authorized to access this request");
        }
    }

    public async Task<T> GetDataFromBlob<T>(string filePath, CancellationToken ct)
    {
        var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync(ContainerName, filePath, ct);
        using var reader = new StreamReader(stream);
        var jsonString = await reader.ReadToEndAsync(cancellationToken: ct);
        return JsonConvert.DeserializeObject<T>(jsonString);
    }

    public async Task<List<TerritoryOutletModel>> GetTerritoryOutletData(long id,List<string> territoryIds, CancellationToken ct = default)
    {
        var result = new ConcurrentBag<List<TerritoryOutletModel>>();
        var maxDegreeOfParallelism = 5;
        await Parallel.ForEachAsync(territoryIds, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism,
            CancellationToken = ct
        },
        async (territory, token) =>
        {
            var fileName = $"{id}/Territory/{territory}/territory_outlet.json";
            var territoryOutletData = await GetDataFromBlob<List<TerritoryOutletModel>>(fileName, ct);

            if (territoryOutletData != null)
            {
                result.Add(territoryOutletData);
            }
        });

        return result.SelectMany(s => s).ToList();
    }

    public async Task SaveTerritoryChanges(long id, Dictionary<string,TerritoryChangeOutletModel> outletsData, CancellationToken ct = default)
    {
        await territoryOptimizationRepository.UpdateTerritoryDetailStatus(id, currentUser.LocalId, ct);
        var outletJsonData = JsonConvert.SerializeObject(outletsData);
        var fileName = $"change-{DateTime.UtcNow.ToString("dd-MM-yyyy HH:mm:ss")}.json";
        await faiDataLakeBlobWriter.UploadJsonToBlobAsync(ContainerName, $"{id}/{fileName}", outletJsonData,ct);
        var queueHandler = new QueueHandler<TerritoryChangeQueueModel>(QueueType.TerritoryOptimizationUpdationQueue, appConfigSettings.StorageConnectionString);
        var queueObj = new TerritoryChangeQueueModel
        {
            Id = id,
            Filename = fileName
        };
        await queueHandler.AddToQueue(queueObj);
    }
}
