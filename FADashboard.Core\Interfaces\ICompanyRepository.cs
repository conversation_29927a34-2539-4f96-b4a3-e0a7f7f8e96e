﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ICompanyRepository
{
    Task<RepositoryResponse> ActionCompany(long Id, bool action = true);

    Task<bool> CompanyNameExists(CompanyRegistrationModel registrationModel);

    Task<bool> CompanyShortCodeExists(CompanyRegistrationModel registrationModel);


    Task<RepositoryResponse> CreateCompany(CompanyRegistrationModel registrationModel, CompanyAppMetaModel appMeta);
    Task<RepositoryResponse> UpdateCompany(CompanyRegistrationModel registrationModel, CompanyAppMetaModel appMeta);


    Task<Dictionary<long, CompanyNameAndLogo>> GetAllCompanyDictionary();

    Task<CompanyInfo> GetCompanyInfo(long companyId);

    Task<List<EntityMinWithCompany>> GetCompanyList();

    ShortNameMin GetShortName(long companyId);
    Task<List<CompanyConfigModules>> GetCompanyConfig(long companyId);
    Task<RepositoryResponse> CreateUpdateCompanyConfig(long companyId, List<CompanyConfigModules> companyConfigs);
    Task<RepositoryResponse> SyncToFlo(long companyId);
    Task<RepositoryResponse> SyncToFloCompany(long companyId);
}
