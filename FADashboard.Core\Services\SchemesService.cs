﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class SchemesService(
    ISchemesRepository schemesRepository,
    IProductRepository productRepository,
    ICurrentUser currentUser,
    IShopTypeRepository shopTypeRepository,
    IAdminRepository adminRepository,
    IOutletMasterRepository outletMasterRepository) : RepositoryResponse
{
    private static bool CheckBasketEqual(List<List<long>> list)
    {
        foreach (var l in list)
        {
            foreach (var c in list)
            {
                if (l.Count != c.Count || l.Any(m => !c.Contains(m)))
                {
                    return false;
                }
            }
        }

        return true;
    }

    private static List<long> GetIdsFromString(string str) => string.IsNullOrEmpty(str) ? [] : str.SplitCsv().Select(long.Parse).ToList();

    private static string GetStringFromIds(List<long> ids) => (ids != null && ids.Count != 0) ? string.Join(",", ids) : null;

    private async Task<List<string>> GetChannelEnumsStringFromIds(List<long> channelIds)
    {
        if (channelIds.Count != 0)
        {
            var channelDict = (await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => ((int)s.Enum).ToString());
            return channelIds.Select(cId => channelDict.GetValueOrDefault(cId)).Where(p => p != null).ToList();
        }

        return [];
    }

    private async Task<List<long>> GetChannelIdsFromEnums(List<string> channelEnums)
    {
        if (channelEnums.Count != 0)
        {
            var channelDict = (await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, false)).ToDictionary(s => ((int)s.Enum).ToString(), s => s.Id);
            return channelEnums.Select(cEnum => channelDict.GetValueOrDefault(cEnum, 0)).ToList();
        }

        return [];
    }

    private async Task<List<string>> GetSegmentationEnumsStringFromIds(List<long> segmentationIds)
    {
        if (segmentationIds.Count != 0)
        {
            var segDict = (await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.Segmentation.ToString());
            return segmentationIds.Select(segId => segDict.GetValueOrDefault(segId)).Where(p => p != null).ToList();
        }

        return [];
    }

    private async Task<List<long>> GetSegmentationIdsFromEnums(List<string> segmentationEnums)
    {
        if (segmentationEnums.Count != 0)
        {
            var segDict = (await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, false)).ToDictionary(s => s.Segmentation.ToString(), s => s.Id);
            return segmentationEnums.Select(segEnum => segDict.GetValueOrDefault(segEnum, 0)).ToList();
        }

        return [];
    }

    private async Task<List<string>> GetShopNamesFromShopTypeIds(List<long> shopTypeIds)
    {
        if (shopTypeIds.Count != 0)
        {
            var shopTypeDict = (await shopTypeRepository.GetShopTypes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.ShopTypeName);
            return shopTypeIds.Select(Id => shopTypeDict.GetValueOrDefault(Id)).Where(p => p != null).ToList();
        }

        return [];
    }

    private async Task<List<long>> GetShopTypeIdsFromShopNames(List<string> shopNames)
    {
        if (shopNames.Count != 0)
        {
            var shopTypeDict = (await shopTypeRepository.GetShopTypes(currentUser.CompanyId, false)).ToDictionary(s => s.ShopTypeName, s => s.Id);
            return shopNames.Select(name => shopTypeDict.GetValueOrDefault(name, 0)).ToList();
        }

        return [];
    }

    private async Task<RepositoryResponse> IsValidSchemeBasket(SchemeBucketList schemeBasket)
    {
        var baskets = await GetSchemeBaskets(true);
        if (schemeBasket.Id != 0)
        {
            baskets = baskets.Where(p => p.Id != schemeBasket.Id).ToList();
        }

        var basketNames = baskets.Select(p => p.Name.NormalizeCaps().Trim()).ToList();

        if (basketNames.Contains(schemeBasket.Name.NormalizeCaps().Trim()))
        {
            return new RepositoryResponse
            {
                Id = schemeBasket.Id,
                ExceptionMessage = "Scheme Basket with this name already Exists!",
                Message = "Scheme Basket Creation Failed!",
                IsSuccess = false,
            };
        }

        if (!string.IsNullOrEmpty(schemeBasket.ErpId))
        {
            var basketERPIds = baskets.Where(p => !string.IsNullOrEmpty(p.ErpId)).Select(p => p.ErpId.NormalizeCaps().Trim()).ToList();
            if (basketERPIds.Contains(schemeBasket.ErpId.NormalizeCaps().Trim()))
            {
                return new RepositoryResponse
                {
                    Id = schemeBasket.Id,
                    ExceptionMessage = "Scheme Basket with this ERPId already Exists!",
                    Message = "Scheme Basket Creation Failed!",
                    IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = schemeBasket.Id, Message = "Scheme Basket Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> CreateUpdateScheme(SchemesInput scheme)
    {
        if (scheme.Id == 0)
        {
            var activeSchemeERPs = (await schemesRepository.GetSchemesMin(currentUser.CompanyId, false)).Select(p => p.ErpId.NormalizeCaps()).ToList();

            if (activeSchemeERPs.Contains(scheme.ErpId, StringComparer.OrdinalIgnoreCase))

            // asana: Nov 2,2023. Add ERPId duplicate check on New Dashboard for Schemes. link : https://app.asana.com/0/1204853877178779/1205820263883071/f
            {
                return new RepositoryResponse
                {
                    Id = 0,
                    ExceptionMessage = "Please enter a unique ERP Id",
                    Message = "Scheme Creation Failed!",
                    IsSuccess = false,
                };
            }

            // Model Validation checks

            if (scheme.PayoutType == PayoutType.Basket)
            {
                scheme.DiscountOn = DiscountBlock.All;
            }
            //  scheme.SchemeSlabs = JsonConvert.DeserializeObject<List<SchemeSlabs>>(scheme.SchemeSlabsStr);

            if (scheme.EndTime < scheme.StartTime)
                return new RepositoryResponse
                {
                    Id = 0,
                    ExceptionMessage = "Start date should be earlier than end date. Please correct the same and try again",
                    Message = "Scheme Creation Failed!",
                    IsSuccess = false,
                };

            if (scheme.PayoutType == PayoutType.Basket && !CheckBasketEqual(scheme.SchemeSlabs.Select(s => s.QualifierSchemeSlab.Select(q => q.BasketId.Value).ToList()).ToList()))
                return new RepositoryResponse
                {
                    Id = 0,
                    ExceptionMessage = "Error creating scheme : Please select same baskets in all slabs ",
                    Message = "Scheme Creation Failed!",
                    IsSuccess = false,
                };
            if (!scheme.EligibleForBudget)
            {
                scheme.SchemeBudget = null;
                scheme.SchemeBudgetType = null;
            }

            //Db Format manipulatipon
            scheme.DiscountBlockArray = scheme.DiscountBlockArrayList != null ? GetStringFromIds(scheme.DiscountBlockArrayList) : null;
            scheme.DistributorIdBlock = scheme.DistributorIdBlockList != null ? GetStringFromIds(scheme.DistributorIdBlockList) : null;
            scheme.SelectedProductBlockArray = scheme.SelectedProductBlockArrayList != null ? GetStringFromIds(scheme.SelectedProductBlockArrayList) : null;
            scheme.States = scheme.StateIds.Count > 0 ? scheme.StateIds.ConvertListToString() : null;

            if (scheme.IsOutletConstraint)
            {
                scheme.OutletConstraints.RequiredShopTypes = scheme.OutletConstraints.RequiredShopTypesList != null ? await GetShopNamesFromShopTypeIds(scheme.OutletConstraints.RequiredShopTypesList) : [];

                scheme.OutletConstraints.RequiredChannels = scheme.OutletConstraints.RequiredChannelsList != null ? await GetChannelEnumsStringFromIds(scheme.OutletConstraints.RequiredChannelsList) : [];

                scheme.OutletConstraints.RequiredSegmentations = scheme.OutletConstraints.RequiredSegmentationsList != null ? await GetSegmentationEnumsStringFromIds(scheme.OutletConstraints.RequiredSegmentationsList) : [];

                scheme.OutletConstraints.OutLetChains = scheme.OutletConstraints.RequiredOutletChainsList != null ? scheme.OutletConstraints.RequiredOutletChainsList.Select(l => l.ToString()).ToList() : [];

                scheme.OutletConstraints.CustomTags = scheme.OutletConstraints.CustomTagsList != null ? scheme.OutletConstraints.CustomTagsList.Select(l => l.ToString()).ToList() : [];
                scheme.OutletConstraints.OutletIds = scheme.OutletConstraints.OutletIdsList != null ? scheme.OutletConstraints.OutletIdsList.Select(l => l.ToString()).ToList() : [];
            }

            if (scheme.IsDistributorConstraint)
            {
                scheme.DistributorConstraints.DistributorRequiredChannels = scheme.DistributorConstraints.DistributorRequiredChannelsList?.Select(l => l.ToString()).ToList();
                scheme.DistributorConstraints.DistributorRequiredSegmentations = scheme.DistributorConstraints.DistributorRequiredSegmentationsList?.Select(l => l.ToString()).ToList();
            }

            if (scheme.IsUserConstraint)
            {
                scheme.UserConstraints.Cohorts = scheme.UserConstraints.CohortsList?.Select(l => l.ToString()).ToList();
            }

            return await schemesRepository.CreateScheme(scheme, currentUser.CompanyId);
        }

        throw new NotImplementedException();
    }

    public async Task<RepositoryResponse> CreateUpdateSchemeBasket(SchemeBucketList schemeBasket)
    {
        var checkValid = await IsValidSchemeBasket(schemeBasket);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (schemeBasket.Id == 0)
        {
            return await schemesRepository.CreateSchemeBasket(schemeBasket, currentUser.CompanyId);
        }

        return await schemesRepository.UpdateSchemeBasket(schemeBasket, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> DeactivateScheme(Guid schemeGuid, bool isForceEnd, DateTime? endDate = null) => await schemesRepository.DeactivateScheme(schemeGuid, isForceEnd, currentUser.CompanyId, endDate);

    public async Task<List<RepositoryResponse>> DeactivateSchemes(List<Guid> schemeGuids) => await schemesRepository.DeactivateSchemes(schemeGuids, currentUser.CompanyId);

    public async Task<RepositoryResponse> DeactivateSchemeBasket(long id) => await schemesRepository.DeactivateSchemeBasket(id, currentUser.CompanyId);

    public async Task<SchemeBucketInput> GetSchemeBasketById(long id)
    {
        var basket = await schemesRepository.GetSchemeBasketById(id, currentUser.CompanyId);

        if (basket.ProductIds.Count != 0)
        {
            basket.PrimaryCategoryIds = (await productRepository.GetPCsForProducts(currentUser.CompanyId, basket.ProductIds)).Select(p => p.Id).ToList();
            basket.SecondaryCategoryIds = (await productRepository.GetSCsForProducts(currentUser.CompanyId, basket.ProductIds)).Select(p => p.Id).ToList();
        }

        return basket;
    }

    public async Task<SchemeBucketDetails> GetSchemeBasketDetails(long id)
    {
        var basket = await schemesRepository.GetSchemeBasketDetails(id, currentUser.CompanyId);
        basket.ProductsNameString = basket.Products.Count != 0 ? string.Join(",", basket.Products.Select(p => p.Name).ToList()) : null;
        return basket;
    }

    public async Task<List<SchemeBucketList>> GetSchemeBaskets(bool showActive)
    {
        var schemes = await schemesRepository.GetSchemeBaskets(currentUser.CompanyId, showActive);
        return schemes;
    }

    public async Task<SchemesInput> GetSchemeById(Guid schemeGuid)
    {
        var scheme = await schemesRepository.GetSchemeById(schemeGuid, currentUser.CompanyId);
        scheme.DiscountBlockArrayList = GetIdsFromString(scheme.DiscountBlockArray);
        scheme.DistributorIdBlockList = GetIdsFromString(scheme.DistributorIdBlock);
        scheme.SelectedProductBlockArrayList = GetIdsFromString(scheme.SelectedProductBlockArray);
        scheme.StateIds = scheme.States.ConvertStringToList();
        if (scheme.IsOutletConstraint)
        {
            scheme.OutletConstraints.RequiredChannelsList = scheme.OutletConstraints.RequiredChannels != null ? await GetChannelIdsFromEnums(scheme.OutletConstraints.RequiredChannels) : [];
            scheme.OutletConstraints.RequiredShopTypesList = scheme.OutletConstraints.RequiredShopTypes != null ? await GetShopTypeIdsFromShopNames(scheme.OutletConstraints.RequiredShopTypes) : [];
            scheme.OutletConstraints.RequiredSegmentationsList = scheme.OutletConstraints.RequiredSegmentations != null ? await GetSegmentationIdsFromEnums(scheme.OutletConstraints.RequiredSegmentations) : [];
            scheme.OutletConstraints.RequiredOutletChainsList = scheme.OutletConstraints.OutLetChains != null ? scheme.OutletConstraints.OutLetChains.Select(long.Parse).ToList() : [];
            scheme.OutletConstraints.CustomTagsList = scheme.OutletConstraints.CustomTags != null ? scheme.OutletConstraints.CustomTags.Select(long.Parse).ToList() : [];
            scheme.OutletConstraints.RequiredIsFocusedBoolean = scheme.OutletConstraints.RequiredIsFocused != null ? scheme.OutletConstraints.RequiredIsFocused == "true" : null;
            scheme.OutletConstraints.OutletIdsList = scheme.OutletConstraints.OutletIds != null ? scheme.OutletConstraints.OutletIds.Select(long.Parse).ToList() : [];
        }

        if (scheme.IsDistributorConstraint)
        {
            scheme.DistributorConstraints.DistributorRequiredChannelsList = scheme.DistributorConstraints.DistributorRequiredChannels != null ? scheme.DistributorConstraints.DistributorRequiredChannels.Select(long.Parse).ToList() : [];
            scheme.DistributorConstraints.DistributorRequiredSegmentationsList = scheme.DistributorConstraints.DistributorRequiredSegmentations != null ? scheme.DistributorConstraints.DistributorRequiredSegmentations.Select(long.Parse).ToList() : [];
        }

        if (scheme.IsUserConstraint)
        {
            scheme.UserConstraints.CohortsList = scheme.UserConstraints.Cohorts != null ? scheme.UserConstraints.Cohorts.Select(long.Parse).ToList() : [];
        }

        return scheme;
    }

    public async Task<SchemesDetail> GetSchemeDetails(Guid schemeGuid)
    {
        var schemeDetails = await schemesRepository.GetSchemeDetails(schemeGuid, currentUser.CompanyId);
        return schemeDetails;
    }

    public async Task<List<SchemesList>> GetSchemes(bool includeDeactivate, DateTime? getSchemesTill)
    {
        var today = DateTime.Today;
        var lastSixMonthDate = today.AddMonths(-6).Date;
        var schemes = await schemesRepository.GetSchemes(currentUser.CompanyId, getSchemesTill ?? lastSixMonthDate, includeDeactivate);

        var admins = await adminRepository.GetUsersAndGlobalAdmins(currentUser.CompanyId, true);
        var adminDict = admins.ToDictionary(u => u.Id, u => u.Name);
        var financeHeads = await adminRepository.GetFinanceHeads(currentUser.CompanyId, true);
        var financeHeadDict = admins.ToDictionary(u => u.Id, u => u.Name);
        schemes.ForEach(scheme =>
            scheme.ActionTakenBy = !string.IsNullOrEmpty(scheme.ActionTakenBy)
                ? Enum.TryParse(scheme.ActionTakenBy.Split(':')[0], out PortalUserRole userRoleEnum)
                    ? userRoleEnum == PortalUserRole.FinanceHead
                        ? financeHeadDict.ContainsKey(long.Parse(scheme.ActionTakenBy.Split(':')[1])) ? financeHeadDict[long.Parse(scheme.ActionTakenBy.Split(':')[1])] : ""
                        : adminDict.ContainsKey(long.Parse(scheme.ActionTakenBy.Split(':')[1]))
                            ? adminDict[long.Parse(scheme.ActionTakenBy.Split(':')[1])]
                            : ""
                    : ""
                : "");

        return schemes;
    }

    public async Task<PagedResponse<List<SchemesList>>> SearchSchemes(PaginationFilter validFilter, PayoutType? payoutType, int? isSchemeApproved,
        PayoutCalculationType? payoutCalculationType, ConstraintType? constraintType, DateTime? getSchemesTill)
    {
        var today = DateTime.Today;
        var lastSixMonthDate = today.AddMonths(-6).Date;
        var schemes = await schemesRepository.SearchSchemes(currentUser.CompanyId, validFilter, payoutType, isSchemeApproved, payoutCalculationType, constraintType, getSchemesTill ?? lastSixMonthDate);
        var admins = await adminRepository.GetUsersAndGlobalAdmins(currentUser.CompanyId, true);
        var adminDict = admins.ToDictionary(u => u.Id, u => u.Name);
        var financeHeads = await adminRepository.GetFinanceHeads(currentUser.CompanyId, true);
        var financeHeadDict = admins.ToDictionary(u => u.Id, u => u.Name);
        schemes.SchemeRecords.ForEach(scheme =>
            scheme.ActionTakenBy = !string.IsNullOrEmpty(scheme.ActionTakenBy)
                ? Enum.TryParse(scheme.ActionTakenBy.Split(':')[0], out PortalUserRole userRoleEnum)
                    ? userRoleEnum == PortalUserRole.FinanceHead
                        ? financeHeadDict.ContainsKey(long.Parse(scheme.ActionTakenBy.Split(':')[1])) ? financeHeadDict[long.Parse(scheme.ActionTakenBy.Split(':')[1])] : ""
                        : adminDict.ContainsKey(long.Parse(scheme.ActionTakenBy.Split(':')[1]))
                            ? adminDict[long.Parse(scheme.ActionTakenBy.Split(':')[1])]
                            : ""
                    : ""
                : "");
        var totalRecords = schemes.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(schemes.SchemeRecords, validFilter, totalRecords);

        return pagedReponse;
    }

    public async Task<List<EntityMinWithErp>> GetAlternateSchemes()
    {
        var schemes = await schemesRepository.GetAlternateSchemesMin(currentUser.CompanyId);
        return schemes;
    }

    public async Task<List<SchemesList>> GetSchemesWithFilters(SchemeFilters schemeFilters)
    {
        var schemes = await schemesRepository.GetSchemesWithFilters(currentUser.CompanyId, schemeFilters);
        return schemes;
    }

    //Review a scheme
    public async Task<List<RepositoryResponse>> ReviewScheme(List<Guid> guids, int action, string reason) => await schemesRepository.ReviewScheme(guids, action, currentUser, reason);
}
