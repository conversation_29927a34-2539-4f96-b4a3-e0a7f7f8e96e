﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces;

public interface IDistributorAddressRepository
{
    Task<RepositoryResponse> CreateDistributorAddress(DistributorInput distributor, long distributorId, long companyId, CancellationToken ct = default);
    Task<RepositoryResponse> UpdateDistributorAddress(DistributorInput distributor, long distributorId, long companyId, CancellationToken ct = default);
    Task<List<DistributorAddressesDTO>> getDistributorAddressViaDistributorIds(List<long> distributorIds, long companyId);

}
