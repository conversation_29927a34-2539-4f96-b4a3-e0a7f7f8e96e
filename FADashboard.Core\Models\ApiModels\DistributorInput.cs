﻿using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class DistributorInput
{
    public string Address { set; get; }
    public long? ChannelId { get; set; }
    public long CompanyId { get; set; }
    public string ContactNo { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string DistributorCategory { get; set; }
    public int DistributorGrade { get; set; }
    public string DistributorType { get; set; }
    public string EmailId { get; set; }
    public string ErpId { get; set; }
    public string FSSAINumber { get; set; }
    public string GSTIN { get; set; }
    public Guid Guid { get; set; }
    public long Id { get; set; }
    public bool IsAppUser { set; get; }
    public bool IsDeactive { set; get; }
    public bool IsGSTRegistered { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string LocalName { get; set; }
    public string ManagerName { set; get; }
    public string Name { get; set; }
    public string PANNumber { get; set; }
    public long? ParentId { get; set; }
    public string PinCode { set; get; }
    public string Place { get; set; }
    public string PlaceOfSupply { get; set; }
    public List<long> ProductDivisionIds { get; set; }
    public long? RegionId { get; set; }
    public string SecondaryEmailId { get; set; }
    public string State { set; get; }
    public StockistType StockistType { get; set; }
    public DateTime TimeAdded { get; set; }
    public string Zone { set; get; }

    //public bool Deleted { set; get; }
    public string WarehouseAddress { get; set; }

    public string WarehouseCity { get; set; }
    public string WarehouseState { get; set; }
    public string WarehouseId { get; set; }
    public string WarehouseName { get; set; }
    public int? CreditDuration { set; get; }
    public double? CreditValue { set; get; }
    public int? CreditInvoiceCount { set; get; }
    public int? IntransitPeriod { set; get; }
    public string SecurityDeposit { set; get; }
    public string DDNumber { set; get; }
    public string PaymentMode { set; get; }
    public DateTime? DateOfPayment { get; set; }

    public bool HaveSeperateProducts { get; set; }

    public long? DistributorSegmentationId { get; set; }
    public long? DistributorChannelId { get; set; }
    public List<long> IDTIds { get; set; }
    public bool IsIDT { get; set; }
    public DateTime? FSSAIExpiryDate { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string OwnerName { get; set; }
    public string OwnerNo { get; set; }
    public string DOB { get; set; }
    public string District { get; set; }
    public string CityName { get; set; }
    public string SubCity { get; set; }
    public string Country { get; set; }
    public double? DistanceFromSuperStockist { get; set; }
    public int? TownCovered { get; set; }
    public int? RetailOutletCovered { get; set; }
    public int? WholeSaleOutletCovered { get; set; }
    public int? SalesmanCount { get; set; }
    public int? VehicleCount { get; set; }
    public string SelfieWithOwner { get; set; }
    public string Aadhar { get; set; }
    public string AadharImage { get; set; }
    public string CurrentTurnOver { get; set; }
    public string InitialInvestment { get; set; }
    public int? YearsinAgencyBusiness { get; set; }
    public string OtherCompanyAgencies { get; set; }
    public bool? DBorSuperstockist { get; set; }
    public string GodownImage { get; set; }
    public string Remark { get; set; }
    public SubDistributorType? SubDistType { get; set; }
    public long? GeographicalMappingId { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string BankIFSCCode { get; set; }
    public string BankAccountName { get; set; }
    public string BankAccountNumber { get; set; }
    public decimal? CashDiscountLimit { get; set; }

    public string? MsmeNumber { get; set; }

    public List<KycDocumentDetails> KycDocumentList { get; set; }
}
