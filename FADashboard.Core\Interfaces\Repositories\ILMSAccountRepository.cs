﻿using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSAccountRepository
    {
        Task<LMSAccountDto> GetByIdAsync(long id);
        Task<LMSAccountDto> GetAccountDetailByIdAsync(long id);
        Task<IEnumerable<LMSAccountDto>> GetByCompanyIdAsync(long companyId);
        Task<LMSAccountDto> AddAsync(LMSAccountDto account);
        Task<LMSAccountDto> UpdateAsync(LMSAccountDto account);
        Task<IEnumerable<LMSAccountDto>> GetByAccountOwnerAsync(long accountOwnerId);
        Task<IEnumerable<LMSAccountDto>> GetByPositionCodeAsync(long positionCode);

        // Uniqueness Checks
        Task<bool> IsAccountNameUniqueAsync(string name, long companyId, long? accountId = null);
        Task<bool> IsEmailUniqueAsync(string email, long companyId, long? accountId = null);

        // Paginated list
        Task<PagedResult<LMSAccountDto>> GetAccountsAsync(long companyId, LMSAccountQueryParameters queryParameters);
    }
}
