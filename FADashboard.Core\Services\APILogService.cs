﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonEnums;
using Library.DateTimeHelpers;

namespace FADashboard.Core.Services;

public class APILogService(IAPILogRepository apiLogRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<ApiLog>> GetAPILogs(ApiType apiType, string requestId, string userName, long startTimeEpoch, long endTimeEpoch, int? statusCode)
    {
        if (!string.IsNullOrWhiteSpace(userName))
        {
            var currentDate = startTimeEpoch == 0 ? DateTime.UtcNow : DateTimeOffset.FromUnixTimeSeconds(startTimeEpoch).DateTime;
            var mtdRange = DateTimeExtentions.GetMTDDateRange(currentDate, 1, 1);
            startTimeEpoch = mtdRange.StartDate.ToUnixTime();
            endTimeEpoch = mtdRange.EndDate.ToUnixTime();
        }

        return await apiLogRepository.GetAPILogs(currentUser.CompanyId, startTimeEpoch, endTimeEpoch, apiType, requestId, userName, statusCode);
    }

    public async Task<string> GetError(string id, ApiType? apiType) => await apiLogRepository.GetError(id, apiType);

    public async Task<string> GetInput(string id, ApiType? apiType) => await apiLogRepository.GetInput(id, apiType);

    public async Task<string> GetOutput(string id, ApiType? apiType) => await apiLogRepository.GetOutput(id, apiType);

    public async Task<string> GetCompleteRequest(string id, ApiType? apiType) => await apiLogRepository.GetCompleteRequest(id, apiType);
}
