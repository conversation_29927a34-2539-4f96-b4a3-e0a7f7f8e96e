﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class PmsRulesService
{
    private readonly IPmsRulesRepository _pmsRulesRepository;
    private readonly ICurrentUser currentUser;

    public PmsRulesService(IPmsRulesRepository pmsRulesRepository, ICurrentUser _currentUser)
    {
        _pmsRulesRepository = pmsRulesRepository;
        currentUser = _currentUser;
    }

    public async Task<RepositoryResponse> ActivateDeactivatePmsRule(long ruleId, bool action)
    {
        return await _pmsRulesRepository.ActivateDeactivatePmsRule(ruleId, action, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> CreateUpdatePmsRule(PmsRuleInput input)
    {
        return await _pmsRulesRepository.CreateUpdatePmsRule(input, currentUser.CompanyId);

    }

    public async Task<PmsRule> GetPmsRuleById(long ruleId)
    {
        var rule = await _pmsRulesRepository.GetPmsRuleById(ruleId, currentUser.CompanyId);
        return rule;
    }

    public async Task <List<PmsRule>> GetPmsRules(bool includeDeactive)
    {
        var rules = await _pmsRulesRepository.GetPmsRules(currentUser.CompanyId, includeDeactive);
        return rules;
    }
}
