﻿namespace FADashboard.Core.Models
{
    public class HCCBIntegrationAPILog
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public int EntityEnum { get; set; }
        public string APIName { get; set; }
        public string APIRequestId { get; set; }
        public string Status { get; set; }
        public string BlobLink { get; set; }
        public DateTime CreatedAt { get; set; }
        public long HCCBIntegrationLogID { get; set; }
        public int APIType { get; set; }
        public string InputErpIds { get; set; } // JSON data stored as string
    }

}
