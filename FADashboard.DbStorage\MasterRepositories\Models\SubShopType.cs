﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class SubShopType : IAuditedEntity
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string ErpId { get; set; }
    public long ShopTypeId { get; set; }
    [ForeignKey("ShopTypeId")]
    public ShopTypes ShopType { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long CompanyId { get; set; }
}
