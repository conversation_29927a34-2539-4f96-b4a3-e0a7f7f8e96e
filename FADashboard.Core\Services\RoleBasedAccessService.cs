﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class RoleBasedAccessService(
    ICurrentUser currentUser,
    IModuleRepository moduleRepository,
    IScreenListRepository screenListRepository,
    IRoleMasterRepository roleMasterRepository,
    IIdsRepository idsRepository,
    ICompanyRepository companyRepository,
    IReportRepository reportRepository
) : RepositoryResponse
{
    public async Task<List<ModulesView>> GetAllModules()
    {
        var modules = await moduleRepository.GetAllModules();
        return modules;
    }

    public async Task<ModuleScreenListView> GetModuleScreenListView(long moduleId)
    {
        var dbModule = await moduleRepository.GetModuleById(moduleId);
        var moduleScreenListView = await screenListRepository.GetModuleScreenListView(dbModule);
        return moduleScreenListView;
    }

    public async Task<List<ModuleScreenListView>> GetAllModuleScreenLists()
    {
        var moduleScreenListViews = await screenListRepository.GetAllModuleScreenLists();
        var companyConfigModules = await companyRepository.GetCompanyConfig(currentUser.CompanyId);
        var sfaConfigModules = companyConfigModules.FirstOrDefault(a => a.Product == "SFA")?.Modules;

        // Check if both moduleScreenListViews and sfaConfigModules are not null
        if (moduleScreenListViews != null && sfaConfigModules != null)
        {
            // Intersect the data based on the module name
            var commonModules = moduleScreenListViews.Where(module => sfaConfigModules.ContainsKey(module.ModuleEnums));

            // Filter the screens within the common modules
            foreach (var module in commonModules)
            {
                module.ScreenList = module.ScreenList
                    .Where(screen => sfaConfigModules[module.ModuleEnums].Contains(screen.ScreenEnum))
                    .ToList();
            }

            return commonModules.ToList();
        }

        // Handle the case where either moduleScreenListViews or sfaConfigModules is null
        return [];
    }


    public async Task<List<RoleMastersView>> GetAllRoles(bool showDeleted)
    {
        var roles = await roleMasterRepository.GetAllRoles(currentUser.CompanyId, showDeleted);
        return roles;
    }

    public async Task<RoleDetailsById> GetRoleDetailsById(long id)
    {
        var roleDetails = await roleMasterRepository.GetRoleDetailsById(currentUser.CompanyId, id);
        var companyConfigModules = await companyRepository.GetCompanyConfig(currentUser.CompanyId);
        var sfaConfigModules = companyConfigModules.FirstOrDefault(a => a.Product == "SFA")?.Modules;
        var reportSubscriptions = await reportRepository.GetSubscribedReportsForRole(currentUser.CompanyId, id);

        if (roleDetails.Permissions != null && sfaConfigModules != null)
        {
            var commonPermissions = sfaConfigModules.Keys.Intersect(roleDetails.Permissions.Keys)
                .ToDictionary(
                    module => module,
                    module => sfaConfigModules[module].Intersect(roleDetails.Permissions[module].Keys)
                        .ToDictionary(key => key, key => roleDetails.Permissions[module][key])
                );

            roleDetails.Permissions = commonPermissions.Count != 0 ? commonPermissions : [];
        }
        else
        {
            roleDetails.Permissions = [];
        }

        if (reportSubscriptions.Count > 0)
        {
            roleDetails.ReportSubscriptionDetails = reportSubscriptions;
        }

        return roleDetails;
    }

    public async Task<RepositoryResponse> IsValidRole(RoleDetailsById roleDetails)
    {
        if (roleDetails.Id != 0)
        {
            var dbRole = await roleMasterRepository.GetAllRoles(currentUser.CompanyId);
            var existingRole = dbRole.FirstOrDefault(a => a.RoleName == roleDetails.RoleName && a.Id != roleDetails.Id);
            if (existingRole != null)
            {
                return new RepositoryResponse { Id = existingRole.Id, IsSuccess = false, Message = "Role with this name already exists" };
            }

            return new RepositoryResponse { IsSuccess = true, Message = "Role with this name does not exist" };
        }
        else
        {
            var dbRole = await roleMasterRepository.GetAllRoles(currentUser.CompanyId);
            var existingRole = dbRole.FirstOrDefault(a => a.RoleName == roleDetails.RoleName);
            if (existingRole != null)
            {
                return new RepositoryResponse { Id = existingRole.Id, IsSuccess = false, Message = "Role with this name already exists" };
            }

            return new RepositoryResponse { IsSuccess = true, Message = "Role with this name does not exist" };
        }
    }

    public async Task<RepositoryResponse> CreateUpdateUserRole(RoleDetailsById roleDetails)
    {
        var isValidRole = await IsValidRole(roleDetails);
        if (isValidRole.IsSuccess)
        {
            if (roleDetails.Id == 0)
            {
                var response = await roleMasterRepository.CreateUserRole(currentUser.CompanyId, roleDetails);
                if (roleDetails.ReportSubscriptionDetails.Count > 0 && response.IsSuccess && response.Id.HasValue)
                {
                    var response2 = await reportRepository.AddRoleBasedReportSubscriptions(currentUser.CompanyId, response.Id.Value, roleDetails.ReportSubscriptionDetails);
                    if (response2.IsSuccess)
                        return new RepositoryResponse { IsSuccess = true, Message = "Role created successfully and Report Subscriptions for Role created successfully", };
                    return new RepositoryResponse { IsSuccess = true, Message = "Role created successfully and Report Subscriptions for Role failed", };
                }

                return response;
            }
            else
            {
                var response = await roleMasterRepository.UpdateUserRole(currentUser.CompanyId, roleDetails);
                if (response.IsSuccess)
                {
                    var response2 = await reportRepository.UpdateRoleBasedReportSubscriptions(currentUser.CompanyId, roleDetails.Id, roleDetails.ReportSubscriptionDetails);
                    if (response2.IsSuccess)
                        return new RepositoryResponse { IsSuccess = true, Message = "Role and Report Subscriptions updated successfully", };
                    return new RepositoryResponse { IsSuccess = true, Message = "Role updated successfully and Report Subscriptions updation for Role failed", };
                }

                return response;
            }
        }

        return isValidRole;
    }

    public async Task<RepositoryResponse> IsValidDeactivation(long id)
    {
        var idsWithRole = await idsRepository.GetIDSWithRoleIds(currentUser.CompanyId);
        var associatedEmailIds = new List<string>();

        foreach (var kvp in idsWithRole)
        {
            if (kvp.Value.Contains(id))
            {
                associatedEmailIds.Add(kvp.Key);
            }
        }

        if (associatedEmailIds.Count > 0)
        {
            return new RepositoryResponse { IsSuccess = false, Message = $"Error deactivating the role. Associated EmailIds: {string.Join(", ", associatedEmailIds)}" };
        }

        return new RepositoryResponse { IsSuccess = true, Message = "ID can be deactivated." };
    }

    public async Task<RepositoryResponse> ActivateDeactivateRole(long id, bool action)
    {
        if (!action)
        {
            var isValidDeactivation = await IsValidDeactivation(id);
            if (isValidDeactivation.IsSuccess)
            {
                var response = await roleMasterRepository.ActivateDeactivateRole(currentUser.CompanyId, id, action);
                return response;
            }

            return isValidDeactivation;
        }

        {
            var response = await roleMasterRepository.ActivateDeactivateRole(currentUser.CompanyId, id, action);
            return response;
        }
    }
}
