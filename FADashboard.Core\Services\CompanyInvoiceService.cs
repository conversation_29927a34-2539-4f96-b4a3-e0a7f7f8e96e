﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class CompanyInvoiceService(ICompanyInvoiceDetailsRepository companyInvoiceDetailsRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<CompanyInvoiceDetails> GetCompanyInvoiceDetails()
    {
        var invoiceDetail = await companyInvoiceDetailsRepository.GetCompanyInvoiceDetails(currentUser.CompanyId);
        return invoiceDetail;
    }
}
