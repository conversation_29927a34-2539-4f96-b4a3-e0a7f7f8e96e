﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class FlexibleTargetMaster
{
    public long Id { get; set; }
    public string? TargetIdentifier { get; set; }
    public string? TargetName { get; set; }
    public Heirarchy? Hierarchy1 { get; set; }
    public Heirarchy? Hierarchy2 { get; set; }
    public Heirarchy? Hierarchy3 { get; set; }
    public string? TargetOn { get; set; }
    public string? Hierarchy1Query { get; set; }
    public string? Hierarchy2Query { get; set; }
    public string? Hierarchy3Query { get; set; }
    public string? AchievementQuery { get; set; }
    public int AchievementDb { get; set; }
    public int AchievementReturnType { get; set; }
    public int? AppScreen { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string? CreationContext { get; set; }
    public int? VisualizationType { get; set; }
    public string? TargetFilters { get; set; }
    public bool? IsForApp { get; set; }
    public int? DisplayAxis { get; set; }
    public int? Target_type { get; set; }
}
