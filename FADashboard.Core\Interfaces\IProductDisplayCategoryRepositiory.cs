﻿using FADashboard.Core.Models;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IProductDisplayCategoryRepositiory
{
    Task<List<ProductCategoryDivision>> GetDetailedProductDisplayCategories(long companyId, bool includeDeactivate = false);
    Task<List<EntityMinWithErp>> GetProductDisplayCategoriesMin(long companyId, bool includeDeactivate = false);
    Task<RepositoryResponse> ActivateDeactivateProductDisplayCategory(long id, long companyId, bool action);
    Task<RepositoryResponse> CreateProductDisplayCategory(ProductCategoryDivision pc, long companyId);
    Task<RepositoryResponse> UpdateProductDisplayCategory(ProductCategoryDivision pc, long companyId);
    Task<ProductCategoryDivision> GetProductDisplayCategoryById(long companyId, long id);
}
