namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadTemplateQueryParameters
    {
        private const int MaxPageSize = 100;
        private int _pageSize = 10;

        public string TemplateName { get; set; } // Search filter

        public string IsActive { get; set; } = "all"; // Status filter: "active", "inactive", "all"

        public int PageNumber { get; set; } = 1;

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        public string SortBy { get; set; } = "UpdatedAt"; // Default sort
        public string Order { get; set; } = "desc";
    }
}
