﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs;

public class ReportSubscription
{
    [ForeignKey("Company")] public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public Guid EncryptionKey { get; set; } = Guid.NewGuid();
    public long Id { get; set; }

    public bool IsDeactive { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string Link => $"DataVisualization/Home/ShowReport/{EncryptionKey}";

    [StringLength(256)] public string Name { set; get; }

    public PortalUserRole PortalUserRole { get; set; }
    public virtual Report Report { get; set; }
    public long ReportId { get; set; }
    public SubscriptionType SubscriptionType { get; set; }
}

public class SubscribedReports
{
    public long ReportId { get; set; }
    public string ReportName { get; set; }
    public Guid EncryptionKey { get; set; }
}
