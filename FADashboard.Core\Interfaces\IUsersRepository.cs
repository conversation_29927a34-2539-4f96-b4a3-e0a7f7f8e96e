﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IUsersRepository
{
    Task<List<UserWithPositionFilter>> GetAllManagersUnderPositions(long companyId, List<long> positionCodeIds);

    Task<List<UserWithPositionFilter>> GetAllManagersWithPositions(long companyId);

    Task<List<UserWithPositionFilter>> GetPositionLevelWiseUsers(long companyId, PositionCodeLevel level);

    Task<List<UserWithPositionFilter>> GetPositionLevelWiseUsersUnderPositions(long companyId, List<long> positionCodeIds);
}
