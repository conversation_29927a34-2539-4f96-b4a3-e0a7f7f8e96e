﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;


namespace FADashboard.Core.Interfaces;

public interface IPositionCodeRepository
{
    Task<bool> ActionVacant(long employeeId, bool action);
    Task<Dictionary<long, PositionDetailed>> GetPositionHierarchy(int highestLevel, long companyId, List<long> pcIds, bool isdeactive = false);
    Task<List<PositionMinParent>> GetPositionByEmployeeHierarchy(long companyId, List<long> employeeIds);

    Task<RepositoryResponse> ActivateDeactivatePositionCode(long positionId, long companyId, bool action);

    Task<bool> CreateNewPCMappings(List<long> lists, long userId, long companyId);

    Task<List<PositionCodeRecord>> GetActivePositions(PositionCodeLevel highestLevel, long companyId, bool includeDeactivate);

    Task<List<PositionCodeEntityMappingsInfo>> GetPositionEntityMappings(List<long> PositionIds);

    Task<long> GetPositionCount(long companyId, PaginationFilter validFilter);

    Task<PositionCodeRecordsWithTotal> GetActivePositions(PositionCodeLevel highestLevel, long companyId, PaginationFilter validFilter, int? positionLevel, List<long> positionCodeIds);

    Task<List<PositionCodeDTO>> GetActivePositionsByOrder(PositionCodeLevel highestLevel, long companyId, string searchString = "");

    Task<List<UserWithPositionFilter>> GetAllEmployeesWithPositions(long companyId);

    Task<List<PositionCodeDetails>> GetAllUserUnderPositionCodesIncludeUserInfo(List<long> positionCodeIds, long companyId, PortalUserRole userRole = PortalUserRole.Unknown, SaleType saleType = SaleType.All, List<long> regionIds = null);

    Task<List<UserWithPosition>> GetAllPositionsEmployeeData(long companyId, SaleType saleType = SaleType.All, List<long> regionIds = null);
    Task<List<EntityMinIncludeParent>> GetAllPositionsOfLevel(PositionCodeLevel level, long companyId);

    Task<List<EntityMin>> GetAllUsersAttachedToPositionCodes(long companyId);

    Task<List<EntityMinWithStatusandUserType>> GetEmployeesToAttach(long companyId, PositionCodeLevel level, List<long> positionIds);

    Task<List<EntityMin>> GetEmployeeUnderPosition(long positionCodeId, long companyId);

    Task<List<PositionCodeDTO>> GetInactivePositionCodes(PositionCodeLevel highestLevel, long companyId, int leave, int take, string searchString = "");

    Task<List<PositionMinParent>> GetPositionByEmployee(long companyId, List<long> employeeIds);

    Task<PositionCodeDTO> GetPositionCodeById(long companyId, long id);

    Task<Dictionary<string, long>> GetPositionCodeDic(long companyId);

    Task<List<PositionCodeDTO>> GetPositionCodes(PositionCodeLevel highestLevel, long companyId);

    Task<int> GetPositionCodesCount(long companyId, PositionCodeLevel highestLevel, string searchString = null, bool includeDeactivate = false);

    Task<List<PositionCodeRecord>> GetPositionCodesForEmployee(long employeeId, long companyId);

    Task<List<PositionCodeDTO>> GetPositionCodesInBatch(PositionCodeLevel highestLevel, long companyId, int leave, int take, string searchString = "");

    Task<List<PositionCodeEntityMapping>> GetPositionEntityMaps(List<long> posIds, long companyId);

    Task<List<PositionCodeDTO>> GetPositionsBySearch(PositionCodeLevel highestLevel, long companyId, string searchString);

    Task<List<PositionDetails>> GetReportingToPositions(long companyId, string positionCodeLevel, List<long> positionIds);

    Task<List<PositionDetails>> GetUserPositionCodeMin(long userId, long companyId);

    Task<Dictionary<long, PositionCodeLevel>> GetUserPositionCodes(long userId, long companyId);

    Task<RepositoryResponse> SavePositionCode(PositionCodeDTO positionCode, long companyId);

    Task<RepositoryResponse> UpdatePositionCode(PositionCodeInput positionCode, long companyId);

    Task<List<PositionsFlat>> GetPositionCodeFlatWithUsers(PositionCodeLevel highestPosLevel, long companyId);

    Task<List<EntityMin>> GetPositionsMin(List<long> positionCodeIds, long companyId);

    Task<List<PositionDetails>> GetPositionsMin(long companyId, bool includeDeactivate = false);

    Task<PositionDetails> GetPositionMin(long id, long companyId);

    Task<List<PositionMin>> GetPositionIdsUnderPosition(List<long> positionCodeIds, long companyId);

    Task<Dictionary<long, PositionCodeHierarchy>> GetPositionCodeHierarchyDictionary(long companyId);
    Task<PositionSummary> GetPositionSummary(PositionCodeLevel highestPosLevel, long companyId, List<long> positionCodeIds);
    Task<List<long>> GetEmployeeForPosition(List<long> positionCodeIds, long newEmployeeId, long companyId);

    Task<List<long>> GetRegionsUnderPositions(List<long> positionCodeIds, long companyId);
    Task<Dictionary<long, List<long>>> GetPositionWiseProductDiv(long companyId, List<long> pcIds);
    Task<List<long>> GetPositionsByEmployeeId(long companyId, List<long> empIds);
    Task<List<UserWithPosition>> GetPositionLevelWisePositions(long companyId, PositionCodeLevel level);
    Task<List<PositionCodeList>> GetDistinctPositionOfBeat(long companyId, List<long> beatIds);
    Task<List<PositionCodeList>> GetDistinctPositionOfDistributor(long companyId, List<long> distributorIds);

    Task<List<PositionCodeDetails>> GetAllPositionCodesIncludeUserInfo(List<long> positionCodeIds, long companyId, SaleType saleType);
    Task<List<PositionCodeDetails>> GetAllUserUnderPositionCodesIncludeUserInfoForDailySummary(List<long> positionCodeIds, long companyId, PortalUserRole userRole = PortalUserRole.Unknown, List<long> regionIds = null
        , bool includeDeactiveUsers = true);
}
