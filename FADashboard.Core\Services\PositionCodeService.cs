﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class PositionCodeService : RepositoryResponse
{
    private readonly CompanySettingService companySettingService;
    private readonly ICurrentUser currentUser;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IPositionBeatRepository positionBeatRepository;
    private readonly IPositionCodeRepository positionCodeRepository;
    private readonly IPositionDistributorRepository positionDistributorRepository;
    private readonly IBeatRepository beatRepository;
    private readonly IAdminRepository adminRepository;
    private readonly IManagerRepository managerRepository;
    private readonly IPositionOutletRepository positionOutletRepository;
    private readonly ICompanySettingsRepository companySettingsRepository;
    private readonly IISROutletMappingRepository iSROutletMappingRepository;
    private readonly IProductDivRepository productDivRepository;

    public PositionCodeService(IPositionCodeRepository positionCodeRepository,
        IEmployeeRepository employeeRepository,
        IPositionBeatRepository positionBeatRepository,
        IPositionDistributorRepository positionDistributorRepository,
        ICurrentUser currentUser,
        IBeatRepository beatRepository,
        CompanySettingService companySettingService,
        IAdminRepository adminRepository,
        IProductDivRepository productDivRepository,
        IManagerRepository managerRepository, IPositionOutletRepository positionOutletRepository, ICompanySettingsRepository companySettingsRepository, IISROutletMappingRepository iSROutletMappingRepository)
    {
        this.positionCodeRepository = positionCodeRepository;
        this.employeeRepository = employeeRepository;
        this.positionBeatRepository = positionBeatRepository;
        this.positionDistributorRepository = positionDistributorRepository;
        this.currentUser = currentUser;
        this.companySettingService = companySettingService;
        this.beatRepository = beatRepository;
        this.companySettingService = companySettingService;
        this.adminRepository = adminRepository;
        this.managerRepository = managerRepository;
        this.positionOutletRepository = positionOutletRepository;
        this.companySettingsRepository = companySettingsRepository;
        this.iSROutletMappingRepository = iSROutletMappingRepository;
        this.productDivRepository = productDivRepository;
    }

    private async Task<List<PositionCodeList>> GetPositionCodeList(List<PositionCodeDTO> positionCodes)
    {
        var employeeDic = await employeeRepository.GetEmployeeDictionary(currentUser.CompanyId);
        var positionIds = positionCodes.Select(p => p.Id).ToList();
        var positionBeatMappings = await positionBeatRepository.GetPositionBeatMappingDic(currentUser.CompanyId, positionIds);
        var positionDistMappings = await positionDistributorRepository.GetPositionDistributorMappingDic(currentUser.CompanyId, positionIds);

        var positionList = new List<PositionCodeList>();
        foreach (var item in positionCodes)
        {
            var employee = item.PositionCodeEntityMappings.ToList();
            long? employeeId = employee.Count > 0 ? employee.FirstOrDefault().EntityId : null;

            positionList.Add(new PositionCodeList
            {
                AttachedTo = employeeId.HasValue ? (employeeDic.TryGetValue(employeeId.Value, out var value) ? value.Name : string.Empty) : string.Empty,
                CodeId = item.CodeId,
                CompanyId = item.CompanyId,
                Id = item.Id,
                Level = item.Level,
                Name = item.Name,
                ReportingTo = item.ParentId.HasValue ? item.ParentName : string.Empty,
                ReportingToPositionLevel = item.ParentId.HasValue ? item.ParentLevel : 0,
                AttachedErpId = employeeId.HasValue ? (employeeDic.TryGetValue(employeeId.Value, out var value1) ? value1.ErpId : string.Empty) : string.Empty,
                AttachedToId = employeeId.HasValue ? (employeeDic.TryGetValue(employeeId.Value, out var value2) ? value2.Id : 0) : 0,
                ReportingToCodeId = item.ParentId.HasValue ? item.ParentCode : string.Empty,
                ReportingToId = item.ParentId ?? 0,
                BeatIds = positionBeatMappings.TryGetValue(item.Id, out var mapping) ? mapping.Select(p => p.BeatId).ToList() : null,
                DistributorIds = positionDistMappings.TryGetValue(item.Id, out var distMapping) ? distMapping.Select(p => p.DistributorId).ToList() : null
            });
        }

        return positionList;
    }

    private async Task<RepositoryResponse> IsValidPosition(PositionCodeInput positionCode)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var highestLevel = companySettings.GetCompanyHighestPositionLevel;
        var positionCodes = await GetPositionCodes(highestLevel);
        if (positionCode.Id != 0)
        {
            positionCodes = positionCodes.Where(p => p.Id != positionCode.Id).ToList();
        }

        var positionNameList = positionCodes.Select(p => p.Name.NormalizeCaps()).ToList();
        var positionCodeList = positionCodes.Select(p => p.CodeId.NormalizeCaps()).ToList();

        if (positionNameList.Contains(positionCode.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = positionCode.Id, ExceptionMessage = "Position Name is not unique", Message = "Position Code Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (positionCodeList.Contains(positionCode.CodeId.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = positionCode.Id, ExceptionMessage = "Position Code Id is not unique", Message = "Position Code Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = positionCode.Id, Message = "Position Code Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivatePositionCode(long positionId, bool action)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesSecondaryPositionBeatMapping = companySettings.UsesSecondaryPositionBeatMapping;
        if (!action)
        {
            string empName = null;
            var errorList = new ErrorList
            {
                Count1 = (await positionBeatRepository.GetBeatForPosition(positionId, currentUser.CompanyId))?.Count ?? 0,
                Count2 = usesSecondaryPositionBeatMapping ? (await positionBeatRepository.GetSecondaryBeatForPosition(positionId, currentUser.CompanyId))?.Count ?? 0 : 0,
                Count3 = (await positionDistributorRepository.GetDistributorForPosition(positionId))?.Count ?? 0
            };

            var positionCode = await positionCodeRepository.GetPositionCodeById(currentUser.CompanyId, positionId);

            if (positionCode?.PositionCodeEntityMappings?.Count > 0)
            {
                var employeeAttachedId = positionCode.PositionCodeEntityMappings.FirstOrDefault().EntityId;
                var employee = await employeeRepository.GetEmployee(employeeAttachedId, currentUser.CompanyId);
                if (employee != null)
                    empName = employee.Name;
            }

            if (errorList.Count1 != 0 || errorList.Count2 != 0 || errorList.Count3 != 0 || !string.IsNullOrEmpty(empName))
            {
                return new RepositoryResponse { Id = positionId, Message = $"User : {empName}; Primary Beat Count : {errorList.Count1}; Secondary Beat Count : {errorList.Count2}; Distributor Count : {errorList.Count3}", IsSuccess = false };
            }
        }

        return await positionCodeRepository.ActivateDeactivatePositionCode(positionId, currentUser.CompanyId, action);
    }

    public async Task<RepositoryResponse> AttachBeats(long positionCodeId, List<long> beatIds, long? productDivisionId)
    {
        try
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            return await positionBeatRepository.AttachBeats(currentUser.CompanyId, positionCodeId, beatIds, productDivisionId, companySettings);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = positionCodeId, ExceptionMessage = ex.Message, Message = "An Error Occurred while trying to save new Mappings", IsSuccess = false };
        }
    }

    public async Task<RepositoryResponse> AttachTemporaryBeats(long positionCodeId, List<long> beatIds, long? productDivisionId)
    {
        try
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            return await positionBeatRepository.AttachTemporaryBeats(currentUser.CompanyId, positionCodeId, beatIds, productDivisionId, companySettings);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = positionCodeId, ExceptionMessage = ex.Message, Message = "An Error Occurred while trying to save new Mappings", IsSuccess = false };
        }
    }


    public async Task<RepositoryResponse> AttachSecondaryPositionBeatMapping(long positionCodeId, List<long> beatIds, long? productDivisionId)
    {
        try
        {
            //secondary position beat mapping validation
            // check if primary mapping exists
            var getExistingMappings = (await positionBeatRepository.GetPositionBeatMappings(currentUser.CompanyId, beatIds, productDivisionId))
                .Where(p => !p.IsSecondaryMapping && p.PositionId != positionCodeId).Select(p => p.BeatId).Distinct().ToList();

            var invalidBeatIds = beatIds.Where(p => !getExistingMappings.Contains(p)).ToList();

            if (invalidBeatIds.Count > 0)
            {
                var invalidBeatList = (await beatRepository.GetBeatsMin(currentUser.CompanyId, invalidBeatIds)).Select(p => p.Name).ToList();
                return GetRejectResponse($"The Beats[{string.Join(',', invalidBeatList)}] doesn't have any other primary mapping attached to a position for the same product division");
            }

            // check if position falls under same manager if not a admin
            if (!adminRepository.UserHaveAdminPrivileges(currentUser.UserRole))
            {
                var posIds = new List<long>();

                if (currentUser.UserRole == PortalUserRole.RegionalAdmin)
                {
                    var admin = await adminRepository.GetRegionalAdminAndItsChildPositions(currentUser.LocalId, currentUser.CompanyId);
                    posIds.AddRange(admin.AllChildPositionIds);
                    posIds.AddRange(admin.PositionIds);
                }
                else
                {
                    posIds = await managerRepository.GetPositionIdsUnderManager(currentUser.LocalId, currentUser.CompanyId);
                }

                if (!posIds.Contains(positionCodeId))
                {
                    return GetRejectResponse("The selected position code doesn't fall under the user");
                }
            }

            return await positionBeatRepository.AttachSecondaryPositionBeatMapping(currentUser.CompanyId, positionCodeId, beatIds, productDivisionId); // position-beat-prod division active checks and creation
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = positionCodeId, ExceptionMessage = ex.Message, Message = "An Error Occurred while trying to save new Mappings", IsSuccess = false };
        }
    }

    public async Task<RepositoryResponse> AttachDistributors(long positionCodeId, List<long> distributorIds)
    {
        try
        {
            return await positionDistributorRepository.AttachDistributors(currentUser.CompanyId, positionCodeId, distributorIds);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = positionCodeId, ExceptionMessage = ex.Message, Message = "An Error Occurred while trying to save new Mappings", IsSuccess = false };
        }
    }

    public async Task<List<PositionCodeList>> GetAllActivePositionCodes(PositionCodeLevel highestLevel, int page, string searchString = "")
    {
        var positionCodes = await positionCodeRepository.GetActivePositionsByOrder(highestLevel, currentUser.CompanyId, searchString);
        return await GetPositionCodeList(positionCodes);
    }

    public async Task<List<PositionCodeRecord>> GetAllActivePositions(bool includeDeactivate)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var highestLevel = companySettings.GetCompanyHighestPositionLevel;

        var positions = await positionCodeRepository.GetActivePositions(highestLevel, currentUser.CompanyId, includeDeactivate);

        var positionIds = positions.Select(p => p.Id).ToList();

        var productDivisionInfo = await productDivRepository.GetPositionProductDivisionInfo(positionIds);
        var entityMappings = await positionCodeRepository.GetPositionEntityMappings(positionIds);


        var result = positions.Select(position =>
        {
            var divisions = productDivisionInfo.Where(d => d.PositionId == position.Id).ToList();
            position.AttachedToProductDivisionIds = divisions.Select(d => d.ProductDivisionId).ToList();
            position.AttachedToProductDivisionNames = string.Join(",", divisions.Select(d => d.ProductDivisionName));

            var mappings = entityMappings.Where(e => e.PositionId == position.Id).ToList();
            position.AttachedToId = mappings.Select(e => e.ClientEmployeeId).FirstOrDefault();
            position.AttachedTo = mappings.Select(e => e.ClientEmployeeName).FirstOrDefault();
            position.IsVacant = !mappings.Any();
            return position;
        }).ToList();

        return result;
    }


    public async Task<PagedResponse<List<PositionCodeRecord>>> GetAllActivePositions(PaginationFilter validFilter, int? positionLevel, List<long> positionCodeIds)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var level = companySettings.GetCompanyHighestPositionLevel;
        var positions = await positionCodeRepository.GetActivePositions(level, currentUser.CompanyId, validFilter, positionLevel, positionCodeIds);
        var totalRecords = positions.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(positions.PositionCodeRecords, validFilter, totalRecords);
        return pagedReponse;
    }

    public async Task<List<PositionCodeDetails>> GetAllPositionCodesUnderPositionCode(long positionCodeId)
    {
        var posCodeList = new List<long> { positionCodeId };
        return await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(posCodeList, currentUser.CompanyId);
    }

    public async Task<List<PositionCodeDetails>> GetAllPositionCodesUnderUser(PaginationFilter filter)
    {
        var positionList = new List<PositionDetails>();
        if (currentUser.UserRole == PortalUserRole.RegionalAdmin)
        {
            positionList = (await adminRepository.GetUserById(currentUser.LocalId, currentUser.CompanyId)).UserPositionIds?.Select(s => s.HasValue ? new PositionDetails
            {
                Id = s ?? 0
            } : new PositionDetails()).ToList() ?? new List<PositionDetails>();
        }
        else
        {
            positionList = await positionCodeRepository.GetUserPositionCodeMin(currentUser.LocalId, currentUser.CompanyId);
        }
        var positionMin = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionList.Select(p => p.Id).ToList(), currentUser.CompanyId);
        var searchString = filter.QuerySearch;
        if (positionMin.Count == 0 || string.IsNullOrEmpty(searchString))
            return positionMin;
        positionMin = positionMin.Where(p => p.Name.Contains(searchString, StringComparison.CurrentCultureIgnoreCase) || p.EmployeeName.Contains(searchString, StringComparison.CurrentCultureIgnoreCase)).ToList();
        return positionMin;
    }

    public async Task<List<PositionCodeDetails>> GetAllPositionCodesUnderPositionCodes(List<long> positionCodeIds)
    {
        var data = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionCodeIds, currentUser.CompanyId);
        foreach (var posCode in data)
        {
            posCode.LevelName = posCode.Level.ToString();
        }

        return data;
    }

    public async Task<List<EntityMinIncludeParent>> GetAllPositionsOfLevel(PositionCodeLevel level) => await positionCodeRepository.GetAllPositionsOfLevel(level, currentUser.CompanyId);

    public async Task<List<EntityMinWithStatusandUserType>> GetEmployeesToAttach(PositionCodeLevel level, List<long> positionIds = null)
    {
        var employees = await positionCodeRepository.GetEmployeesToAttach(currentUser.CompanyId, level, positionIds);
        return employees;
    }

    public async Task<List<PositionCodeList>> GetInactivePositionCodes(PositionCodeLevel highestLevel, int page, string searchString = "")
    {
        const int batchSize = 25;
        var positionCodes = await positionCodeRepository.GetInactivePositionCodes(highestLevel, currentUser.CompanyId, (page - 1) * batchSize, batchSize, searchString);
        return positionCodes.Select(p => new PositionCodeList
        {
            CodeId = p.CodeId,
            CompanyId = p.CompanyId,
            Id = p.Id,
            Level = p.Level,
            Name = p.Name,
            ReportingTo = p.ParentId.HasValue ? p.ParentName : string.Empty,
            ReportingToCodeId = p.ParentId.HasValue ? p.ParentCode : string.Empty,
            ReportingToId = p.ParentId ?? 0,
            ReportingToPositionLevel = p.ParentId.HasValue ? p.ParentLevel : 0
        }).ToList();
    }

    public async Task<PositionCodeDTO> GetPositionCodeById(long positionId)
    {
        var position = await positionCodeRepository.GetPositionCodeById(currentUser.CompanyId, positionId);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesModernTrade = companySettings.UsesModernTrade;
        if (usesModernTrade)
        {
            var positionOutletMappings = await positionOutletRepository.GetPositionOutletMappingUsingPositionId(positionId, currentUser.CompanyId);
            position.OutletIds = positionOutletMappings.Select(s => s.LocationId).ToList();
        }

        return position;
    }

    public async Task<List<PositionCodeList>> GetPositionCodes(PositionCodeLevel highestLevel, string searchString = "")
    {
        var positionCodes = new List<PositionCodeDTO>();
        if (string.IsNullOrEmpty(searchString))
            positionCodes = await positionCodeRepository.GetPositionCodes(highestLevel, currentUser.CompanyId);
        else
            positionCodes = await positionCodeRepository.GetPositionsBySearch(highestLevel, currentUser.CompanyId, searchString);
        return await GetPositionCodeList(positionCodes);
    }

    public async Task<int> GetPositionCodesCount(PositionCodeLevel highestLevel, string searchString = null, bool includeDeactivate = false) =>
        await positionCodeRepository.GetPositionCodesCount(currentUser.CompanyId, highestLevel, searchString, includeDeactivate);

    public async Task<List<PositionCodeRecord>> GetPositionCodesForEmployee(long employeeId) => await positionCodeRepository.GetPositionCodesForEmployee(employeeId, currentUser.CompanyId);

    public async Task<List<PositionCodeList>> GetPositionCodesInBatch(PositionCodeLevel highestLevel, int page, string searchString = "")
    {
        const int batchSize = 25;
        var positionCodes = await positionCodeRepository.GetPositionCodesInBatch(highestLevel, currentUser.CompanyId, (page - 1) * batchSize, batchSize, searchString);
        return await GetPositionCodeList(positionCodes);
    }

    public async Task<List<PositionDetails>> GetReportingToPositions(string positionCodeLevel, List<long> positionIds = null)
    {
        var positionCodes = await positionCodeRepository.GetReportingToPositions(currentUser.CompanyId, positionCodeLevel, positionIds);
        return positionCodes;
    }

    public async Task<Dictionary<long, PositionCodeLevel>> GetUserPositionCodes(long userId) => await positionCodeRepository.GetUserPositionCodes(userId, currentUser.CompanyId);

    public async Task<List<PositionDetails>> GetUserPositionCodesMin(long userId) => await positionCodeRepository.GetUserPositionCodeMin(userId, currentUser.CompanyId);

    public async Task<RepositoryResponse> SavePositionCode(PositionCodeInput positionCode)
    {
        var checkValid = await IsValidPosition(positionCode);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        var positionCodeEntry = new PositionCodeDTO
        {
            CodeId = positionCode.CodeId,
            CompanyId = currentUser.CompanyId,
            CreatedAt = DateTime.UtcNow,
            Deleted = false,
            LastUpdatedAt = DateTime.UtcNow,
            Level = positionCode.Level,
            Name = positionCode.Name,
            EmployeeType = positionCode.EmployeeType,
            ParentId = positionCode.IsReportingToAttached ? positionCode.ReportingTo.Value : null
        };

        if (!positionCode.IsPositionVacant)
        {
            positionCodeEntry.PositionCodeEntityMappings =
            [
                new PositionCodeEntityMapping { CompanyId = currentUser.CompanyId, CreatedAt = DateTime.UtcNow, EntityId = positionCode.EmployeeId.Value, IsDeactive = false }
            ];
        }


        var positionCodeRepositoryResponse = await positionCodeRepository.SavePositionCode(positionCodeEntry, currentUser.CompanyId);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesModernTrade = companySettings.UsesModernTrade;
        if (usesModernTrade && positionCodeRepositoryResponse.IsSuccess)
        {
            if (positionCode.EmployeeType == EmployeeType.ISR)
            {
                var positionOutletRepositoryResponse = await positionOutletRepository.SavePositionOutletMapping((long)positionCodeRepositoryResponse.Id, positionCode.OutletIds, currentUser.CompanyId);
                var isrOutletRepositoryResponse = new RepositoryResponse { IsSuccess = true };
                if (!positionCode.IsPositionVacant)
                {
                    isrOutletRepositoryResponse = await iSROutletMappingRepository.attatchOutletToIsr(positionCode.OutletIds, positionCode.EmployeeId.Value);
                }

                if (!positionOutletRepositoryResponse.IsSuccess)
                    return positionOutletRepositoryResponse;
                if (!isrOutletRepositoryResponse.IsSuccess)
                    return isrOutletRepositoryResponse;
                return positionCodeRepositoryResponse;
            }
        }

        return positionCodeRepositoryResponse;
    }

    public async Task<RepositoryResponse> UpdatePositionCode(PositionCodeInput positionCode)
    {
        var checkValid = await IsValidPosition(positionCode);
        if (checkValid.IsSuccess)
        {
            var positionCodeRepositoryResponse = await positionCodeRepository.UpdatePositionCode(positionCode, currentUser.CompanyId);
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var usesModernTrade = companySettings.UsesModernTrade;
            if (usesModernTrade && positionCodeRepositoryResponse.IsSuccess)
            {
                var positionOutletRepositoryResponse = await positionOutletRepository.SavePositionOutletMapping(positionCode.Id, positionCode.OutletIds, currentUser.CompanyId);
                var isrOutletRepositoryResponse = new RepositoryResponse { IsSuccess = true };
                if (!positionCode.IsPositionVacant)
                {
                    isrOutletRepositoryResponse = await iSROutletMappingRepository.attatchOutletToIsr(positionCode.OutletIds, positionCode.EmployeeId.Value);
                }

                if (!positionOutletRepositoryResponse.IsSuccess)
                    return positionOutletRepositoryResponse;
                if (!isrOutletRepositoryResponse.IsSuccess)
                    return isrOutletRepositoryResponse;
                return positionCodeRepositoryResponse;
            }

            return positionCodeRepositoryResponse;
        }

        return checkValid;
    }

    public async Task<long> GetPositionCount(long companyId, PaginationFilter validFilter)
    {
        return await positionCodeRepository.GetPositionCount(companyId, validFilter);
    }

    public async Task<List<EntityMin>> GetProductDivisionsForPositions(List<long> positionCodeIds) => await positionBeatRepository.GetProductDivisionsForPositions(positionCodeIds, currentUser.CompanyId);

    public async Task<List<EntityMin>> GetAllActivePositionCode() => (await positionCodeRepository.GetPositionsMin(currentUser.CompanyId)).Select(p => new EntityMin { Id = p.Id, Name = p.Name }).ToList();

    public async Task<List<PositionsFlat>> GetPositionCodeFlatWithUsersData()
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var highestPosLevel = companySettings.GetCompanyHighestPositionLevel;
        var positions = await positionCodeRepository.GetPositionCodeFlatWithUsers(highestPosLevel, currentUser.CompanyId);
        return positions;
    }

    public async Task<PositionSummary> GetPositionSummary(List<long> positionCodeIds)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var highestPosLevel = companySettings.GetCompanyHighestPositionLevel;
        var positions = await positionCodeRepository.GetPositionSummary(highestPosLevel, currentUser.CompanyId, positionCodeIds);
        return positions;
    }

    public async Task<List<PositionHierarchyDTO>> GetPositionHierarchy(PositionCodeLevel level, bool includeDeactive = false)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        int GetLevelNumberFromEnumValue(int level) => level switch
        {
            8 => 8,
            9 => 7,
            10 => 6,
            20 => 5,
            30 => 4,
            40 => 3,
            50 => 2,
            60 => 1,
        };

        var highestPosLevel = (int)companySettings.GetCompanyHighestPositionLevel;
        int maxLevelToCheck = GetLevelNumberFromEnumValue(highestPosLevel);
        var employeeDic = await employeeRepository.GetEmployeeDictionary(currentUser.CompanyId);
        var employeeIds = employeeDic.Select(e => e.Value.Id).ToList();
        var employeeErpidsDic = await employeeRepository.GetEmployeeERPDict(employeeIds, currentUser.CompanyId);
        var positionList = (await positionCodeRepository.GetPositionByEmployeeHierarchy(currentUser.CompanyId, employeeIds)).ToList();
        var allPositions = positionList
            .SelectMany(p => p.Positions)
            .Select(pos => new PositionsInfo
            {
                Id = pos.Id,
                Level = pos.Level
            })
            .Distinct()
            .ToList();
        var allPositionIds = allPositions.Select(x => x.Id).ToList();
        var positionLevel1Ids = allPositions.Where(x => x.Level == PositionCodeLevel.L1Position).Select(x => x.Id).Distinct().ToList();
        var positionUserDict = (await positionCodeRepository.GetPositionEntityMappings(allPositionIds))
            .GroupBy(x => x.PositionId)
            .ToDictionary(g => g.Key, g => g.First().ClientEmployeeId);
        var positionsDict = await positionCodeRepository.GetPositionHierarchy(highestPosLevel, currentUser.CompanyId, positionLevel1Ids, includeDeactive);

        var res = positionLevel1Ids.Select(x =>
        { 
            positionsDict.TryGetValue(x, out var pos);

            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level1Id ?? 0, out var empId1) ? empId1 : 0, out var level1User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level2Id ?? 0, out var empId2) ? empId2 : 0, out var level2User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level3Id ?? 0, out var empId3) ? empId3 : 0, out var level3User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level4Id ?? 0, out var empId4) ? empId4 : 0, out var level4User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level5Id ?? 0, out var empId5) ? empId5 : 0, out var level5User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level6Id ?? 0, out var empId6) ? empId6 : 0, out var level6User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level7Id ?? 0, out var empId7) ? empId7 : 0, out var level7User);
            employeeDic.TryGetValue(positionUserDict.TryGetValue(pos?.Level8Id ?? 0, out var empId8) ? empId8 : 0, out var level8User);

            return new PositionHierarchyDTO
            {
                Level1Code = pos?.Level1Code ?? string.Empty,
                Level1UserName = level1User?.Name ?? string.Empty,
                Level1ErpId = level1User?.ErpId ?? string.Empty,
                Level2Code = pos?.Level2Code ?? string.Empty,
                Level2UserName = level2User?.Name ?? string.Empty,
                Level2ErpId = level2User?.ErpId ?? string.Empty,
                Level3Code = pos?.Level3Code ?? string.Empty,
                Level3UserName = level3User?.Name ?? string.Empty,
                Level3ErpId = level3User?.ErpId ?? string.Empty,
                Level4Code = pos?.Level4Code ?? string.Empty,
                Level4UserName = level4User?.Name ?? string.Empty,
                Level4ErpId = level4User?.ErpId ?? string.Empty,
                Level5Code = pos?.Level5Code ?? string.Empty,
                Level5UserName = level5User?.Name ?? string.Empty,
                Level5ErpId = level5User?.ErpId ?? string.Empty,
                Level6Code = pos?.Level6Code ?? string.Empty,
                Level6UserName = level6User?.Name ?? string.Empty,
                Level6ErpId = level6User?.ErpId ?? string.Empty,
                Level7Code = pos?.Level7Code ?? string.Empty,
                Level7UserName = level7User?.Name ?? string.Empty,
                Level7ErpId = level7User?.ErpId ?? string.Empty,
                Level8Code = pos?.Level8Code ?? string.Empty,
                Level8UserName = level8User?.Name ?? string.Empty,
                Level8ErpId = level8User?.ErpId ?? string.Empty,
            };
        }).ToList();
        bool TryGetLevelFromPropertyName(string propName, out int levelNum)
        {
            levelNum = 0;
            if (propName.StartsWith("Level") && propName.Length > 5)
            {
                var levelChar = propName[5];
                if (char.IsDigit(levelChar))
                {
                    levelNum = int.Parse(levelChar.ToString());
                    return true;
                }
                else if (propName.Length > 6 && char.IsDigit(propName[6]))
                {
                    levelNum = int.Parse(propName.Substring(5, 2));
                    return true;
                }
            }
            return false;
        }

        var filteredRes = res.Where(dto =>
        {
            var stringProperties = typeof(PositionHierarchyDTO).GetProperties()
                .Where(p => p.PropertyType == typeof(string)
                            && TryGetLevelFromPropertyName(p.Name, out int levelNum)
                            && levelNum <= maxLevelToCheck);

            return stringProperties.Any(p =>
            {
                var value = (string)p.GetValue(dto);
                return !string.IsNullOrEmpty(value);
            });
        }).ToList();
        return res;
    }
    public async Task<List<UserWithPosition>> GetPositionLevelWisePositions(PositionCodeLevel level) => await positionCodeRepository.GetPositionLevelWisePositions(currentUser.CompanyId, level);

    public async Task<List<PositionCodeList>> GetDistinctPositionOfBeat(long beatId) => 
        await positionCodeRepository.GetDistinctPositionOfBeat(currentUser.CompanyId, new List<long> { beatId });

    public async Task<List<PositionCodeList>> GetDistinctPositionOfDistributor(long distributorId) =>
        await positionCodeRepository.GetDistinctPositionOfDistributor(currentUser.CompanyId, new List<long> { distributorId });
}

