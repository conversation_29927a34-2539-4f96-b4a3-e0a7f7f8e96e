﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
[Table("FilterConstraintDetails")]
public class FilterConstraintDetails : IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public FilterConstraintEntityType EntityType { get; set; }
    public string ConstraintJson { get; set; }
    public string Name { get; set; }
    public bool IsDeactive { get; set; }
    public string Description { get; set; }
    public UserPlatform UserPlatform { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}

