﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountTemplateDto
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public List<TemplateCustomFields> CustomFields { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public int AccountsCount { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long CreatedBy { get; set; }
    }

    public class LMSAccountTemplateInput
    {
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        public string Description { get; set; }

        public List<TemplateCustomFields> CustomFields { get; set; }

        public bool IsActive { get; set; }

        public bool IsDefault { get; set; }
    }
}
