using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSAccountTemplateRepository
    {
        Task<LMSAccountTemplateDto> GetByIdAsync(long id);
        Task<PagedResult<LMSAccountTemplateDto>> GetTemplatesAsync(long companyId, LMSAccountTemplateQueryParameters queryParameters);
        Task<LMSAccountTemplateDto> AddAsync(LMSAccountTemplateDto dto);
        Task<LMSAccountTemplateDto> UpdateAsync(LMSAccountTemplateDto dto);
        Task<bool> TemplateNameExistsAsync(long companyId, string templateName, long? currentTemplateId = null);
        Task<LMSAccountTemplateDto> GetDefaultTemplateAsync(long companyId);
        Task<int> GetAccountsCountAsync(long templateId);
    }
}
