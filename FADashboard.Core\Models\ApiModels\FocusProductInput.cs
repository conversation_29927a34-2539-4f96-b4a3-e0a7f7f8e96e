﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class FocusProductInput
{
    public string Description { get; set; }
    public List<long> DisplayCategoryIds { get; set; }
    public DateTime EndDate { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
    public List<long> PositionCodeIds { get; set; }
    public List<long?> PrimaryCategoryIds { get; set; }
    public List<long> SecondaryCategoryIds { get; set; }
    public List<long> SKUIds { get; set; }
    public DateTime StartDate { get; set; }
    public List<long> ShopTypeIds { get; set; }
    public List<long> OutletTagIds { get; set; }
}
