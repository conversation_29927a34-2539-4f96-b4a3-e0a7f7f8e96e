﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSCustomFieldValueRepository
    {
        Task<LMSCustomFieldValueDto> GetByIdAsync(long id);
        Task<List<LMSCustomFieldValueDto>> GetByEntityAsync(int entityType, long entityId);
        Task<LMSCustomFieldValueDto> AddAsync(LMSCustomFieldValueDto customFieldValue);
        Task UpdateAsync(LMSCustomFieldValueDto customFieldValue);
        Task DeleteAsync(long id);
        Task UpsertRangeForEntityAsync(int entityType, long entityId, List<LMSCustomFieldValueDto> values);
        Task DeleteByEntityAsync(int entityType, long entityId);
    }
}
