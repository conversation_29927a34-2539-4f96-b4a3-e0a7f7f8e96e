﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FATradeNewLaunchProducts")]
public class TradeNewLaunchProduct : IAuditedEntity, IDeletable
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }

    [StringLength(256)] public string Description { get; set; }

    public DateTime EndDate { get; set; }

    public long Id { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    [StringLength(64)] public string Name { get; set; }

    [NotMapped]
    public List<long> SKUIds
    {
        get => string.IsNullOrEmpty(SKUList) ? [] : JsonConvert.DeserializeObject<List<long>>(SKUList);
        set => SKUList = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use SKUIds Instead!")]
    [StringLength(1024)]
    public string SKUList { get; set; }

    public DateTime StartDate { get; set; }

    [NotMapped]
    public List<long> ZoneIds
    {
        get => string.IsNullOrEmpty(ZoneList) ? [] : JsonConvert.DeserializeObject<List<long>>(ZoneList);
        set => ZoneList = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use ZoneIds Instead!")]
    [StringLength(1024)]
    public string ZoneList { get; set; }

    [NotMapped]
    public List<long> RegionIds
    {
        get => string.IsNullOrEmpty(RegionList) ? [] : JsonConvert.DeserializeObject<List<long>>(RegionList);
        set => RegionList = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use RegionIds Instead!")]
    [StringLength(1024)]
    public string RegionList { get; set; }

    [NotMapped]
    public List<long> DistErpIds
    {
        get => string.IsNullOrEmpty(DistErpIdsList) ? [] : JsonConvert.DeserializeObject<List<long>>(DistErpIdsList);
        set => DistErpIdsList = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use DistErpIds Instead!")]
    [StringLength(1024)]
    public string DistErpIdsList { get; set; }
}
