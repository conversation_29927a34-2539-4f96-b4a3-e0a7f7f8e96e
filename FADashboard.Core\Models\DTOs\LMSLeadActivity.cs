﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadActivityDTO
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long CompanyId { get; set; }

        [Required]
        public long LeadId { get; set; }

        [Required]
        public LMSActivityType ActivityType { get; set; }

        [Required]
        public LMSActivitySource Source { get; set; }

        [StringLength(255)]
        public string Title { get; set; }

        public string Description { get; set; }

        [StringLength(500)]
        public List<string> Attachment { get; set; }

        // Meeting
        public DateTime? MeetingStartTime { get; set; }
        public DateTime? MeetingEndTime { get; set; }
        public bool? MeetingLocation { get; set; }

        // Call Details
        public long? CallDuration { get; set; }
        public LMSCallType? CallType { get; set; }
        public long? LeadContactId { get; set; }

        // Task Details
        public long? TaskOwner { get; set; }
        public long? PositionCode { get; set; }
        public LMSTaskStatus? TaskStatus { get; set; }
        public LMSTaskPriority? Priority { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }
        public DateTime? ClosedDateTime { get; set; }

        public bool? isOVC { get; set; }
        public bool? isDeleted { get; set; }

        // New consolidated audit column
        public string AuditTrail { get; set; }  // Stores JSON
        public string Remark { get; set; }

        // Auditing
        [Required]
        public long CreatedBy { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
