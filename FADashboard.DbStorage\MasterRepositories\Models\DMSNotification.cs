﻿using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class DMSNotification
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string DistributorArray { get; set; }
    public Guid Guid { get; set; }
    public long Id { get; set; }
    public string Image { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string Link { get; set; }
    public long? Region { get; set; }
    public string Text { get; set; }
    public int? Time { get; set; }
    public string Title { get; set; }
    public DMSNotificationType Type { get; set; }
    public long? Zone { get; set; }
}
