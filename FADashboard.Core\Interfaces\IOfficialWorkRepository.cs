﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;
namespace FADashboard.Core.Interfaces;

public interface IOfficialWorkRepository
{
    Task<List<OfficialWorkFlat>> GetAllOfficialWorks(bool showInvalid, long companyId);
    Task<OfficialWorkFlat> GetOfficialWorkById(long id, long companyId);
    Task<RepositoryResponse> ActivateDeactivateOfficialWork(long id, bool action, long companyId);
    Task<RepositoryResponse> CreateOfficialWork(OfficialWorkFlat officialWorkFlat, long companyId);
    Task<RepositoryResponse> UpdateOfficialWork(OfficialWorkFlat officialWorkFlat, long companyId);

    Task<List<OfficialWorkFlat>> GetOfficialWorksTypes(long companyId, PortalUserRole userRole);
}
