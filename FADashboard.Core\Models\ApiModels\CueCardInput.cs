﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class CueCardInput
{
    public long Id { get; set; }
    [StringLength(1000)]
    public string Name { get; set; }
    [StringLength(1000)]
    public string Description { get; set; }
    public int Sequence { get; set; }
    public CueCardPrimaryType PrimaryType { get; set; }
    public CueCardSecondaryType SecondaryType { get; set; }
    [StringLength(1000)]
    public string Title { get; set; }
    public bool IsActionable { get; set; }
    public CueCardActionFlow? ActionFlow { get; set; }
    public CueCardPerspective Perspective { get; set; }
    public string Section { get; set; }
    public List<OutletMetricInput> OutletMetrics { get; set; }
    public UserPlatform UserPlatform { get; set; }
    public string Header { get; set; }


}

public class OutletMetricInput
{
    public long Id { get; set; }
    public string Name { get; set; }
    public bool IsExternal { get; set; }
    public bool IsActive { get; set; }
    public long CueCardId { get; set; }
    public OutletMetricFrequency? Frequency { get; set; }
    public OutletMetricCurrentDayOfWeek? WeekStartDay { get; set; }
    public List<OutletMetricParameterValueInput> ParameterValues { get; set; }
    public long? GlobalMetricId { get; set; }
    public long? ExternalMetricId { get; set; }

    [Required(ErrorMessage = "Description is required.")]
    [StringLength(1000)]
    public string Description { get; set; }
    public bool? DateFilter { get; set; }
}

public class OutletMetricParameterValueInput
{
    public string ParameterName { get; set; }
    public string Reference { get; set; }
    public List<long> ParameterValues { get; set; }

}

public class CueCardSequence
{
    public long Id { get; set; }
    public int Sequence { get; set; }
}
