﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class OrderBlockService(IOrderBlockRepository orderBlockRepository) : RepositoryResponse
{
    public async Task<List<OrderBlockList>> GetAllBlockOrder()
    {
        var allBlockOrders = await orderBlockRepository.GetAllBlockOrder();
        return allBlockOrders;
    }

    public async Task<OrderBlockModel> GetOrderBlockById(long id)
    {
        var orderBlock = await orderBlockRepository.GetOrderBlockById(id);
        return orderBlock;
    }

    public async Task<RepositoryResponse> CreateUpdateOrderBlock(OrderBlockModel orderBlock)
    {
        if (orderBlock.Id == 0)
        {
            return await orderBlockRepository.CreateOrderBlock(orderBlock);
        }

        return await orderBlockRepository.UpdateOrderBlock(orderBlock);
    }

    public async Task<RepositoryResponse> ActivateDeactivateOrderBlock(long id, bool action) => await orderBlockRepository.ActivateDeactivateOrderBlock(id, action);
}
