﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSAccountAddressService
    {
        Task<LMSAccountAddressDto> GetAccountAddressByIdAsync(long id);
        Task<IEnumerable<LMSAccountAddressDto>> GetAllAccountAddressesAsync();
        Task<IEnumerable<LMSAccountAddressDto>> GetAccountAddressesByAccountIdAsync(long accountId);
        Task<LMSAccountAddressDto> CreateAccountAddressAsync(LMSAccountAddressCreateInput accountAddressDto, long createdByUserId, long companyId);
        Task<LMSAccountAddressDto> UpdateAccountAddressAsync(long id, LMSAccountAddressUpdateInput accountAddressDto, long? updatedByUserId);
        Task<bool> DeleteAccountAddressAsync(long id, long? updatedByUserId);
    }
}
