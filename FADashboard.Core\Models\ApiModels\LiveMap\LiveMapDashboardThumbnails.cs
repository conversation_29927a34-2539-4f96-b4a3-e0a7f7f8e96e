﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels.LiveMap;

public class LiveMapThumbnail
{
    public string AssignedBeat { get; set; }
    public string AssignedRoute { get; set; }
    public decimal? DayEndLatitude { get; set; }
    public decimal? DayEndLongitude { get; set; }
    public string DayStartAddress { get; set; }
    public DayStartType DayStartType { get; set; }
    public long EmployeeId { get; set; }
    public bool IsDayEnd { get; set; }
    public bool IsDayStart { get; set; }
    public bool IsJointWorking { get; set; }
    public long? JWEmployeeId { get; set; }
    public decimal? Latitude { get; set; }
    public List<LiveMapThumbnailLocation> LiveMapThumbnailLocations { get; set; }
    public decimal? Longitude { get; set; }
    public int? OVC { get; set; }
    public int? PC { get; set; }
    public string SelectedBeat { get; set; }
    public string SelectedRoute { get; set; }
    public int? TC { get; set; }
}

public class LiveMapThumbnailLocation : LiveMapEmployeeLocations
{
    public long VisitOrder { get; set; }
}
