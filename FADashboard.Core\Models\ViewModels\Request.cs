﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ViewModels;

public class Requests
{
    public string ActionTakenBy { get; set; }
    public long Id { get; set; }
    public bool? IsApproved { get; set; }
    public OutletRequestStatus ManagerApprovedStatus { set; get; }
    public string ManagerApprovedStatusString { set; get; }
    public string ManagerName { get; set; }
    public DateTime? OrderByTime { get; set; }
    public DateTime RequestedOn { get; set; }
    public string RequestId { get; set; }
    public long RequestorId { get; set; }
    public string RequestorName { get; set; }
    public bool Reviewed { get; set; }
    public string OutletName { get; set; }
    public UserPlatform UserPlatform { get; set; }
    public string OwnersName { get; set; }
    public long? ShopTypeId { get; set; }
    public long? ReplacedOutletId { get; set; }

}
public class JourneyDiversionList
{
    public long Id { set; get; }
    public long RequestorId { get; set; }
    public string RequestorName { get; set; }
    public DateTime RequestedOn { get; set; }
    public string ManagerName { get; set; }
    public string ActionTakenBy { get; set; }
    public bool IsApproved { get; set; }
    public DateTime? OrderByTime { set; get; }
}
public class JourneyDiversionRequest
{
    public long Id { set; get; }
    public string AlertTitle { get; set; }
    public string AlertDescription { get; set; }
    public long? EntityId { set; get; }
}
