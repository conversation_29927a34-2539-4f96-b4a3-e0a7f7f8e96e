﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs;

public class ReportFilterDetails
{
    public ReportFilterDetails(ReportSubscription subscription, Guid subscriptionKey)
    {
        var enumForReport = subscription.Report.ReportType;
        ReportName = subscription.Name;
        EnumForReportAssembly = enumForReport;
        SubscriptionKey = subscriptionKey;

        //Hierarchy Filters
        switch (enumForReport)
        {
            case EnumForReportAssembly.AttendanceReportFromReportPerspective:
                IsQueueable = true;
                IsQueueableDownload = true;
                FilterType = FilterType.EmployeeHierarchyMultiSelect;
                break;
            case EnumForReportAssembly.Unknown:
                break;
            case EnumForReportAssembly.ActivityDesign:
                break;
            case EnumForReportAssembly.AttendanceReport:
                break;
            case EnumForReportAssembly.OutletWiseSalesReport:
                break;
            case EnumForReportAssembly.OutletWiseSalesReportEmpHierarchy:
                break;
            case EnumForReportAssembly.OutletWiseSalesReportV4:
                break;
            case EnumForReportAssembly.DetailedSalesReport:
                break;
            case EnumForReportAssembly.NoSalesAnalysisReport:
                break;
            case EnumForReportAssembly.DeliveryOrderFlatDesign:
                break;
            case EnumForReportAssembly.OutletWiseTarget:
                break;
            case EnumForReportAssembly.OutletWiseTargetPC:
                break;
            case EnumForReportAssembly.PeoplePerformanceReport:
                break;
            case EnumForReportAssembly.SummarySheet:
                break;
            case EnumForReportAssembly.SummarySheetUsingPivot:
                break;
            case EnumForReportAssembly.ISRSummaryReportVMT:
                break;
            case EnumForReportAssembly.OpeningClosingReport:
                break;
            case EnumForReportAssembly.OpeningClosingReportV4:
                break;
            case EnumForReportAssembly.OpeningClosingReportDetailed:
                break;
            case EnumForReportAssembly.NewAttendanceSummary:
                break;
            case EnumForReportAssembly.NewAttendanceSummarySecondsheet:
                break;
            case EnumForReportAssembly.CategoryAnalysis:
                break;
            case EnumForReportAssembly.CategoryAnalysisReport:
                break;
            case EnumForReportAssembly.MonthOnMonthCategoryAnalysis:
                break;
            case EnumForReportAssembly.CategoryAnalysisReportFromPerspective:
                break;
            case EnumForReportAssembly.MonthOnMonthCategoryAnalysisFromPerspective:
                break;
            case EnumForReportAssembly.FlatSales:
                break;
            case EnumForReportAssembly.FlatSalesWithParams:
                break;
            case EnumForReportAssembly.FlatSalesWithAllAtZSM:
                break;
            case EnumForReportAssembly.FlatSalesFromReportPerspective:
                break;
            case EnumForReportAssembly.FlatSalesFromReportPerspectiveCreatedInPartsMechanism:
                break;
            case EnumForReportAssembly.FlateSalesGeographicalHierarchy:
                break;
            case EnumForReportAssembly.FlatSalesDistFromSales:
                break;
            case EnumForReportAssembly.FlatSalesLive:
                break;
            case EnumForReportAssembly.NoSalesReasonReport:
                break;
            case EnumForReportAssembly.PersonalizedSalesSecondaryOrderDump:
                break;
            case EnumForReportAssembly.FlatRetailerStockReport:
                break;
            case EnumForReportAssembly.FlatSaleReturnReplacement:
                break;
            case EnumForReportAssembly.FlatDistributorStock:
                break;
            case EnumForReportAssembly.SecondaryOrderValidationReport:
                break;
            case EnumForReportAssembly.SecondaryOrderValidationReportFromReportPerspective:
                break;
            case EnumForReportAssembly.SecondaryOrderValidationReportWithParams:
                break;
            case EnumForReportAssembly.EmployeePerformanceReport:
                break;
            case EnumForReportAssembly.EmployeePerformanceReportFromReportsPerspective:
                break;
            case EnumForReportAssembly.LVCR:
                break;
            case EnumForReportAssembly.SummarySheetPDWiseFromPerspective:
                break;
            case EnumForReportAssembly.SummarySheet_SC:
                break;
            case EnumForReportAssembly.SummarySheetJockey:
                break;
            case EnumForReportAssembly.SummarySheetJockeyFromPerspective:
                break;
            case EnumForReportAssembly.SummarySheetFromPerspective:
                break;
            case EnumForReportAssembly.SummarySheetSCWiseFromPerspective:
                break;
            case EnumForReportAssembly.SummarySheetSCWiseFromPerspectiveUsingNewIds:
                break;
            case EnumForReportAssembly.SummarySheetMT:
                break;
            case EnumForReportAssembly.SummarySheetManagerWorkingFromPerspective:
                break;
            case EnumForReportAssembly.SummarySheetMTSuperVisor:
                break;
            case EnumForReportAssembly.PeoplePerformanceReportSC:
                break;
            case EnumForReportAssembly.MOMEmpPerformance:
                break;
            case EnumForReportAssembly.ModernTradeTeritiarySales:
                break;
            case EnumForReportAssembly.ModernTradeStoreStock:
                break;
            case EnumForReportAssembly.ModernTradeStoreStockVMT:
                break;
            case EnumForReportAssembly.ModernTradePurchaseOrdedrs:
                break;
            case EnumForReportAssembly.ModernTradeFillRate:
                break;
            case EnumForReportAssembly.ASMRSMWorking:
                break;
            case EnumForReportAssembly.NewOutletSale:
                break;
            case EnumForReportAssembly.NewKRATargetReport:
                break;
            case EnumForReportAssembly.EmployeeWiseTargetReport:
                break;
            case EnumForReportAssembly.EmployeeWiseTargetReportOverAll:
                break;
            case EnumForReportAssembly.DetailedSalesReportBook:
                break;
            case EnumForReportAssembly.DistributorPerformanceOnBasis:
                break;
            case EnumForReportAssembly.NewActivitySheet:
                break;
            case EnumForReportAssembly.ProductSurveyResponseReport:
                break;
            case EnumForReportAssembly.FlatSchemeSales:
                break;
            case EnumForReportAssembly.FlatPrimaryOrderReport:
                break;
            case EnumForReportAssembly.ConsolidatedSurveyReport:
                break;
            case EnumForReportAssembly.FlatSurveyReport:
                break;
            case EnumForReportAssembly.FlatSurveyReportDailySummary:
                break;
            case EnumForReportAssembly.FlatSurveyReportFromV4:
                break;
            case EnumForReportAssembly.EmbeddedDailyEmail:
                break;
            case EnumForReportAssembly.OutletDumpGeoHierarchy:
                break;
            case EnumForReportAssembly.OutletDumpEmpHierarchy:
                break;
            case EnumForReportAssembly.TourPlanSubmission:
                break;
            case EnumForReportAssembly.PJPAdherence:
                break;
            case EnumForReportAssembly.PJPAdherenceFromReportPerspective:
                break;
            case EnumForReportAssembly.PJPAdherenceFromReportPerspectiveAllSeg:
                break;
            case EnumForReportAssembly.FlatPaymentReport:
                break;
            case EnumForReportAssembly.OCER:
                break;
            case EnumForReportAssembly.OCERFromReport:
                break;
            case EnumForReportAssembly.BCER:
                break;
            case EnumForReportAssembly.FlatStarTVReport:
                break;
            case EnumForReportAssembly.BrandWiseMTDReport:
                break;
            case EnumForReportAssembly.FlatRetailerReturnReportDump:
                break;
            case EnumForReportAssembly.FlatRetailerReturnReport:
                break;
            case EnumForReportAssembly.TADAReport:
                break;
            case EnumForReportAssembly.DeliveryOrderApparel:
                break;
            case EnumForReportAssembly.FlatSalesDistFromSalesWithoutParams:
                break;
            case EnumForReportAssembly.FlatWalkInOrderReport:
                break;
            case EnumForReportAssembly.StockInwardReport:
                break;
            case EnumForReportAssembly.StockInwardReportVMT:
                break;
            case EnumForReportAssembly.StockTertiaryReport:
                break;
            case EnumForReportAssembly.StockTertiaryReportVMT:
                break;
            case EnumForReportAssembly.RouteChangeRequestReport:
                break;
            case EnumForReportAssembly.ModernTradeClosingStockReport:
                break;
            case EnumForReportAssembly.ModernTradeClosingStockReportVMT:
                break;
            case EnumForReportAssembly.OutletCreationRequestReport:
                break;
            case EnumForReportAssembly.MasterDataDumpOutletCountReport:
                break;
            case EnumForReportAssembly.MasterDataPositionDumpOutletCountReport:
                break;
            case EnumForReportAssembly.MasterDataPositionDumpOutletCountReportV4:
                break;
            case EnumForReportAssembly.ImageRecognitionReport:
                break;
            case EnumForReportAssembly.SummaryReportVersion2:
                break;
            case EnumForReportAssembly.ImageAuditingReportTotalScore:
                break;
            case EnumForReportAssembly.MustSellReportFromReportPerspective:
                break;
            case EnumForReportAssembly.KRAAdherenceReportFromReportPerspective:
                break;
            case EnumForReportAssembly.NewOutletDetailedReport:
                break;
            case EnumForReportAssembly.EmployeeProductivityReportFromPerspective:
                break;
            case EnumForReportAssembly.FocusProductReportFromV4:
                break;
            case EnumForReportAssembly.FocusProductReportFromV4WithoutTargets:
                break;
            case EnumForReportAssembly.ProductMasterReport:
                break;
            case EnumForReportAssembly.TourPlanAdherenceReportFromPerspective:
                break;
            case EnumForReportAssembly.POCER:
                break;
            case EnumForReportAssembly.OutletReachReportV4:
                break;
            case EnumForReportAssembly.BVCRFromPerspective:
                break;
            case EnumForReportAssembly.DailyDistributorSalesReport:
                break;
            case EnumForReportAssembly.TimeLineReport:
                break;
            case EnumForReportAssembly.DailyLocationReportV4:
                break;
            case EnumForReportAssembly.TransactionDumpReport:
                break;
            case EnumForReportAssembly.OpeningClosingStockReportV4:
                break;
            case EnumForReportAssembly.RoutePlanReport:
                break;
            case EnumForReportAssembly.Other:
                break;
            case EnumForReportAssembly.VisitDumpReportFromPerspective:
                break;
            case EnumForReportAssembly.EmployeeWiseTargetVsAch:
                break;
            case EnumForReportAssembly.MTEmployeeTargetVsAch:
                break;
            case EnumForReportAssembly.MTCalculatedClosingReport:
                break;
            case EnumForReportAssembly.MTCalculatedClosingReportVMT:
                break;
            case EnumForReportAssembly.MTEmployeeWiseTargetVsAch:
                break;
            case EnumForReportAssembly.MTOutletWiseTargetVsAch:
                break;
            case EnumForReportAssembly.MTCampaignWiseIrReport:
                break;
            case EnumForReportAssembly.MTOutletWiseFocusProductTarget:
                break;
            case EnumForReportAssembly.MTOutletDailyTargetReport:
                break;
            case EnumForReportAssembly.MTAttendanceReport:
                break;
            case EnumForReportAssembly.MTAttendanceRegularizationReport:
                break;
            case EnumForReportAssembly.CampaignImageAuditReport:
                break;
            case EnumForReportAssembly.MTFocusProductTargetReportFromV4:
                break;
            case EnumForReportAssembly.DailyStatsReportLiveV4:
                break;
            case EnumForReportAssembly.DeadOutletReport:
                break;
            case EnumForReportAssembly.EmployeeDistributorPerformanceReport:
                break;
            case EnumForReportAssembly.PositionDistributorPerformanceReport:
                break;
            case EnumForReportAssembly.LivePrimaryOrderReport:
                break;
            case EnumForReportAssembly.PaymentCollectionReport:
                break;
            case EnumForReportAssembly.BattleGroundReport:
                break;
            case EnumForReportAssembly.FABattlegroundKPIAchievmentReport:
                break;
            case EnumForReportAssembly.MTSummaryReport:
                break;
            case EnumForReportAssembly.MTPerformanceAnalysisReport:
                break;
            case EnumForReportAssembly.MTISRSummaryReport:
                break;
            case EnumForReportAssembly.FlatSchemePerformanceReport:
                break;
            case EnumForReportAssembly.FlatSchemePerformanceV4Report:
                break;
            case EnumForReportAssembly.CouponUtilizationDumpReport:
                break;
            case EnumForReportAssembly.OutletTargetReportV4:
                break;
            case EnumForReportAssembly.CensusWiseOutletDistribution:
                break;
            case EnumForReportAssembly.NewOutletDetailedHybridReport:
                break;
            case EnumForReportAssembly.VanStockReport:
                break;
            case EnumForReportAssembly.CampaignTaskResponseDumpReport:
                break;
            case EnumForReportAssembly.PrimaryTargetVSAchievement:
                break;
            case EnumForReportAssembly.TertiaryOfftake:
                break;
            case EnumForReportAssembly.TransactionDumpReportLive:
                break;
            case EnumForReportAssembly.NewSubDDetailedReport:
                break;
            case EnumForReportAssembly.AttendanceNormBasedReport:
                break;
            case EnumForReportAssembly.PrimaryOrderValidationReport:
                break;
            case EnumForReportAssembly.UserManagementNewDashboardReport:
                break;
            case EnumForReportAssembly.JockeyRetailerSalesDispatch:
                break;
            case EnumForReportAssembly.FlexibleReport:
                break;
            case EnumForReportAssembly.MonthlyDistributerStockReport:
                break;
            case EnumForReportAssembly.LiveDistributerStockReport:
                break;
            case EnumForReportAssembly.DateWiseDistributerStockReport:
                break;
            case EnumForReportAssembly.PrimarySalesReport_FromMaster:
                break;
            case EnumForReportAssembly.PrimaryOrdersReport:
                break;
            case EnumForReportAssembly.UserLoginActivity:
                break;
            case EnumForReportAssembly.FlatSalesFromReportPerspectiveIsProductive:
                break;
            case EnumForReportAssembly.LDMSFlatSales:
                break;
            case EnumForReportAssembly.LDMSClaimReports:
                break;
            case EnumForReportAssembly.LDMSInventory:
                break;
            case EnumForReportAssembly.InvoiceReport:
                break;
            case EnumForReportAssembly.OutletCategoryWiseSalesReport:
                break;
            case EnumForReportAssembly.InvoiceReportForAccountManager:
                break;
            case EnumForReportAssembly.UserJourneyLocationReport:
                break;
            case EnumForReportAssembly.L3MEmpAnalysis:
                break;
            case EnumForReportAssembly.TADAExpenseDump:
                break;
            case EnumForReportAssembly.TADAExpenseDumpMonthly:
                break;
            case EnumForReportAssembly.TADAMonthlyExpense:
                break;
            case EnumForReportAssembly.PaymentCollectionReportVanSales:
                break;
            case EnumForReportAssembly.RepeatSalesReport:
                break;
            case EnumForReportAssembly.DMSOneByTwoReport:
                break;
            case EnumForReportAssembly.DMSOneByOneReport:
                break;
            case EnumForReportAssembly.DMSInvoicesDumpReport:
                break;
            case EnumForReportAssembly.StockLedgerReport:
                break;
            case EnumForReportAssembly.CurrentOpeningStockReport:
                break;
            case EnumForReportAssembly.SecondaryOrderVsInvoiceSummary:
                break;
            case EnumForReportAssembly.SecondaryOrderVsInvoiceDump:
                break;
            case EnumForReportAssembly.GILSecondarySalesDump:
                break;
            case EnumForReportAssembly.DSMWiseTargetVsAch:
                break;
            case EnumForReportAssembly.DistributorSummaryReport:
                break;
            case EnumForReportAssembly.SchemeUtilizationReport:
                break;
            case EnumForReportAssembly.SummarySheetManagerWorkingWithPCpivot:
                break;
            case EnumForReportAssembly.NewPaymentCollectionReport:
                break;
            case EnumForReportAssembly.SKUWiseOutletReport:
                break;
            case EnumForReportAssembly.NoSalesReasonReportForMustSellProducts:
                break;
            case EnumForReportAssembly.SecondaryInvoiceDumpReport:
                break;
            case EnumForReportAssembly.SecondaryOrderDumpWithLivePosition:
                break;
            case EnumForReportAssembly.DailySalesTrackerCategoryWise:
                break;
            case EnumForReportAssembly.MTLiveAttendance:
                break;
            case EnumForReportAssembly.MTCampaignStockReport:
                break;
            case EnumForReportAssembly.MTShelfShareReport:
                break;
            case EnumForReportAssembly.MTPOandDispatchReportVMT:
                break;
            case EnumForReportAssembly.OpenCampaignResponseReport:
                break;
            case EnumForReportAssembly.CompetitorSurveyReportVMT:
                break;
            case EnumForReportAssembly.MTOutletDumpEmployeeHierarchyReport:
                break;
            case EnumForReportAssembly.ConsumerWiseTertiaryReport:
                break;
            case EnumForReportAssembly.LocationBulkUploadDump:
                break;
            case EnumForReportAssembly.FAEngageReport:
                break;
            case EnumForReportAssembly.OutletWiseSchemeUtiLizationReport:
                break;
            case EnumForReportAssembly.AssetManagementReport:
                break;
            case EnumForReportAssembly.OutletDumpGeoHierarchyV4:
                break;
            case EnumForReportAssembly.QPSSchemeReport:
                break;
            case EnumForReportAssembly.OutletDumpReportEmami:
                break;
            case EnumForReportAssembly.ProductRecommendationDumpReport:
                break;
            case EnumForReportAssembly.BeatWiseTAReport:
                break;
            case EnumForReportAssembly.BTEmpProductivityReport:
                break;
            case EnumForReportAssembly.VisitWiseSalesSAR:
                break;
            case EnumForReportAssembly.GamificationReport:
                break;
            case EnumForReportAssembly.TADAApprovedPendingExpenseDump:
                break;
            case EnumForReportAssembly.NewTADAApprovedExpenseDump:
                break;
            case EnumForReportAssembly.VanSalesReport:
                break;
            case EnumForReportAssembly.GILRePrimarySalesDump:
                break;
            case EnumForReportAssembly.Custom_SAR_TargetvsAchivement:
                break;
            case EnumForReportAssembly.EmpRouteOutletDumpReport:
                break;
            case EnumForReportAssembly.RetailerStockReport:
                break;
            default:
                FilterType = FilterType.EmployeeHierarchy;
                IsDownloadable = true;
                break;
        }
    }

    public EnumForReportAssembly EnumForReportAssembly { get; private set; }
    public FilterType FilterType { get; private set; }
    public bool IsDownloadable { get; private set; }
    public bool IsQueueable { get; private set; }
    public bool IsQueueableDownload { get; private set; }
    public bool IsRequestable { get; set; }
    public string ReportName { get; set; }
    public Guid SubscriptionKey { get; set; }
}
