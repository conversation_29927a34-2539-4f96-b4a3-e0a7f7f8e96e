﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("RegionwiseODS")]
public class RegionWiseODS : IAuditedEntity, ICreatedEntity
{
    public long Id { get; set; }
    public long RegionId { get; set; }
    public long ZoneId { get; set; }
    public Zone Zone { get; set; }
    public Regions Region { get; set; }
    public long CompanyId { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
