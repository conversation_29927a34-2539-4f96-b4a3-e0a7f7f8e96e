﻿using FADashboard.Core.Models;
using FADashboard.DbStorage.MasterRepositories.Models;

namespace FADashboard.Core.Interfaces;

public interface ICESSCategoryRepository
{
    Task<List<ProductCESSCategoryFlat>> GetCESSCategories(long companyId, bool showDeactive = false);
    Task<RepositoryResponse> CreateCESSCategory(CESSCategoryInput cessCategoryInput, long companyId);
    Task<RepositoryResponse> UpdateCESSCategory(CESSCategoryInput cessCategoryInput, long companyId);
    Task<RepositoryResponse> DeactiveCESSCategory(long id, long companyId);
}
