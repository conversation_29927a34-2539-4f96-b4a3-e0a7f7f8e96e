﻿using System.Text;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class OpenMarketService(IOpenMarketRepository openMarketRepository) : RepositoryResponse
{
    public async Task<StockLoadoutRequestModel> GetStockLoadOutModelForRecipientGuid(string encryptedString)
    {
        var decryptedString = Decrypt(encryptedString);
        var recipientGuid = new Guid();
        try
        {
            recipientGuid = new Guid(decryptedString);
        }
        catch (Exception ex)
        {
            throw;
        }

        var openMarketDayStockId = await openMarketRepository.GetOpenMarketDayStockId(recipientGuid);
        var (dayStartStockStatus, companyId) = await openMarketRepository.GetDayStartStockStatusAndCompanyId(openMarketDayStockId);
        if ((bool)dayStartStockStatus)
        {
            var stockRequestDetails = await openMarketRepository.GetStockLoadOut(openMarketDayStockId, (bool)dayStartStockStatus, companyId);
            return stockRequestDetails;
        }

        return null;
    }

    public async Task<StockReconcilationModel> GetStockReconciliationModelForRecipientGuid(string encryptedString)
    {
        var decryptedString = Decrypt(encryptedString);
        var recipientGuid = new Guid();
        try
        {
            recipientGuid = new Guid(decryptedString);
        }
        catch (Exception)
        {
            throw;
        }

        var openMarketDayStockId = await openMarketRepository.GetOpenMarketDayStockId(recipientGuid);
        var (dayStartStockStatus, companyId) = await openMarketRepository.GetDayStartStockStatusAndCompanyId(openMarketDayStockId);
        if (!(bool)dayStartStockStatus)
        {
            var stockSettlementDetails = await openMarketRepository.GetStockForOrderReconcilation(openMarketDayStockId, (bool)dayStartStockStatus, companyId);
            return stockSettlementDetails;
        }

        return null;
    }

    public async Task<RepositoryResponse> UpdateDayStockStatus(UpdateStockRequest updateStockRequest)
    {
        var approvalStatus = await openMarketRepository.VerifyApprovalStatus(updateStockRequest.OpenMarketDayStockId);

        return approvalStatus switch
        {
            ApprovedStatus.Pending => await openMarketRepository.UpdateDayStockStatus(updateStockRequest),
            ApprovedStatus.Approved => new RepositoryResponse { Message = "This has been already approved", IsSuccess = false },
            ApprovedStatus.Rejected => new RepositoryResponse { Message = "This has been rejected", IsSuccess = false },
            ApprovedStatus.Cancelled => throw new NotImplementedException(),
            ApprovedStatus.UnderReview => throw new NotImplementedException(),
            ApprovedStatus.ForceEnd => throw new NotImplementedException(),
            null => throw new NotImplementedException(),
            _ => throw new NotImplementedException()
        };
    }

    private static string Decrypt(string encryptedValue)
    {
        var encryptedId = encryptedValue;
        var decodedBytes = Convert.FromBase64String(encryptedId);
        var decodedData = Encoding.UTF8.GetString(decodedBytes);
        return decodedData;
    }
}
