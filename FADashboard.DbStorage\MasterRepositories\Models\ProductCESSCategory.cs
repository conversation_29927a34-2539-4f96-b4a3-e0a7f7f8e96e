﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("CESSCategoryTaxes")]
public class CESSCategoryTax : ICreatedEntity
{
    public decimal CESS { get; set; }
    public Company Company { get; set; }
    public ProductCESSCategory CompanyCESSCategory { get; set; }
    public long CompanyCESSCategoryId { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    public DateTime? EndedAt { get; set; }
    public long Id { get; set; }

    [Audited] public bool IsEnded { get; set; }
}

[Table("ProductCESSCategories")]
public class ProductCESSCategory : ICreatedEntity, IDeletable
{
    public IEnumerable<CESSCategoryTax> CESSCategoryTaxes { get; set; }
    public Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    public bool Deleted { get; set; }
    public long Id { get; set; }

    [StringLength(256)] public string Name { get; set; }
}
