﻿using System.ComponentModel;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ProductVisibilityRuleInput
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public ProductOrderType OrderType { get; set; }
    public ProductRuleType RuleType { get; set; }
    public ProductVisibilityTag ProductVisibilityTag { get; set; }
    public ProductGeographyConstraint GeographyConstraint { get; set; }
    public ProductDistributorConstraint DistributorConstraint { get; set; }
    public ProductFactoryConstraint FactoryConstraint { get; set; }
    public ProductOutletConstraint OutletConstraint { get; set; }
    public ProductConstraint ProductConstraint { get; set; }
    public ProductTertiaryConstraint TertiaryConstraint { get; set; }
    public bool IsOutletConstraint { set; get; }
    public bool IsDistributorConstraint { set; get; }
    public bool IsFactoryConstraint { set; get; }
    public bool IsProductConstraint { set; get; }
    public bool IsGeographyConstraint { set; get; }
    public bool IsTertiaryConstraint { set; get; }
    public bool IsDeactive { get; set; }
}

public class ProductVisibilityRuleList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public ProductOrderType OrderType { get; set; }
    public ProductRuleType RuleType { get; set; }
    public ProductVisibilityTag ProductVisibilityTag { get; set; }
    public bool IsDeactive { get; set; }
    public string ApplicableOn { get; set; }
    public ProductGeographyConstraint GeographyConstraint { get; set; }
    public ProductDistributorConstraint DistributorConstraint { get; set; }
    public ProductFactoryConstraint FactoryConstraint { get; set; }
    public ProductOutletConstraint OutletConstraint { get; set; }
    public ProductConstraint ProductConstraint { get; set; }
    public ProductTertiaryConstraint TertiaryConstraint { get; set; }
}

public class ProductVisibilityGeographyConstraintToBeSaved
{
    public List<string> RequiredZones { set; get; }
    public List<string> RequiredRegions { set; get; }
    public List<string> RequiredTerritories { set; get; }
}
public class ProductVisibilityDistributorConstraintToBeSaved
{
    public List<string> RequiredDistributors { set; get; }
}
public class ProductVisibilityFactoryConstraintToBeSaved
{
    public List<string> RequiredFactories { set; get; }
}
public class ProductVisibilityOutletConstraintToBeSaved
{
    public List<string> RequiredChannels { set; get; }
    public List<string> RequiredSegmentations { set; get; }
    public List<string> RequiredShopTypes { set; get; }
    public List<string> OutletIds { set; get; }
}
public class ProductVisibilityTertiaryConstraintToBeSaved
{
    public List<string> RequiredTertiaryEntityIds { set; get; }
    public List<string> RequiredAttributeText1 { set; get; }
    public List<string> RequiredAttributeText2 { set; get; }
    public List<string> RequiredAttributeText3 { set; get; }

}

public class ProductConstraintToBeSaved
{
    public List<string> RequiredProductDivisionIds { set; get; }
    public List<string> RequiredPrimaryCategoryIds { set; get; }
    public List<string> RequiredSecondaryCategoryIds { set; get; }
    public List<string> RequiredProductIds { set; get; }
    public List<string> RequiredMRPs { set; get; }
    public List<string> RequiredProductAttributeText1 { set; get; }
    public List<string> RequiredProductAttributeText2 { set; get; }
    public List<string> RequiredProductAttributeNumber1 { set; get; }
    public List<string> RequiredProductAttributeNumber2 { set; get; }

}
public class ProductGeographyConstraint
{
    [DisplayName("Select a Zone")] public List<string> RequiredZones { set; get; }

    public List<long> RequiredZonesList { set; get; }

    [DisplayName("Select a Region")] public List<string> RequiredRegions { set; get; }

    public List<long> RequiredRegionsList { set; get; }

    [DisplayName("Select a Territory")] public List<string> RequiredTerritories { set; get; }

    public List<long> RequiredTerritoriesList { set; get; }
}

public class ProductDistributorConstraint
{
    [DisplayName("Select a Distributor")] public List<string> RequiredDistributors { set; get; }

    public List<long> RequiredDistributorsList { set; get; }
}
public class ProductFactoryConstraint
{
    [DisplayName("Select a Factory")] public List<string> RequiredFactories { set; get; }

    public List<long> RequiredFactoriesList { set; get; }
}

public class ProductOutletConstraint
{
    [DisplayName("Select a channel")] public List<string> RequiredChannels { set; get; }

    public List<long> RequiredChannelsList { set; get; }

    [DisplayName("Select a segmentation")] public List<string> RequiredSegmentations { set; get; }

    public List<long> RequiredSegmentationsList { set; get; }

    [DisplayName("Select a shoptype")] public List<string> RequiredShopTypes { set; get; }

    public List<long> RequiredShopTypesList { set; get; }

    [DisplayName("Select a outlet")] public List<string> OutletIds { set; get; }
    public List<long> OutletIdsList { set; get; }
}
public class ProductTertiaryConstraint
{
    [DisplayName("Select a Tertiary Entity")] public List<string> RequiredTertiaryEntityIds { set; get; }

    public List<long> RequiredTertiaryEntitiesList { set; get; }

    [DisplayName("Select a Attribute Text 1")] public List<string> RequiredAttributeText1 { set; get; }

    public List<string> RequiredAttributeText1List { set; get; }

    [DisplayName("Select a Attribute Text 2")] public List<string> RequiredAttributeText2 { set; get; }

    public List<string> RequiredAttributeText2List { set; get; }
    [DisplayName("Select a Attribute Text 3")] public List<string> RequiredAttributeText3 { set; get; }

    public List<string> RequiredAttributeText3List { set; get; }
}
public class ProductConstraint
{
    [DisplayName("Select a Product Division")] public List<string> RequiredProductDivisionIds { set; get; }
    public List<long> RequiredProductDivisionsList { set; get; }
    [DisplayName("Select a Primary Category")] public List<string> RequiredPrimaryCategoryIds { set; get; }
    public List<long> RequiredPrimaryCategoriesList { set; get; }
    [DisplayName("Select a Secondary Category")] public List<string> RequiredSecondaryCategoryIds { set; get; }
    public List<long> RequiredSecondaryCategoriesList { set; get; }
    [DisplayName("Select a Product")] public List<string> RequiredProductIds { set; get; }
    public List<long> RequiredProductsList { set; get; }
    [DisplayName("Select a MRP")] public List<string> RequiredMRPs { set; get; }
    public List<long> RequiredMRPsList { set; get; }
    [DisplayName("Select a Attribute Text 1")] public List<string> RequiredProductAttributeText1 { set; get; }
    public List<string> RequiredProductAttributeText1List { set; get; }
    [DisplayName("Select a Attribute Text 2")] public List<string> RequiredProductAttributeText2 { set; get; }
    public List<string> RequiredProductAttributeText2List { set; get; }
    [DisplayName("Select a Attribute Number 1")] public List<string> RequiredProductAttributeNumber1 { set; get; }
    public List<double> RequiredProductAttributeNumber1List { set; get; }
    [DisplayName("Select a Attribute Number 2")] public List<string> RequiredProductAttributeNumber2 { set; get; }
    public List<double> RequiredProductAttributeNumber2List { set; get; }
}
