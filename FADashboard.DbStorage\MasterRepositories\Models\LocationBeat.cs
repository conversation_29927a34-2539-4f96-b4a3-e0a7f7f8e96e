﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class LocationBeat : IAuditedEntity, IERPEntity
{
    public LocationBeat()
    {
        DistributorBeatMappings = [];
        Guid = Guid.NewGuid();
    }

    [Column("Beat_AttributeBoolean1")] public bool? AttributeBoolean1 { get; set; }

    [Column("Beat_AttributeBoolean2")] public bool? AttributeBoolean2 { get; set; }

    [Column("Beat_AttributeNumber1")] public double? AttributeNumber1 { get; set; }

    [Column("Beat_AttributeNumber2")] public double? AttributeNumber2 { get; set; }

    [Column("Beat_AttributeNumber3")] public double? AttributeNumber3 { get; set; }

    [Column("Beat_AttributeNumber4")] public double? AttributeNumber4 { get; set; }

    [StringLength(50)]
    [Column("Beat_AttributeText1")]
    public string AttributeText1 { get; set; }

    [StringLength(50)]
    [Column("Beat_AttributeText2")]
    public string AttributeText2 { get; set; }

    [StringLength(50)]
    [Column("Beat_AttributeText3")]
    public string AttributeText3 { get; set; }

    [StringLength(50)]
    [Column("Beat_AttributeText4")]
    public string AttributeText4 { get; set; }

    public BeatGrade BeatGrade { get; set; }

    [StringLength(50)] [Audited] public string City { get; set; }

    [ForeignKey("Company1")] public long Company { get; set; }

    public virtual Company Company1 { get; set; }

    [StringLength(50)] [Audited] public string Country { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public virtual ICollection<DistributorBeatMapping> DistributorBeatMappings { get; set; }

    [StringLength(100)] [Audited] public string DivisionZone { get; set; }

    [StringLength(50)] [Audited] public string ErpId { set; get; }

    public Guid Guid { set; get; }
    public long Id { get; set; }

    [Audited] public bool IsDeactive { set; get; }

    //[Audited]
    //public long? PJPId { get; set; }
    [Obsolete("Use LastUpdatedAt")]
    [Column(TypeName = "datetime2")]
    public DateTime? LastUpdated { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public virtual ICollection<Location> Locations { get; set; }

    [Required]
    [StringLength(100)]
    [Audited]
    public string Name { get; set; }

    public virtual ICollection<PositionBeatMapping> PositionBeatMapping { get; set; }

    [StringLength(50)] [Audited] public string State { get; set; }

    public virtual Territory Territory { get; set; }

    [Audited] public long? TerritoryId { get; set; }
}
