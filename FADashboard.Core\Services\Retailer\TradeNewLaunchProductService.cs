﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services.Retailer;

public class TradeNewLaunchProductService(ITradeNewLaunchRepository tradeNewLaunchRepository, IProductRepository productRepository, ISecondaryCategoryRepository secondaryCategoryRepository, ICurrentUser currentUser)
{
    private async Task<RepositoryResponse> IsValidNewLaunchProduct(TradeNewLaunchProdInput newLaunchProdInput)
    {
        var newLaunchProductList = await GetNewLaunchProducts(true);
        if (newLaunchProdInput.Id != 0)
        {
            newLaunchProductList = newLaunchProductList.Where(p => p.Id != newLaunchProdInput.Id).ToList();
        }

        if (!string.IsNullOrEmpty(newLaunchProdInput.Name))
        {
            var newLaunchProdNameList = newLaunchProductList.Select(p => p.Name.NormalizeCaps()).ToList();
            if (newLaunchProdNameList.Contains(newLaunchProdInput.Name.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = newLaunchProdInput.Id, ExceptionMessage = "New Launch Product Name is already present in the System", Message = "New Launch Product Creation/Updation Failed", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = newLaunchProdInput.Id, Message = "New Launch Product Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> CreateUpdateNewLaunchProduct(TradeNewLaunchProdInput newLaunchProd)
    {
        var checkNewLaunchProd = await IsValidNewLaunchProduct(newLaunchProd);
        if (!checkNewLaunchProd.IsSuccess)
        {
            return checkNewLaunchProd;
        }

        if (newLaunchProd.Id == 0)
        {
            return await tradeNewLaunchRepository.CreateNewLaunchProduct(newLaunchProd, currentUser.CompanyId);
        }

        return await tradeNewLaunchRepository.UpdateNewLaunchProduct(newLaunchProd, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> DeactivateNewLaunchProduct(long newLaunchProductId)
    {
        var data = await tradeNewLaunchRepository.DeactivateNewLaunchProduct(newLaunchProductId, currentUser.CompanyId);
        return data;
    }

    public async Task<TradeNewLaunchProdInput> GetNewLaunchProductById(long newLaunchProductId)
    {
        var newLaunchProd = await tradeNewLaunchRepository.GetNewLaunchProductById(newLaunchProductId, currentUser.CompanyId);
        var sKUIds = newLaunchProd.SKUIds;
        var secondaryCategories = await secondaryCategoryRepository.GetDetailedSecondaryCategories(currentUser.CompanyId, true);
        var secondaryCategoriesDict = secondaryCategories.ToDictionary(s => s.Id, s => s.ProductPrimaryCategoryId);
        if (sKUIds is { Count: > 0 })
        {
            var products = await productRepository.GetProducts(currentUser.CompanyId, true);
            var productDict = products.ToDictionary(s => s.Id, s => s.SecondaryCategoryId);
            var secondaryCategoryIds = sKUIds.Select(skuId => productDict[skuId]).Distinct().ToList();
            var primaryCategoryIds = secondaryCategoryIds.Select(scId => secondaryCategoriesDict[scId]).Distinct().ToList();
            newLaunchProd.PrimaryCategoryIds = primaryCategoryIds;
            newLaunchProd.SecondaryCategoryIds = secondaryCategoryIds;
        }

        return newLaunchProd;
    }

    public async Task<List<TradeNewLaunchProductList>> GetNewLaunchProducts(bool includeDeactivate)
    {
        var newLaunchProducts = await tradeNewLaunchRepository.GetNewLaunchProducts(currentUser.CompanyId, includeDeactivate);
        return newLaunchProducts;
    }
}
