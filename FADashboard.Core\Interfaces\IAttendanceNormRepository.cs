﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IAttendanceNormRepository
{
    Task<List<long>> GetActiveNormTeam(long companyid);
    Task<List<string>> GetUsedAttendanceNormName(long companyid);
    Task DeactivateNorm(long companyId, long normId);
    Task<int> GetNormsCount(long companyId, string searchString = null, bool includeDeactive = false);
    Task<List<NormsList>> GetNormsBySearch(long companyId, string searchString, bool includeDeactive = false);
    Task<List<NormsList>> GetNormsList(long companyId, bool includeDeactive = false);
    Task<AttendanceNorms> GetNormDetailsById(long normId, long companyId, Dictionary<long, string> teamNameDict);
    Task CreatePolicy(AttendanceNorms policy, long companyId);
}
