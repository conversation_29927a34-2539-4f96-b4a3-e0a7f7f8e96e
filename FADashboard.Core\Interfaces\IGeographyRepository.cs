﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;


namespace FADashboard.Core.Interfaces;

public interface IGeographyRepository
{
    Task<RepositoryResponse> ActivateDeactivateGeography(long geographyId, bool action, long companyId);

    Task<RepositoryResponse> CreateGeographies(GeographiesListWithParent geography, long companyId);

    Task<List<EntityMin>> GetAllActiveGeographiesOfLevel(long companyId, GeographyLevel level, List<long> Ids);

    Task<List<EntityMinIncludeParent>> GetAllActiveGeographiesOfLevel(long companyId, GeographyLevel level);

    Task<List<GeographiesListWithParent>> GetAllActiveGeographiesOfLevelWithParent(long companyId, GeographyLevel highestLevel, bool includeDeactivate);

    Task<GeographyInput> GetGeographyById(long geographyId, long companyId);

    Task<List<GeographyList>> GetGeographyBySearch(long companyId, string searchString, GeographyLevel geoLevel);

    Task<Dictionary<long, GeographyList>> GetGeographyDictionary(long companyId);

    Task<List<GeographiesListParent>> GetParentByParentId(long companyId, GeographyLevel highestLevel);

    Task<RepositoryResponse> UpdateGeographies(GeographiesListWithParent geography, long companyId);
}
