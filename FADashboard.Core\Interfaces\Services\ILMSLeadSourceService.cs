using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSLeadSourceService
    {
        Task<LMSLeadSourceDto> GetLeadSourceByIdAsync(long id);
        Task<PagedResult<LMSLeadSourceDto>> GetAllLeadSourcesByCompanyAsync(long companyId, LMSLeadSourceQueryParameters queryParameters);
        Task<LMSLeadSourceDto> CreateLeadSourceAsync(long companyId, LMSLeadSourceInput source, long createdByUserId);
        Task<LMSLeadSourceDto> UpdateLeadSourceAsync(long id, long companyId, LMSLeadSourceInput source, long updatedByUserId);
        Task<bool> DeleteLeadSourceAsync(long id, long updatedByUserId);
    }
}
