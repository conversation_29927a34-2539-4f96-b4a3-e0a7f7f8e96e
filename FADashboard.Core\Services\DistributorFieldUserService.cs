﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class DistributorFieldUserService(IDistributorFieldUserRepository distributorFieldUserRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<DistributorFieldUserMapping>> GetFieldUserForDistributor(long distributorId)
    {
        var list = await distributorFieldUserRepository.GetFieldUserForDistributor(currentUser.CompanyId, distributorId);
        return list;
    }
}
