﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class SurveyChoiceInput
{
    public string Condition { get; set; }
    public bool? Deleted { set; get; }
    public long ID { get; set; }
    public bool IsSelected { get; set; }
    public long QuestionId { get; set; }
    public string Text { get; set; }

    public string Value { get; set; }
}

public class SurveyInput
{
    public bool? Deleted { set; get; }
    public string Description { get; set; }
    public string DynamicDescription { get; set; }
    public bool Disable { get; set; }
    public long Id { get; set; }
    public bool IsSkippable { get; set; }
    public IEnumerable<string> Outlets { get; set; }
    public List<SurveyQuestionGroupInput> QuestionGroups { get; set; }
    public bool SingleTime { get; set; }
    public SurveyType? SurveyType { get; set; }
    public string Title { get; set; }
}

public class SurveyQuestionGroupInput
{
    public bool Deactivated { get; set; }

    public bool? Deleted { set; get; }

    public string Description { get; set; }
    public string DynamicDescription { get; set; }

    public int DisplayOrder { get; set; }

    public long FormId { get; set; }

    public GroupType GroupType { get; set; }

    public long ID { get; set; }

    public long JsonFormId { get; set; }

    public List<SurveyQuestionInput> Questions { get; set; }
    public string ShowCondition { get; set; }

    public string Title { get; set; }
}

public class SurveyQuestionInput
{
    public virtual List<SurveyChoiceInput> Choices { get; set; }
    public bool Deactivated { get; set; }
    public string DefaultValue { get; set; }
    public bool? Deleted { set; get; }
    public string Description { get; set; }
    public string DynamicDescription { get; set; }
    public int DisplayOrder { get; set; }
    public DisplayWidth DisplayWidth { get; set; }
    public string Hint { get; set; }
    public long ID { get; set; }
    public string OutletMetadata { set; get; }
    public long QuestionGroupId { get; set; }
    public SurveyQuestionType QuestionType { get; set; }
    public bool Required { get; set; }
    public string Title { get; set; }
    public string ValidationErrorMessage { get; set; }

    public string ValidationRule { get; set; }
}

public class SurveyConstraints
{
    public List<long> CustomTags { get; set; }
    public List<long> RequiredChannelsList { set; get; }
    public List<long> ShopTypes { get; set; }
    public List<string> Channels { get; set; }
    public List<string> Segmentations { get; set; }
    public List<long> RequiredSegmentationsList { set; get; }
    public string IsFocused { get; set; }
    public List<long> Cohorts { get; set; }
}

public class SurveyConstraintsDb
{
    public List<long> CustomTags { get; set; }
    public List<long> ShopTypes { get; set; }
    public List<string> Channels { get; set; }
    public List<string> Segmentations { get; set; }
    public string IsFocused { get; set; }
    public List<long> Cohorts { get; set; }
}


public class SurveyResponses
{
    public long? BeatId { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTimeOffset DeviceTime { get; set; }
    public long? DistributorId { get; set; }
    public long? EmployeeId { get; set; }
    public long FaEventId { get; set; }
    public long? FormId { get; set; }
    public long Id { get; set; }
    public long? OutletId { get; set; }
    public long? PositionCodeId { get; set; }
    public long ProductId { get; set; }
    public DateTimeOffset ServerTime { get; set; }
    public ICollection<SurveyResponseItem> SurveyResponseItems { get; set; }
}

public class SurveyResponseItem
{
    public long Id { get; set; }
    public long SurveyResponseId { get; set; }
    public string Image { get; set; }
    public string Options { get; set; }
    public long? ProductId { set; get; }
    public long QuestionId { get; set; }
    public string Text { get; set; }
}

public class SurveyResponseOutput
{
    public string Date { get; set; }
    public string Question { get; set; }
    public string Answer { get; set; }
    public string Outlet { get; set; }
    public SurveyQuestionType QuestionType { get; set; }
}

public class SurveyResponseOutputWithAssetType
{
    public long? AssetTypeId { set; get; }
    public List<SurveyResponseOutput> SurveyResponseOutput { get; set; }
}
