﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

internal sealed class UserOTPDetails
{
    public long Id { get; set; }
    public long EmployeeId { get; set; }
    public long? ManagerIdLevel1 { get; set; }
    public long? ManagerIdLevel2 { get; set; }
    public bool IsReviewed { get; set; }
    public long? ReviewedByManagerId { get; set; }
    public bool IsApproved { get; set; }
    public string Reason { get; set; }
    public int OTP { get; set; }
    public OTPRequestType OTPRequestType { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
