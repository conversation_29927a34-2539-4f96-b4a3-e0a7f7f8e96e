using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSAccountContactRepository
    {
        Task<LMSAccountContactDto> GetByIdAsync(long accountId, long contactId);
        Task<IEnumerable<LMSAccountContactDto>> GetByAccountIdAsync(long accountId);
        Task<LMSAccountContactDto> CreateContactAsync(LMSAccountContactCreateInput contactDto, long createdByUserId, long companyId);
        Task<LMSAccountContactDto> UpdateContactAsync(long contactId, LMSAccountContactUpdateInput contactDto, long updatedByUserId);
        Task<bool> DeleteContactAsync(long contactId, long deletedByUserId);
    }
}
