﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class OutletMasterService(
    IOutletMasterRepository outletMasterRepository,
    ICurrentUser currentUser,
    ICompanySettingsRepository companySettingsRepository,
    IChainRepository chainRepository,
    IPositionOutletRepository positionOutletRepository) : RepositoryResponse
{
    public async Task<RepositoryResponse> ActivateDeactivateOutlet(long outletId, bool activateAction)
    {
        if (activateAction)
        {
            return new RepositoryResponse { Id = outletId, Message = "Please activate the outlet through Update API", IsSuccess = false, };
        }

        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesRoutePlan = companySettings.OldJourneyPlanType == "RoutePlan";
        return await outletMasterRepository.DeactivateOutlet(currentUser.CompanyId, outletId, usesRoutePlan);
    }

    public async Task<RepositoryResponse> CreateOutlet(OutletInput outlet) => await outletMasterRepository.CreateOutlet(outlet, currentUser.CompanyId, null);

    public async Task<List<string>> GetAllFranchises()
    {
        var franchiseList = await outletMasterRepository.GetAllFranchises(currentUser.CompanyId);
        return franchiseList;
    }

    public async Task<List<EntityMinWithStatus>> GetAllRoutes(bool inludeDeactive)
    {
        var routes = await outletMasterRepository.GetAllRoutes(currentUser.CompanyId, inludeDeactive);
        return routes;
    }

    public async Task<List<string>> GetAllStates(string country)
    {
        var states = await outletMasterRepository.GetAllStates(country);
        var response = states.Select(s => s.ToUpperInvariant()).Distinct().ToList();
        return response;
    }

    public async Task<BeatMinModel> GetBeatById(long id)
    {
        var beat = await outletMasterRepository.GetBeatById(currentUser.CompanyId, id);
        return beat;
    }

    public async Task<List<BeatMinModel>> GetBeats(bool inludeDeactive, List<long> regionIds = null)
    {
        var beats = await outletMasterRepository.GetBeats(currentUser.CompanyId, inludeDeactive, regionIds);
        return beats;
    }

    public async Task<MarginSlabMin> GetMarginSlabById(long id)
    {
        var marginSlab = await outletMasterRepository.GetMarginSlabById(currentUser.CompanyId, id);
        return marginSlab;
    }

    public async Task<List<MarginSlabMin>> GetMarginSlabs(bool inludeDeactive)
    {
        var marginSlabs = await outletMasterRepository.GetMarginSlabs(currentUser.CompanyId, inludeDeactive);
        return marginSlabs;
    }

    public async Task<OutletInput> GetOutletById(long id)
    {
        var Outlet = await outletMasterRepository.GetOutletById(currentUser.CompanyId, id);
        return Outlet;
    }

    public async Task<List<OutletMasterList>> GetOutlets(bool showDeactive)
    {
        var Outlets = await outletMasterRepository.GetOutlets(currentUser.CompanyId, showDeactive);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesModernTrade = companySettings.UsesModernTrade;
        if (usesModernTrade)
        {
            var usesMultipleManning = companySettings.CompanyUsesMultipleManning;
            Outlets = Outlets.Where(w => w.ISRAvailability is ISRAvailability.Dedicated or ISRAvailability.Shared).ToList();
            var outletIds = Outlets.Select(s => s.Id).ToList();
            var positionOutletMappingOfAllOutlets = await positionOutletRepository.GetPositionOutletMappingUsingOutletIds(outletIds, currentUser.CompanyId);
            var mappedOutletsIds = positionOutletMappingOfAllOutlets.Select(s => s.LocationId).ToList();
            var nonMappedOutletIds = outletIds.Except(mappedOutletsIds).ToList();
            var finalOutletsIds = nonMappedOutletIds;
            if (usesMultipleManning)
            {
                var mappedSharedOutletIds = Outlets.Where(w => mappedOutletsIds.Contains(w.Id) && w.ISRAvailability == ISRAvailability.Shared).Select(s => s.Id).ToList();
                finalOutletsIds.AddRange(mappedSharedOutletIds);
            }
            var finalOutlets = Outlets.Where(w => finalOutletsIds.Contains(w.Id)).ToList();
            Outlets = finalOutlets;
        }

        return Outlets;
    }

    public async Task<List<LocationModel>> GetOutletsViaRegionIds(List<long> regionIds)
    {
        var companyId = currentUser.CompanyId;
        if (regionIds.Count > 0)
        {
            return await outletMasterRepository.GetAllOutletByRegion(companyId, regionIds);
        }
        else
        {
            return await outletMasterRepository.GetAllOutletWithRegion(companyId);
        }
    }

    public async Task<List<OutletMasterList>> GeAllMTOutletsThatCanBeMappedToPosition(long positionId)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesMultipleManning = companySettings.CompanyUsesMultipleManning;
        var Outlets = await outletMasterRepository.GetOutlets(currentUser.CompanyId, false);
        Outlets = Outlets.Where(w => w.ISRAvailability is ISRAvailability.Dedicated or ISRAvailability.Shared).ToList();
        var outletIds = Outlets.Select(s => s.Id).ToList();
        var positionOutletMappingOfAllOutlets = await positionOutletRepository.GetPositionOutletMappingUsingOutletIds(outletIds, currentUser.CompanyId);
        var mappedOutletsIds = positionOutletMappingOfAllOutlets.Select(s => s.LocationId).ToList();
        var nonMappedOutletIds = outletIds.Except(mappedOutletsIds).ToList();
        var finalOutletsIds = nonMappedOutletIds;
        if (usesMultipleManning)
        {
            var mappedSharedOutletIds = Outlets.Where(w => mappedOutletsIds.Contains(w.Id) && w.ISRAvailability == ISRAvailability.Shared).Select(s => s.Id).ToList();
            finalOutletsIds.AddRange(mappedSharedOutletIds);
        }
        var finalOutlets = Outlets.Where(w => finalOutletsIds.Contains(w.Id)).ToList();
        var outletPositionMappingMappedToPosition = await positionOutletRepository.GetPositionOutletMappingUsingPositionId(positionId, currentUser.CompanyId);
        var outletsMappedToPosition = Outlets.Where(w => outletPositionMappingMappedToPosition.Select(s => s.LocationId).ToList().Contains(w.Id)).ToList();
        Outlets = [.. finalOutlets, .. outletsMappedToPosition];

        return Outlets.GroupBy(d => d.Id).Select(g => g.First()).ToList();
    }
    public async Task<List<OutletMasterList>> GetAllOutlets(bool showDeactive)
    {
        var Outlets = await outletMasterRepository.GetOutlets(currentUser.CompanyId, showDeactive);
        return Outlets;
    }

    public async Task<PagedResponse<List<OutletMasterList>>> GetOutlets(PaginationFilter validFilter, int? verificationStatusEnum, List<long> regionIds = null)
    {
        var Outlets = await outletMasterRepository.GetOutlets(currentUser.CompanyId, validFilter, verificationStatusEnum, regionIds);
        var totalRecords = Outlets.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(Outlets.OutletRecords, validFilter, totalRecords);

        return pagedReponse;
    }

    public async Task<EntityMinWithStatus> GetRouteById(long id)
    {
        var route = await outletMasterRepository.GetRouteById(currentUser.CompanyId, id);
        return route;
    }

    public async Task<List<OutletSegmentationAttribute>> GetSegmentations(bool inludeDeactive)
    {
        var segmentations = await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, inludeDeactive);
        return segmentations;
    }

    public async Task<RepositoryResponse> ResetLocation(long outletId) => await outletMasterRepository.ResetLocation(currentUser.CompanyId, outletId);

    public async Task<RepositoryResponse> UpdateOutlet(OutletInput outlet) => await outletMasterRepository.UpdateOutlet(outlet, currentUser.CompanyId);

    public async Task<List<EntityMin>> GetAllOutletChains(bool includeDeactivate) => await chainRepository.GetOutletChains(currentUser.CompanyId, includeDeactivate);

    public async Task<List<OutletMasterList>> GetOutletsOfBeats(List<long> beatIds, bool includeDeactivate) => await outletMasterRepository.GetOutletsOfBeats(currentUser.CompanyId, beatIds, includeDeactivate);

    public async Task<List<LocationDTO>> GetOutletsByIds(List<long> ids) => await outletMasterRepository.GetOutletsByIds(currentUser.CompanyId, ids);
}
