﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class UserLoginActivity : ICreatedEntity
{
    public long Id { set; get; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public long UserId { set; get; }
    public PortalUserRole PortalRole { get; set; }
    public string ModuleFeature { set; get; }
    public ClientPlatform ClientPlatform { get; set; }
    public long CompanyId { set; get; }
    public virtual Company Company { get; set; }
    public int? ScreenWidth { get; set; }
    public int? ScreenHeight { get; set; }
    public long? AppVersion { get; set; }
    public string IPAddress { get; set; }
    public Guid? SessionId { get; set; }
    public bool? IsActive { get; set; }
}
