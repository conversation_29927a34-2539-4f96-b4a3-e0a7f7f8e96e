﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class GeographyInput
{
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }

    [EnumDataType(typeof(GeographyLevel))] public GeographyLevel Level { get; set; }

    public string LevelName { get; set; }
    public string Name { get; set; }
    public long? ParentId { get; set; }
    public string ParentName { get; set; }
    public string RegionShortCode { get; set; }
    public bool? ODSActive { get; set; }
}
