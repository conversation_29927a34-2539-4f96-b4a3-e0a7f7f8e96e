﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonModels;
namespace FADashboard.Core.Interfaces;

public interface ITerritoryRepository
{
    Task<RepositoryResponse> ActivateDeactivateTerritory(long territoryId, bool action, long companyId);

    Task<RepositoryResponse> CreateTerritory(GeographiesListWithParent zone, long companyId);

    Task<List<EntityMinIncludeParent>> GetAllActiveTerritories(long companyId);

    Task<List<EntityMin>> GetAllActiveTerritories(long companyId, List<long> ids);

    Task<List<GeographiesListWithParent>> GetAllTerritories(long companyId, bool includeDeactivate);

    Task<List<GeographiesListParent>> GetRegionByTerritory(long companyId);

    Task<GeographyInput> GetTerritoryById(long territoryId, long companyId);

    Task<List<EntityMinIncludeParent>> GetTerritoryByRegionIds(long companyId, List<long> regionIds);

    Task<List<GeographyList>> GetTerritoryBySearch(long companyId, string searchString);

    Task<Dictionary<long, GeographyList>> GetTerritoryDictionary(long companyId);

    Task<RepositoryResponse> UpdateTerritory(GeographiesListWithParent territory, long companyId);
}
