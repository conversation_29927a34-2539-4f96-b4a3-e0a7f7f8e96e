﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ProductPricingMasterV2 : IAuditedEntity
{
    public ProductPricingMasterV2()
    {
    }

    public long Id { get; set; }

    public bool IsDeactive { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public long CompanyId { get; set; }

    public Company Company { get; set; }

    public long ProductId { get; set; }

    public CompanyProduct Product { get; set; }

    public long? BatchId { get; set; }

    public ProductBatchMaster Batch { get; set; }

    public long? RegionId { get; set; }

    public Regions Region { get; set; }

    public long? DistributorId { get; set; }

    public Distributor Distributor { get; set; }

    public long? SubStockistId { get; set; }

    public Distributor SubStockist { get; set; }

    public long? SuperStockistId { get; set; }

    public Distributor SuperStockist { get; set; }

    public long? DistributorSegmentationId { get; set; }

    public DistributorSegmentations DistributorSegmentation { get; set; }
    public long? DistributorChannelId { get; set; }

    [Audited]
    public decimal MRP { get; set; }

    [Audited]
    [Column("PTR_MT")]
    public decimal? PTRMT { get; set; }

    [Audited]
    public decimal PTR { get; set; }

    public decimal? PTRMargin { get; set; }

    [Audited]
    public decimal? PTSubStockist { get; set; }

    public decimal? PTSubStockistMargin { get; set; }

    [Audited]
    public decimal? PTD { get; set; }

    public decimal? PTDMargin { get; set; }

    [Audited]
    public decimal? PTSuperStockist { get; set; }

    public decimal? PTSuperStockistMargin { get; set; }

    [Audited] public decimal? SuperStockistTradeDiscount { get; set; }
    [Audited] public decimal? DistributorTradeDiscount { get; set; }
    [Audited] public decimal? SubStockistTradeDiscount { get; set; }
    [Audited] public decimal? RetailerTradeDiscount { get; set; }
    public string MRPBatch { get; set; }
    [StringLength(50)] public string StockSegment { get; set; }

    public PricingMastertype PricingMasterType { get; set; }

    public string BatchNumber { get; set; }

    public string InventoryMasterBatchNo { get; set; }
}
