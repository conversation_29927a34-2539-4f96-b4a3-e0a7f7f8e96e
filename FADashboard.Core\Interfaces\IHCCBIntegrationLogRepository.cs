﻿using FADashboard.Core.Models;

namespace FADashboard.Core.Interfaces
{
    public interface IHCCBIntegrationLogRepository
    {
        Task<List<HCCBIntegrationLog>> GetHCCBIntegrationDetails(long companyId, DateTime date);
        Task<List<HCCBValidationResult>> ValidateOutletBeat(long companyId, List<string> inputs);
        Task<List<HCCBValidationResult>> ValidateOutletBeatDistributorEmployee(long companyId, List<string> inputs);
        Task<List<HCCBValidationResult>> ValidateOrders(long companyId, DateTime? date = null);
        Task<List<HCCBValidationResult>> ValidateOrderStatus(long companyId, DateTime startDate);
        Task<List<HCCBIntegrationAPILog>> GetIntegrationLogDetails(long companyId, long integrationLogId);
    }
}
