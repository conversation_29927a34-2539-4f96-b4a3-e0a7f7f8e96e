﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class PositionOutletMapping : IAuditedEntity
{
    public virtual Company Company { get; set; }

    [ForeignKey("Company")] public long CompanyId { get; set; }

    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }
    public long Id { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public virtual Location Location { get; set; }

    [ForeignKey("Location")] public long LocationId { get; set; }

    public virtual PositionCode PositionCode { get; set; }

    [ForeignKey("PositionCode")] public long PositionId { get; set; }
}
