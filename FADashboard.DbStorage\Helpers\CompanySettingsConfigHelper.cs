﻿using FADashboard.Core.Helper;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.DbStorage.Helpers;

public static class CompanySettingsConfigHelper
{
    //Currently checks only implementation for these settings
    //{

    //Id =      SettingKey                                                      SettingKeyText
    //518 = CompanyUsesSchemeApproval,                                  	Company Uses Scheme Approval
    //102 = CompanyUsesAddOutletRequest,                                	Company Uses Add Outlet Request
    //187 = UsesAdvanceDeadOutletFlow,                                  	UsesAdvanceDeadOutletFlow
    //186 = UsesAdvanceLeaveApproval,                                   	UsesAdvanceLeaveApproval
    //111 = JourneyPlanType,                                            	Type of Journey Plan Used by Company
    //290 = UsesNewAttendanceModule,                                    	Uses New Attendance Module
    //124 = RouteDiversion,	                                                OTP methodology for Journey Diversion
    //323 = JourneyPlanVersion,	                                            Journey Plan Version
    //494 = IsCompanyUsingProductRecommendationEngine,                      Is Company Using Product Recommendation Engine
    //493 = IsCompanyUsingOutletClustering,                                 Is Company Using Outlet Clustering
    //176 = outletMargin,                                               	Outlet Margin
    //198 = CompanyUsesOutletReach,                                     	CompanyUsesOutletReach
    //244 = IsOnlineDMS,                                                	IsOnlineDMS(Company rolled out to new mechanism for dispatches (ver:Dashboard) will automatically turn ON)
    //295 = AllowSubDistributorCreationthroughApp,                          Allow SubDistributor Creation through App
    //114 = UsesJourneyCalendar,                                        	Uses Journey Calendar
    //197 = UsesFocusProductTarget,                                     	Uses Focus Product Target
    //584 = CompanyUsesFlexibleTargetModule,                            	Company Uses Flexible Target Module
    //516 = IsCompanyUsingProductWinback,                               	Is Company Using Product Winback
    //354 = CompanyUsesCouponBasedLoyaltyScheme,                        	Company uses Coupon Based Loyalty Scheme
    //201 = CompanyUsesGamification,                                    	Company Uses Gamification
    //59  = CompanyUsesIntelligentSchemes,                              	Company Uses Intelligent Schemes?
    //115 = DiscountType,                                                   Select The way Discount Could be Applied In Company
    //206 = FABattleGround,                                                 Company uses level of  FieldAssist Battle Ground
    //111 = JourneyPlanType,                                                Type of Journey Plan Used by Company	
    //40615 = CompanyUsesHCCBUserFlows                                      Company uses HCCB User Flows
    //}

    //-------------------------------------------------Please mention setting above before any additions below-------------------------------------------------
    ///Note that this method is not used anywhere in the codebase
    public static void GetCompanyConfigIntersectingSettings(ref List<CompanyConfigModules> companyConfigModules, Dictionary<string, object> companySettings)
    {
        // To Do: Refactor this method to use the CompanySettings object for checking settings instead of directly accessing the companySettings dictionary.
        var companySettingsObj = new CompanySettings(companySettings);

        // Iterate over the list and perform the removal
        foreach (var companyConfig in companyConfigModules)
        {
            //--------------------SCHEME MODULE
            if (companyConfig.Modules.ContainsKey("Scheme"))
            {
                // Check if the SchemeApproval is present in the list
                var companyUsesSchemeApprovalObject = companySettings["CompanyUsesSchemeApproval"];

                if (companyUsesSchemeApprovalObject != null &&
                    string.Equals(companyUsesSchemeApprovalObject.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Scheme");
                    a.Value.Remove("SchemeApproval");
                }
            }


            //--------------------GTM STRATEGY MODULE
            if (companyConfig.Modules.ContainsKey("GTMStrategy"))
            {
                // Check if the BeatOMeter is present in the list
                var beatoMeterObject = companySettings["CompanyUsesOutletReach"];

                if (beatoMeterObject != null &&
                    string.Equals(beatoMeterObject.ToString(), "false", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "GTMStrategy");
                    a.Value.Remove("BeatoMeter");
                }


                // Check if the InternationalDiscount is present in the list
                var internationalDiscountObject = companySettings["DiscountType"];
                var intelligentSchemeObject = companySettings["usesIntelligentSchemes"];

                if (string.Equals(internationalDiscountObject?.ToString(), "InternationalDiscount", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(intelligentSchemeObject?.ToString(), "True", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "GTMStrategy");
                    a.Value.Remove("InternationalDiscount");
                }


                // Check if the CouponsScheme is present in the list
                var couponsScheme = companySettings["CompanyUsesCouponBasedLoyaltyScheme"];
                if (couponsScheme != null &&
                    string.Equals(couponsScheme.ToString(), "false", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the CouponsScheme screen
                    var a = companyConfig.Modules.First(module => module.Key == "GTMStrategy");
                    a.Value.Remove("CouponsScheme");
                    // Remove the CouponsDetails screen
                    var b = companyConfig.Modules.First(module => module.Key == "GTMStrategy");
                    b.Value.Remove("CouponsDetails");
                }
            }


            //--------------------PRODUCT RECOMMENDATION MODULE
            if (companyConfig.Modules.ContainsKey("ProductRecom"))
            {
                // Check if RecommendationLogic is present in the list
                var isCompanyUsingOutletClusteringObject = companySettings["IsCompanyUsingOutletClustering"];
                var isCompanyUsingProductRecommendationEngineObject = companySettings["IsCompanyUsingProductRecommendationEngine"];

                if (string.Equals(isCompanyUsingOutletClusteringObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(isCompanyUsingProductRecommendationEngineObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "ProductRecom");
                    a.Value.Remove("RecommendationLogic");
                }


                // Check if Winback is present in the list
                var isCompanyUsingProductWinbackObject = companySettings["IsCompanyUsingProductWinback"];

                if (string.Equals(isCompanyUsingProductWinbackObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "ProductRecom");
                    a.Value.Remove("Winback");
                }
            }


            //--------------------CONFIGURATION MODULE
            if (companyConfig.Modules.ContainsKey("Configuration"))
            {
                //Check if CreateMarginPage is present in the list
                var outletMargin = companySettings["outletMargin"];
                if (outletMargin == "CreateMarginPage")
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Configuration");
                    a.Value.Remove("CreateMarginPage");
                }


                // Check if the AddOutletRequest is present in the list
                var companyUsesAddOutletRequestObject = companySettings["CompanyUsesAddOutletRequest"];

                if (string.Equals(companyUsesAddOutletRequestObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Configuration");
                    a.Value.Remove("CompanyUsesAddOutletRequest");
                }


                // Check if the SubDFieldsForApp is present in the list
                var allowSubDistributorCreationthroughAppObject = companySettings["AllowSubDistributorCreationthroughApp"];

                if (string.Equals(allowSubDistributorCreationthroughAppObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Configuration");
                    a.Value.Remove("SubDFieldsForApp");
                }
            }


            //--------------------REQUESTS MODULE
            if (companyConfig.Modules.ContainsKey("RequestAndAlerts"))
            {
                //Check if TourPlanRequest is present in the list
                var journeyPlanType = companySettings["JourneyPlanType"];
                if (journeyPlanType == "TourPlan")
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "RequestAndAlerts");
                    a.Value.Remove("TourPlanRequest");
                }


                // Check if DeadOutletRequest is present in the list
                var usesAdvanceDeadOutletFlowObject = companySettings["UsesAdvanceDeadOutletFlow"];

                if (string.Equals(usesAdvanceDeadOutletFlowObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "RequestAndAlerts");
                    a.Value.Remove("DeadOutletRequest");
                }


                // Check if AdvancedLeaveRequest is present in the list
                var usesAdvanceLeaveApprovalObject = companySettings["UsesAdvanceLeaveApproval"];

                if (string.Equals(usesAdvanceLeaveApprovalObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "RequestAndAlerts");
                    a.Value.Remove("AdvancedLeaveRequest");
                }


                // Check if JourneyDiversionRequest is present in the list
                if (companySettings.TryGetValue("RouteDiversion", out var routeDiversionValue) && routeDiversionValue is List<string> routeDiversionList)
                {
                    if (routeDiversionList.Count == 0)
                    {
                        // Remove the specific screen
                        var requestAndAlertsModule = companyConfig.Modules.FirstOrDefault(module => module.Key == "RequestAndAlerts");

                        if (requestAndAlertsModule.Value is List<string> screensList)
                        {
                            screensList.Remove("JourneyDiversionRequest");
                        }
                    }
                }


                //Check if JourneyPlanRequest is present in the list
                var journeyPlanVersion = companySettings["JourneyPlanVersion"];
                if (journeyPlanVersion == "New Journey Plan")
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "RequestAndAlerts");
                    a.Value.Remove("JourneyPlanRequest");
                }


                // Check if AttendanceRegularizationRequest is present in the list
                var usesNewAttendanceModuleObject = companySettings["UsesNewAttendanceModule"];

                if (string.Equals(usesNewAttendanceModuleObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "RequestAndAlerts");
                    a.Value.Remove("AttendanceRegularizationRequest");
                }
            }


            //--------------------TARGETS MODULE
            if (companyConfig.Modules.ContainsKey("Target"))
            {
                // Check if TourPlanRequest is present in the list
                var usesFocusProductTargetObject = companySettings["UsesFocusProductTarget"];

                if (string.Equals(usesFocusProductTargetObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Target");
                    a.Value.Remove("InsertFocussedProductTarget");
                }

                var UsesJourneyCalendarObject = companySettings["UsesJourneyCalendar"];

                if (string.Equals(UsesJourneyCalendarObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Target");
                    a.Value.Remove("InsertEmployeeTargetJC");
                }

                if (string.Equals(UsesJourneyCalendarObject?.ToString(), "True", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Target");
                    a.Value.Remove("InsertEmployeeTarget");
                }
            }


            //--------------------Miscellaneous
            if (companyConfig.Modules.ContainsKey("Miscellaneous"))
            {
                // Check if the CompanyFactory is present in the list
                var isOnlineDMSObject = companySettings["IsOnlineDMS"];

                if (string.Equals(isOnlineDMSObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "Miscellaneous");
                    a.Value.Remove("CompanyFactory");
                }
            }

            //--------------------Master Management
            if (companyConfig.Modules.TryGetValue("MasterManagement", out var masterManagementScreens))
            {
                var isPJPBulkUpload = companySettings["CompanyUsesCustomPJPBulkUpload"];
                var isMarginPricingUpload = companySettings["CompanyUsesMarginPricingBulkUpload"];

                if (string.Equals(isPJPBulkUpload?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    masterManagementScreens.Remove("PjpBulkUpload");
                }

                if (string.Equals(isMarginPricingUpload?.ToString(), "False", StringComparison.OrdinalIgnoreCase))
                {
                    masterManagementScreens.Remove("DistributorWisePricingUpload");
                }
            }



            //--------------------FABattleGround
            if (companyConfig.Modules.ContainsKey("FABattleground"))
            {
                // Check if the AlertsConfiguration is present in the list
                var companyUsesGamificationObject = companySettings["CompanyUsesGamification"];
                var fABattleGroundObject = companySettings["FABattleGround"];

                if (string.Equals(companyUsesGamificationObject?.ToString(), "False", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(fABattleGroundObject?.ToString(), "Premium Model", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "FABattleground");
                    a.Value.Remove("AlertsConfiguration");
                }
            }


            //--------------------New Journey Plan
            if (companyConfig.Modules.ContainsKey("JourneyPlan"))
            {
                // Check if the AlertsConfiguration is present in the list   
                var journeyPlanVersion = companySettings["JourneyPlanVersion"];
                if (!string.Equals(journeyPlanVersion?.ToString(), "New Journey Plan", StringComparison.OrdinalIgnoreCase))
                {
                    // Remove the specific screen
                    var a = companyConfig.Modules.First(module => module.Key == "JourneyPlan");
                    a.Value.Remove("JourneyPlan");

                    // Remove the specific screen
                    var b = companyConfig.Modules.First(module => module.Key == "JourneyPlan");
                    b.Value.Remove("JourneyPlantype");

                    // Remove the specific screen
                    var c = companyConfig.Modules.First(module => module.Key == "JourneyPlan");
                    c.Value.Remove("InsertJourneyPlan");
                }
            }


            //--------------------Journey Plan
            if (companyConfig.Modules.ContainsKey("OldJourneyPlan"))
            {
                // Check if the AlertsConfiguration is present in the list   
                var journeyPlanVersion = companySettings["JourneyPlanVersion"];
                var journeyPlanType = companySettings["JourneyPlanType"];
                if (string.Equals(journeyPlanVersion?.ToString(), "Old Journey Plan", StringComparison.OrdinalIgnoreCase))
                {
                    if (journeyPlanType == "TourPlan")
                    {
                        // Remove the specific screen
                        var a = companyConfig.Modules.First(module => module.Key == "JourneyPlan");
                        a.Value.Remove("InsertRoutePlan");
                    }

                    else
                    {
                        // Remove the specific screen
                        companyConfig.Modules.TryGetValue("JourneyPlan", out var b);
                        b?.Remove("InsertTourPlan");
                    }
                }
                else
                {
                    companyConfig.Modules.Remove("OldJourneyPlan");
                }
            }


            // Remove AssistedOrders if CompanyUsesHCCBUserFlows is false
            if (companyConfig.Modules.ContainsKey("AssistedOrders"))
            {
                var companyUsesHCCBUserFlows = companySettingsObj.CompanyUsesHCCBUserFlows;
                if (!companyUsesHCCBUserFlows)
                {
                    companyConfig.Modules.Remove("AssistedOrders");
                }
            }
            // Remove "HistoricalWeeklyOffUpload" if CompanyUsesHCCBUserFlows is false
            if (!companySettingsObj.CompanyUsesHCCBUserFlows &&
                companyConfig.Modules.TryGetValue("OldJourneyPlan", out var screens))
            {
                screens.Remove("HistoricalWeeklyOffUpload");
            }
        }
    }
}
