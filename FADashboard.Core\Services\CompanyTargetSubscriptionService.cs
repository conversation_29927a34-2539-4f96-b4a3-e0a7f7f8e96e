﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class CompanyTargetSubscriptionService(
    ICurrentUser currentUser,
    ICompanyTargetSubscriptionsRepository companyTargetSubscriptionsRepository) : RepositoryResponse
{
    public async Task<List<CompanyTargetSubscriptionList>> GetAllCompanyTargetSubscriptions(bool includeDeactive, CancellationToken ct) => await companyTargetSubscriptionsRepository.GetAllCompanyTargetSubscriptions(currentUser.CompanyId, includeDeactive, ct);
    public async Task<CompanyTargetSubscriptionList> GetCompanyTargetSubscriptionById(long id, CancellationToken ct) => await companyTargetSubscriptionsRepository.GetCompanyTargetSubscriptionById(currentUser.CompanyId, id, ct);
    public async Task<TargetMasterView> GetTargetMasterById(long id, CancellationToken ct) => await companyTargetSubscriptionsRepository.GetTargetMasterById(id, ct);
    public async Task<RepositoryResponse> ActivateDeactivateCompanyTargetSubscription(long id, bool action, CancellationToken ct) => await companyTargetSubscriptionsRepository.ActivateDeactivateCompanyTargetSubscription(currentUser.CompanyId, id, action, ct);
    public async Task<TargetMastersWithStatus> GetAllTargetMasters(CancellationToken ct)
    {
        var targetMasters = await companyTargetSubscriptionsRepository.GetAllTargetMasters(ct);
        var companyTargetSubscriptions = (await companyTargetSubscriptionsRepository.GetTargetSubscriptionExistStatus(currentUser.CompanyId, ct)).ToDictionary(l => l.Id, l => l.IsActive);

        var companyTargetSubscriptionsStatus = targetMasters.ToDictionary(

          tm => tm.Id,
          tm => companyTargetSubscriptions.ContainsKey(tm.Id) ? companyTargetSubscriptions[tm.Id] : true
          );

        return new TargetMastersWithStatus
        {
            TargetMasters = targetMasters,
            CompanyTargetSubscriptionsStatus = companyTargetSubscriptionsStatus
        };

    }
    public async Task<RepositoryResponse> CreateCompanyTargetSubscription(CompanyTargetSubscriptionList cTSView, CancellationToken ct) => await companyTargetSubscriptionsRepository.CreateCompanyTargetSubscription(currentUser.CompanyId, cTSView, ct);
    public async Task<List<EntityMin>> GetCompanyTargetSubscriptions(bool includeDeactive, CancellationToken ct) => await companyTargetSubscriptionsRepository.GetCompanyTargetSubscriptions(currentUser.CompanyId, includeDeactive, ct);
    public async Task<RepositoryResponse> UpdateCompanyTargetSubscription(CompanyTargetSubscriptionList companyTargetSubscriptionList, long id, CancellationToken ct) => await companyTargetSubscriptionsRepository.UpdateCompanyTargetSubscription(currentUser.CompanyId, companyTargetSubscriptionList, id, ct);
}
