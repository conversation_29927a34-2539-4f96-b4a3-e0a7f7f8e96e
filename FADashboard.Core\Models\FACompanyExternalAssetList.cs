﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class FACompanyExternalAssetList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Url { get; set; }
    public long Company { get; set; }
    public long? UserId { get; set; }
    public bool VisibleToHierarchy { get; set; }
    public string Image { get; set; }
    public FACompanyExternalAssetFilterList FACompanyExternalAssetFilter { get; set; }
    public ExternalAssetVisibleFor VisibleFor { get; set; }
    public List<string> UserPlatforms { get; set; }
}

public class FACompanyExternalAssetFilterList
{
    public long Id { get; set; }
    public long FACompanyExternalAssetId { get; set; }
    public FACompanyExternalAssetList FACompanyExternalAsset { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ExternalAssetGeography FilterOn { get; set; }
    public string FilterIds { get; set; }
    public string Channel { get; set; }
    public string Chain { get; set; }
    public string ShopType { get; set; }
    public string Segmentation { get; set; }
    public string CustomTag { get; set; }
    public bool? IsFocused { get; set; }
    public bool ApplyAssetsOnNoTagOutlets { get; set; }
}

public class CompanyExternalAssetModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Url { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string Applicability { get; set; }
}
