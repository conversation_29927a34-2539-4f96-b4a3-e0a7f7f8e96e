﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class JourneyCalendarService(IJourneyCalendarRepository journeyCalendarRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<JourneyCalendarModel>> GetJourneyCalendars()
    {
        var journeyCalendars = await journeyCalendarRepository.GetJourneyCalendars();
        return journeyCalendars;
    }

    public async Task<RepositoryResponse> CreateUpdateJourneyCalendar(JourneyCalendarModel journeyCalendar)
    {
        if (journeyCalendar.Id == 0)
        {
            CreateJourneyWeeks(journeyCalendar);
            return await journeyCalendarRepository.CreateJourneyCalendar(journeyCalendar);
        }

        var response = await journeyCalendarRepository.UpdateJourneyCalendar(journeyCalendar);
        if (response.IsSuccess)
        {
            await journeyCalendarRepository.DeleteJourneyWeeksForCalendar(journeyCalendar.Id);
            CreateJourneyWeeks(journeyCalendar);
            return await journeyCalendarRepository.CreateJourneyWeeksForCalendar(journeyCalendar);
        }

        return GetRejectResponse("Failed");
    }

    public async Task<JourneyCalendarModel> GetJourneyCalendarById(long id)
    {
        var journeyCalendar = await journeyCalendarRepository.GetJourneyCalendarById(id);
        return journeyCalendar;
    }

    public async Task<RepositoryResponse> ActivateDeactivateJourneyCalendar(long id, bool action) => await journeyCalendarRepository.ActivateDeactivateJourneyCalendar(id, action);

    public static void CreateJourneyWeeks(JourneyCalendarModel journeyCalendar)
    {
        var currentYearWeek = 0;
        var currentQuarter = 0;
        var currentQuarterWeek = 0;
        journeyCalendar.JourneyCycles = journeyCalendar.JourneyCycles.Select(cycle =>
        {
            var monthQuarter = ((cycle.MonthNumber - 1) / 3) + 1;
            if (monthQuarter != currentQuarter && monthQuarter < 5)
            {
                currentQuarter = monthQuarter;
                currentQuarterWeek = 0;
            }
            else if (monthQuarter == 5)
            {
                monthQuarter = currentQuarter;
            }

            cycle.JourneyWeeks = CreateWeekForCycle(cycle, ref currentYearWeek, ref currentQuarterWeek, currentQuarter);
            return cycle;
        }).ToList();
    }

    public static List<JourneyWeekModel> CreateWeekForCycle(JourneyCycleModel journeyCycle, ref int currentYearWeek, ref int currentQuarterWeek, int currentQuarter)
    {
        var monthLength = (journeyCycle.MonthEndDate - journeyCycle.MonthStartDate).TotalDays;
        var currentMonthWeek = 0;
        var journeyWeeks = new List<JourneyWeekModel>();
        while (monthLength > 0)
        {
            currentYearWeek++;
            currentMonthWeek++;
            currentQuarterWeek++;
            var week = new JourneyWeekModel
            {
                WeekForYear = currentYearWeek,
                WeekForMonth = currentMonthWeek,
                WeekForQuarter = currentQuarterWeek,
                QuarterNumber = currentQuarter,
                WeekStartDate = journeyCycle.MonthStartDate.AddDays((currentMonthWeek - 1) * 7)
            };
            week.WeekEndDate = monthLength > 7 ? week.WeekStartDate.AddDays(6) : journeyCycle.MonthEndDate;
            journeyWeeks.Add(week);
            monthLength -= 7;
        }

        return journeyWeeks;
    }

    public async Task<JourneyCalendarModel> GetJourneyCalendarByYear(int year)
    {
        var journeyCalendar = await journeyCalendarRepository.GetJourneyCalendarByYear(currentUser.CompanyId, year);
        return journeyCalendar;
    }
}
