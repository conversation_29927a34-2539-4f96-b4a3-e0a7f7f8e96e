﻿using FADashboard.Core.Models;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ICompanySettingsRepository
{
    Task<List<CompanySettingsValues>> GetAllConfigs(long companyId);

    Task<List<CompanySetting>> GetAllSettingsAsync();
    Task<Dictionary<string, object>> GetSettingsCompany(long companyId);

    Task<RepositoryResponse> UpdateSettingValues(List<CompanySettingsValues> vals, long companyId);
    Task<List<EntityMin>> GetAllCompanySettingsMin(long companyId);
}
