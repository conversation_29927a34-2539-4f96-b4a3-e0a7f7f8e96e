﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.Core.Services
{
    public class LMSAccountTemplateService(ILMSAccountTemplateRepository accountTemplateRepository, ILMSCustomFieldService customFieldService) : ILMSAccountTemplateService
    {
        public async Task<PagedResult<LMSAccountTemplateDto>> GetAccountTemplatesAsync(long companyId, LMSAccountTemplateQueryParameters queryParameters) =>
            await accountTemplateRepository.GetTemplatesAsync(companyId, queryParameters);

        public async Task<LMSAccountTemplateDto> GetAccountTemplateByIdAsync(long id) => await accountTemplateRepository.GetByIdAsync(id);

        public async Task<LMSAccountTemplateDto> CreateAccountTemplateAsync(long companyId, LMSAccountTemplateInput input, long createdByUserId)
        {
            if (await accountTemplateRepository.TemplateNameExistsAsync(companyId, input.Name))
                throw new InvalidOperationException("An account template with this name already exists.");

            if (input.IsDefault)
            {
                await UnsetPreviousDefaultTemplate(companyId, createdByUserId);
            }

            var templateDto = new LMSAccountTemplateDto
            {
                CompanyId = companyId,
                Name = input.Name,
                Description = input.Description,
                IsDefault = input.IsDefault,
                IsActive = input.IsActive,
                CreatedBy = createdByUserId,
                CreatedAt = DateTime.UtcNow
            };

            var createdTemplate = await accountTemplateRepository.AddAsync(templateDto);

            if (input.CustomFields != null && input.CustomFields.Count != 0)
            {
                var customFields = input.CustomFields.Select(field => new LMSCustomFieldDto
                {
                    EntityType = (int)LMSCustomFieldsEntityType.Account,
                    EntityId = createdTemplate.Id,
                    Name = field.Label,
                    FieldType = (int)field.Type,
                    Placeholder = field.Placeholder,
                    Options = JsonConvert.SerializeObject(field.Options),
                    IsRequired = field.Required,
                    IsActive = field.IsActive,
                    Sequence = field.Sequence,
                    CreatedBy = createdByUserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = createdByUserId,
                    CompanyId = companyId,
                    Description = field.Description
                }).ToList();

                await customFieldService.AddRangeAsync(customFields, createdByUserId);
            }

            return createdTemplate;
        }

        public async Task<LMSAccountTemplateDto> UpdateAccountTemplateAsync(long id, long companyId, LMSAccountTemplateInput input, long updatedByUserId)
        {
            var existingTemplate = await GetAndValidateTemplate(id, companyId);

            // Validate MinValue and MaxValue for custom fields
            ValidateCustomFieldMinMaxValues(input.CustomFields);

            if (await accountTemplateRepository.TemplateNameExistsAsync(companyId, input.Name, id))
                throw new InvalidOperationException("An account template with this name already exists.");

            if (input.IsDefault && !existingTemplate.IsDefault)
            {
                await UnsetPreviousDefaultTemplate(companyId, updatedByUserId, id);
            }

            existingTemplate.Name = input.Name;
            existingTemplate.Description = input.Description;
            existingTemplate.IsActive = input.IsActive;
            existingTemplate.IsDefault = input.IsDefault;
            existingTemplate.UpdatedBy = updatedByUserId;
            existingTemplate.UpdatedAt = DateTime.UtcNow;

            var updatedTemplate = await accountTemplateRepository.UpdateAsync(existingTemplate);

            // Efficient upsert pattern: update existing, create new, delete obsolete
            await UpsertCustomFieldsAsync(id, input.CustomFields, companyId, updatedByUserId);

            return updatedTemplate;
        }

        public async Task<bool> SetDefaultAsync(long id, long companyId, long updatedByUserId)
        {
            var templateToSet = await GetAndValidateTemplate(id, companyId);
            if (templateToSet.IsDefault)
                return true; // Already the default

            await UnsetPreviousDefaultTemplate(companyId, updatedByUserId, id);

            templateToSet.IsDefault = true;
            templateToSet.UpdatedBy = updatedByUserId;
            templateToSet.UpdatedAt = DateTime.UtcNow;

            await accountTemplateRepository.UpdateAsync(templateToSet);
            return true;
        }

        public async Task<bool> SetActiveStatusAsync(long id, long companyId, bool isActive, long updatedByUserId)
        {
            var template = await GetAndValidateTemplate(id, companyId);

            if (template.IsDefault && !isActive)
            {
                throw new InvalidOperationException("The default template cannot be made inactive.");
            }

            template.IsActive = isActive;
            template.UpdatedBy = updatedByUserId;
            template.UpdatedAt = DateTime.UtcNow;

            await accountTemplateRepository.UpdateAsync(template);
            return true;
        }

        private async Task<LMSAccountTemplateDto> GetAndValidateTemplate(long id, long companyId)
        {
            var template = await accountTemplateRepository.GetByIdAsync(id);
            if (template == null || template.CompanyId != companyId)
                throw new KeyNotFoundException($"LMSAccountTemplate with ID {id} not found for this company.");
            return template;
        }

        private async Task UnsetPreviousDefaultTemplate(long companyId, long updatedByUserId, long? excludeTemplateId = null)
        {
            var currentDefault = await accountTemplateRepository.GetDefaultTemplateAsync(companyId);
            if (currentDefault != null && currentDefault.Id != excludeTemplateId)
            {
                currentDefault.IsDefault = false;
                currentDefault.UpdatedBy = updatedByUserId;
                currentDefault.UpdatedAt = DateTime.UtcNow;
                await accountTemplateRepository.UpdateAsync(currentDefault);
            }
        }

        private async Task UpsertCustomFieldsAsync(long entityId, List<TemplateCustomFields> inputCustomFields, long companyId, long userId)
        {
            // Get existing custom fields for this entity
            var existingFields = await customFieldService.GetByEntityAsync((int)LMSCustomFieldsEntityType.Account, entityId);
            // Handle null or empty input
            if (inputCustomFields == null || inputCustomFields.Count == 0)
            {
                // Delete all existing fields if no input provided
                foreach (var existingField in existingFields)
                {
                    existingField.IsActive = false;
                    existingField.UpdatedAt = DateTime.UtcNow;
                    existingField.UpdatedBy = userId;
                    await customFieldService.UpdateAsync(existingField.Id, existingField, userId);
                }
                return;
            }

            // Create a dictionary for quick lookup of existing fields by ID
            var existingFieldsDict = existingFields.ToDictionary(f => f.Id, f => f);
            var processedFieldIds = new HashSet<long>();

            // Process input fields - update existing or create new
            foreach (var inputField in inputCustomFields)
            {
                if (inputField.CustomFieldId > 0 && existingFieldsDict.TryGetValue(inputField.CustomFieldId, out var existingField))
                {
                    // Update existing field using CustomFieldId
                    processedFieldIds.Add(existingField.Id);
                    var updatedField = new LMSCustomFieldDto
                    {
                        Id = existingField.Id,
                        EntityType = existingField.EntityType,
                        EntityId = existingField.EntityId,
                        Name = inputField.Label,
                        FieldType = (int)inputField.Type,
                        Placeholder = inputField.Placeholder,
                        Options = JsonConvert.SerializeObject(inputField.Options),
                        IsRequired = inputField.Required,
                        IsActive = inputField.IsActive,
                        CompanyId = companyId,
                        Sequence = existingField.Sequence,
                        Description = inputField.Description,
                        CreatedBy = existingField.CreatedBy,
                        CreatedAt = existingField.CreatedAt,
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = userId,
                        MinValue = inputField.MinValue,
                        MaxValue = inputField.MaxValue
                    };
                    await customFieldService.UpdateAsync(existingField.Id, updatedField, userId);
                }
                else
                {
                    // Create new field (CustomFieldId is 0 or doesn't exist in existing fields)
                    var newField = new LMSCustomFieldDto
                    {
                        EntityType = (int)LMSCustomFieldsEntityType.Account,
                        EntityId = entityId,
                        Name = inputField.Label,
                        FieldType = (int)inputField.Type,
                        Placeholder = inputField.Placeholder,
                        Options = JsonConvert.SerializeObject(inputField.Options),
                        IsRequired = inputField.Required,
                        Sequence = inputField.Sequence,
                        IsActive = inputField.IsActive,
                        CreatedBy = userId,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = userId,
                        CompanyId = companyId,
                        Description = inputField.Description,
                        MinValue = inputField.MinValue,
                        MaxValue = inputField.MaxValue
                    };
                    await customFieldService.CreateAsync(newField, userId);
                }
            }

            // Delete fields that are no longer in the input
            foreach (var existingField in existingFields)
            {
                if (!processedFieldIds.Contains(existingField.Id) && existingField.IsActive)
                {
                    existingField.IsActive = false;
                    existingField.UpdatedAt = DateTime.UtcNow;
                    existingField.UpdatedBy = userId;
                    await customFieldService.UpdateAsync(existingField.Id, existingField, userId);
                }
            }
        }

        private static void ValidateCustomFieldMinMaxValues(List<TemplateCustomFields> customFields)
        {
            if (customFields == null)
                return;

            foreach (var field in customFields)
            {
                if (field.Type == LMSCustomFieldType.Number)
                {
                    if (!string.IsNullOrEmpty(field.MinValue) && !decimal.TryParse(field.MinValue, out _))
                    {
                        throw new InvalidOperationException("minValue is not valid number.");
                    }
                    if (!string.IsNullOrEmpty(field.MaxValue) && !decimal.TryParse(field.MaxValue, out _))
                    {
                        throw new InvalidOperationException("maxValue is not valid number.");
                    }
                }
                else if (field.Type == LMSCustomFieldType.Date)
                {
                    if (!string.IsNullOrEmpty(field.MinValue) && !DateTime.TryParse(field.MinValue, out _))
                    {
                        throw new InvalidOperationException("minValue is not valid date.");
                    }
                    if (!string.IsNullOrEmpty(field.MaxValue) && !DateTime.TryParse(field.MaxValue, out _))
                    {
                        throw new InvalidOperationException("maxValue is not valid date.");
                    }
                }
            }
        }
    }
}
