﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class SurveyToCompanyZoneMap : IUpdatableEntity
{
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public long Id { get; set; }

    public bool IsActive { set; get; }

    [Column("UpdatedOn")] public DateTime LastUpdatedAt { get; set; }

    [Column("Survey")] public long SurveyId { get; set; }

    public virtual Zone Zone { get; set; }

    [Column("Zone")] public long ZoneId { get; set; }
}
