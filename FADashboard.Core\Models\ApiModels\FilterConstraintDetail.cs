﻿using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;


public class CohortV2View
{
    public long Id { get; set; }
    public string? Name {  get; set; }
    public string? Description {  get; set; }
    public FilterConstraintEntityType EntityType { get; set; }
    public bool IsDeactive {  get; set; }
    public UserPlatform? UserPlatform { get; set; }
    public string ExtraInfoJson {  get; set; }
}
public class FilterConstraintDetailInput : Constraints
{
    public long Id {  get; set; }
    public long CompanyId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public UserPlatform UserPlatform { get; set; }
    public FilterConstraintEntityType EntityType { get; set; }
    public string distributorCohortInput { get; set; }
    public ExtraInfoJson ExtraInfoJson { get; set; }

}

public class DistributorConstraints
{
    public List<long> ChannelsList { get; set; }
    public List<string> Channels {  get; set; }
    public List<long> SegmentationsList { get; set; }
    public List<string> Segmentations { get; set; }
}


public class DistributorId
{
    public List<long> DistributorIds { get; set; } 
}

public class GeographyConstraints
{
    public List<long> RegionIds { get; set; } 
    public List<long> ZoneIds { get; set; } 
}

public class OutletCohortConstraints
{
    public List<long> ChannelsList { get; set; }
    public List<string> Channels { get; set; }
    public List<long> ShopTypesList {  get; set; }
    public List<long> ShopTypes { get; set; }
    public List<string> Segmentations { get; set; }
    public List<long> SegmentationsList { get; set; }
    public string? IsFocused { get; set; }
    public List<long> CustomTag { get; set; }
    public string? AttributeText1 { get; set; }
    public int? AttributeNumber1 { get; set; }
    public bool AttributeBoolean1 { get; set; }
}

public class OutletCohortConstraintsToBeSaved
{
    public List<string> Channels { get; set; }
    public List<long> ShopTypes { get; set; }
    public List<string> Segmentations { get; set; }
    public string IsFocused { get; set; }
    public List<long> CustomTag { get; set; }
    public string AttributeText1 { get; set; }
    public int? AttributeNumber1 { get; set; }
    public bool AttributeBoolean1 { get; set; }
}

public class Constraints
{
    public GeographyConstraints GeographyConstraints { get; set; }
    public OutletCohortConstraints OutletCohortConstraints { get; set; }

    public DistributorConstraints DistributorConstraints { get; set; }
    public DistributorId Distributor {  get; set; }
}

public class FilterConstraintById : Constraints
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public UserPlatform UserPlatform { get; set; }
    public FilterConstraintEntityType EntityType { get; set; }
    public string distributorCohortInput { get; set; }
    public string ExtraInfoJson { get; set; }

}
