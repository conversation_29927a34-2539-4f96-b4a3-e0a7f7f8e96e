﻿namespace FADashboard.Core.Models.ApiModels;

public class ProductWinbackInput
{
    public long Id { get; set; }
    public List<long> AsmIds { get; set; }
    public List<OutletConstraintsInput> OutletConstraints { get; set; }
    public List<ProductConstraintsInput> ProductConstraints { get; set; }
    public DateTime ActiveFrom { get; set; }
    public DateTime ActiveTill { get; set; }
    public DateTime TrainFrom { get; set; }
    public DateTime TrainTill { get; set; }
    public string LogicRemark { get; set; }
    public double DeviationAllowed { get; set; }
    public int MaxRecordsToBeTagged { get; set; }
    public bool? IsDeactive { get; set; }
}

public class ProductWinbackList
{
    public long Id { get; set; }
    public string LogicRemark { get; set; }
    public DateTime ActiveFrom { get; set; }
    public DateTime ActiveTill { get; set; }
    public DateTime TrainFrom { get; set; }
    public DateTime TrainTill { get; set; }
    public bool IsDeactive { get; set; }
}
