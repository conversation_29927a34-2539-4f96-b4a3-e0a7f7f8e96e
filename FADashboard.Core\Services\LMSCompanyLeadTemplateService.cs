﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.Core.Services
{
    public class LMSCompanyLeadTemplateService(ILMSCompanyLeadTemplateRepository companyLeadTemplateRepository, ILMSCustomFieldService customFieldService) : ILMSCompanyLeadTemplateService
    {
        public async Task<LMSCompanyLeadTemplateDto> GetCompanyLeadTemplateByIdAsync(long id) =>
            await companyLeadTemplateRepository.GetByIdAsync(id);

        public async Task<PagedResult<LMSCompanyLeadTemplateDto>> GetCompanyLeadTemplatesAsync(long companyId, LMSLeadTemplateQueryParameters queryParameters) =>
            await companyLeadTemplateRepository.GetTemplatesAsync(companyId, queryParameters);

        public async Task<LMSCompanyLeadTemplateDto> CreateCompanyLeadTemplateAsync(long companyId, LMSCompanyLeadTemplateInput templateInput, long createdByUserId)
        {
            // Validate MinValue and MaxValue for custom fields
            ValidateCustomFieldMinMaxValues(templateInput.CustomFields);

            if (await companyLeadTemplateRepository.TemplateNameExistsAsync(companyId, templateInput.TemplateName))
                throw new InvalidOperationException("A lead template with this name already exists.");

            if (templateInput.IsDefault)
                await UnsetPreviousDefaultTemplate(companyId, createdByUserId);

            var templateDto = new LMSCompanyLeadTemplateDto
            {
                CompanyId = companyId,
                TemplateName = templateInput.TemplateName,
                Description = templateInput.Description,
                IsDefault = templateInput.IsDefault,
                IsActive = templateInput.IsActive,
                CreatedBy = createdByUserId,
                CreatedAt = DateTime.UtcNow
            };

            var createdTemplate = await companyLeadTemplateRepository.AddAsync(templateDto);

            if (templateInput.CustomFields != null && templateInput.CustomFields.Count != 0)
            {
                var customFields = templateInput.CustomFields.Select(field => new LMSCustomFieldDto
                {
                    EntityType = (int)LMSCustomFieldsEntityType.Lead,
                    EntityId = createdTemplate.Id,
                    CompanyId = companyId,
                    Name = field.Label,
                    FieldType = (int)field.Type,
                    Placeholder = field.Placeholder,
                    Options = JsonConvert.SerializeObject(field.Options),
                    Sequence = field.Sequence,
                    Description = field.Description,
                    MinValue = field.MinValue,
                    MaxValue = field.MaxValue,
                    IsRequired = field.Required,
                    IsActive = field.IsActive,
                    CreatedBy = createdByUserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                }).ToList();

                await customFieldService.AddRangeAsync(customFields, createdByUserId);
            }

            return createdTemplate;
        }

        public async Task<LMSCompanyLeadTemplateDto> UpdateCompanyLeadTemplateAsync(long id, long companyId, LMSCompanyLeadTemplateInput templateInput, long updatedByUserId)
        {
            var existingTemplate = await GetAndValidateTemplate(id, companyId);

            // Validate MinValue and MaxValue for custom fields
            ValidateCustomFieldMinMaxValues(templateInput.CustomFields);

            if (await companyLeadTemplateRepository.TemplateNameExistsAsync(companyId, templateInput.TemplateName, id))
                throw new InvalidOperationException("A lead template with this name already exists.");

            if (templateInput.IsDefault && !existingTemplate.IsDefault)
                await UnsetPreviousDefaultTemplate(companyId, updatedByUserId, id);

            existingTemplate.TemplateName = templateInput.TemplateName;
            existingTemplate.Description = templateInput.Description;
            existingTemplate.IsDefault = templateInput.IsDefault;
            existingTemplate.IsActive = templateInput.IsActive;
            existingTemplate.UpdatedBy = updatedByUserId;
            existingTemplate.UpdatedAt = DateTime.UtcNow;

            var updatedTemplate = await companyLeadTemplateRepository.UpdateAsync(existingTemplate);

            // Efficient upsert pattern: update existing, create new, delete obsolete
            await UpsertCustomFieldsAsync(id, templateInput.CustomFields, companyId, updatedByUserId);

            return updatedTemplate;
        }

        public async Task<bool> DeleteCompanyLeadTemplateAsync(long id, long companyId)
        {
            var existingTemplate = await GetAndValidateTemplate(id, companyId);
            if (await companyLeadTemplateRepository.GetLeadsCountAsync(id) > 0)
                throw new InvalidOperationException("This lead template cannot be deleted because it has associated leads.");

            return await companyLeadTemplateRepository.DeleteAsync(id, null); // updatedByUserId is missing from interface
        }

        public async Task<bool> SetDefaultAsync(long id, long companyId, long updatedByUserId)
        {
            var templateToSet = await GetAndValidateTemplate(id, companyId);
            if (templateToSet.IsDefault) return true;

            await UnsetPreviousDefaultTemplate(companyId, updatedByUserId, id);

            templateToSet.IsDefault = true;
            templateToSet.UpdatedBy = updatedByUserId;
            templateToSet.UpdatedAt = DateTime.UtcNow;

            await companyLeadTemplateRepository.UpdateAsync(templateToSet);
            return true;
        }

        public async Task<bool> SetActiveStatusAsync(long id, long companyId, bool isActive, long updatedByUserId)
        {
            var template = await GetAndValidateTemplate(id, companyId);

            if (isActive)
            {
                // TODO: Add validation: A template cannot be marked as active unless it has been assigned stages.
                // This requires a repository for template-stage associations.
            }

            template.IsActive = isActive;
            template.UpdatedBy = updatedByUserId;
            template.UpdatedAt = DateTime.UtcNow;

            await companyLeadTemplateRepository.UpdateAsync(template);
            return true;
        }

        private async Task<LMSCompanyLeadTemplateDto> GetAndValidateTemplate(long id, long companyId)
        {
            var template = await companyLeadTemplateRepository.GetByIdAsync(id);
            if (template == null || template.CompanyId != companyId)
                throw new KeyNotFoundException($"LMSCompanyLeadTemplate with ID {id} not found for this company.");
            return template;
        }

        private async Task UnsetPreviousDefaultTemplate(long companyId, long updatedByUserId, long? excludeTemplateId = null)
        {
            var currentDefault = await companyLeadTemplateRepository.GetDefaultTemplateAsync(companyId);
            if (currentDefault != null && currentDefault.Id != excludeTemplateId)
            {
                currentDefault.IsDefault = false;
                currentDefault.UpdatedBy = updatedByUserId;
                await companyLeadTemplateRepository.UpdateAsync(currentDefault);
            }
        }

        private async Task UpsertCustomFieldsAsync(long entityId, List<TemplateCustomFields> inputCustomFields, long companyId, long userId)
        {
            // Get existing custom fields for this entity
            var existingFields = await customFieldService.GetByEntityAsync((int)LMSCustomFieldsEntityType.Lead, entityId);
            // Handle null or empty input
            if (inputCustomFields == null || inputCustomFields.Count == 0)
            {
                // Delete all existing fields if no input provided
                foreach (var existingField in existingFields)
                {
                    existingField.IsActive = false;
                    existingField.UpdatedAt = DateTime.UtcNow;
                    existingField.UpdatedBy = userId;
                    await customFieldService.UpdateAsync(existingField.Id, existingField, userId);
                }
                return;
            }

            // Create a dictionary for quick lookup of existing fields by ID
            var existingFieldsDict = existingFields.ToDictionary(f => f.Id, f => f);
            var processedFieldIds = new HashSet<long>();

            // Process input fields - update existing or create new
            foreach (var inputField in inputCustomFields)
            {
                if (inputField.CustomFieldId > 0 && existingFieldsDict.TryGetValue(inputField.CustomFieldId, out var existingField))
                {
                    // Update existing field using CustomFieldId
                    processedFieldIds.Add(existingField.Id);
                    var updatedField = new LMSCustomFieldDto
                    {
                        Id = existingField.Id,
                        EntityType = existingField.EntityType,
                        EntityId = existingField.EntityId,
                        Name = inputField.Label,
                        FieldType = (int)inputField.Type,
                        Placeholder = inputField.Placeholder,
                        Options = JsonConvert.SerializeObject(inputField.Options),
                        IsRequired = inputField.Required,
                        IsActive = inputField.IsActive,
                        Sequence = inputField.Sequence,
                        CompanyId = companyId,
                        Description = inputField.Description,
                        CreatedBy = existingField.CreatedBy,
                        CreatedAt = existingField.CreatedAt,
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = userId,
                        MinValue = inputField.MinValue,
                        MaxValue = inputField.MaxValue
                    };
                    await customFieldService.UpdateAsync(existingField.Id, updatedField, userId);
                }
                else
                {
                    // Create new field (CustomFieldId is 0 or doesn't exist in existing fields)
                    var newField = new LMSCustomFieldDto
                    {
                        EntityType = (int)LMSCustomFieldsEntityType.Lead,
                        EntityId = entityId,
                        Name = inputField.Label,
                        FieldType = (int)inputField.Type,
                        Placeholder = inputField.Placeholder,
                        Options = JsonConvert.SerializeObject(inputField.Options),
                        IsRequired = inputField.Required,
                        IsActive = inputField.IsActive,
                        Sequence = inputField.Sequence,
                        CreatedBy = userId,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = userId,
                        CompanyId = companyId,
                        Description = inputField.Description,
                        MinValue = inputField.MinValue,
                        MaxValue = inputField.MaxValue
                    };
                    await customFieldService.CreateAsync(newField, userId);
                }
            }

            // Delete fields that are no longer in the input
            foreach (var existingField in existingFields)
            {
                if (!processedFieldIds.Contains(existingField.Id) && existingField.IsActive)
                {
                    existingField.IsActive = false;
                    existingField.UpdatedAt = DateTime.UtcNow;
                    existingField.UpdatedBy = userId;
                    await customFieldService.UpdateAsync(existingField.Id, existingField, userId);
                }
            }
        }

        private static void ValidateCustomFieldMinMaxValues(List<TemplateCustomFields> customFields)
        {
            if (customFields == null)
                return;

            foreach (var field in customFields)
            {
                if (field.Type == LMSCustomFieldType.Number)
                {
                    if (!string.IsNullOrEmpty(field.MinValue) && !decimal.TryParse(field.MinValue, out _))
                    {
                        throw new InvalidOperationException("minValue is not valid number.");
                    }
                    if (!string.IsNullOrEmpty(field.MaxValue) && !decimal.TryParse(field.MaxValue, out _))
                    {
                        throw new InvalidOperationException("maxValue is not valid number.");
                    }
                }
                else if (field.Type == LMSCustomFieldType.Date)
                {
                    if (!string.IsNullOrEmpty(field.MinValue) && !DateTime.TryParse(field.MinValue, out _))
                    {
                        throw new InvalidOperationException("minValue is not valid date.");
                    }
                    if (!string.IsNullOrEmpty(field.MaxValue) && !DateTime.TryParse(field.MaxValue, out _))
                    {
                        throw new InvalidOperationException("maxValue is not valid date.");
                    }
                }
            }
        }
    }
}
