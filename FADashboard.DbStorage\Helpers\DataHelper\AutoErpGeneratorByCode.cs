﻿using EntityHelper;
using FADashboard.DbStorage.MasterRepositories.Models;
using FADashboard.Core.Interfaces;


namespace FADashboard.DbStorage.Helpers.DataHelper;
public class AutoERPGenerateByCode(ICompanyRepository companyRepository)
{
    public RepositoryResponse GenerateUpdateErpIdByCode<T>(T entity, long companyId, string shortRegionCode = null) where T : IERPEntityByCode
    {
        var shortName = companyRepository.GetShortName(companyId);
        var shouldGenerateErpId = string.IsNullOrEmpty(entity.ErpCode);
        if (shouldGenerateErpId)
        {
            if (typeof(T) == typeof(CompanyProduct) && !string.IsNullOrEmpty(shortName.ProductShortName))
            {
                entity.ErpCode = $"FP_{shortName.ProductShortName}_{(entity.Id - 191) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }
            return RepositoryResponse.GetSuccessResponse(entity.Id);
        }

        return RepositoryResponse.GetSuccessResponse(entity.Id, "Not Valid for ERPID Generation");
    }
}
