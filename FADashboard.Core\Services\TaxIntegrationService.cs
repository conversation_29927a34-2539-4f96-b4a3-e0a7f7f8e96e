﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class TaxIntegrationService(ITaxIntegrationDeviceRepository taxIntegrationDeviceRepository,
    IDistributorWiseTaxIntegrationRepository distributorWiseTaxIntegrationRepository,
    ICurrentUser currentUser)
{
    private readonly ITaxIntegrationDeviceRepository taxIntegrationDeviceRepository = taxIntegrationDeviceRepository;
    private readonly IDistributorWiseTaxIntegrationRepository distributorWiseTaxIntegrationRepository = distributorWiseTaxIntegrationRepository;
    private readonly ICurrentUser currentUser = currentUser;

    public async Task<List<TaxIntegrationDeviceResponse>> GetTaxIntegrationDevices(string country)
    {
        var data = await taxIntegrationDeviceRepository.GetTaxIntegrationDeviceInfo(country);
        return data;
    }
    public async Task<List<TaxIntegrationColumnMeta>> GetTaxIntegrationColumnMeta(long taxIntegrationDeviceId)
    {
        var data = await taxIntegrationDeviceRepository.GetTaxIntegrationColumnMeta(taxIntegrationDeviceId);
        return data;
    }
    public async Task<List<long?>> GetDeviceConfiguredDistributors()
    {
        var data = await distributorWiseTaxIntegrationRepository.GetDeviceConfiguredDistributors(currentUser.CompanyId);
        return data;
    }
    public async Task<RepositoryResponse> CreateDistributorWiseTaxIntegration(TaxIntegrationInputModel inputModel)
    {
        var result = await distributorWiseTaxIntegrationRepository.CreateDistributorWiseTaxIntegration(inputModel, currentUser.CompanyId);
        return result;
    }
    public async Task<List<DeviceConfiguredDistributorsMeta>> GetAllTaxDevicesConfiguredDistributors()
    {
        var result = await distributorWiseTaxIntegrationRepository.GetAllTaxDevicesConfiguredDistributors(currentUser.CompanyId);
        return result;
    }
    public async Task<DeviceConfiguredDistributorsMeta> GetDeviceConfiguredDistributor(long id)
    {
        var result = await distributorWiseTaxIntegrationRepository.GetDeviceConfiguredDistributor(id, currentUser.CompanyId);
        return result;
    }
    public async Task<RepositoryResponse> DeactivateTaxDeviceConfiguredDistributor(long id)
    {
        var result = await distributorWiseTaxIntegrationRepository.DeactivateTaxDeviceConfiguredDistributor(id, currentUser.CompanyId);
        return result;
    }
    public async Task<RepositoryResponse> UpdateDistributorWiseTaxIntegration(TaxIntegrationInputModel inputModel)
    {
        var result = await distributorWiseTaxIntegrationRepository.UpdateDistributorWiseTaxIntegration(inputModel, currentUser.CompanyId);
        return result;
    }



}
