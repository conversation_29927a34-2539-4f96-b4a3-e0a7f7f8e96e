﻿using System.Linq.Expressions;
using System.Reflection;
using LinqKit;

namespace FADashboard.DbStorage.Helpers;

public class SearchHelper<T> : ISearchHelper<T>
{
    public IQueryable<T> ApplyColumnSearch(IQueryable<T> entities, Dictionary<string, string> columnFilters)
    {
        if (!entities.Any())
            return entities;

        if (columnFilters == null || columnFilters.Count == 0)
            return entities;

        var propertyInfos = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var predicate = PredicateBuilder.New<T>();
        foreach (var param in columnFilters)
        {
            var propertyFromQueryName = param.Key;
            var objectProperty = propertyInfos.FirstOrDefault(pi => pi.Name.Equals(propertyFromQueryName, StringComparison.OrdinalIgnoreCase));

            if (objectProperty == null)
                continue;

            if (objectProperty.PropertyType == typeof(string))
            {
                var likeTerm = $"{param.Value}";
                predicate = predicate.And(GenerateLikeExpression<T>(objectProperty.Name, likeTerm));
            }
            //TODO Generalize and make a common helper
            else if (objectProperty.PropertyType == typeof(int) || objectProperty.PropertyType == typeof(double))
            {
                var likeTerm = $"{param.Value}";
                predicate = predicate.And(GenerateNumberExpression<T>(objectProperty.Name, likeTerm));
            }
            //Date: Aug 06,2024  Asana: https://app.asana.com/0/139097763031412/1207830164604596/f Reason: Add Condition for List for Route Position Mapping
            else if (typeof(IList<string>).IsAssignableFrom(objectProperty.PropertyType))
            {
                var likeTerm = $"{param.Value}";
                predicate = predicate.And(GenerateLikeExpressionList<T>(objectProperty.Name, likeTerm));
            }
        }

        return entities.Where(predicate);
    }

    private static Expression<Func<T, bool>> GenerateLikeExpression<T>(string propertyName, string searchTerm)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);
        var method = typeof(string).GetMethod("Contains", [typeof(string)]);
        var contains = Expression.Call(property, method, Expression.Constant(searchTerm, typeof(string)));
        return Expression.Lambda<Func<T, bool>>(contains, parameter);
    }

    private static Expression<Func<T, bool>> GenerateLikeExpressionList<T>(string propertyName, string searchTerm)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);
        var anyMethod = typeof(Enumerable).GetMethods().FirstOrDefault(m => m.Name == "Any" && m.GetParameters().Length == 2)?.MakeGenericMethod(typeof(string));
        var lambdaParameter = Expression.Parameter(typeof(string), "y");
        var containsMethod = typeof(string).GetMethod("Contains", [typeof(string)]);
        var containsCall = Expression.Call(lambdaParameter, containsMethod, Expression.Constant(searchTerm, typeof(string)));
        var lambdaBody = containsCall;
        var lambda = Expression.Lambda(lambdaBody, lambdaParameter);
        var anyCall = Expression.Call(anyMethod, property, lambda);

        return Expression.Lambda<Func<T, bool>>(anyCall, parameter);
    }

    private static Expression<Func<T, bool>> GenerateNumberExpression<T>(string propertyName, string searchTerm)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);

        if (property.Type == typeof(int))
        {
            if (int.TryParse(searchTerm, out var intValue))
            {
                var equality = Expression.Equal(property, Expression.Constant(intValue));
                return Expression.Lambda<Func<T, bool>>(equality, parameter);
            }
        }
        else if (property.Type == typeof(double))
        {
            if (double.TryParse(searchTerm, out var decimalValue))
            {
                var floorValue = Math.Floor(decimalValue);
                var greaterThanOrEqual = Expression.GreaterThanOrEqual(property, Expression.Constant(decimalValue));
                var lessThan = Expression.LessThan(property, Expression.Constant(floorValue + 1));
                var andExpression = Expression.AndAlso(greaterThanOrEqual, lessThan);
                return Expression.Lambda<Func<T, bool>>(andExpression, parameter);
            }
        }

        return x => true;
    }
}
