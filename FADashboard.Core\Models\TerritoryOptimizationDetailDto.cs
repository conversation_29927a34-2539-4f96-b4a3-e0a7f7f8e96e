﻿using Libraries.CommonEnums;
using Microsoft.AspNetCore.Http;

namespace FADashboard.Core.Models;
public class TerritoryOptimizationDetailDto
{
    public long Id { get; set; }

    public string InputFileName { get; set; }

    public TerritoryOptimizationStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public string EmailId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string EstimatedTime { get; set; }

    public TerritoryInputConstraints InputConstraints { get; set; }

    public long CompanyId { get; set; }

}

public class TerritoryInputConstraints {
    public int NumberOfIterations { get; set; }
    public int NumberOfTerritories { get; set; }
    public double FeasibleDeviation { get; set; }
    public int IdealOutletCount { get; set; }

}

public class TerritoryOptimizationFileModel
{
    public string inputConstraints { get; set; }
    public IFormFile file { get; set; }
}

public class TerritoryOptimizationQueueModel
{
    public long Id { get; set; }
    public string FileName { get; set; }
    public int NumberOfIterations { get; set; }
    public int NumberOfTerritories { get; set; }
    public double FeasibleDeviation { get; set; }
    public int IdealOutletCount { get; set; }
    public string Country { get; set; }
}

public class TerritoryBoundaryModel
{
    public List<List<double>> Boundaries { get; set; }
    public double TerritoryArea { get; set; }
    public double? TerritoryOverlapArea { get; set; }
    public int? TerritoryVisitFrequency { get; set; }
}

public class TerritoryDataReponseModel
{
    public List<string> TerritoryList { get; set; }
    public Dictionary<string, TerritoryBoundaryModel> TerritoryBoundaries { get; set; }
    public Dictionary<string, List<List<double>>> SubterritoryBoundaries { get; set; }
}

public class TerritoryOutletModel
{
    public string OutletErpId { get; set; }
    public decimal Longitude { get; set; }
    public decimal Latitude { get; set; }
    public string TerritoryId { get; set; }
    public string SubTerritoryId { get; set; }
    public string EmployeeId { get; set; }
    //public decimal RetailTime { get; set; }
    public int VisitFrequency { get; set; }
}

public class TerritoryOutletDataInputModel
{
    public long Id { get; set; }
    public List<string> TerritoryIds { get; set; }
}

public class TerritoryChangeInputModel
{
    public long Id { get; set; }
    public Dictionary<string,TerritoryChangeOutletModel> Territories { get; set; }
}
public class TerritoryChangeOutletModel
{
    public List<string> Outlets { get; set; }
    public bool IsDeleted { get; set; }
    public double TerritoryArea { get; set; }
    public double TerritoryOverlapArea { get; set; }
    public int? TerritoryVisitFrequency { get; set; }
}

public class TerritoryChangeQueueModel
{
    public long Id { get; set; }
    public string Filename { get; set; }

}
