﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.AsanaHelpers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.AsanaModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Microsoft.AspNetCore.Http;


namespace FADashboard.Core.Services;
public class BugCreationService(ICompanyRepository companyRepository,
    IBugCreationRepository bugCreationRepository,
    ICurrentUser currentUser,
    AsanaTaskService asanaTaskService) : RepositoryResponse
{
    public async Task<List<EntityMinWithCompany>> GetAllCompanies()
    {
        var data = await companyRepository.GetCompanyList();
        return data;
    }

    public async Task<List<BugIssueListView>> GetAllAsanas()
    {
        var data = await bugCreationRepository.GetAllAsanaBugs(currentUser.CompanyId);
        return data;
    }
    public async Task<RepositoryResponse> AddAsanaToDb(BugViewModel input)
    {
        try
        {
            //create asana
            var asanaDetails = await FileBug(input);
            var asanaLink = asanaDetails?.AsanaLink;
            if (asanaLink == null)
            {
                return new RepositoryResponse { Id = 0, Message = "No asana account associated to your email found" };
            }
            var asanaModel = new AsanaBugModel
            {
                CompanyId = input.ClientId,
                Platform = input.Platform,
                Module = input.SelectedModule,
                AsanaTitle = input.IssueTitle,
                IssueDescription = input.IssueDescription,
                AsanaLink = asanaLink,
                RaisedSeverity = input.Severity,
                UserId = input.UserId,
                ClientCategory = input.ClientCategory ?? "",
                ListOfCompany =  input.ListOfCompanies ?? "",
                CreatedBy = currentUser.Name,
            };
            //add created asana in db
            return await bugCreationRepository.SaveAsana(asanaModel);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = 0, ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }
    public async Task<List<AsanaBugModel>> GetRecentAsanas(Platform platform, string module)
    {
        return await bugCreationRepository.GetAsanaBugs(platform, module);
    }

    public async Task<RepositoryResponse> AddToExistingAsana(long asanaId)
    {
        var companyId = currentUser.CompanyId;
        return await bugCreationRepository.AddToExistingAsana(companyId, asanaId);
    }

    private async Task<AsanaTask> FileBug(BugViewModel model)
    {
        try
        {
            var taskModel = AsanaNewTask.CreateFrom(model);
            var attachments = AsanaNewTask.Attachments(model).Attachment ?? new List<IFormFile>();

            var newTask = await asanaTaskService.CreateNewTask(taskModel, currentUser.EmailId, new AsanaProjectOwner
            {
                ProjectIds = model.GetProjects(),
                ImportanceScale = model.Severity,
                UserCredentials = AsanaCredentials.GetCredentialsForUser(model.GetOwnerName()),
            });

            foreach (var attachment in attachments)
            {
                var ms = new MemoryStream();
                attachment.CopyTo(ms);
                var fileBytes = ms.ToArray();
                var uploadattachment = await asanaTaskService.AttachFile(fileBytes, attachment.FileName, newTask.Id, AsanaCredentials.GetCredentialsForUser(model.GetOwnerName()));
            }

            return newTask;
        }
        catch (Exception ex)
        {
            return new AsanaTask { Id = "0", ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }
}
