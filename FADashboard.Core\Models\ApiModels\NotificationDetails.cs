﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class KPITrigger
{
    public long KpiId { get; set; }
    public string KpiName { get; set; }
    public ComparisonOperator ComparisonOperator { get; set; }
    public string Value { get; set; }
}

public class NotificationDetails
{
    public long Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public NotificationCondition NotificationType { get; set; }

    public UserPlatform UserPlatform { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    public bool Deleted { get; set; }

    public List<long> CohortIds { get; set; }

    public List<string> CohortNames { get; set; }
    public string Cron { get; set; }
    public bool IsAggregated { get; set; }
    public string AggregationCron { get; set; }
    public int? AggregationDelay { get; set; }
    public List<long> ManagerLevel { get; set; }
    public List<KPITrigger> KPITriggers { set; get; }
    public NotificationMessageInput NotificationMessages { set; get; }
    public NotificationMessageInput AggregatedMessages { set; get; }
}
