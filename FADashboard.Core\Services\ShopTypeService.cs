﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Library.CommonHelpers;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class ShopTypeService(IShopTypeRepository shopTypeRepository, ICurrentUser currentUser) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidShopType(ShopType shopType)
    {
        var shopTypes = await GetShopTypes(false);

        if (shopType.Id != 0)
        {
            shopTypes = shopTypes.Where(p => p.Id != shopType.Id).ToList();
        }

        var shopNameList = shopTypes.Select(p => p.ShopTypeName.NormalizeCaps().Trim()).ToList();
        if (shopNameList.Contains(shopType.ShopTypeName.NormalizeCaps().Trim()))
        {
            return new RepositoryResponse
            {
                Id = shopType.Id, ExceptionMessage = "Shop Type with this name already Exists!", Message = "Shop Type Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (shopType.ErpId != null) // Non mandatory field
        {
            var shopTypeErpList = shopTypes.Where(b => !string.IsNullOrEmpty(b.ErpId) && b.ErpId.isSameWhenNormalized(shopType.ErpId));
            if (shopTypeErpList.Any())
            {
                return new RepositoryResponse { Id = shopType.Id, Message = "Shop Type with this ERPId already exists", IsSuccess = false, };
            }
        }

        return new RepositoryResponse { Id = shopType.Id, Message = "Shop Type Unique", IsSuccess = true, };
    }

    private async Task<RepositoryResponse> IsValidSubShopType(SubshopType subShopType)
    {
        var subShopTypes = await GetSubShopTypes(false);

        if (subShopType.Id != 0)
        {
            subShopTypes = subShopTypes.Where(p => p.Id != subShopType.Id).ToList();
        }

        var shopNameList = subShopTypes.Select(p => p.Name.NormalizeCaps().Trim()).ToList();
        if (shopNameList.Contains(subShopType.Name.NormalizeCaps().Trim()))
        {
            return new RepositoryResponse
            {
                Id = subShopType.Id,
                ExceptionMessage = "Sub Shop Type with this name already Exists!",
                Message = "Sub Shop Type Creation/Updation Failed!",
                IsSuccess = false,
            };
        }

        if (subShopType.ErpId != null) // Non mandatory field
        {
            var shopTypeErpList = subShopTypes.Where(b => !string.IsNullOrEmpty(b.ErpId) && b.ErpId.isSameWhenNormalized(subShopType.ErpId));
            if (shopTypeErpList.Any())
            {
                return new RepositoryResponse { Id = subShopType.Id, ExceptionMessage = "Sub Shop Type with this ERPId already exists", Message = "Sub Shop Type with this ERPId already exists", IsSuccess = false, };
            }
        }

        return new RepositoryResponse { Id = subShopType.Id, Message = "Sub Shop Type Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> CreateUpdateShopType(ShopType shopType)
    {
        var checkValid = await IsValidShopType(shopType);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (shopType.Id == 0)
        {
            return await shopTypeRepository.CreateShopType(shopType, currentUser.CompanyId);
        }

        return await shopTypeRepository.UpdateShopType(shopType, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> DeactivateShopType(long shopTypeId) => await shopTypeRepository.DeactivateShopType(shopTypeId, currentUser.CompanyId);

    public async Task<List<Channel>> GetAllChannelsList(bool includeDeactivate)
    {
        var activeChannels = await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, includeDeactivate);
        var channelEnumDict = activeChannels.ToDictionary(p => p.Enum, p => p);
        var getChannels = new List<Channel>();
        foreach (OutletChannel item in Enum.GetValues(typeof(OutletChannel)))
        {
            var dbChannel = channelEnumDict.GetValueOrDefault(item);
            var channel = new Channel
            {
                Id = dbChannel?.Id ?? 0,
                Enum = item,
                CustomName = dbChannel?.CustomName,
                DefaultName = item.GetDisplayName(),
                ErpId = dbChannel?.ErpId,
                IsValid = dbChannel?.IsValid ?? false,
            };
            getChannels.Add(channel);
        }

        return getChannels;
    }

    public async Task<List<Channel>> GetChannels(bool includeDeactivate) => await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, includeDeactivate);

    public async Task<Channel> GetOutletChannelById(long id)
    {
        var channel = await shopTypeRepository.GetOutletChannelById(currentUser.CompanyId, id);
        return channel;
    }

    public async Task<ShopType> GetShopTypeById(long id)
    {
        var shopType = await shopTypeRepository.GetShopTypeById(currentUser.CompanyId, id);
        return shopType;
    }

    public async Task<List<ShopType>> GetShopTypes(bool inludeDeactive)
    {
        var shopTypes = await shopTypeRepository.GetShopTypes(currentUser.CompanyId, inludeDeactive);
        return shopTypes;
    }

    public async Task<List<ShopType>> GetShopTypesByChannelId(long channelId)
    {
        var shopTypes = await shopTypeRepository.GetShopTypesByChannelId(currentUser.CompanyId, channelId);
        return shopTypes;
    }

    public async Task<RepositoryResponse> UpdateChannels(List<Channel> channel) => await shopTypeRepository.UpdateChannels(channel, currentUser.CompanyId);

    public async Task<SubshopType> GetSubshopTypeById(long subshopTypeId, CancellationToken ct = default) => await shopTypeRepository.GetSubshopTypeById(subshopTypeId, currentUser.CompanyId, ct);
    public async Task<List<SubshopType>> GetSubShopTypes(bool includeDeactive = false, CancellationToken ct = default) => await shopTypeRepository.GetSubShopTypes(currentUser.CompanyId, includeDeactive, ct);
    public async Task<RepositoryResponse> CreateUpdateSubShopType(SubshopType subShopType, CancellationToken ct = default)
    {
        var checkValid = await IsValidSubShopType(subShopType);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }
        return await shopTypeRepository.CreateUpdateSubShopType(subShopType, currentUser.CompanyId, ct);
    }
    public async Task<RepositoryResponse> DeactivateSubShopType(long subShopTypeId, CancellationToken ct = default) => await shopTypeRepository.DeactivateSubShopType(subShopTypeId, currentUser.CompanyId, ct);

}
