﻿using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Teams : IAuditedEntity
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    public TeamModuleType ModuleType { get; set; }
}

public class TeamUserMappings : IAuditedEntity
{
    public long Id { get; set; }
    [ForeignKey("Teams")] public long TeamId { get; set; }
    [ForeignKey("ClientEmployee")] public long TeamPlayerId { get; set; }
    [Audited] public bool IsActive { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public ClientEmployee ClientEmployee { get; set; }
    public  Teams Team { get; set; }
}
