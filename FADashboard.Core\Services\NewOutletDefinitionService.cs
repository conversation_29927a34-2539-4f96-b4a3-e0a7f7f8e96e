﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class NewOutletDefinitionService(INewOutletDefinitionRepository newOutletDefinitionRepository, NomenclatureService nomenclatureService, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task CreateUpdateFields(List<NewOutletFieldAppMeta> fields)
    {
        var fieldsToCreate = fields.Where(f => f.Id == 0).ToList();
        var fieldstoUpdate = fields.Where(f => f.Id != 0).ToList();
        if (fieldsToCreate.Count > 0)
        {
            await newOutletDefinitionRepository.CreateFields(fieldsToCreate, currentUser);
        }

        if (fieldstoUpdate.Count > 0)
        {
            await newOutletDefinitionRepository.UpdateFields(fieldstoUpdate, currentUser);
        }
    }

    public async Task<List<NewOutletFieldAppMeta>> GetFields()
    {
        var fields = await newOutletDefinitionRepository.GetFields(currentUser.CompanyId);
        var nomenclatureDict = await nomenclatureService.GetCompanyNomenclatureDict();
        nomenclatureDict = nomenclatureDict.ToDictionary(k => k.Key.ToUpper(System.Globalization.CultureInfo.CurrentCulture), k => k.Value);

        foreach (var field in fields)
        {
            if (nomenclatureDict.ContainsKey(field.FieldName.ToUpper(System.Globalization.CultureInfo.CurrentCulture)))
            {
                field.FieldName = nomenclatureDict[field.FieldName.ToUpper(System.Globalization.CultureInfo.CurrentCulture)].DisplayName;
            }
        }

        return fields;
    }
}
