﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSLeadContactRepository
    {
        Task<LMSLeadContactDto> GetByIdAsync(long leadId, long contactId);
        Task<IEnumerable<LMSLeadContactDto>> GetAllByLeadIdAsync(long leadId);
        Task<LMSLeadContactDto> CreateAsync(long leadId, LMSLeadContactCreateInput input, long userId);
        Task<LMSLeadContactDto> UpdateAsync(long leadId, long contactId, LMSLeadContactUpdateInput input, long userId);
        Task<bool> DeleteAsync(long leadId, long contactId, long userId);
    }
}
