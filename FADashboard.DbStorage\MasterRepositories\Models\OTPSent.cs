﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("OTPsSent")]
public class OTPSent : ICreatedEntity, ICompanyEntity
{
    public OTPSent()
    {
        ValidityInMinutes = 10;
    }

    public long Id { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    [Required, StringLength(16)] public string PhoneNo { get; set; }
    public string OTPReason { get; set; }
    public Guid OTPSeed { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public int ValidityInMinutes { get; private set; }

    public int OTPHash { get; set; }
    public bool IsUtilized { get; set; }

    public long CompanyId { get; set; }

    public bool IsValid(string reason)
    {
        var minutesAllowed = ValidityInMinutes > 30 ? 30 : ValidityInMinutes;
        var isValid = !IsUtilized && (DateTime.UtcNow - CreatedAt).TotalMinutes < minutesAllowed;
        return isValid;
    }
}
