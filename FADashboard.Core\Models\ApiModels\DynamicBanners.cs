﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class DynamicBanners
{
    public long DynamicBannerId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public UserPlatform Platform { get; set; }
    public bool IsDeactive { get; set; }
    public List<long> CohortIds { get; set; }
    public List<CarouselBanner> CarouselBanners { get; set; }
}

public class CarouselBanner
{
    public long CarouselBannerId { get; set; }
    public string BannerImageGUID { get; set; }
    public string BannerRedirectUrl { get; set; }
    public int Sequence { get; set; }
}
