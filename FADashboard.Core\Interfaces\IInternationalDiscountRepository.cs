﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IInternationalDiscountRepository
{
    Task<RepositoryResponse> ActivateDeactivateDiscount(long discountId, long companyId, bool action);

    Task<RepositoryResponse> CreateInternationalDiscount(InternationalDiscountInput discount, long companyId);

    Task<InternationalDiscountInput> GetDiscountById(long discountId, long companyId);

    Task<List<InternationalDiscount>> GetInactiveInternationalDiscounts(long companyId, string searchTerm = null);

    Task<int> GetInactiveInternationalDiscountsCount(long companyId, string searchTerm = null);

    Task<List<InternationalDiscount>> GetInternationalDiscounts(long companyId, bool inludeDeactive);

    Task<int> GetInternationalDiscountsCount(long companyId, string searchTerm = null);

    Task<List<SegmentationList>> GetSegmentation(long companyId);

    Task<RepositoryResponse> UpdateInternationalDiscount(InternationalDiscountInput discount, long companyId);
}
