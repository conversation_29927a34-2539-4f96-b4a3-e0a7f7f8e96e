﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class OutletSegmentationAttributesService(
    ICurrentUser currentUser,
    IOutletSegmentationAttributesRepository outletSegmentationAttributesRepository
) : RepositoryResponse
{
    public async Task<List<OutletSegmentationAttribute>> GetAllOutletSegmentationAttributes() => await outletSegmentationAttributesRepository.GetAllOutletSegmentationAttributes(currentUser.CompanyId);

    public async Task<List<RepositoryResponse>> CreateUpdateDynamicSegmentations(List<OutletSegmentationInput> outletSegmentationInputs)
    {
        var responses = new List<RepositoryResponse>();
        var creations = new List<OutletSegmentationInput>();
        var updates = new List<OutletSegmentationInput>();

        foreach (var outletSegmentationInput in outletSegmentationInputs)
        {
            if (outletSegmentationInput.Id == 0 && !string.IsNullOrEmpty(outletSegmentationInput.ErpId) && !string.IsNullOrEmpty(outletSegmentationInput.DisplayName))
            {
                creations.Add(outletSegmentationInput);
            }
            else
            {
                updates.Add(outletSegmentationInput);
            }
        }

        if (creations.Count > 0)
        {
            var createResponses = await outletSegmentationAttributesRepository.CreateDynamicSegmentations(creations, currentUser.CompanyId);
            responses.AddRange(createResponses);
        }

        if (updates.Count > 0)
        {
            var updateResponses = await outletSegmentationAttributesRepository.UpdateDynamicSegmentations(updates, currentUser.CompanyId);
            responses.AddRange(updateResponses);
        }

        return responses;
    }
}
