﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ManagerAlerts")]
public class ManagerAlerts : ICreatedEntity
{
    public AlertAction Action { get; set; }
    public string ActionTakenBy { get; set; }
    public DateTime? ActionTakenTime { get; set; }
    public string AlertDescription { get; set; }
    public DateTime AlertServerTime { get; set; }
    public string AlertTitle { get; set; }
    public AlertType AlertType { get; set; }
    public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime DeviceTime { get; set; }
    public long? EntityId { get; set; }

    [Column("EventId")] public long? FAEventId { set; get; }

    public long Id { get; set; }
    public bool IsReviewed { get; set; }

    //ReportingManager ids are new ids
    public long? ReportingManager { get; set; }

    public long? ReportingManagerLevel2 { get; set; }
    public PortalUserRole ReportingManagerLevel2UserRole { get; set; }
    public PortalUserRole ReportingManagerUserRole { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public long? RequestorPosition { get; set; }
}
