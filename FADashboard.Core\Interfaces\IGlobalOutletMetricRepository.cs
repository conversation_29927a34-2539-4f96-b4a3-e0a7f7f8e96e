﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IGlobalOutletMetricRepository
{
    Task<RepositoryResponse> CreateGlobalOutletMetric(GlobalOutletMetricInput globalOutletMetric);

    Task<RepositoryResponse> UpdateGlobalOutletMetric(GlobalOutletMetricInput globalOutletMetric);

    Task<RepositoryResponse> ActivateDeactivateGlobalOutletMetric(long globalOutletMetricId, bool action);

    Task<GlobalOutletMetricInput> GetGlobalOutletMetricById(long globalOutletMetricId);

    Task<List<GlobalOutletMetricList>> GetGlobalOutletMetrics(bool includeDeactive , bool perspectiveOutlet = false);
}
