﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IDayStartRepository
{
    Task<List<DayStartMin>> GetDayRecordsForDate(long companyId, List<long> positionId, PortalUserRole userRole, string todayKey);
    Task<DayRecordsMinWithCount> GetSearchedDayRecords(long companyId, PaginationFilter validFilter, List<int> positionCodeLvls, DayStartType? dayStartType, List<PositionCodeDetails> positionData, PortalUserRole userRole, string todayKey);
    Task<ManagerDailyDataModel> GetDailyDataForManager(long companyId, List<long> positionId, PortalUserRole userRole, DateTime dateTime);
    Task<List<DayRecordsMin>> GetAllOpenDayStartForEmployee(long companyId, long employeeId);
    Task<List<long>> GetDayStartsData(long companyId, int? dormantDays);
}
