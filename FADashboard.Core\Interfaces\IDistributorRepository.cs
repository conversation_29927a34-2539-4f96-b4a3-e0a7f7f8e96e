﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IDistributorRepository
{
    Task<RepositoryResponse> ActivateDeactivateDistributor(long id, long companyId, bool action, CompanySettings companySettings);

    Task<RepositoryResponse> CreateDistributor(DistributorInput distributor, long companyId, CompanySettings companySettings, CancellationToken ct = default);

    Task<List<EntityMinWithStatus>> GetAllDistributorsMin(long companyId, bool includeDeactivate = false);

    Task<long> GetDistributerCount(long companyId, PaginationFilter validFilter);

    Task<DistributorDTO> GetDistributorById(long id, long companyId);

    Task<List<DistributorDTO>> GetDistributorByRegionIds(long companyId, List<long> regionIds);

    Task<List<DistributorDTO>> GetDistributors(long companyId, bool includeDeactivate = false);

    Task<List<DistributorOptionsList>> GetDistributorList(long companyId);
    Task<List<DistributorOptionsList>> GetDistributorListwithAdress(long companyId);

    Task<DistributorTotal> GetDistributors(long companyId, PaginationFilter validFilter, int? stockistType, List<long> regionIds);

    Task<List<EntityMin>> GetDistributorsForProductDivision(long productDivisionId, long companyId);

    Task<List<EntityMin>> GetProductDivisionsForDistributor(long distributorId, long companyId);

    Task<List<DistributorList>> GetDistributorsForTerritory(long territoryId, long companyId);

    Task<List<DistributorDTO>> GetDistributorsOfBeat(long companyId, long beatId);

    Task<List<DistributorDTO>> GetDistributorsForBeats(long companyId, List<long> beatIds);

    Task<List<DistributorDTO>> GetDistributorsOfPosition(long companyId, long positionId);

    Task<List<DistributorDTO>> GetDistributorsForPositions(long companyId, List<long> positionIds);

    Task<List<EntityMinWithErpStatus>> GetDistributorsOfStockistType(StockistType stockistType, long companyId, bool includeDeactivate = false);

    Task<RepositoryResponse> UpdateDistributor(DistributorInput distributor, long companyId, CompanySettings companySettings, CancellationToken ct = default);

    Task<List<EntityMinWithStatus>> GetSubStockistsForSuperStockist(long companyId, long superStockistId);
    Task<List<DistributorDTO>> GetDistributorData(List<long> distributorIds, long companyId);
    Task<List<EntityMin>> GetSegmentations(long companyId, bool includeDeactivate = false);
    Task<List<EntityMin>> GetAllDistributorChannels(long companyId, bool includeDeactivate = false);
    Task<List<DistChannels>> GetDistributorChannels(long companyId, bool includeDeactivate = true);
    Task<RepositoryResponse> ActivateDeactivateDistributorChannel(long id, long companyId, bool action);
    Task<List<DistSegmentations>> GetDistributorSegmentation(long companyId, bool includeDeactivate = true);
    Task<RepositoryResponse> ActivateDeactivateDistributorSegmentation(long id, long companyId, bool action);
    Task<RepositoryResponse> CreateDistributorChannel(DistributorChannelsInput distributorChannel, long companyId);
    Task<RepositoryResponse> UpdateDistributorChannel(DistributorChannelsInput distributorChannel, long companyId);
    Task<RepositoryResponse> CreateDistributorSegmentation(DistributorSegmentationInput distributorChannel, long companyId);
    Task<RepositoryResponse> UpdateDistributorSegmentation(DistributorSegmentationInput distributorChannel, long companyId);
    Task<RepositoryResponse> RegisterDistributor(long employeeId, bool action, long companyId, string redirectUrl, bool twoFactorEnabled, string userName);
    Task<bool> DoesContactExist(long companyId, string contactNo, long distributorId);
}
