﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class MustSellProductInput
{
    public List<long> channelIds { get; set; }
    public string Description { get; set; }
    public DateTime EndDate { get; set; }
    public OutletFocusType FocusType { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public List<long> OutletSegmentationAttributeIds { get; set; }
    public List<long> PositionCodeIds { get; set; }
    public List<long> ShopTypeIds { get; set; }
    public List<long> CustomTags { get; set; }

    // not required for create and update.Used in Get By Id.
    public List<MustSellSKU> SKUs { get; set; }

    public DateTime StartDate { get; set; }
    public List<long> PrimaryCategoryIds { get; set; }
    public List<long> SecondaryCategoryIds { get; set; }
    public List<long> SkuIds { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
}

public class MustSellSKU
{
    public bool IsMustSell { get; set; }
    public long MaxBuyingQuantity { get; set; }
    public long MinBuyingQuantity { get; set; }
    public long? PrimaryCategoryId { get; set; }
    public string PrimaryCategoryName { get; set; }
    public long? SecondaryCategoryId { get; set; }
    public string SecondaryCategoryName { get; set; }
    public long SKUId { get; set; }
    public string SKUName { get; set; }
    public bool StockNotRequired { get; set; }
    public bool ReasonNotRequired { get; set; }
}
