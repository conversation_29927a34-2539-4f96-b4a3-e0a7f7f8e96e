﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ISurveyRepository
{
    Task<RepositoryResponse> AttachZonesToSurvey(long companyId, long formId, List<long> zoneIds);

    Task<RepositoryResponse> AttachSurveyConstraints(long companyId, long formId, SurveyConstraintsDb surveyConstraints);

    Task<RepositoryResponse> CreateSurveyForm(SurveyInput formInput, long companyId, long createdBy);

    Task<RepositoryResponse> EnableDisableSurveyForm(long formId, long companyId, bool action);

    Task<List<EntityMin>> GetAllInStoreSurveysMin(long companyId);

    Task<List<QuestionDTO>> GetAllSurveyQuestions(long surveyId);

    Task<SurveyInput> GetSurveyById(long companyId, long formId);

    Task<List<SurveyFormView>> GetSurveyForms(long companyId, bool showDisable);

    Task<List<long>> GetZonesIdsAttachedToSurvey(long formId, long companyId, bool includeDeactivate);

    Task<SurveyConstraints> GetSurveyConstraints(long formId, long companyId, bool includeDeactivate);

    Task<RepositoryResponse> UpdateSurveyForm(SurveyInput formInput, long companyId, long createdBy);
    Task<List<SurveyFormView>> GetSurveyFormsOfSurveyType(long companyId, SurveyType surveyType, bool showDisable = false);
}
