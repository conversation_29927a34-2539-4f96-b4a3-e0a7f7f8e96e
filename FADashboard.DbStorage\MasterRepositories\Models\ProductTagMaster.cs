﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ProductTagMaster : IAuditedEntity, ICompanyEntity, IDeactivatable
{
    public long Id { get; set; }
    public long CompanyId { get; set; }

    [StringLength(64)] public string Name { get; set; }

    public RecommendationTag RecommendationTag { get; set; }

    [StringLength(16)] public string ERPId { get; set; }

    public DateTime CreatedAt { get; set; }

    [StringLength(32)] public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public bool IsDeactive { get; set; }
}
