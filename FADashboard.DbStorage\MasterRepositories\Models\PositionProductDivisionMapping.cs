﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class PositionProductDivisionMapping : IAuditedEntity, IDeactivatable, ICompanyEntity
{
    public long Id { get; set; }

    public long CompanyId { get; set; }
    public long PositionId { get; set; }
    public long ProductDivisionId { get; set; }

    public DateTime CreatedAt { get; set; }

    [StringLength(32)]
    public string CreationContext { get; set; }

    public bool IsDeactive { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public virtual ProductDivision ProductDivision { get; set; }

    public virtual PositionCode Position { get; set; }
    public virtual Company Company { get; set; }
}
