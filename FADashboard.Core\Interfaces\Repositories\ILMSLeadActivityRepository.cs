﻿
using FADashboard.Core.Models;
using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSLeadActivityRepository
    {
        Task AddBulkAsync(List<LMSLeadActivityDTO> activities);
        Task<IEnumerable<LMSLeadActivityTimelineDto>> GetLeadTimelineAsync(long leadId, LMSLeadActivityQueryParameters queryParameters);
        Task<PagedResult<LMSLeadNoteDto>> GetLeadNotesAsync(long leadId, LMSLeadNoteQueryParameters queryParameters);
        Task<PagedResult<LMSLeadCallDto>> GetLeadCallsAsync(long leadId, LMSLeadCallQueryParameters queryParameters);
        Task<PagedResult<LMSLeadTaskDto>> GetLeadTasksAsync(long leadId, LMSLeadTaskQueryParameters queryParameters);
        Task<PagedResult<LMSLeadMeetingDto>> GetLeadMeetingsAsync(long leadId, LMSLeadMeetingQueryParameters queryParameters);
        Task<PagedResult<LMSLeadMasterTaskDto>> GetLeadMasterTasksAsync(long leadId, LMSLeadTaskQueryParameters queryParameters);
        Task<PagedResult<LMSLeadMasterMeetingDto>> GetLeadMasterMeetingsAsync(long leadId, LMSLeadMeetingQueryParameters queryParameters);
        Task<PagedResult<LMSLeadMasterCallDto>> GetLeadMasterCallsAsync(long leadId, LMSLeadCallQueryParameters queryParameters);
        Task<LMSLeadTaskDto> CreateLeadTaskAsync(CreateLMSLeadTaskDto taskDto, long companyId, long createdBy);
        Task<LMSLeadMeetingDto> CreateLeadMeetingAsync(CreateLMSLeadMeetingDto meetingDto, long companyId, long createdBy);
        Task<LMSLeadCallDto> CreateLeadCallAsync(CreateLMSLeadCallDto callDto, long companyId, long createdBy);
        Task<LMSLeadNoteDto> CreateLeadNoteAsync(CreateLMSLeadNoteDto noteDto, long companyId, long createdBy);
        Task<LMSLeadTaskDto> UpdateLeadTaskAsync(long taskId, UpdateLMSLeadTaskDto taskDto, long companyId, long updatedBy);
        Task<LMSLeadTaskDto> GetActivityByIdAsync(long activityId);
        Task<LMSLeadTaskDto> GetTaskByIdAsync(long taskId);
        Task<LMSLeadMeetingDto> UpdateLeadMeetingAsync(long meetingId, UpdateLMSLeadMeetingDto meetingDto, long companyId, long updatedBy);
        Task<LMSLeadMeetingDto> GetMeetingByIdAsync(long meetingId);
        Task<LMSLeadCallDto> UpdateLeadCallAsync(long callId, UpdateLMSLeadCallDto callDto, long companyId, long updatedBy);
        Task<LMSLeadTaskDto> GetLeadTaskDetailsAsync(long taskId);
        Task<LMSLeadMeetingDto> GetLeadMeetingDetailsAsync(long meetingId);
        Task<LMSLeadCallDto> GetLeadCallDetailsAsync(long callId);
        Task<LMSLeadCallDto> GetCallByIdAsync(long callId);
        Task DeleteLeadActivityAsync(long activityId, long companyId, long deletedBy);
    }
}
