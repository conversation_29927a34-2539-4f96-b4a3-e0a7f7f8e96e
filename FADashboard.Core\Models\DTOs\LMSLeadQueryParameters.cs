﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadQueryParameters
    {
        private const int MaxPageSize = 100;
        private int _pageSize = 10;

        // Filtering
        public string SearchTerm { get; set; }
        public long? AssignedTo { get; set; }
        public LMSPriority? Priority { get; set; }
        public DateTime? CreatedAtStart { get; set; }
        public DateTime? CreatedAtEnd { get; set; }
        public LMSLeadStatus? Status { get; set; }
        public long? LeadStageId { get; set; }
        public string StageCategory { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public long? LeadTemplateId { get; set; }

        // Pagination
        public int PageNumber { get; set; } = 1;

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        // Sorting
        public string SortBy { get; set; } = "CreatedAt";
        public string Order { get; set; } = "desc";
    }
}
