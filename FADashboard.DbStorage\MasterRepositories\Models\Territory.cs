﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Territory : IDeletable, IAuditedEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public string ErpId { set; get; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public virtual ICollection<LocationBeat> LocationBeats { get; set; }
    public string Name { get; set; }

    [ForeignKey("TheRegion")] public long? RegionId { set; get; }

    public Regions TheRegion { set; get; }
}
