using System;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSGlobalLeadStageDto
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        public string StageName { get; set; }

        public int Order { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; }

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
