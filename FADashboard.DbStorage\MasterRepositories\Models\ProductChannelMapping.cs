﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ProductChannelMappings")]
public class ProductChannelMapping : ICreatedTransactionEntity
{
    public OutletChannel ChannelEnum { get; set; }
    public long ChannelId { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public long Id { get; set; }
    public virtual CompanyProduct Product { get; set; }
    public long ProductId { get; set; }
}
