﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class AssetService(
    ICurrentUser currentUser,
    IOutletRepository outletRepository,
    IAssetRepository assetRepository,
    IAttendanceRepository attendanceRepository,
    IAssetDefinitionsRepository assetDefinitionsRepository,
    IAssetRuleDefinitionsRepository assetRuleDefinitionsRepository,
    IAssetTypesRepository assetTypesRepository,
    ISurveyRepository surveyRepository,
    ISurveyTransactionRepository surveyTransactionRepository) : RepositoryResponse
{
    /// <summary>
    /// Gets the audit survey responses (questions and answers) for the given asset ID.
    /// </summary>
    public async Task<List<SurveyResponseOutput>> GetAuditSurveyResponsesById(long assetId)
    {
        var companyId = currentUser.CompanyId;
        var surveyResponseId = await assetRepository.GetSurveyResponseIdByAssetId(assetId, companyId);
        if (surveyResponseId == null)
            return new List<SurveyResponseOutput>();

        var surveyResponse = await surveyTransactionRepository.GetSurveyResponsesById(surveyResponseId.Value, companyId);
        if (surveyResponse == null || surveyResponse.SurveyResponseItems == null)
            return new List<SurveyResponseOutput>();
        var questionIds = surveyResponse.SurveyResponseItems.Select(x => x.QuestionId).Distinct().ToList();
        var formId = surveyResponse.FormId;
        var questions = formId.HasValue ? await surveyRepository.GetAllSurveyQuestions(formId.Value) : new List<FADashboard.Core.Models.DTOs.QuestionDTO>();
        var questionDictionary = questions.ToDictionary(q => q.QuestionId, q => q);
        var result = surveyResponse.SurveyResponseItems.Select(item => new SurveyResponseOutput
        {
            Date = surveyResponse.CreatedAt.ToString("yyyy-MM-dd"),
            Question = questionDictionary.TryGetValue(item.QuestionId, out var qDto) ? qDto.Title : item.QuestionId.ToString(),
            Answer = !string.IsNullOrEmpty(item.Text) ? item.Text :
                     (!string.IsNullOrEmpty(item.Options) && qDto != null && qDto.Choices != null ?
                        qDto.Choices.FirstOrDefault(choice => choice.Id.ToString() == item.Options)?.Name :
                        (item.Image ?? string.Empty)),
            QuestionType = questionDictionary.TryGetValue(item.QuestionId, out var qDto2) ? qDto2.QuestionType : SurveyQuestionType.Text
        }).ToList();
        return result;
    }

    public async Task<RepositoryResponse> UpdateReferenceNumber(long AssetOutletId, string ReferenceNumber) => await assetRepository.UpdateReferenceNumber(currentUser.CompanyId, AssetOutletId, ReferenceNumber.Trim());

    public async Task<RepositoryResponse> DeleteAssetMapping(long AssetOutletId) => await assetRepository.DeleteAssetMapping(currentUser.CompanyId, AssetOutletId);

    public async Task<List<AttendanceAsset>> GetAssetInfo(long beatId, DateTime StartDtae, DateTime EndDate)
    {
        var visitData = await attendanceRepository.GetDetails(currentUser.CompanyId, StartDtae, EndDate, beatId);
        var OutletList = visitData.Select(a => a.OutletId).ToList();
        var outletinfo = await outletRepository.GetLocations(currentUser.CompanyId, OutletList, true); // asana 11 dec 2023. reason :getting key not found. link: https://app.asana.com/0/1203071525101825/1206126676110926/f,
        var outletAssetId = new List<long>();
        var noAuditAssetInfo = new List<long>();
        foreach (var item in visitData)
        {
            if (item.AssetAuditingData is { Count: > 0 })
            {
                var Idlist = item.AssetAuditingData.Select(a => a.AssetMappingId).ToList();
                outletAssetId.AddRange(Idlist);
            }
            else
            {
                var outlist = item.OutletId;
                noAuditAssetInfo.Add(outlist);
            }
        }

        outletAssetId = outletAssetId.Distinct().ToList();
        var assetInfo = await assetRepository.GetAssetData(currentUser.CompanyId, outletAssetId);
        var Outletdata = await assetRepository.GetAssetOutletData(currentUser.CompanyId, noAuditAssetInfo);
        var data = new List<AttendanceAsset>();
        foreach (var item in visitData)
        {
            if (item.AssetAuditingData == null)
            {
                if (Outletdata.TryGetValue(item.OutletId, out var value))
                {
                    foreach (var assetdata in value) // asset just mapped to outlet
                    {
                        var att = new AttendanceAsset
                        {
                            OutletName = outletinfo[item.OutletId].ShopName,
                            OutletErpId = outletinfo[item.OutletId].ErpId,
                            AssetReferenceNumber = assetdata.AssetReferenceNumber,
                            ValueEfficiency = item.ValueEfficiency,
                            VolumeEfficiency = item.VolumeEfficiency,
                            AssetName = assetdata.AssetName,
                            AssetType = assetdata.AssetType,
                            AssetOutletId = assetdata.AssetOutletId,
                            ImageId = null,
                            Remark = "Asset Audit not done",
                            AssetAuditingStatus = "Not-Audited",
                            AttendanceDate = item.AttendanceDate,
                            Reason = null,
                            ConfidenceScore = null,
                            AssetHealth = null,
                            AssetHealthRemarks = null,
                            FieldUserName = item.LastVisitedFieldUser
                        };
                        data.Add(att);
                    }
                }
                else // no asset mapped
                {
                    var att = new AttendanceAsset
                    {
                        OutletName = outletinfo[item.OutletId].ShopName,
                        OutletErpId = outletinfo[item.OutletId].ErpId,
                        AssetReferenceNumber = null,
                        ValueEfficiency = item.ValueEfficiency,
                        VolumeEfficiency = item.VolumeEfficiency,
                        AssetName = null,
                        AssetType = null,
                        AssetOutletId = null,
                        ImageId = null,
                        Remark = "No asset mapped",
                        Reason = null,
                        AssetAuditingStatus = "Not-Audited",
                        AttendanceDate = item.AttendanceDate,
                        ConfidenceScore = null,
                        AssetHealth = null,
                        AssetHealthRemarks = null,
                        FieldUserName = item.LastVisitedFieldUser
                    };
                    data.Add(att);
                }
            }
            else // asset mapped to outlet and audited
            {
                foreach (var dat in item.AssetAuditingData)
                {
                    var att = new AttendanceAsset
                    {
                        OutletName = outletinfo[item.OutletId].ShopName,
                        OutletErpId = outletinfo[item.OutletId].ErpId,
                        AssetReferenceNumber = assetInfo.TryGetValue(dat.AssetMappingId, out var value) ? value.AssetReferenceNumber : string.Empty,
                        ValueEfficiency = item.ValueEfficiency,
                        VolumeEfficiency = item.VolumeEfficiency,
                        AssetName = assetInfo.TryGetValue(dat.AssetMappingId, out var value1) ? value1.AssetName : string.Empty,
                        AssetType = assetInfo.TryGetValue(dat.AssetMappingId, out var value2) ? value2.AssetType : string.Empty,
                        AssetOutletId = assetInfo.TryGetValue(dat.AssetMappingId, out var value3) ? value3.AssetOutletId : null,
                        ImageId = dat.ImageId,
                        Remark = dat.Remark,
                        Reason = dat.Reason,
                        AssetAuditingStatus = "Audited",
                        AttendanceDate = item.AttendanceDate,
                        ConfidenceScore = dat.ConfidenceScore.ToString(),
                        AssetHealth = dat.AssetHealth.ToString(),
                        AssetHealthRemarks = dat.AssetHealthRemarks,
                        FieldUserName = dat.AuditedBy
                    };
                    data.Add(att);
                }
            }
        }

        return data;
    }

    public async Task<List<AssetDefinitionsList>> GetAllAssets(bool includeDeactive = false) => await assetDefinitionsRepository.GetAllAssets(currentUser.CompanyId, includeDeactive);

    public async Task<AssetDefinitionsList> GetAssetById(long id) => await assetDefinitionsRepository.GetAssetById(currentUser.CompanyId, id);

    public async Task<RepositoryResponse> DeactivateAsset(long id) => await assetDefinitionsRepository.DeactivateAsset(currentUser.CompanyId, id);

    public async Task<RepositoryResponse> CreateUpdateAsset(AssetDefinitionsList assetDefinitionsInput)
    {
        if (assetDefinitionsInput.Id == 0)
        {
            return await assetDefinitionsRepository.CreateAsset(currentUser.CompanyId, assetDefinitionsInput);
        }

        return await assetDefinitionsRepository.UpdateAsset(currentUser.CompanyId, assetDefinitionsInput.Id, assetDefinitionsInput);
    }

    public async Task<IEnumerable<AssetRuleDefinitionsView>> GetAssetRule() => await assetRuleDefinitionsRepository.GetAssetRule(currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateAssetRule(AssetRuleDefinitionsView assetRule) => await assetRuleDefinitionsRepository.CreateAssetRule(currentUser.CompanyId, assetRule);

    public async Task<List<AssetDefinitionsList>> GetActiveDisplayTypes() => await assetDefinitionsRepository.GetActiveDisplayTypes(currentUser.CompanyId);

    public async Task<List<AssetDefinitionsList>> GetActiveDisplayTypesForEntities(List<long> entityIds) => await assetDefinitionsRepository.GetActiveDisplayTypesForEntities(currentUser.CompanyId, entityIds);
    public async Task<IEnumerable<AssetTypeList>> GetAllAssetTypes(bool includeDeactive) => await assetTypesRepository.GetAllAssetTypes(currentUser.CompanyId, includeDeactive);
    public async Task<RepositoryResponse> CreateUpdateAssetType(AssetTypeList assetTypeInput)
    {
        if (assetTypeInput.Id == 0)
        {
            return await assetTypesRepository.CreateAssetType(currentUser.CompanyId, assetTypeInput);
        }

        return await assetTypesRepository.UpdateAssetType(currentUser.CompanyId, assetTypeInput);
    }
    public async Task<RepositoryResponse> DeactivateAssetType(long id) => await assetTypesRepository.DeactivateAssetType(currentUser.CompanyId, id);
    public async Task<AssetTypeList> GetAssetTypeById(long id) => await assetTypesRepository.GetAssetTypeById(id, currentUser.CompanyId);
}
