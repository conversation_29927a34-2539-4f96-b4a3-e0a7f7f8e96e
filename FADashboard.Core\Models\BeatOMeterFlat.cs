﻿using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.Core.Models;

public class BeatOMeterFlat
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public int ActiveMonthLimit { get; set; }
    public int ToBeDormantMonthLimit { get; set; }
    public int DormantMonthLimit { get; set; }
    public int NoOrderLimit { get; set; }
    public int NeverVisitedLimit { get; set; }
    public int NewActivationMonthLimit { get; set; }
    public bool Deleted { get; set; }
}

public class BeatOMeterById
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public string Description { get; set; }
    public int ActiveMonthLimit { get; set; }
    public int ToBeDormantMonthLimit { get; set; }
    public int DormantMonthLimit { get; set; }
    public int NoOrderLimit { get; set; }
    public int NeverVisitedLimit { get; set; }
    public int NewActivationMonthLimit { get; set; }
    public List<long> BeatOMeterRuleForSegmentationIds { get; set; }
    public OutletFocusType FocusType { get; set; }
    public RuleOn RuleOn { get; set; }
    public PositionCodeLevel? PositionLevel { get; set; }
    public long? PositionId { get; set; }
}
public class BeatOMeterKpiBasedFlat
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public string Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Frequency { get; set; }
    public bool IsActive { get; set; }
}
public class KpiBasedBeatOMeterById
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public string Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Frequency { get; set; }
    public List<PropertyData> OutletConstraints { get; set; }
    public PropertyData GeographyConstraints { get; set; }
    public bool IsActive { get; set; }
    public List<TagDetail> TagDetails { get; set; }
    public List<long?> ProductDivisionIds { get; set; }
}
public class PropertyData
{
    public string Property { get; set; }
    public List<string> PropertyValue { get; set; }
}
public class TagDetail
{
    public string TagName { get; set; }
    public string TagDescription { get; set; }
    public string TagColor { get; set; }
    public string TagIcon { get; set; }
    public int Sequence { get; set; }
    public bool VisitType { get; set; }
    public string TagLogic { get; set; }
    public TagConstraint TagConstraintDetails { get; set; }
}
public class TagConstraint
{
    public List<MetricDetail> Conditions { get; set; }
    public string Operation { get; set; }
}
public class MetricDetail
{
    public long? MetricId { get; set; }
    public OutletMetricFrequency? MetricFrequency { get; set; }
    public int? NumberOfDays { get; set; }
    public List<Parameter>? ParameterValue { get; set; }
    public int? Condition { get; set; }
    public string ConstraintValue { get; set; }
    public string Metric { get; set; }
    public bool? IsVisited { get; set; }
    public int? ConstraintDays { get; set; }
}
public class Parameter
{
    public string ParameterName { get; set; }
    public string Reference { get; set; }
    public List<long> ParameterValues { get; set; }
}
public class VisitTypeDetail
{
    public string Metric { get; set; }
    public bool? IsVisited { get; set; }
    public int? ConstraintDays { get; set; }
}
