﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class TargetMaster : IAuditedEntity
{
    [Key] public long Id { get; set; }
    public string TargetIdentifier { get; set; }
    public string TargetName { get; set; }
    public Heirarchy Hierarchy1 { get; set; }
    public Heirarchy? Hierarchy2 { get; set; }
    public Heirarchy? Hierarchy3 { get; set; }
    public string TargetOn { get; set; }
    public string Hierarchy1Query { get; set; }
    public string Hierarchy2Query { get; set; }
    public string Hierarchy3Query { get; set; }
    public string AchievementQuery { get; set; }
    public AchievementDb? AchievementDb { get; set; }
    public AchievementReturnType? AchievementReturnType { get; set; }
    public AppScreen? AppScreen { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public VisualizationType? VisualizationType { get; set; }
    public string TargetFilters { get; set; }
    public bool? IsForApp { get; set; }
    public TargetFilters? DisplayAxis { get; set; }
    public TargetType? Target_type { get; set; }
    public string AnalyticalQuery { get; set; }
    public string ReportDataQuery { get; set; }
    public int? ReportDataDb { get; set; }
}

public class CompanyTargetSubscription : IAuditedEntity
{
    public CompanyTargetSubscription()
    {
        IsDeactive = false;
    }

    [Key] public long Id { get; set; }
    public string Name { get; set; }
    public long CompanyId { get; set; }
    public virtual TargetMaster TargetMaster { get; set; }
    public long TargetMasterId { get; set; }
    public bool IsPremium { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string TargetDescription { get; set; }
    public bool IsForLandingScreen { get; set; }
    public bool IsTargetNeedToBreakDown { get; set; }
    public long? ParentId { get; set; }
    public bool ShowInManagerApp { get; set; }
    public TargetFilters? DisplayHierarchy { get; set; }
    public TargetFilters? FilterHierarchy { get; set; }
    public AchievementReturnType? AchievementValueType { get; set; }
    public FlexibleTargetFrequency? TargetFrequency { get; set; }
    public TargetSplitCriteria? TargetSplitCriteria { get; set; }
    public TargetSplitRatio? TargetSplitRatio { get; set; }
    public AchievementReturnType? AchievementReturnType { get; set; }
    public AppScreen? AppScreen { get; set; }
    public VisualizationType? VisualizationType { get; set; }
    public string TargetFilters { get; set; }
    public TargetFilters? DisplayAxis { get; set; }
    public bool? IsForApp { get; set; }
}
