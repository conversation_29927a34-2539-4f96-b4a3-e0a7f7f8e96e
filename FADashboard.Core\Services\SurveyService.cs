﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class SurveyService(
    ISurveyRepository surveyRepository,
    ICurrentUser currentUser,
    ISurveyTransactionRepository surveyTransactionRepository,
    IZoneRepository zoneRepository,
    IShopTypeRepository shopTypeRepository,
    IOutletMasterRepository outletMasterRepository) : RepositoryResponse
{
    public async Task<RepositoryResponse> AttachZonesToSurvey(long formId, List<long> zoneIds)
    {
        try
        {
            return await surveyRepository.AttachZonesToSurvey(currentUser.CompanyId, formId, zoneIds);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = formId, ExceptionMessage = ex.Message, Message = "An Error Occurred while trying to save new Mappings", IsSuccess = false };
        }
    }
    private async Task<List<string>> GetChannelEnumsStringFromIds(List<long> channelIds)
    {
        if (channelIds.Count != 0)
        {
            var channelDict = (await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => ((int)s.Enum).ToString());
            return channelIds.Select(cId => channelDict.GetValueOrDefault(cId)).Where(p => p != null).ToList();
        }

        return [];
    }

    private async Task<List<string>> GetSegmentationEnumsStringFromIds(List<long> segmentationIds)
    {
        if (segmentationIds.Count != 0)
        {
            var segDict = (await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.Segmentation.ToString());
            return segmentationIds.Select(segId => segDict.GetValueOrDefault(segId)).Where(p => p != null).ToList();
        }

        return [];
    }

    public async Task<RepositoryResponse> AttachSurveyConstraints(long formId, SurveyConstraints surveyConstraints)
    {
        try
        {
            surveyConstraints.Channels = surveyConstraints.RequiredChannelsList != null ? await GetChannelEnumsStringFromIds(surveyConstraints.RequiredChannelsList) : [];
            surveyConstraints.Segmentations = surveyConstraints.RequiredSegmentationsList != null ? await GetSegmentationEnumsStringFromIds(surveyConstraints.RequiredSegmentationsList) : [];
            var surveyDetails = new SurveyConstraintsDb
            {
                CustomTags = surveyConstraints.CustomTags,
                ShopTypes = surveyConstraints.ShopTypes,
                Channels = surveyConstraints.Channels,
                Segmentations = surveyConstraints.Segmentations,
                IsFocused = surveyConstraints.IsFocused,
                Cohorts = surveyConstraints.Cohorts
            };
            return await surveyRepository.AttachSurveyConstraints(currentUser.CompanyId, formId, surveyDetails);
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = formId, ExceptionMessage = ex.Message, Message = "An Error Occurred while trying to save new Mappings", IsSuccess = false };
        }
    }

    public async Task<RepositoryResponse> CreateUpdateSurveyForm(SurveyInput formInput) =>
        formInput.Id == 0 ? await surveyRepository.CreateSurveyForm(formInput, currentUser.CompanyId, currentUser.LocalId) : await surveyRepository.UpdateSurveyForm(formInput, currentUser.CompanyId, currentUser.LocalId);

    public async Task<RepositoryResponse> EnableDisableSurveyForm(long formId, bool action) => await surveyRepository.EnableDisableSurveyForm(formId, currentUser.CompanyId, action);

    public async Task<List<EntityMin>> GetAllInStoreSurveysMin()
    {
        var data = await surveyRepository.GetAllInStoreSurveysMin(currentUser.CompanyId);
        return data;
    }

    public async Task<SurveyInput> GetSurveyById(long formId)
    {
        var survey = await surveyRepository.GetSurveyById(currentUser.CompanyId, formId);
        return survey;
    }

    public async Task<List<SurveyFormView>> GetSurveyForms(bool showDisable = false)
    {
        var forms = await surveyRepository.GetSurveyForms(currentUser.CompanyId, showDisable);
        var formIds = forms.Select(f => f.Id).ToList();

        var responsesCountDict = await surveyTransactionRepository.GetSurveyResponseCountDict(formIds, currentUser.CompanyId);

        return forms.Select(s => new SurveyFormView
        {
            CreatedOn = s.CreatedOn,
            Deleted = s.Deleted,
            Disable = s.Disable,
            Id = s.Id,
            ResponseCount = responsesCountDict.GetValueOrDefault(s.Id, 0),
            SurveyType = s.SurveyType,
            Title = s.Title,
            IsGlobalAdmin = currentUser.UserRole == PortalUserRole.GlobalAdmin,
            CanUserActivateDeactivate = currentUser.UserRole is PortalUserRole.GlobalAdmin
                or PortalUserRole.CompanyAdmin
                or PortalUserRole.AccountManager,
            CanUserEditAttachZone = currentUser.UserRole is PortalUserRole.GlobalAdmin
                or PortalUserRole.CompanyAdmin
                or PortalUserRole.AccountManager
                or PortalUserRole.ChannelPartner
                or PortalUserRole.CompanyExecutive
                or PortalUserRole.RegionalAdmin
        }).ToList();
    }

    public async Task<List<EntityMin>> GetZonesAttachedToSurvey(long formId)
    {
        var zoneIds = await surveyRepository.GetZonesIdsAttachedToSurvey(formId, currentUser.CompanyId, false);
        var zonesDict = (await zoneRepository.GetZonesMinByIds(currentUser.CompanyId, zoneIds)).ToDictionary(p => p.Id, p => p.Name);

        return zoneIds.Select(id => new EntityMin { Id = id, Name = zonesDict.GetValueOrDefault(id, "") }).ToList();
    }

    public async Task<SurveyConstraints> GetSurveyConstraints(long formId)
    {
        var zoneIds = await surveyRepository.GetSurveyConstraints(formId, currentUser.CompanyId, false);

        return zoneIds;
    }
    public async Task<List<SurveyFormView>> GetSurveyFormsOfSurveyType(SurveyType surveyType, bool showDisable = false)
    {
        return await surveyRepository.GetSurveyFormsOfSurveyType(currentUser.CompanyId, surveyType, showDisable);
    }
}
