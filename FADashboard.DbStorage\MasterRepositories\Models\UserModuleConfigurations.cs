﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class UserModuleConfiguration : IAuditedEntity, ICompanyEntity
{
    public long Id { get; set; }

    public UserType UserType { get; set; }
    public UserBaseModule Module { get; set; }
    public bool Visible { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [StringLength(32)]
    public string CreationContext { get; set; }

    public long CompanyId { get; set; }
}
