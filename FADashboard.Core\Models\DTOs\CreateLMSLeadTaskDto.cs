﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class CreateLMSLeadTaskDto
    {
        [Required]
        public string Title { get; set; }

        [Required]
        public long AssignedTo { get; set; }
        [Required]
        public long PositionCode { get; set; }

        [Required]
        public long LeadId { get; set; }

        [Required]
        public LMSTaskStatus Status { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        [Required]
        public LMSTaskPriority Priority { get; set; }

        public string Description { get; set; }

        public List<string> Attachment { get; set; }
    }
}
