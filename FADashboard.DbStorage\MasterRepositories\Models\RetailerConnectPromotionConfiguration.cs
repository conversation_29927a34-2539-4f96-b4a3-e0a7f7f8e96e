﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class RetailerConnectPromotionConfiguration
{
    [Key]
    public long Id { get; set; }

    [Required]
    public long CompanyId { get; set; }

    [Required]
    public bool IsHeaderVisible { get; set; } = false;

    public string? HeaderText { get; set; }

    [Required]
    public bool IsFooterVisible { get; set; } = false;

    public string? FooterText { get; set; }

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; set; }

    [Required]
    public string CreationContext { get; set; } = string.Empty;
}

