﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Library.CommonHelpers;

namespace FADashboard.Core.Services;

public class ReportService(
    IReportRepository reportRepository,
    CustomReportService customReportService,
    PositionCodeService positionCodeService,
    NomenclatureService nomenclatureService,
    IEmployeeRepository employeeRepository,
    ICurrentUser currentUser,
    ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    private static bool userIdIsOldId(PortalUserRole userRole)
    {
        var managerRoles = new List<PortalUserRole>
        {
            PortalUserRole.AreaSalesManager,
            PortalUserRole.RegionalSalesManager,
            PortalUserRole.ZonalSalesManager,
            PortalUserRole.NationalSalesManager,
            PortalUserRole.GlobalSalesManager
        };
        return managerRoles.Contains(userRole);
    }

    public async Task<List<Report>> GetAllReports()
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var rBACSetting = companySettings.CompanyUsesRoleBasedAccessRightsModule;
        //If Role Based Access Setting is not enabled then show all reports
        if (!rBACSetting)
        {
            var reportList = await reportRepository.GetAllReports(currentUser.CompanyId, currentUser.UserRole);
            return reportList.Select(x => new Report
            {
                Name = x.Name,
                Value = x.ReportCategory.ToString(),
                Description = x.Description,
                CanEmail = x.CanEmail,
                Link = x.Link,
                EncryptionKey = x.EncryptionKey,
                Header = x.ReportSectionName,
                HeaderEnum = (int)x.ReportSectionEnum,
                ReportType = (int)x.ReportType
            }).ToList();
        }
        //If Role Based Access Setting is enabled then show only subscribed reports
        else
        {
            var roleIds = currentUser.RoleIds.ToLowerInvariant().Split(',').Select(long.Parse).ToList();
            var reportList = await reportRepository.GetAllRoleWiseSubscribedReports(currentUser.CompanyId, roleIds);
            return reportList.Select(x => new Report
            {
                Name = x.Name,
                Value = x.ReportCategory.ToString(),
                Description = x.Description,
                CanEmail = x.CanEmail,
                Link = x.Link,
                EncryptionKey = x.EncryptionKey,
                Header = x.ReportSectionName,
                HeaderEnum = (int)x.ReportSectionEnum,
                ReportType = (int)x.ReportType
            }).DistinctBy(a => a.ReportType).ToList();
        }
    }

    public async Task<ReportDetails> GetReportDetails(Guid subscriptionKey, long companyId, long userId, PortalUserRole userRole)
    {
        var subscription = await reportRepository.GetReportSubscription(companyId, subscriptionKey);
        var customReport = await customReportService.GetCustomReport(subscription.ReportId, companyId, userId, userRole);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var highestRole = companySettings.GetCompanyHighestHierarchy;
        var highestLevel = companySettings.GetCompanyHighestPositionLevel;
        var userPositions = await positionCodeService.GetUserPositionCodes(userId);
        var userHighestPosition = userPositions.Values.OrderBy(l => l).FirstOrDefault();
        var companyUsesPositionCode = companySettings.UsesPositionCodes;
        customReport.CustomReportItems = customReport.CustomReportItems.Where(c => companyUsesPositionCode || !c.ReportColumnName.Contains("position", StringComparison.OrdinalIgnoreCase)).ToList();
        var nomenclatures = (await nomenclatureService.GetCompanyNomenclatureDict()).ToDictionary(p => p.Key, p => string.IsNullOrEmpty(p.Value.DisplayName) ? p.Value.Name : p.Value.DisplayName);
        var newId = userId;
        if (userIdIsOldId(userRole))
            newId = await employeeRepository.GetNewId(companyId, userRole, userId); //Needed for showing managers under User Only

        var res = new ReportDetails
        {
            CanCustomize = customReport != null,
            ReportId = subscription.ReportId,
            CustomReportId = customReport.Id,
            Name = subscription.Name,
            maxDaysAllowed = customReport.MaxDaysForDownload,
            MaxColumnsSelectable = 30, //TODO: Set this based on report
            UserDetails = new User
            {
                Id = userId,
                newId = newId,
                HighestRole = highestRole,
                UserRole = userRole,
                HighestPosition = highestLevel,
                UserHighestPosition = userHighestPosition,
                PositionIds = userPositions.Where(p => p.Value == userHighestPosition).Select(p => p.Key).ToList(),
                UsesPositionCodes = companyUsesPositionCode, //TODO: Change it as per Report
            },
            DateFilterType = customReport.DateFiltersType,
            DatePresets = [DateRangePreset.MTD.GetDisplayName(), DateRangePreset.Last7Days.GetDisplayName()], //TODO: Generate it dynamically as per Report
            Columns = customReport.CustomReportItems.Select(c => new ColumnMin
            {
                Id = c.Id,
                CategoryDisplayName = c.CategoryName.GetDisplayName(),
                CategoryName = c.CategoryName.ToString(),
                DisplayName = string.Join(" ", c.ReportColumnName.Split(' ').Select(s => nomenclatures.GetValueOrDefault(s, s))),
                ReportColumnName = c.ReportColumnName,
                IsNewColumn = c.IsNewColumn,
                IsSelected = c.IsSelected,
                IsStandardColumn = c.IsStandardColumn,
            }).ToList(),
            OtherPreferences = customReport.ReportPreferences.Where(p => companyUsesPositionCode || !p.DisplayName.Contains("position", StringComparison.OrdinalIgnoreCase)).ToList(),
        };
        ReportDetails.GetFilters(res, customReport.FilterType);
        return res;
    }

    public static Task<ReportFilterDetails> GetReportFilters(ReportSubscription subscription, Guid subscriptionKey)
    {
        var reportFilters = new ReportFilterDetails(subscription, subscriptionKey);
        return Task.FromResult(reportFilters);
    }

    public async Task<ReportSubscription> GetReportSubscription(Guid subscriptionKey)
    {
        var subs = await reportRepository.GetReportSubscription(currentUser.CompanyId, subscriptionKey);
        return subs;
    }

    public async Task<List<SubscribedReports>> GetSubscribedReports()
    {
        var subscribedReports = await reportRepository.GetSubscribedReports(currentUser.CompanyId);
        return subscribedReports;
    }
}
