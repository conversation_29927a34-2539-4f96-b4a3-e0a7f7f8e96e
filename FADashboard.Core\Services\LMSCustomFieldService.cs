﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSCustomFieldService(ILMSCustomFieldRepository repository) : ILMSCustomFieldService
    {
        public async Task<LMSCustomFieldDto> CreateAsync(LMSCustomFieldDto customFieldDto, long createdByUserId)
        {
            customFieldDto.CreatedBy = createdByUserId;
            customFieldDto.CreatedAt = DateTime.UtcNow;
            return await repository.AddAsync(customFieldDto);
        }

        public async Task AddRangeAsync(List<LMSCustomFieldDto> customFields, long createdByUserId)
        {
            await repository.AddRangeAsync(customFields);
        }

        public async Task DeleteAsync(long id)
        {
            await repository.DeleteAsync(id);
        }

        public async Task DeleteByEntityAsync(int entityType, long entityId)
        {
            await repository.DeleteByEntityAsync(entityType, entityId);
        }

        public async Task<List<LMSCustomFieldDto>> GetByEntityAsync(int entityType, long entityId)
        {
            return await repository.GetByEntityAsync(entityType, entityId);
        }

        public async Task<LMSCustomFieldDto> GetByIdAsync(long id)
        {
            return await repository.GetByIdAsync(id);
        }

        public async Task UpdateAsync(long id, LMSCustomFieldDto customFieldDto, long updatedByUserId)
        {
            var existingField = await GetByIdAsync(id);
            if (existingField == null)
            {
                throw new Exception("Custom field not found."); // Replace with custom exception
            }

            existingField.Name = customFieldDto.Name;
            existingField.FieldType = customFieldDto.FieldType;
            existingField.Placeholder = customFieldDto.Placeholder;
            existingField.Description = customFieldDto.Description;
            existingField.Options = customFieldDto.Options;
            existingField.IsRequired = customFieldDto.IsRequired;
            existingField.Sequence = customFieldDto.Sequence;
            existingField.IsActive = customFieldDto.IsActive;
            existingField.UpdatedBy = updatedByUserId;
            existingField.UpdatedAt = DateTime.UtcNow;
            existingField.MaxValue = customFieldDto.MaxValue;
            existingField.MinValue = customFieldDto.MinValue;

            await repository.UpdateAsync(existingField);
        }
    }
}
