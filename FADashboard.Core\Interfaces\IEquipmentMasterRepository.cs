﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IEquipmentMasterRepository
{
    Task<List<EquipmentMasterListView>> GetAllEquipmentMaster(long companyId, bool includeDeactive);
    Task<EquipmentMasterView> GetEquipmentById(long companyId, long id);
    Task<RepositoryResponse> ActivateDeactivateEquipmentMaster(long companyId, long id, bool action);
    Task<RepositoryResponse> CreateEquipmentMaster(long companyId, EquipmentMasterView equipmentMasterDefinitionsInput);
    Task<RepositoryResponse> UpdateEquipmentMaster(long companyId, EquipmentMasterView equipmentMasterDefinitionsInput);
}
