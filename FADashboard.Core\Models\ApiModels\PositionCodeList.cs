﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class PositionCodeList
{
    public string AttachedErpId { get; set; }
    public string AttachedTo { get; set; }
    public long AttachedToId { get; set; }
    public List<long> BeatIds { get; set; }
    public string CodeId { get; set; }
    public long CompanyId { get; set; }
    public List<long> DistributorIds { get; set; }
    public string Geography { get; set; }
    public long GeographyId { get; set; }
    public GeographyLevel GeographyLevel { get; set; }
    public long Id { get; set; }
    public PositionCodeLevel Level { get; set; }
    public string Name { get; set; }
    public string ReportingTo { get; set; }
    public string ReportingToCodeId { get; set; }
    public long ReportingToId { get; set; }
    public PositionCodeLevel ReportingToPositionLevel { get; set; }
}
