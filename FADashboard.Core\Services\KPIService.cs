﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class KPIService(IKPIRepository kPIRepository) : RepositoryResponse
{
    private readonly IKPIRepository kPIRepository = kPIRepository;

    public async Task<List<KPIList>> GetKPIs(bool showDeactive)
    {
        var kpis = await kPIRepository.GetKPIs(showDeactive);
        return kpis;
    }

    public async Task<List<KPIsList>> GetKPIList(bool showDeactive)
    {
        var kpi = await kPIRepository.GetKPIList(showDeactive);
        return kpi;
    }

    public async Task<KPIRequestInput> GetKPIById(long id)
    {
        var kpi = await kPIRepository.GetKPIById(id);
        return kpi;
    }

    public async Task<RepositoryResponse> ActivateDeactivateKPI(long id, bool action)
    {
        var kpi = await kPIRepository.ActivateDeactivateKPI(id, action);
        return kpi;
    }

    private async Task<RepositoryResponse> IsValidKPI(KPIRequestInput kpi)
    {
        var allKPIs = await kPIRepository.GetKPIs(false);
        if (kpi.Id != 0)
        {
            allKPIs = allKPIs.Where(p => p.Id != kpi.Id).ToList();
        }

        var kpiNameList = allKPIs.Select(p => p.Name.NormalizeCaps()).ToList();

        if (kpiNameList.Contains(kpi.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = kpi.Id, ExceptionMessage = "KPI name is not unique", Message = "KPI creation/updation Failed!", IsSuccess = false,
            };
        }

        return GetSuccessResponse(kpi.Id, "KPI unique");
    }

    public async Task<RepositoryResponse> CreateUpdateKPI(KPIRequestInput kpi)
    {
        var checkValid = await IsValidKPI(kpi);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (kpi.Id == 0)
        {
            return await kPIRepository.CreateKPI(kpi);
        }

        return await kPIRepository.UpdateKPI(kpi);
    }
}
