﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class TrainingDashboardService(ITrainingDashboardRepository trainingDashboardRepository)
{
    public async Task<TrainingDashboardResponseDto> GetDashboardDataAsync(long companyId)
    {
        var data =  await trainingDashboardRepository.GetTrainingDashboardDataAsync(companyId);
        return data;
    }
}
