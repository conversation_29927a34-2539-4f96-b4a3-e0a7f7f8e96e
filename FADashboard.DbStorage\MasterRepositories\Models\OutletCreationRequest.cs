﻿using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FAOutletCreationRequests")]
public class OutletCreationRequest
{
    public string Aadhar { get; set; }
    public string AccountHoldersName { get; set; }
    public string Address { get; set; }
    public string AlternateImageId { set; get; }
    public long? ApprovedByManagerId { set; get; }
    public DateTime? ApprovedByManagerOn { set; get; }
    public PortalUserRole? ApprovedByManagerRole { set; get; }
    public long? ApprovedByRegionAdminId { set; get; }
    public DateTime? ApprovedByRegionalAdminOn { set; get; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public string AttributeImage3 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }
    public string BankAccountNumber { get; set; }
    public long BeatId { get; set; }
    public string City { get; set; }
    public long CompanyId { get; set; }
    public string CreationContext { get; set; }
    public DateTime DeviceTime { set; get; }
    public string DisapprovalReason { get; set; }
    public bool? Disapproved { set; get; }
    public long? DistributorId { set; get; }
    public string DistributorIdsJsonString { get; set; }
    public string Email { get; set; }
    public long EmployeeId { set; get; }
    public long FAEventId { get; set; }
    public string FormattedAddress { get; set; }
    public double? GeoAccuracy { get; set; }
    public string GSTN { get; set; }
    public bool GSTRegistered { get; set; }
    public string Guid { get; set; }
    public long Id { get; set; }
    public string IFSCCode { get; set; }
    public Guid? ImageId { set; get; }
    public bool IsKYC { get; set; }
    public string LandlineNumber { get; set; }
    public string Landmark { get; set; }
    public decimal? Latitude { get; set; }
    public long? LocationId { set; get; }
    public decimal? Longitude { get; set; }
    public OutletRequestStatus ManagerApprovedStatus { set; get; }
    public string MarketName { set; get; }
    public string ModeOfDataCollection { get; set; }
    public double OrderInRevenue { get; set; }
    public double OrderInStdUnits { get; set; }
    public OutletChannel? OutletChannel { get; set; }
    public virtual ICollection<OutletCreationRequestManagerEdit> OutletCreationRequestManagerEditMapping { get; set; }
    public string OwnersName { get; set; }
    public string OwnersNo { get; set; }
    public string PAN { get; set; }
    public string PhotoProofId { set; get; }
    public string PinCode { get; set; }
    public string PlaceOfDelivery { get; set; }
    public OutletRequestStatus RegionalAdminApprovedStatus { set; get; }
    public string RejectionReason { get; set; }
    public string RequestId { set; get; }
    public bool Reviewed { set; get; }
    public long? ReviewedBy { set; get; }
    public PortalUserRole? ReviewedByUserRole { set; get; }
    public DateTime? ReviewedOn { set; get; }
    public string RouteIds { get; set; }
    public OutletSegmentation? Segmentation { set; get; }
    public DateTime ServerTime { set; get; }
    public string ShopName { get; set; }
    public string ShopNumber { get; set; }
    public string ShopType { get; set; }
    public int SizeForAreaForCompany { get; set; }
    public int SizeOfShop { get; set; }
    public string State { get; set; }
    public string SubCity { get; set; }
    public string TypeOfIdProof { get; set; }
    public Guid? VisitId { get; set; }
}
