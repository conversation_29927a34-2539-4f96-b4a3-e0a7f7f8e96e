﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class GamesService(
    IGameRepository gameRepository,
    ICurrentUser currentUser
) : RepositoryResponse
{
    private static Task<List<GamesList>> GetGamesList(List<GameList> games)
    {
        var GamesList = games.Select(item => new GamesList
            {
                Id = item.Id,
                Name = item.Name,
                StartDate = item.StartDate.ToString("dd MMM yyyy"),
                EndDate = item.EndDate.ToString("dd MMM yyyy"),
                IsActive = item.IsActive
            })
            .ToList();
        return Task.FromResult(GamesList);
    }

    public async Task ActivateGame(long GameId, long companyId) => await gameRepository.ActivateGame(companyId, GameId);

    public async Task DeactivateGame(long GameId, long companyId) => await gameRepository.DeactivateGame(companyId, GameId);

    public async Task<List<GamesList>> GetGames(long companyId, string searchString = "")
    {
        _ = new List<GameList>();
        List<GameList> games;
        if (string.IsNullOrEmpty(searchString))
            games = await gameRepository.GetGames(companyId);
        else
            games = await gameRepository.GetGamesBySearch(companyId, searchString);

        return await GetGamesList(games);
    }

    public async Task<int> GetGamesCount(long companyId, string searchString = null, bool includeDeactivate = false) => await gameRepository.GetGamesCount(companyId, searchString, includeDeactivate);

    public async Task<List<GamesList>> GetInactiveGames(long companyId, string searchString = "")
    {
        var games = await gameRepository.GetInactiveGames(companyId, searchString);
        return games.Select(p => new GamesList
        {
            Id = p.Id,
            Name = p.Name,
            StartDate = p.StartDate.ToString("dd MMM yyyy"),
            EndDate = p.EndDate.ToString("dd MMM yyyy"),
            IsActive = p.IsActive
        }).ToList();
    }

    public async Task<RepositoryResponse> CreateGame(CreateGame gameDetails) => await gameRepository.CreateGame(currentUser.CompanyId, gameDetails);

    public async Task<CreateGame> GetGameById(long id)
    {
        var survey = await gameRepository.GetGameById(currentUser.CompanyId, id);
        return survey;
    }

    public async Task<List<EntityMin>> GetGamesForDateRange(long companyId, DateTime startdate)
    {
        var games = await gameRepository.GetGamesForDateRange(companyId, startdate);
        return games;
    }
}
