﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IProductVisibilityRuleRepository
{
    Task<List<ProductVisibilityRuleList>> GetAllProductVisibilityRule(long companyId, bool includeDeactive);
    Task<ProductVisibilityRuleInput> GetProductVisibilityRuleById(long companyId, long id);
    Task<RepositoryResponse> DeactivateProductVisibilityRule(long companyId, long id, bool action);
    Task<RepositoryResponse> CreateProductVisibilityRule(long companyId, ProductVisibilityRuleInput pvcr);
    Task<RepositoryResponse> UpdateProductVisibilityRule(long companyId, ProductVisibilityRuleInput pvcr);
    Task<List<EntityMin>> GetAllRuleMin(long companyId, bool includeDeactive);
}
