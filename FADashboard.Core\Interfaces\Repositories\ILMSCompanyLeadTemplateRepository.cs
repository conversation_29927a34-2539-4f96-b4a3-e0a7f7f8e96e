using FADashboard.Core.Models.DTOs;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSCompanyLeadTemplateRepository
    {
        Task<LMSCompanyLeadTemplateDto> GetByIdAsync(long id);
        Task<PagedResult<LMSCompanyLeadTemplateDto>> GetTemplatesAsync(long companyId, LMSLeadTemplateQueryParameters queryParameters);
        Task<bool> TemplateNameExistsAsync(long companyId, string templateName, long? currentTemplateId = null);
        Task<LMSCompanyLeadTemplateDto> GetDefaultTemplateAsync(long companyId);
        Task<int> GetLeadsCountAsync(long templateId);
        Task<LMSCompanyLeadTemplateDto> AddAsync(LMSCompanyLeadTemplateDto entity);
        Task<LMSCompanyLeadTemplateDto> UpdateAsync(LMSCompanyLeadTemplateDto entity);
        Task<bool> DeleteAsync(long id, long? updatedByUserId);
    }
}
