﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Models;
public class RewardRequest
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public FilterConstraintEntityType EntityType { get; set; }
    public long EntityId { get; set; }
    public double? Achievement { get; set; }
    public double Rewards { get; set; }
    public ApprovalEngineRequestStatus Status { get; set; }
    public string? ExtraInfoJson { get; set; }
    public string? CreationContext { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
public class RewardRequestDto : RewardRequest
{
    public string? DistributorName { get; set; }
}

public class RewardRequestDetailDto
{
    public long Id { get; set; }
    public DateTime Date { get; set; }
    public string DistributorName { get; set; }
    public string DistributorERPId { get; set; }
    public string BeatIdName { get; set; }
    public string EntityType => "O";
    public string EntityIdName { get; set; }
    public decimal TotalAchievement { get; set; }
    public decimal TotalRewardPoints { get; set; }
    public List<RewardInfo> RewardInfo { get; set; }
    public List<long> GroupedByIds { get; set; } = new();
}

public class RewardInfo
{
    public string RuleName { get; set; }
    public string CriteriaName { get; set; }
    public double Achievement { get; set; }
    public double RewardPoints { get; set; }
}

public class EnhancedRewardRequestDto : RewardRequestDto
{
    public List<RewardInfo> RewardInfo { get; set; }
    public string RuleName { get; set; }  // Add this
    public string CriteriaName { get; set; }  // Add this
    public DateTime RequestedOn { get; set; }
    public bool IsApproved { get; set; }
    public long RequestorId { get; set; }
    public string RequestorName { get; set; }
    public string ManagerName { get; set; }
    public string ActionTakenBy { get; set; }
    public object OrderByTime { get; set; }
    public List<long> DistributorIds { get; set; } = new();
    public List<long> GroupedIds { get; set; } = new();
}

public class ApproveRejectRequest
{
    public long[] RequestIds { get; set; }
    public bool IsApproved { get; set; }
}
