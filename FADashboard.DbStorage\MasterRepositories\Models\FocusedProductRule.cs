﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FAFocusedProductRule")]
public class FocusedProductRule : IAuditedEntity, IDeletable
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }

    [StringLength(256)] public string Description { get; set; }

    [NotMapped]
    public List<long> DisplayCatIds
    {
        get => DisplayCatList == null ? [] : JsonConvert.DeserializeObject<List<long>>(DisplayCatList);
        set => DisplayCatList = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use DisplayCatIds Instead!")]
    [StringLength(1024)]
    public string DisplayCatList { get; set; }

    [Audited] public DateTime EndDate { get; set; }

    public List<FocusedProductRulePositionCodeMapping> FocusedProductRulePositionCodeMappings { get; set; }
    public long Id { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public long ManagerId { get; set; }

    public PortalUserRole ManagerRole { get; set; }

    [StringLength(64)] public string Name { get; set; }

    [NotMapped]
    public List<long> SKUIds
    {
        get => SKUList == null ? [] : JsonConvert.DeserializeObject<List<long>>(SKUList);
        set => SKUList = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use SKUIds Instead!")]
    [StringLength(4096)]
    public string SKUList { get; set; }

    public DateTime StartDate { get; set; }

    [Obsolete("Only for DbAccess, Use ShopTypeIds Instead!")]
    [StringLength(1024)]
    public string ShopTypes { get; set; }

    [NotMapped]
    public List<long> ShopTypeIds
    {
        get => ShopTypes == null ? [] : JsonConvert.DeserializeObject<List<long>>(ShopTypes);
        set => ShopTypes = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use OutletTagIds Instead!")]
    [StringLength(1024)]
    public string OutletTags { get; set; }

    [NotMapped]
    public List<long> OutletTagIds
    {
        get => OutletTags == null ? [] : JsonConvert.DeserializeObject<List<long>>(OutletTags);
        set => OutletTags = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }
}
