﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IOpenMarketRepository
{
    Task<long> GetOpenMarketDayStockId(Guid recipientGuid);
    Task<ApprovedStatus?> VerifyApprovalStatus(long openMarketDayStocksId);
    Task<(bool? IsDayStartStock, long CompanyId)> GetDayStartStockStatusAndCompanyId(long openMarketDayStocksId);
    Task<string> GetSKUName(long productId);
    Task<List<SKUDetails>> GetSKUDetails(bool isDayStart, long openMarketDayStocksId);
    Task<RepositoryResponse> UpdateDayStockStatus(UpdateStockRequest updateStockRequest);
    Task<StockLoadoutRequestModel> GetStockLoadOut(long Id, bool isDayStart, long companyId);
    Task<StockReconcilationModel> GetStockForOrderReconcilation(long Id, bool isDayStart, long companyId);
}
