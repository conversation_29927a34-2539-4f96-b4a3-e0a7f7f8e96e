﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class EffectivePcKPIService(IEffectivePcKPIRepository effectivePcKPIRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<EffectivePcKpiInput> GetRuleById(long ruleId) => await effectivePcKPIRepository.GetRuleById(ruleId, currentUser.CompanyId);

    public async Task<List<EffectivePcKpiInput>> GetAllRules(bool getDeactivated = false) => await effectivePcKPIRepository.GetAllRules(currentUser.CompanyId, getDeactivated);

    public async Task<RepositoryResponse> CreateUpdateRules(EffectivePcKpiInput effectivePcKpi)
    {
        if (effectivePcKpi.RuleId == 0)
        {
            return await effectivePcKPIRepository.CreateRule(effectivePcKpi, currentUser.CompanyId);
        }

        return await effectivePcKPIRepository.UpdateRule(effectivePcKpi, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> ActivateDeactivateRule(long ruleId, bool deactivate) => await effectivePcKPIRepository.ActivateDeactivateRule(currentUser.CompanyId, ruleId, deactivate);
    public async Task<RepositoryResponse> SetDefaultRule(long ruleId, bool isDefault) => await effectivePcKPIRepository.SetDefaultRule(currentUser.CompanyId, ruleId, isDefault);
}
