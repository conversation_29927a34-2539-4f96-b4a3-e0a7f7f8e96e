﻿using FADashboard.Core.Models.ViewModels;

namespace FADashboard.Core.Models.ApiModels;

public class Team
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public long CompanyId { get; set; }

    public TeamModuleType TeamType { get; set; }
}

public class TeamUserMappingsView
{
    public long TeamId { get; set; }
    public bool IsTeamActive { get; set; }
    public List<long> TeamPlayerIds { get; set; }
}

public class TeamUserMappingsWithPlayers
{
    public long TeamId { get; set; }
    public bool IsTeamActive { get; set; }
    public List<TeamPlayerWithPosition> TeamPlayers { get; set; }
}

public class TeamPlayerWithPosition
{
    public long TeamPlayerId { get; set; }
    public string TeamPlayerName { get; set; }
    public string TeamPlayerErpId { get; set; }
    public List<PositionMinParent> Positions { get; set; }
}
