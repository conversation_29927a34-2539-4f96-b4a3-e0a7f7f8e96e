﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("F2KLocations")]
public class Location : IAuditedEntity, IERPEntity
{
    public string Aadhar { set; get; }
    public string AccountHoldersName { get; set; }
    public string Address { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public string AttributeImage3 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }
    public string BankAccountNumber { get; set; }
    public virtual LocationBeat Beat { get; set; }

    [Audited] public long BeatId { get; set; }

    public string City { set; get; }
    public long CodeId { get; set; }

    public long CompanyId { get; set; }

    public OutletSegmentation CompanySegmentation { set; get; }

    [Audited] [Column("OwnersNo")] public string ContactNo { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string Email { get; set; }
    public long? EntityMarginSlabId { get; set; }

    [Audited] public string ErpId { get; set; }
    public string Franchise { set; get; }

    [Audited] public double? GeoAccuracy { get; set; }
    public long? GeographicalMappingId { get; set; }
    public string GSTIN { get; set; }
    public bool GSTRegistered { get; set; }
    public string GUID { get; set; }

    [Column("ID")] public long Id { get; set; }

    public string IFSCCode { get; set; }
    public string ImageId { set; get; }
    public bool IsAssetPresent { get; set; }

    [Column("AssetDesc")] public string AssetDescription { set; get; }

    public string AssetCode { set; get; }
    public string AssetType { set; get; }
    public string AssetSize { set; get; }

    [Audited] public bool IsBlocked { get; set; }

    public bool IsFocused { set; get; }
    public bool IsKYC { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public long? MarginSlabId { get; set; }
    public string MarketName { set; get; }

    [Obsolete("Use Via ShopType")] public OutletChannel OutletChannel { set; get; }

    [Audited] public string OwnersName { get; set; }

    public string PAN { set; get; }
    public string PinCode { get; set; }
    public string PlaceOfDelivery { set; get; }
    public virtual ICollection<PositionOutletMapping> PostionOutletMappings { get; set; }
    public virtual ICollection<RouteOutletMappings> RouteOutletMappings { get; set; }
    public string SecondaryEmail { get; set; }
    public OutletSegmentation Segmentation { set; get; }
    public string SegmentationScope { set; get; }
    public string ShopName { get; set; }
    public virtual ShopTypes ShopType { get; set; }

    [Audited] public long? ShopTypeId { get; set; }

    public int SizeForAreaForCompany { get; set; }
    public int SizeOfShop { get; set; }
    public string State { set; get; }
    public string CustomTags { set; get; }

    // public virtual ICollection<RouteOutletMapping> RouteOutletMappings { get; set; }
    public DateTime TimeAdded { get; set; }

    public VerificationStatus VerificationStatus { get; set; }
    public string FSSAINumber { get; set; }
    public DateTime? FSSAIExpiryDate { get; set; }
    public LocationConsumerType? ConsumerType { get; set; }
    public ISRAvailability? IsrAvailibilty { get; set; }

    [StringLength(50)] public string TIN { get; set; }

    public long? SubShopTypeId { get; set; }
}
