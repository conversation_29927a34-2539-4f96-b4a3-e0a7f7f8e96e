﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("EmployeeTourPlans")]
public class EmployeeTourPlan : IDeviceEntity
{
    public EmployeeTourPlan()
    {
        EmployeeTourPlanItems = [];
    }

    public virtual Company Company { get; set; }
    public long CompanyId { set; get; }
    public DateTime DeviceTime { get; set; }
    public DateTime EffectiveStartDate { set; get; }
    public virtual ClientEmployee Employee { set; get; }
    public long EmployeeId { set; get; }
    public virtual ICollection<EmployeeTourPlanItem> EmployeeTourPlanItems { set; get; }
    public DateTime EndDate { set; get; }
    public long Id { set; get; }

    public long? PositionCodeId { get; set; }

    public virtual PositionCode PositionCode { get; set; }
    public long? ReviewedById { set; get; }
    public PortalUserRole? ReviewedByRole { set; get; }
    public DateTime? ReviewedOn { set; get; }
    public ApprovedStatus ReviewedStatus { set; get; }
    public DateTime ServerTime { get; set; }
    public DateTime StartDate { set; get; }
    public long? UpdatedFromId { get; set; }
    public int ForDays { get; set; }
    public bool IsRepeatable { get; set; }
}

[Table("EmployeeTourPlanItems")]
public class EmployeeTourPlanItem
{
    public EmployeeTourPlanItem()
    {
        EmployeeTourPlanItemsSecondary = [];
    }

    public virtual LocationBeat Beat { get; set; }

    [Audited] public long? BeatId { set; get; }

    public virtual Company Company { get; set; }
    public long CompanyId { set; get; }
    public virtual EmployeeTourPlan EmployeeTourPlan { get; set; }
    public long EmployeeTourPlanId { set; get; }
    public long Id { set; get; }
    public DateTime ItemDate { set; get; }
    public long? JWFieldUserId { set; get; }
    public virtual ClientEmployee JWFieldUser { get; set; }
    public virtual PositionCode JWFieldUserPosition { get; set; }

    [Audited] [StringLength(1024)] public string Reason { set; get; }

    [Audited] [StringLength(512)] public string ReasonCategory { set; get; }

    public virtual Routes Route { set; get; }
    public long? RouteId { set; get; }
    public long? JWFieldUserPositionId { get; set; }
    public JourneyPlanEntityType? EntityType { get; set; }
    public long? EntityId { set; get; }
    public virtual ICollection<EmployeeTourPlanItemsSecondary> EmployeeTourPlanItemsSecondary { set; get; }
}

public class EmployeeTourPlanItemsSecondary
{
    public virtual LocationBeat Beat { get; set; }

    [Audited] public long? BeatId { set; get; }

    public virtual Company Company { get; set; }
    public long CompanyId { set; get; }
    public virtual EmployeeTourPlanItem EmployeeTourPlanItem { get; set; }
    public long EmployeeTourPlanItemId { set; get; }
    public long Id { set; get; }
    public DateTime ItemDate { set; get; }
    public long? JWFieldUserId { set; get; }
    public virtual ClientEmployee JWFieldUser { get; set; }
    public virtual PositionCode JWFieldUserPosition { get; set; }
    public long? JWFieldUserPositionId { get; set; }

    [Audited] [StringLength(1024)] public string Reason { set; get; }

    [Audited] [StringLength(512)] public string ReasonCategory { set; get; }

    public virtual Routes Route { set; get; }
    public long? RouteId { set; get; }
    public int Sequence { set; get; }
    public bool IsDeactive { get; set; }
}
