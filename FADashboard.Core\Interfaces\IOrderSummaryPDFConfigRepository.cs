﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IOrderSummaryPDFConfigRepository
{
    Task<List<OrderSummPdfConfig>> GetFields(long companyId, PdfType pdfType, CompanySettings companySettings);
    Task UpdateFields(List<OrderSummPdfConfig> fields, ICurrentUser currentUser, PdfType pdfType);
}
