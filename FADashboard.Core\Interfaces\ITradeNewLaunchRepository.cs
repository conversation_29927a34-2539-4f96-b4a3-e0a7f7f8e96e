﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface ITradeNewLaunchRepository
{
    Task<RepositoryResponse> CreateNewLaunchProduct(TradeNewLaunchProdInput newLaunchProduct, long companyId);
    Task<RepositoryResponse> UpdateNewLaunchProduct(TradeNewLaunchProdInput newLaunchProduct, long companyId);
    Task<List<TradeNewLaunchProductList>> GetNewLaunchProducts(long companyId, bool includeDeactivate = false);
    Task<TradeNewLaunchProdInput> GetNewLaunchProductById(long newLaunchProductId, long companyId);
    Task<RepositoryResponse> DeactivateNewLaunchProduct(long newLaunchProductId, long companyId);





}
