﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Regions : IDeletable, IAuditedEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public string ErpId { set; get; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string Name { get; set; }
    public ICollection<Territory> Territories { get; set; }
    public Zone Zone { get; set; }
    public long ZoneId { get; set; }
    public string ShortRegionCode { get; set; }
    public virtual ICollection<RegionWiseODS> RegionwiseODS { get; set; }
}

public class Zone : IAuditedEntity, IDeactivatable
{
    public Zone()
    {
        SurveyToCompanyZoneMaps = [];
    }

    public long Company { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string Name { get; set; }
    public virtual Geographies Parent { get; set; }
    public long? ParentId { get; set; }
    public ICollection<Regions> Regions { get; set; }
    public virtual ICollection<SurveyToCompanyZoneMap> SurveyToCompanyZoneMaps { get; set; }
}
