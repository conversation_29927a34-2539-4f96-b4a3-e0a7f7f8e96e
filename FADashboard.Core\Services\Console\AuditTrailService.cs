﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services.Console;

public class AuditTrailService(IAuditTrailRepository auditTrailRepository, ICurrentUser currentUser, ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    public async Task<PagedResponse<List<AuditTrailModel>>> GetChanges(PaginationFilter validFilter, string entityName)
    {
        var companySettingValues = await companySettingsRepository.GetAllCompanySettingsMin(currentUser.CompanyId);
        var companySettingDictionary = companySettingValues.ToDictionary(l => l.Id, l => l.Name);
        var keys = companySettingDictionary.Keys.ToList();
        var changesTotal = await auditTrailRepository.GetChanges(validFilter, currentUser.CompanyId, keys, entityName);
        var commonEntityIds = changesTotal.ChangesList.Select(l => l.EntityId).Intersect(companySettingValues.Select(m => m.Id));
        var changes = commonEntityIds
            .Join(changesTotal.ChangesList, entityId => entityId, changes => changes.EntityId,
                (entityId, changes) => new AuditTrailModel
                {
                    EntityId = changes.EntityId,
                    ChangedBy = changes.ChangedBy,
                    ChangedOn = changes.ChangedOn,
                    NewValue = changes.NewValue,
                    OldValue = changes.OldValue,
                    SettingKey = companySettingDictionary[entityId]
                }).OrderByDescending(o => o.ChangedOn).ToList();

        return PaginationHelper.CreatePagedReponse(changes, validFilter, changesTotal.Total);
    }
}
