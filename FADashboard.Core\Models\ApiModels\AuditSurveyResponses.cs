﻿using System;
using System.Collections.Generic;

namespace FADashboard.Core.Models.ApiModels;
public class AuditSurveyResponses
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long? BeatId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime DeviceTime { get; set; }
    public long? DistributorId { get; set; }
    public long EmployeeId { get; set; }
    public long FaEventId { get; set; }
    public long? FormId { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public long? OutletId { get; set; }
    public long? PositionCodeId { get; set; }
    public long? ProductId { get; set; }
    public DateTime ServerTime { get; set; }
    public List<AuditSurveyResponseItem> SurveyResponseItems { get; set; }
}

public class AuditSurveyResponseItem
{
    public long Id { get; set; }
    public long SurveyResponseId { get; set; }
    public string Image { get; set; }
    public string Options { get; set; }
    public long? ProductId { get; set; }
    public long QuestionId { get; set; }
    public string Text { get; set; }
}
