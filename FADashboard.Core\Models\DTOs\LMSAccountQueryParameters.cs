using System;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountQueryParameters
    {
        private const int MaxPageSize = 100;
        private int _pageSize = 10;

        public string SearchTerm { get; set; }
        public long? AssignedTo { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDateStart { get; set; }
        public DateTime? CreatedDateEnd { get; set; }
        public decimal? RevenueMin { get; set; }
        public decimal? RevenueMax { get; set; }

        public int PageNumber { get; set; } = 1;

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        public string SortBy { get; set; } = "CreatedAt";
        public string Order { get; set; } = "desc";
    }
}
