﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace FADashboard.Core.Helper.ReportBuilderHelper;

public class ReportDefinition : ReportDefinitionTemplate
{
    public string reportType { get => $"rpt-{Metadata.CompanyId}"; set { } }
}


public class ReportDefinitionTemplate
{
    [JsonPropertyName("dataSources")]
    public List<DataSource> DataSources { get; set; } = new();
    [JsonPropertyName("dataSets")]
    public DataSets DataSets { get; set; } = new();
    [JsonPropertyName("columns")]
    public List<Column> Columns { get; set; } = new();
    [JsonPropertyName("filters")]
    public List<Filter> Filters { get; set; } = new();
    [JsonPropertyName("groupBy")]
    public List<string> GroupBy { get; set; } = new();
    [JsonPropertyName("metadata")]
    public Metadata Metadata { get; set; } = new();
    [JsonPropertyName("sorting")]
    public List<Sort> Sorting { get; set; } = new();
    [JsonPropertyName("dynamicFilters")]
    public DynamicFilters DynamicFilters { get; set; } = new();

    [JsonPropertyName("dateOptions")]
    public DateOptions DateOptions { get; set; } = new();
    public string id { get => Metadata.ReportName?.ToLower().Replace(" ", "-") ?? string.Empty; set { } }
    public virtual string reportType { get => "template"; set { } }
}

public class DataSource
{
    [JsonPropertyName("id")]
    public int Id { get; set; }
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
}

public class DataSets
{
    [JsonPropertyName("selected")]
    public List<DataSet> Selected { get; set; } = new();
    [JsonPropertyName("joins")]
    public List<Join> Joins { get; set; } = new();
}

public class DataSet
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    [JsonPropertyName("dataSourceId")]
    public int DataSourceId { get; set; }
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
}

public class JoinCondition
{
    [JsonPropertyName("dataSetId")]
    public string DataSetId { get; set; } = string.Empty;
    [JsonPropertyName("column")]
    public string Column { get; set; } = string.Empty;
    [JsonPropertyName("value")]
    public string Value { get; set; } = string.Empty;
}

public class Join
{
    [JsonPropertyName("firstDataSetId")]
    public string FirstDataSetId { get; set; } = string.Empty;
    [JsonPropertyName("firstDataSetColumn")]
    public string FirstDataSetColumn { get; set; } = string.Empty;
    [JsonPropertyName("secondDataSetId")]
    public string SecondDataSetId { get; set; } = string.Empty;
    [JsonPropertyName("secondDataSetColumn")]
    public string SecondDataSetColumn { get; set; } = string.Empty;
    [JsonPropertyName("conditions")]
    public List<JoinCondition> Conditions { get; set; } = new();
}

public class Column
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;
    [JsonPropertyName("elementId")]
    public string ElementId { get; set; } = string.Empty;
    [JsonPropertyName("aggregatorType")]
    public string? AggregatorType { get; set; }
    [JsonPropertyName("dataType")]
    public string DataType { get; set; } = string.Empty;
    [JsonPropertyName("dataSetId")]
    public string DataSetId { get; set; } = string.Empty;
    [JsonPropertyName("formula")]
    public string? Formula { get; set; }
    [JsonPropertyName("derivedElementId")]
    public string? DerivedElementId { get; set; }
    [JsonPropertyName("visible")]
    public bool Visible { get; set; } = true;
}

public class Filter
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    [JsonPropertyName("operator")]
    public string Operator { get; set; } = string.Empty;
    [JsonPropertyName("value")]
    [NotMapped]
    public object Value { get; set; } = string.Empty;
}

public class Sort
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    [JsonPropertyName("sortDirection")]
    public string SortDirection { get; set; } = "ASC";
    //[JsonPropertyName("enabled")]
    //public bool Enabled { get; set; } = false;
}

public class Metadata
{
    [JsonPropertyName("reportName")]
    public string ReportName { get; set; } = string.Empty;
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    [JsonPropertyName("isTemplate")]
    public bool IsTemplate { get; set; }
    [JsonPropertyName("companyId")]
    public long? CompanyId { get; set; }
    [JsonPropertyName("createdAt")]
    public DateTime? CreatedAt { get; set; }
    [JsonPropertyName("updatedAt")]
    public DateTime? UpdatedAt { get; set; }
    [JsonPropertyName("isActive")]
    public bool IsActive { get; set; } = true;
}

public class DynamicFilters
{
    [JsonPropertyName("showUserHierarchyFilter")]
    public bool ShowUserHierarchyFilter { get; set; }

    [JsonPropertyName("showPositionHierarchyFilter")]
    public bool ShowPositionHierarchyFilter { get; set; }

    [JsonPropertyName("showChannelPartnerFilter")]
    public bool ShowChannelPartnerFilter { get; set; }

    [JsonPropertyName("showDateRangeFilter")]
    public bool ShowDateRangeFilter { get; set; }

    [JsonPropertyName("showDateFilter")]
    public bool ShowDateFilter { get; set; }

    [JsonPropertyName("showProductHierarchyFilter")]
    public bool ShowProductHierarchyFilter { get; set; }
}

public class DateRangeTypes
{
    [JsonPropertyName("daily")]
    public bool Daily { get; set; }

    [JsonPropertyName("monthToDate")]
    public bool MonthToDate { get; set; }

    [JsonPropertyName("quarterToDate")]
    public bool QuarterToDate { get; set; }

    [JsonPropertyName("yearToDate")]
    public bool YearToDate { get; set; }

    [JsonPropertyName("customDateRange")]
    public bool CustomDateRange { get; set; }
    [JsonPropertyName("today")]
    public bool Today { get; set; }
    [JsonPropertyName("yesterday")]
    public bool Yesterday { get; set; }
}

public class DateOptions
{
    [JsonPropertyName("dateRangeTypes")]
    public DateRangeTypes DateRangeTypes { get; set; } = new();

    [JsonPropertyName("dateColumnConfig")]
    public string DateColumnConfig { get; set; } = string.Empty;
}
