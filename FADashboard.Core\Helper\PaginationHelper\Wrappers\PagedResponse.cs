﻿namespace FADashboard.Core.Helper.PaginationHelper.Wrappers;

public class PagedResponse<T> : Response<T>
{
    public PagedResponse(T data, int pageNumber, int pageSize)
    {
        PageNumber = pageNumber;
        PageSize = pageSize;
        Data = data;
        Message = null;
        Succeeded = true;
        Errors = null;
    }

    public bool HasNext => PageNumber < TotalPages;
    public bool HasPrevious => PageNumber > 1;
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public long TotalRecords { get; set; }
}
