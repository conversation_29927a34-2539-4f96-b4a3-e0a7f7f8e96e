﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models;

public class CompanyAppMetaModel : CompanyAppMetadatainput
{
    [Display(Name = "App Variant Name")] public string AppVariantName { get; set; }
    public long Id { get; set; }


    [Display(Name = "App Version Number"),
     Required(AllowEmptyStrings = false),
     Range(1, int.MaxValue)]
    public int AppVersionNumber { get; set; }

    [Display(Name = "Minimum app version number")]
    public int? MinRequiredAppVersion { set; get; }

    [Display(Name = "MT App Variant Name")]
    public string MTAppVariantName { get; set; }

    [Display(Name = "MT App Version Number"),
     Required(AllowEmptyStrings = false),
     Range(1, int.MaxValue)]
    public int MTAppVersionNumber { get; set; }

    [Display(Name = "MT Minimum app version number")]
    public int? MTMinRequiredAppVersion { get; set; }
    public int? Soft_Update { get; set; }
    public string? Soft_Update_Msg { get; set; }
    public int? Hard_Update { get; set; }
    public string? Hard_Update_Msg { get; set; }
    public StageValue? StageValue { get; set; }
    public string? StageMessage { get; set; }
    public int? RecommendedAppVersion { get; set; }
    public int ApplicationType {  get; set; }

}

public class CompanyAppMetadatainput
{
    public long Id { get; set; }
    public long CompanyId { get; set; }

    public AlertSource AppType { get; set; }

    public UpdateType UpdateType { get; set; }
    public int AppVersionNumber { get; set; }
    public string UpdateMessage { get; set; }
}
