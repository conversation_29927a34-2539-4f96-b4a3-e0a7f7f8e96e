﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.CommonHelpers;
using Library.DateTimeHelpers;
using Library.Infrastructure.Interface;
using Library.Infrastructure.QueueService;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class RequestsAndAlertsService(
    IRequestsAndAlertsRepository requestsAndAlertsRepository,
    ICurrentUser currentUser,
    IOutletAdditionRequestRepository outletAdditionRequestRepository,
    IDeadOutletRequestRepository deadOutletRequestRepository,
    IAdminRepository adminRepository,
    IAdvanceLeaveRepository advanceLeaveRepository,
    ICompanySettingsRepository companySettingsRepository,
    IAttendanceRegularisationRepository attendanceRegularisationRepository,
    IDeviceRepository deviceRepository,
    AppConfigSettings appConfigSettings,
    FAResilientHttpClient resilientHttpClient,
    IAssetAllocationTransactionRepository assetAllocationTransactionRepository,
    ISurveyTransactionRepository surveyTransactionRepository,
    ISurveyRepository surveyRepository,
    IOutletMasterRepository outletMasterRepository,
    IDistributorAdditionRequestRepository distributorAdditionRequestRepository,
    IPositionCodeRepository positionCodeRepository,
    IRequestApprovalTimelineRepository approvalTimelineRepository,
    IAssetAgreementTransactionRepository assetAgreementTransactionRepository,
    IAssetTypesRepository assetTypesRepository,
    IAssetReallocationTransactionRepository assetReallocationTransactionRepository,
    IOutletVerificationRequestRepository outletVerificationRequestRepository) : RepositoryResponse
{
    #region Dead Outlet Request

    public async Task<RepositoryResponse> ActionTakenOnRequest(long requestId, bool approved) => await deadOutletRequestRepository.ActionTakenOnRequest(requestId, approved);

    public async Task<List<DeadOutletRequests>> GetArchivedRequests(string searchTerm = null)
    {
        var requests = await deadOutletRequestRepository.GetArchivedRequests(currentUser.CompanyId, searchTerm);
        return requests;
    }

    public async Task<int> GetArchivedRequestsCount(string searchTerm = null) => await deadOutletRequestRepository.GetArchivedRequestsCount(currentUser.CompanyId, searchTerm);

    public async Task<List<DeadOutletRequestItems>> GetOutletRequestItems(long requestItemId)
    {
        var requestItems = await deadOutletRequestRepository.GetOutletRequestItems(requestItemId, currentUser.CompanyId);
        return requestItems;
    }

    public async Task<int> GetOutletRequestItemsCount(long requestItemId) => await deadOutletRequestRepository.GetOutletRequestItemsCount(requestItemId);

    public async Task<List<Requests>> GetOutletRequests(bool includeArchived)
    {
        var requests = await deadOutletRequestRepository.GetOutletRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeArchived);
        return requests;
    }

    public async Task<int> GetOutletRequestsCount(string searchTerm = null) => await deadOutletRequestRepository.GetOutletRequestsCount(currentUser.CompanyId, searchTerm);

    #endregion Dead Outlet Request

    #region Outlet Adition Request

    public async Task<RepositoryResponse> ApproveOutletAdditionRequest(OutletAdditionRequestInput request)
    {
        var response = new RepositoryResponse();
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesSingleApproval = companySettings.UsesSingleApprovalForOutletAdditionRequest;
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

        if (companyUsesNewApproval)
        {
            var timelinesCount = await approvalTimelineRepository.GetRequestTimelinesCountForAdmin(currentUser.CompanyId, ApprovalEngineRequesType.OutletCreation);

            if (isRequestedByAdmin)
            {
                if (timelinesCount > 0)
                {
                    await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, request.Id, ApprovalEngineRequesType.OutletCreation);
                    await ProcessForApprovalEngineQueue(request.Id, ApprovalEngineRequesType.OutletCreation, true, isRequestedByAdmin, additionRequest: request);
                    return GetSuccessResponse(request.Id, "Request sent to Queue for Approval.");
                }
                else
                {
                    response = await outletAdditionRequestRepository.ApproveAdditionRequestByAdmin(request);
                }
            }
            else
            {
                var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                var posIds = positions.Select(e => e.Id).ToList();
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, request.Id, posIds, ApprovalEngineRequesType.OutletCreation);
                await ProcessForApprovalEngineQueue(request.Id, ApprovalEngineRequesType.OutletCreation, true, isRequestedByAdmin, additionRequest: request);
                return GetSuccessResponse(request.Id, "Request sent to Queue for Approval.");
            }
        }
        else
        {
            if (usesSingleApproval)
            {
                // For Single Step, fill the Admin column only
                response = await outletAdditionRequestRepository.ApproveAdditionRequestByAdmin(request);
                if (response.IsSuccess)
                {
                    await ProcessQueue(request);
                    return response;
                }
            }
            else if (!userHaveAdminPrivileges) // Manager
            {
                response = await outletAdditionRequestRepository.ApproveAdditionRequestDoubleStep(request);
                if (response.IsSuccess)
                {
                    await ProcessQueue(request);
                    return response;
                }
            }

            // Admin approval
            response = await outletAdditionRequestRepository.ApproveAdditionRequestByAdmin(request);
            if (response.IsSuccess)
            {
                await ProcessQueue(request);
            }
        }

        return response;
    }
    private async Task ProcessQueue(OutletAdditionRequestInput request)
    {
        var faEventId = (await outletAdditionRequestRepository.GetOutletAdditionRequestById(request.Id, currentUser.CompanyId)).FAEventId;
        var faEventData = await outletAdditionRequestRepository.GetRequestIdForFAEventId(currentUser.CompanyId, faEventId);
        var queueHandler = new QueueHandler<Dictionary<string, long>>(QueueType.LocationAdditionWithCallQueue, appConfigSettings.StorageConnectionString);
        await queueHandler.AddToGridQueue(faEventData.RequestId.ToLower(System.Globalization.CultureInfo.CurrentCulture), new Dictionary<string, long> { ["CompanyId"] = faEventData.CompanyId, ["EmployeeId"] = faEventData.EmployeeId });
    }

    private async Task ProcessForApprovalEngineQueue(long requestId, ApprovalEngineRequesType requestType, bool isApprovalRequest, bool IsRequestedByAdmin, string reasonForRejection = null, OutletAdditionRequestInput additionRequest = null, bool companyUsesHCCBUserFlows = false)
    {
        var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
        var attachedPositionIds = positions != null && positions.Count > 0 ? positions.Select(e => e.Id).ToList() : [];

        var newApprovalRequest = new ApprovalEngineModel
        {
            UserId = currentUser.LocalId,
            CompanyId = currentUser.CompanyId,
            RequestId = requestId,
            RequestType = requestType,
            IsApprovalRequest = isApprovalRequest,
            IsRequestedByAdmin = IsRequestedByAdmin,
            AttachedPositionsIds = attachedPositionIds,
            ReasonForRejection = reasonForRejection,
            UserRole = currentUser.UserRole,
            OutletAdditionRequestInput = additionRequest,
            CompanyUsesHCCBUserFlows = companyUsesHCCBUserFlows
        };

        var queueHandler = new QueueHandler<ApprovalEngineModel>(QueueType.NewApprovalAddUpdateQueue, appConfigSettings.StorageConnectionString);
        await queueHandler.AddToQueue(newApprovalRequest);
    }

    public async Task<RepositoryResponse> DisapproveOutletAdditionRequest(long requestId, string reasonForRejection)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

        if (companyUsesNewApproval)
        {
            if (isRequestedByAdmin)
            {
                var timelinesCount = await approvalTimelineRepository.GetRequestTimelinesCountForAdmin(currentUser.CompanyId, ApprovalEngineRequesType.OutletCreation);
                if (timelinesCount > 0)
                {
                    await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, ApprovalEngineRequesType.OutletCreation);
                    await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.OutletCreation, false, isRequestedByAdmin, reasonForRejection);
                    return GetSuccessResponse(requestId, "Request sent to queue for Disapproval.");
                }
                else
                {
                    return await outletAdditionRequestRepository.DisapproveOutletAdditionRequest(requestId, reasonForRejection);
                }
            }
            else
            {
                var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                var posIds = positions.Select(e => e.Id).ToList();
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, ApprovalEngineRequesType.OutletCreation);
                await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.OutletCreation, false, isRequestedByAdmin, reasonForRejection);
                return GetSuccessResponse(requestId, "Request sent to queue for Disapproval.");
            }
        }
        else
        {
            return await outletAdditionRequestRepository.DisapproveOutletAdditionRequest(requestId, reasonForRejection);
        }
    }

    public async Task<OutletAdditionRequestInput> GetOutletAdditionRequestById(long requestId)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var request = new OutletAdditionRequestInput();

        if (companyUsesNewApproval)
        {
            request = await outletAdditionRequestRepository.GetOutletAdditionRequestByIdForAdmin(requestId, currentUser.CompanyId);
        }
        else
        {
            if (userHaveAdminPrivileges)
            {
                request = await outletAdditionRequestRepository.GetOutletAdditionRequestByIdForAdmin(requestId, currentUser.CompanyId);
            }
            else
            {
                request = await outletAdditionRequestRepository.GetOutletAdditionRequestById(requestId, currentUser.CompanyId);
            }
        }

        return request;
    }

    public async Task<List<Requests>> GetOutletAdditionRequests(bool includeArchived)
    {
        var Outlets = await outletAdditionRequestRepository.GetOutletAdditionRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeArchived);
        return Outlets;
    }

    #endregion Outlet Adition Request

    #region Outlet Updation Request

    public async Task<RepositoryResponse> ApproveOutletUpdationRequest(long requestId)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

        if (companyUsesNewApproval)
        {
            if (isRequestedByAdmin)
            {
                var timelinesCount = await approvalTimelineRepository.GetRequestTimelinesCountForAdmin(currentUser.CompanyId, ApprovalEngineRequesType.OutletCreation);
                if (timelinesCount > 0)
                {
                    await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, ApprovalEngineRequesType.OutletUpdation);
                    await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.OutletUpdation, true, isRequestedByAdmin);
                    return GetSuccessResponse(requestId, "Request sent to Queue for Approval.");
                }
                else
                {
                    return await requestsAndAlertsRepository.ApproveOutletUpdationRequest(requestId);
                }
            }
            else
            {
                var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                var posIds = positions.Select(e => e.Id).ToList();
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, ApprovalEngineRequesType.OutletUpdation);
                await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.OutletUpdation, true, isRequestedByAdmin);
                return GetSuccessResponse(requestId, "Request sent to Queue for Approval.");
            }
        }
        else
        {
            return await requestsAndAlertsRepository.ApproveOutletUpdationRequest(requestId);
        }
    }

    public async Task<RepositoryResponse> DisapproveOutletUpdationRequest(long requestId, string reasonForRejection)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

        if (companyUsesNewApproval)
        {
            if (isRequestedByAdmin)
            {
                var timelinesCount = await approvalTimelineRepository.GetRequestTimelinesCountForAdmin(currentUser.CompanyId, ApprovalEngineRequesType.OutletCreation);
                if (timelinesCount > 0)
                {
                    await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, ApprovalEngineRequesType.OutletUpdation);
                    await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.OutletUpdation, false, isRequestedByAdmin, reasonForRejection);
                    return GetSuccessResponse(requestId, "Request sent to Queue for Disapproval.");
                }
                else
                {
                    return await requestsAndAlertsRepository.DisapproveOutletUpdationRequest(requestId, reasonForRejection);
                }
            }
            else
            {
                var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                var posIds = positions.Select(e => e.Id).ToList();
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, ApprovalEngineRequesType.OutletUpdation);
                await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.OutletUpdation, false, isRequestedByAdmin, reasonForRejection);
                return GetSuccessResponse(requestId, "Request sent to Queue for Disapproval.");
            }
        }
        else
        {
            return await requestsAndAlertsRepository.DisapproveOutletUpdationRequest(requestId, reasonForRejection);
        }
    }

    public async Task<OutletUpdationRequest> GetOutletUpdationRequestById(long requestId)
    {
        var request = await requestsAndAlertsRepository.GetOutletUpdationRequestById(requestId, currentUser.CompanyId);
        return request;
    }

    public async Task<List<Requests>> GetOutletUpdationRequests(bool includeArchived)
    {
        var Outlets = await requestsAndAlertsRepository.GetOutletUpdationRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeArchived);
        return Outlets;
    }

    #endregion Outlet Updation Request

    #region Outlet Verification Request

    public async Task<RepositoryResponse> ApproveOutletVerificationRequest(OutletVerificationRequestInput request)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

        if (isRequestedByAdmin)
            return new RepositoryResponse { Id = request.Id, Message = "Admin not Authorized to Approve Outlet Verification request", IsSuccess = false };

        return await outletVerificationRequestRepository.ApproveOutletVerificationRequest(request);
    }

    public async Task<RepositoryResponse> DisapproveOutletVerificationRequest(long requestId, string reasonForRejection)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;

        if (isRequestedByAdmin)
            return new RepositoryResponse { Id = requestId, Message = "Admin not Authorized to Disapprove Outlet Verification request", IsSuccess = false };

        return await outletVerificationRequestRepository.DisapproveOutletVerificationRequest(requestId, reasonForRejection);
    }

    public async Task<OutletVerificationRequestInput> GetOutletVerificationRequestById(long requestId)
    {
        var request = new OutletVerificationRequestInput();

        request = await outletVerificationRequestRepository.GetOutletVerificationRequestById(requestId, currentUser.CompanyId);

        return request;
    }

    public async Task<List<Requests>> GetOutletVerificationRequests(bool includeArchived)
    {
        var Outlets = await outletVerificationRequestRepository.GetOutletVerificationRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeArchived);
        return Outlets;
    }

    #endregion Outlet Verification Request

    #region Advance Leave Request

    public async Task<RepositoryResponse> ApproveRejectAdvanceLeave(long requestId, bool action) => await advanceLeaveRepository.ApproveRejectAdvanceLeaveRequest(requestId, currentUser.CompanyId, action, currentUser.LocalId);

    public async Task<AdvanceLeaveSubmission> GetAdvanceLeaveRequestById(long requestId)
    {
        var leave = await advanceLeaveRepository.GetAdvanceLeaveRequestById(requestId, currentUser.CompanyId);
        return leave;
    }

    public async Task<List<Requests>> GetAdvanceLeaveRequests(bool includeArchived)
    {
        var leaves = await advanceLeaveRepository.GetAdvanceLeaveRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeArchived);
        return leaves;
    }

    #endregion Advance Leave Request

    #region Attendance Regularization Request

    public async Task<RepositoryResponse> ApproveRejectAttendanceRegularisation(long requestId, bool action) => await attendanceRegularisationRepository.ApproveRejectAttendanceRegularisationRequest(requestId, action);

    public async Task<AttendanceRegulariseRequest> GetAttendanceRegularisationRequestById(long requestId)
    {
        var req = await attendanceRegularisationRepository.GetAttendanceRegularizationRequestById(requestId, currentUser.CompanyId);
        if (req != null)
        {
            var api = $"{appConfigSettings.reportApiBaseUrl}api/ds/normsForDay?companyId={req.CompanyId}&fieldUserId={req.EmployeeId}&date={req.AttendanceDate:MM/dd/yyyy}";

            var dataUrl = $"{api}";
            var response = await resilientHttpClient.GetJsonAsync<List<AttendanceKraModel>>(dataUrl, appConfigSettings.reportApiToken);
            if (response.Count > 0)
            {
                req.KRAs = response;
            }
            else
            {
                req.KRAs = [];
            }
        }

        return req;
    }

    public async Task<List<Requests>> GetAttendanceRegularisationRequests(bool includeArchived)
    {
        var leaves = await attendanceRegularisationRepository.GetAttendanceRegularisationRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeArchived);
        return leaves;
    }

    #endregion Attendance Regularization Request

    #region Tour Plan Request

    public async Task<RepositoryResponse> ApproveTourPlanRequest(long requestId, List<TourPlanDetails> tourPlanDetails)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        var todayDate = DateTime.UtcNow.Add(offset).Date;
        var result = await requestsAndAlertsRepository.ApproveTourPlanRequest(requestId, tourPlanDetails, todayDate);
        return result;
    }

    public async Task<RepositoryResponse> DisapproveTourPlanRequest(long requestId) => await requestsAndAlertsRepository.DisapproveTourPlanRequest(requestId);

    public async Task<TourPlanByIdDetails> GetTourPlanDetails(long tourPlanId)
    {
        var tourPlanDetails = await requestsAndAlertsRepository.GetTourPlanDetails(tourPlanId, currentUser.CompanyId);
        return tourPlanDetails;
    }

    public async Task<List<TourPlanList>> GetTourPlanRequests(bool includeApproved)
    {
        var tourPlanRequests = await requestsAndAlertsRepository.GetTourPlanRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, includeApproved);
        return tourPlanRequests;
    }

    public async Task<List<string>> GetTourPlanTypes(PortalUserRole portalRole)
    {
        var getTypes = new List<string>();

        var officialWorkTypes = await requestsAndAlertsRepository.GetOfficialWorkTypesForUserRole(portalRole, currentUser.CompanyId);
        getTypes.AddRange(officialWorkTypes);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var hideFullDayActivity = companySettings.HideFullDayActivity;
        if (!hideFullDayActivity)
        {
            var fullDayActivityTypes = new List<string> { "Meeting", "Training", "Promotional" };
            getTypes.AddRange(fullDayActivityTypes);
        }

        return getTypes;
    }

    #endregion Tour Plan Request

    #region Journey Diversion Request

    public async Task<RepositoryResponse> ApproveRejectJourneyDiversionRequest(long requestId, bool isApproved, long entityId)

    {
        try
        {
            // update UserOTPDetails

            var userId = currentUser.UserRole is <= PortalUserRole.CompanyExecutive or PortalUserRole.RegionalAdmin ? 0 : currentUser.LocalId;
            var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/userOTPDetails/reviewDetails?id={entityId}&status={isApproved}&userId={userId}";
            var data = await resilientHttpClient.PostJsonAsync<UserOTPDetails>(dataUrl, appConfigSettings.reportApiToken, null);

            // trigger Notification
            if (data != null)
            {
                var result = await requestsAndAlertsRepository.ApproveRejectJourneyDiversionRequest(requestId, isApproved);

                var queueHandler = new QueueHandler<PushNotificationMessage>(QueueType.GcmNotification, appConfigSettings.MasterStorageConnectionString);
                var notification = await GetNotificationMessage(data);
                await queueHandler.AddToGridQueue(null, data: notification);
                return result;
            }

            return new RepositoryResponse { Id = requestId, ExceptionMessage = "Could not find OTP corresponding to request", Message = "An Error Occurred while approving request", IsSuccess = false };
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = requestId, ExceptionMessage = ex.Message, Message = "An Error Occurred while approving request", IsSuccess = false };
        }
    }

    public async Task<JourneyDiversionRequest> GetJourneyDiversionRequestById(long requestId)
    {
        var journeyDiversionDetails = await requestsAndAlertsRepository.GetJourneyDiversionRequestById(requestId, currentUser.CompanyId);
        return journeyDiversionDetails;
    }

    public async Task<List<JourneyDiversionList>> GetJourneyDiversionRequests(bool showArchived)
    {
        var journeyDiversionRequests = await requestsAndAlertsRepository.GetJourneyDiversionRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, showArchived);
        return journeyDiversionRequests;
    }

    private async Task<PushNotificationMessage> GetNotificationMessage(UserOTPDetails userOTPDetails)
    {
        var gcmIds = await deviceRepository.GetEmployeeGcmIds(userOTPDetails.EmployeeId);
        var deviceList = await deviceRepository.GetDeviceForEmployeeList(currentUser.CompanyId, [userOTPDetails.EmployeeId]);
        var alertSource = deviceList != null && deviceList.Count > 0 ? deviceList[0].AlertSource != null ? deviceList[0].AlertSource.Value
            : AlertSource.GTPlayStore : AlertSource.GTPlayStore;
        var response = new PushNotificationMessage
        {
            GcmIds = gcmIds.GcmId,
            Message = new ApiMessage(new UserMessages
            {
                chatThreadId = 10000000,
                chatThreadDescription = "Route change request reviewed",
                chatThreadName = "[Important]: Action Taken By Manager For Request",
                dateTime = DateTime.UtcNow.ToUnixTime(),
                fromId = currentUser.LocalId,
                fromRole = currentUser.UserRole.GetDisplayName(),
                messageUniqueId = System.Guid.NewGuid().ToString(),
                participantId = userOTPDetails.EmployeeId,
                participantRole = PortalUserRole.ClientEmployee.GetDisplayName(),
                title = "Action Taken By Manager",
                message = "You request for " + userOTPDetails.OTPRequestType.GetDisplayName() + " is " +
                          (userOTPDetails.IsApproved
                              ? "approved and OTP is " + userOTPDetails.OTP
                              : " disapproved because " + userOTPDetails.Reason),
                entityId = userOTPDetails.Id,
                isApproved = userOTPDetails.IsApproved
            })
        };
        response.Message.NotificationType = NotificationType.OTP;
        response.AlertSource = alertSource;  // Asana Temp. Fix : https://app.asana.com/0/1203071525101825/1207322692236481/f date : 14 Jun,2024. reason: app gt version number is no longer under 100
        return response;
    }

    #endregion Journey Diversion Request

    #region Asset Allocation Request

    public async Task<RepositoryResponse> ApproveRejectAssetAllocationRequest(AssetAllocationRequest requestDetails, bool isApproved)
    {
        var assetTypeData = new AssetTypeList();
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var companyUsesAdminFlowInAssetAllocation = companySettings.CompanyUsesAdminFlowInAssetAllocation;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;
        var requestData = await assetAllocationTransactionRepository.GetAssetAllocationRequestById(requestDetails.RequestId, currentUser.CompanyId);
        if (companyUsesNewApproval)
        {
            if (isRequestedByAdmin)
            {
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestDetails.RequestId, ApprovalEngineRequesType.AssetAllocation);
            }
            else
            {
                var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                var posIds = positions.Select(e => e.Id).ToList();
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestDetails.RequestId, posIds, ApprovalEngineRequesType.AssetAllocation);
            }
            await ProcessForApprovalEngineQueue(requestDetails.RequestId, ApprovalEngineRequesType.AssetAllocation, isApproved, isRequestedByAdmin, companyUsesHCCBUserFlows: companySettings.CompanyUsesHCCBUserFlows, additionRequest: null);
            return GetSuccessResponse(requestDetails.RequestId, "Request sent to Queue for Approval.");
        }
        else
        {
            if (requestData.AssetTypeId != null)
            {
                assetTypeData = await assetTypesRepository.GetAssetTypeById((long)requestData.AssetTypeId, currentUser.CompanyId);
            }
            var response = await assetAllocationTransactionRepository.ApproveRejectAssetAllocationRequest(requestDetails, isApproved, assetTypeData.IsAssetAgreementRequired);
            return response;
        }
    }

    public async Task<SurveyResponseOutputWithAssetType> GetAssetAllocationRequestById(long id)
    {
        var assetAllocation = await assetAllocationTransactionRepository.GetAssetAllocationRequestById(id, currentUser.CompanyId);
        var outletDetail = await outletMasterRepository.GetOutletById(currentUser.CompanyId, assetAllocation.LocationId);
        var surveyResponses = await surveyTransactionRepository.GetSurveyResponsesOnFaEventId(assetAllocation.FAEventId, currentUser.CompanyId);
        var questionIds = surveyResponses.First().SurveyResponseItems.Select(s => s.QuestionId).ToList();
        var surveyId = surveyResponses.First().FormId;
        var surveyItems = surveyResponses.First().SurveyResponseItems;
        var questions = await surveyRepository.GetAllSurveyQuestions(surveyId ?? 0);
        var questionDictionary = questions.ToDictionary(x => x.QuestionId, x => x);
        var surveyItemswithOptions = new List<SurveyResponseItem>();
        foreach (var i in surveyItems)
        {
            var optionsList = i.Options is not null and not "" ? i.Options.Split(',') : null;

            if (optionsList != null && optionsList.Length > 0)
            {
                for (var j = 0; j < optionsList.Length; j++)
                {
                    if (optionsList[j] != "")
                    {
                        i.Options = optionsList[j];
                        surveyItemswithOptions.Add(i);
                    }
                    else
                    {
                        surveyItemswithOptions.Add(i);
                    }
                }
            }
            else
            {
                surveyItemswithOptions.Add(i);
            }
        }

        var returnModel = new SurveyResponseOutputWithAssetType
        {
            AssetTypeId = assetAllocation.AssetTypeId,
            SurveyResponseOutput = surveyItemswithOptions.Select(a => new SurveyResponseOutput
            {
                Question = questionDictionary[a.QuestionId].Title,
                Answer = !string.IsNullOrEmpty(a.Text)
                    ? a.Text
                    : a.Options?.Length > 0
                        ? questionDictionary[a.QuestionId].Choices
                            .FirstOrDefault(choice => choice.Id == Convert.ToInt64(a.Options))?.Name
                        : !string.IsNullOrEmpty(a.Image)
                            ? a.Image
                            : "",
                Outlet = outletDetail.OutletName,
                QuestionType = questionDictionary[a.QuestionId].QuestionType,
            }).ToList()
        };

        return returnModel;
    }

    public async Task<List<Requests>> GetAssetAllocationRequests(bool showArchived)
    {
        var requests = await assetAllocationTransactionRepository.GetAssetAllocationRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, showArchived);
        return requests;
    }

    #endregion Asset Allocation Request


    #region Distributor Addition Request

    public async Task<RepositoryResponse> ApproveDistributorAdditionRequest(DistributorAdditionRequestInput request)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        return await distributorAdditionRequestRepository.ApproveAdditionRequestByAdmin(request, companySettings);
    }
    public async Task<RepositoryResponse> DisapproveDistributorAdditionRequest(long requestId, string reasonForRejection) => await distributorAdditionRequestRepository.DisapproveDistributorAdditionRequest(requestId, reasonForRejection);

    public async Task<DistributorAdditionRequestInput> GetDistributorAdditionRequestById(long requestId)
    {
        var request = new DistributorAdditionRequestInput();
        request = await distributorAdditionRequestRepository.GetDistributorAdditionRequestById(requestId, currentUser.CompanyId);

        return request;
    }

    public async Task<List<Requests>> GetDistributorAdditionRequests(bool includeArchived)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var timeZoneOffsetMinutes = companySettings.TimeZoneOffset;
        var userHaveAdminPrevileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        if (userHaveAdminPrevileges)
        {
            var Distributors = await distributorAdditionRequestRepository.GetDistributorAdditionRequestsForAdmin(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, timeZoneOffsetMinutes, includeArchived);
            return Distributors;
        }
        else
        {
            var Distributors = await distributorAdditionRequestRepository.GetDistributorAdditionRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, timeZoneOffsetMinutes, includeArchived);
            return Distributors;
        }
    }

    #endregion Distributor Addition Request

    #region Onboarding Request

    public async Task<List<OnboardingRequest>> GetOnboardingRequests(bool showArchived, CancellationToken ct)
    {
        var userHaveAdminPrevileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        if (userHaveAdminPrevileges)
        {
            return await requestsAndAlertsRepository.GetAllOnboardingRequests(showArchived, ct);
        }
        else
        {
            return await requestsAndAlertsRepository.GetOnboardingRequestsForUser(showArchived, ct);
        }
    }
    public async Task<OnboardingRequestInput> GetOnboardingRequestById(long requestId, CancellationToken ct) => await requestsAndAlertsRepository.GetOnboardingRequestById(requestId, ct);
    public async Task<RepositoryResponse> DisapproveOnboardingRequest(long requestId, CancellationToken ct) => await requestsAndAlertsRepository.DisapproveOnboardingRequest(requestId, ct);
    public async Task<RepositoryResponse> ApproveOnboardingRequest(OnboardingRequestInput request, CancellationToken ct) => await requestsAndAlertsRepository.ApproveOnboardingRequest(request, ct);

    #endregion Onboarding Request

    #region Asset Agreement Request

    public async Task<List<Requests>> GetAllAssetAgreementRequests(bool showArchived)
    {
        var requests = await assetAgreementTransactionRepository.GetAllAssetAgreementRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, showArchived);

        return requests;
    }

    public async Task<List<SurveyResponseOutput>> GetAssetAgreementRequestById(long id)
    {
        var assetAgreement = await assetAgreementTransactionRepository.GetAssetAgreementRequestById(id, currentUser.CompanyId);
        var outletDetail = await outletMasterRepository.GetOutletById(currentUser.CompanyId, assetAgreement.LocationId);
        var surveyResponses = await surveyTransactionRepository.GetSurveyResponsesOnFaEventId(assetAgreement.FAEventId, currentUser.CompanyId);
        var questionIds = surveyResponses.First().SurveyResponseItems.Select(s => s.QuestionId).ToList();
        var surveyId = surveyResponses.First().FormId;
        var surveyItems = surveyResponses.First().SurveyResponseItems;
        var questions = await surveyRepository.GetAllSurveyQuestions(surveyId ?? 0);
        var questionDictionary = questions.ToDictionary(x => x.QuestionId, x => x);
        var surveyItemswithOptions = new List<SurveyResponseItem>();
        foreach (var i in surveyItems)
        {
            var optionsList = i.Options is not null and not "" ? i.Options.Split(',') : null;

            if (optionsList != null && optionsList.Length > 0)
            {
                for (var j = 0; j < optionsList.Length; j++)
                {
                    if (optionsList[j] != "")
                    {
                        i.Options = optionsList[j];
                        surveyItemswithOptions.Add(i);
                    }
                    else
                    {
                        surveyItemswithOptions.Add(i);
                    }
                }
            }
            else
            {
                surveyItemswithOptions.Add(i);
            }
        }

        var returnModel = surveyItemswithOptions.Select(a => new SurveyResponseOutput
        {
            Question = questionDictionary[a.QuestionId].Title,
            Answer = a.Text?.Length > 0 ? a.Text : a.Options?.Length > 0 ? questionDictionary[a.QuestionId].Choices.FirstOrDefault(choice => choice.Id == Convert.ToInt64(a.Options))?.Name : a.Image?.Length > 0 ? a.Image : "",
            Outlet = outletDetail.OutletName,
            QuestionType = questionDictionary[a.QuestionId].QuestionType,
        }).ToList();

        return returnModel;
    }

    public async Task<RepositoryResponse> ApproveRejectAssetAgreementRequest(long requestId, bool isApproved) => await assetAgreementTransactionRepository.ApproveRejectAssetAgreementRequest(requestId, isApproved);

    #endregion Asset Agreement Request

    #region Asset Reallocation Request

    public async Task<List<Requests>> GetAllAssetReallocationRequests(bool showArchived)
    {
        var requests = await assetReallocationTransactionRepository.GetAllAssetReallocationRequests(currentUser.LocalId, currentUser.UserRole, currentUser.CompanyId, showArchived);

        return requests;
    }

    public async Task<List<AssetReallocationDetails>> GetAssetReallocationRequestById(long id)
    {
        var assetReallocation = await assetReallocationTransactionRepository.GetAssetReallocationRequestById(id, currentUser.CompanyId);
        var outletDetail = await outletMasterRepository.GetOutletById(currentUser.CompanyId, assetReallocation.CurrentOutletId);
        var surveyResponses = new List<SurveyResponses>();
        if (assetReallocation.FAEventId != null)
        {
            surveyResponses = await surveyTransactionRepository.GetSurveyResponsesOnFaEventId(assetReallocation.FAEventId.Value, currentUser.CompanyId);
        }

        var responseList = new List<AssetReallocationDetails>();

        if (surveyResponses == null || !surveyResponses.Any() || assetReallocation.FAEventId == null)
        {
            responseList.Add(new AssetReallocationDetails
            {
                Outlet = outletDetail.OutletName,
                NewOutletId = assetReallocation.NewOutletId,
                AssetReferenceNo = assetReallocation.AssetReferenceNo,
                AssetTypeId = assetReallocation.AssetTypeId,
                EquipmentId = assetReallocation.EquipmentId,
                Reason = assetReallocation.Reason,
                Question = null,
                Answer = null,
                QuestionType = 0
            });

            return responseList;
        }

        var questionIds = surveyResponses.First().SurveyResponseItems.Select(s => s.QuestionId).ToList();
        var surveyId = surveyResponses.First().FormId;
        var surveyItems = surveyResponses.First().SurveyResponseItems;
        var questions = await surveyRepository.GetAllSurveyQuestions(surveyId ?? 0);
        var questionDictionary = questions.ToDictionary(x => x.QuestionId, x => x);
        var surveyItemsWithOptions = new List<SurveyResponseItem>();

        foreach (var i in surveyItems)
        {
            var optionsList = i.Options is not null and not "" ? i.Options.Split(',') : null;

            if (optionsList != null && optionsList.Length > 0)
            {
                for (var j = 0; j < optionsList.Length; j++)
                {
                    if (!string.IsNullOrEmpty(optionsList[j]))
                    {
                        i.Options = optionsList[j];
                        surveyItemsWithOptions.Add(i);
                    }
                    else
                    {
                        surveyItemsWithOptions.Add(i);
                    }
                }
            }
            else
            {
                surveyItemsWithOptions.Add(i);
            }
        }
        responseList = surveyItemsWithOptions.Select(a => new AssetReallocationDetails
        {
            Question = questionDictionary[a.QuestionId].Title,
            Answer = a.Text?.Length > 0
                ? a.Text
                : a.Options?.Length > 0
                    ? questionDictionary[a.QuestionId].Choices.FirstOrDefault(choice => choice.Id == Convert.ToInt64(a.Options))?.Name
                    : a.Image?.Length > 0
                        ? a.Image
                        : null,
            Outlet = outletDetail.OutletName,
            QuestionType = questionDictionary[a.QuestionId].QuestionType,
            NewOutletId = assetReallocation.NewOutletId,
            AssetReferenceNo = assetReallocation.AssetReferenceNo,
            AssetTypeId = assetReallocation.AssetTypeId,
            EquipmentId = assetReallocation.EquipmentId,
            Reason = assetReallocation.Reason,
        }).ToList();

        return responseList;
    }



    public async Task<RepositoryResponse> ApproveRejectAssetReallocationRequest(long requestId, bool isApproved){
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesNewApproval = companySettings.CompanyUsesNewApprovalMechanism;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;
        if (companyUsesNewApproval)
        {
            if (isRequestedByAdmin)
            {
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, ApprovalEngineRequesType.AssetReallocation);
            }
            else
            {
                var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
                var posIds = positions.Select(e => e.Id).ToList();
                await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, ApprovalEngineRequesType.AssetReallocation);
            }
            await ProcessForApprovalEngineQueue(requestId, ApprovalEngineRequesType.AssetReallocation, isApproved, isRequestedByAdmin, companyUsesHCCBUserFlows: companySettings.CompanyUsesHCCBUserFlows, additionRequest: null);
            return GetSuccessResponse(requestId, "Request sent to Queue for Approval.");
        }
        else
        {
            return await assetReallocationTransactionRepository.ApproveRejectAssetReallocationRequest(requestId, isApproved);
        }
    }

    #endregion Asset Reallocation Request

    #region Approval Request

    public async Task<RepositoryResponse> ApproveApprovalRequest(long requestId, ApprovalEngineRequesType requestType)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesHCCBUserFlows = companySettings.CompanyUsesHCCBUserFlows;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;
        if (isRequestedByAdmin)
        {
            await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, requestType);
        }
        else
        {
            var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
            var posIds = positions.Select(e => e.Id).ToList();
            await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, requestType);
        }
        await ProcessForApprovalEngineQueue(requestId, requestType, true, isRequestedByAdmin, companyUsesHCCBUserFlows: companyUsesHCCBUserFlows);
        return GetSuccessResponse(requestId, "Approved Successfully");
    }

    public async Task<RepositoryResponse> DisapproveApprovalRequest(long requestId, ApprovalEngineRequesType requestType, string reason)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var userHaveAdminPrivileges = adminRepository.UserHaveAdminPrivileges(currentUser.UserRole);
        var companyUsesHCCBUserFlows = companySettings.CompanyUsesHCCBUserFlows;
        var isRequestedByAdmin = userHaveAdminPrivileges || currentUser.UserRole == PortalUserRole.RegionalAdmin;
        if (isRequestedByAdmin)
        {
            await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessorForAdmin(currentUser.CompanyId, requestId, requestType);
        }
        else
        {
            var positions = await positionCodeRepository.GetPositionCodesForEmployee(currentUser.LocalId, currentUser.CompanyId);
            var posIds = positions.Select(e => e.Id).ToList();
            await requestsAndAlertsRepository.UpdateRequestToPendingAtProcessor(currentUser.CompanyId, requestId, posIds, requestType);
        }
        await ProcessForApprovalEngineQueue(requestId, requestType, false, isRequestedByAdmin, reason, companyUsesHCCBUserFlows: companyUsesHCCBUserFlows);
        return GetSuccessResponse(requestId, "Disapproved Successfully");
    }

    public async Task<ApprovalRequestDto> GetApprovalRequestById(long requestId, ApprovalEngineRequesType requestType) => await requestsAndAlertsRepository.GetApprovalRequestById(requestId, requestType, currentUser.CompanyId);

    public async Task<List<ApprovalRequestDetailsDto>> GetApprovalRequests(ApprovalEngineRequesType requestType, bool showArchieved) => await requestsAndAlertsRepository.GetApprovalRequests(showArchieved, requestType, currentUser.CompanyId);

    public async Task<List<ApprovalRequestDetailsDto>> GetOpenApprovalRequests(ApprovalEngineRequesType requestType) => await requestsAndAlertsRepository.GetOpenApprovalRequests(requestType, currentUser.CompanyId);

    #endregion Approval Request
}
