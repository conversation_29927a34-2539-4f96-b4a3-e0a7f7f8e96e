﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class ImageRecognitionLogicService(
    ICurrentUser currentUser,
    IImageRecognitionLogicsRepository imageRecognitionLogicsRepository) : RepositoryResponse
{
    public async Task<List<ImageRecognitionLogicList>> GetAllImageRecognitionLogics(bool includeDeactive) => await imageRecognitionLogicsRepository.GetAllImageRecognitionLogics(currentUser.CompanyId, includeDeactive);

    public async Task<ImageRecognitionLogicsView> GetImageRecognitionLogicById(long id) => await imageRecognitionLogicsRepository.GetImageRecognitionLogicById(id, currentUser.CompanyId);

    public async Task<RepositoryResponse> ActivateDeactivateImageRecognitionLogic(long id, bool action) => await imageRecognitionLogicsRepository.ActivateDeactivateImageRecognitionLogic(currentUser.CompanyId, id, action);

    public async Task<RepositoryResponse> CreateUpdateImageRecognitionLogic(ImageRecognitionLogicsView iRLogic)
    {
        if (iRLogic.Id != 0)
        {
            return await imageRecognitionLogicsRepository.UdpateImageRecognitionLogic(currentUser.CompanyId, iRLogic);
        }

        return await imageRecognitionLogicsRepository.CreateImageRecognitionLogic(currentUser.CompanyId, iRLogic);
    }
}
