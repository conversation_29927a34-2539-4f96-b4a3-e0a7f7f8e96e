﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IPmsRulesRepository
{
    Task<RepositoryResponse> ActivateDeactivatePmsRule(long ruleId, bool action, long companyId);
    Task<RepositoryResponse> CreateUpdatePmsRule(PmsRuleInput rule, long companyId);
    Task<PmsRule> GetPmsRuleById(long ruleId, long companyId);
    Task<List<PmsRule>> GetPmsRules(long companyId, bool includeDeactive = false);
}
