﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ExternalApiTracking
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public int RequestCount { get; set; }
    public string ApiName { get; set; }
    public string ApiUser { get; set; }
    public long DateKey { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [Column("status_2xx_count")]
    public int? Status2xxCount { get; set; }

    [Column("status_4xx_count")]
    public int? Status4xxCount { get; set; }

    [Column("status_5xx_count")]
    public int? Status5xxCount { get; set; }
}
