﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models
{
    [Table("LMSAccountContacts")]
    public class LMSAccountContact
    {
        [Key]
        public long Id { get; set; }
        [ForeignKey("LMSAccount")]
        public long AccountId { get; set; }
        public virtual LMSAccount LMSAccount { get; set; }

        public long CompanyId { get; set; }

        [Required]
        public string Name { get; set; }

        [StringLength(50)]
        public string Designation { get; set; }

        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [StringLength(20)]
        [Phone]
        public string Mobile { get; set; }

        public string Photo { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string Description { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
