﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IRoutePlanAutomationRepository
{
    Task<List<RoutePlanAutomationDetailDto>> GetRoutePlanAutomationDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<DateTime> GetAutomationRoutePlanStartDate(long id, CancellationToken ct = default);
    Task<List<RoutePlanManualDJPDetailDto>> GetRoutePlanManualDJPDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<RepositoryResponse> SaveRoutePlanAutomationDetail(long userId, long companyId, PortalUserRole userRole, string fileName, string emailId, DateTime startDate, CancellationToken ct = default);
    Task<RepositoryResponse> SaveRoutePlanManualDJPDetail(long userId, long companyId, PortalUserRole userRole, string fileName, string emailId, DateTime startDate, CancellationToken ct = default);
    Task<DateTime?> UpdateAutomaticRoutePlanUploadStatus(long id, CancellationToken cancellationToken = default);
}
