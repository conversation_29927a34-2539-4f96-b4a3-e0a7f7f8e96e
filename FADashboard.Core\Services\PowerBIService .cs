﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class PowerBIService(IPowerBIRepository powerBIRepository, ICurrentUser currentUser) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidToActivateSubscription(long subscriptionId)
    {
        var deactiveSubs = await powerBIRepository.GetBIReportSubScriptions(currentUser.CompanyId, true);
        var activeSubs = await powerBIRepository.GetBIReportSubScriptions(currentUser.CompanyId, false);
        var sub = deactiveSubs.FirstOrDefault(p => p.Id == subscriptionId);

        if (sub == null)
        {
            return new RepositoryResponse
            {
                Id = subscriptionId, ExceptionMessage = $"Subscription with the Id {subscriptionId} doesn't exist", Message = "Subcription Activation failed", IsSuccess = false,
            };
        }

        //Duplicate Name Check
        var activeSubNameList = activeSubs.Select(p => p.Name.NormalizeCaps()).ToList();
        var activeUserRoleList = activeSubs.Where(p => p.BIReportId == sub.BIReportId).Select(x => x.PortalUserRole).ToList();
        if (activeSubNameList.Any(subName => subName.Equals(sub.Name, StringComparison.OrdinalIgnoreCase)) && activeUserRoleList.Contains(sub.PortalUserRole))
        {
            return new RepositoryResponse
            {
                Id = sub.Id, ExceptionMessage = "Subscription Name is not unique", Message = "Subscription Activation Failed!", IsSuccess = false,
            };
        }

        var duplicateActiveSubs = activeSubs.Where(p => p.BIReportId == sub.BIReportId && p.PortalUserRole == sub.PortalUserRole).ToList();

        if (duplicateActiveSubs.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = subscriptionId, ExceptionMessage = "This Report is already subscribed for this User role", Message = "Subcription Activation failed", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = 0, Message = "Valid to activate", IsSuccess = true, };
    }

    private async Task<RepositoryResponse> IsValidForCreateUpdate(BIReportSubscriptionsInput biReport)
    {
        var isvalid = false;
        var activeSubList = await powerBIRepository.GetBIReportSubScriptions(currentUser.CompanyId, false);
        if (biReport.Id != 0 && biReport.Name == activeSubList.FirstOrDefault(p => p.Id == biReport.Id).Name)
        {
            isvalid = true;
        }

        if (biReport.Id != 0)
        {
            activeSubList = activeSubList.Where(p => p.Id != biReport.Id).ToList();
        }

        var inactiveSubList = await powerBIRepository.GetBIReportSubScriptions(currentUser.CompanyId, true);

        //Duplicate Name Check
        var activeSubNameList = activeSubList.Select(p => p.Name.NormalizeCaps()).ToList();
        if (!isvalid && activeSubNameList.Contains(biReport.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = biReport.Id, ExceptionMessage = "Subscription Name is not unique", Message = "Subscription Creation/Updation Failed!", IsSuccess = false,
            };
        }

        // Check : Should not have same userrole and Power BI Report
        var duplicateActiveSubs = activeSubList.Where(p => p.BIReportId == biReport.BIReportId && biReport.PortalUserRoles.Contains(p.PortalUserRole)).ToList();
        var duplicateInactiveSubs = inactiveSubList.Where(p => p.BIReportId == biReport.BIReportId && biReport.PortalUserRoles.Contains(p.PortalUserRole)).ToList();
        if (duplicateActiveSubs.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = biReport.Id,
                ExceptionMessage =
                    $"An active Report/s ({string.Join(", ", duplicateActiveSubs.Select(p => p.Name).ToList())}) already exists for the User role/s {string.Join(", ", duplicateActiveSubs.Select(p => p.PortalUserRole).ToList())} respectively",
                Message = "Subcription Creation/Updation failed",
                IsSuccess = false,
            };
        }

        if (duplicateInactiveSubs.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = biReport.Id,
                ExceptionMessage =
                    $"An Inactive Report/s ({string.Join(", ", duplicateActiveSubs.Select(p => p.Name).ToList())}) already exists for the User role/s {string.Join(", ", duplicateInactiveSubs.Select(p => p.PortalUserRole).ToList())} respectively",
                Message = "Subcription Creation/Updation failed",
                IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = 0, Message = "Valid to Create/Update", IsSuccess = true, };
    }

    public async Task<List<BIReportSubscriptions>> GetBIReportSubScriptions(bool includeDeactivate)
    {
        var BIreports = await powerBIRepository.GetBIReportSubScriptions(currentUser.CompanyId, includeDeactivate);
        return BIreports;
    }

    public async Task<RepositoryResponse> CreateUpdateBIReportSubscription(BIReportSubscriptionsInput biReport)
    {
        var isValid = await IsValidForCreateUpdate(biReport);

        if (!isValid.IsSuccess)
        {
            return isValid;
        }

        if (biReport.Id == 0)
        {
            return await powerBIRepository.CreatBIReportSubscription(biReport, currentUser.CompanyId, currentUser.LocalId);
        }

        return await powerBIRepository.UpdateBIReportSubscription(biReport, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> ActivateDeactivateBIReports(long subscriptionId, bool action)
    {
        if (action)
        {
            var isValidToActivate = await IsValidToActivateSubscription(subscriptionId);
            if (!isValidToActivate.IsSuccess)
            {
                return isValidToActivate;
            }
        }

        return await powerBIRepository.ActivateDeactivateBIReports(subscriptionId, currentUser.CompanyId, action);
    }

    public async Task<BIReportSubscriptions> GetBIReportSubscriptionById(long id) => await powerBIRepository.GetBIReportSubscriptionById(currentUser.CompanyId, id);

    public async Task<RepositoryResponse> PinUnpinBIReport(long reportId, SubscribedScreenType subscribedScreen, bool action) => await powerBIRepository.PinUnpinBIReport(reportId, subscribedScreen, currentUser.CompanyId, action);
    public async Task<BIReportSubscriptions> GetBIReportSubscribedForUserRole() => await powerBIRepository.GetBIReportSubscribedForUserRole(currentUser.CompanyId, currentUser.UserRole);
}
