﻿namespace FADashboard.Core.Helper.AsanaHelpers;
public class AsanaCredentials
{
    private static Dictionary<string, AsanaCredentials> users;

    static AsanaCredentials()
    {
        users = new Dictionary<string, AsanaCredentials>()
        {
            ["Apurv"] = new AsanaCredentials { UserId = "34125064170539", AsanaToken = "0/3b6270abe622749d9855b20178c4ae0a" },
            ["NikhilPatwari"] = new AsanaCredentials { UserId = "34377429457007", AsanaToken = "0/06d48401f8befecd73b6b5ca5c87c13b" },
            ["RajSingh"] = new AsanaCredentials { UserId = "1202450413513857", AsanaToken = null },
            ["AnandKumar"] = new AsanaCredentials { UserId = "1201667943187083", AsanaToken = null },
            ["jagarti"] = new AsanaCredentials { UserId = "1114046766392465", AsanaToken = null },
            ["Sushant"] = new AsanaCredentials { UserId = "1144365084599216", AsanaToken = null },
            ["Afzal"] = new AsanaCredentials { UserId = "688230827563730", AsanaToken = null },
            ["Iftikar"] = new AsanaCredentials { UserId = "365477005840086", AsanaToken = null },
            ["Sirisht"] = new AsanaCredentials { UserId = "1200193495550679", AsanaToken = null },
            ["Chitransh"] = new AsanaCredentials { UserId = "415063439465832", AsanaToken = null },
            ["Anshul"] = new AsanaCredentials { UserId = "1200563755072518", AsanaToken = null },
            ["PankajSharma"] = new AsanaCredentials { UserId = "358695470565196", AsanaToken = null },
            ["Karishma"] = new AsanaCredentials { UserId = "1166155910100828", AsanaToken = null },
            ["Laraib"] = new AsanaCredentials { UserId = "1202581537166577", AsanaToken = null },
            ["ShrutiShama"] = new AsanaCredentials { UserId = "1203045280607615", AsanaToken = null },
            ["NikhilYadav"] = new AsanaCredentials { UserId = "1202425742487021", AsanaToken = null },
            ["RohanSalunkhe"] = new AsanaCredentials { UserId = "1202537970098578", AsanaToken = null },
            ["Nilesh"] = new AsanaCredentials { UserId = "1202457574818540", AsanaToken = null },
            ["Pushpendra"] = new AsanaCredentials { UserId = "1202374516982945", AsanaToken = null },
            ["Shiva"] = new AsanaCredentials { UserId = "1200558694572132", AsanaToken = null },
            ["AnandSamuel"] = new AsanaCredentials { UserId = "570165688012736", AsanaToken = "2/570165688012734/1207572567872759:1c1a2a4c588023e17eeef008caf01a85" },
            ["RakshitSharma"] = new AsanaCredentials { UserId = "1203436980872452", AsanaToken = null },
            ["ChiragSoni"] = new AsanaCredentials { UserId = "1200987156674659", AsanaToken = null },
            ["DeepaliSrivastava"] = new AsanaCredentials { UserId = "1202450413431674", AsanaToken = null },
            ["RahulKumarGautam"] = new AsanaCredentials { UserId = "1201015423276298", AsanaToken = null },
            ["SushantThakur"] = new AsanaCredentials { UserId = "1202060654836589", AsanaToken = null },
            ["RiyaSethi"] = new AsanaCredentials { UserId = "1201667880633298", AsanaToken = null },
            ["PoojaYadav"] = new AsanaCredentials { UserId = "1200702232293180", AsanaToken = null },
            ["ArvindSingh"] = new AsanaCredentials { UserId = "1204337189829293", AsanaToken = null },
            ["BhushanVerma"] = new AsanaCredentials { UserId = "1204409178168550", AsanaToken = null },
            ["ShivangiSingh"] = new AsanaCredentials { UserId = "1167722081035055", AsanaToken = null },
            ["Haritha"] = new AsanaCredentials { UserId = "1201092936871397", AsanaToken = null },
            ["ArijitRay"] = new AsanaCredentials { UserId = "1201612571886444", AsanaToken = null },
            ["ShriniwasTripathi"] = new AsanaCredentials { UserId = "1205284145028518", AsanaToken = null },
            ["SarthakPratapNarayanSingh"] = new AsanaCredentials { UserId = "1205142647315470", AsanaToken = null },
            ["VishuTyagi"] = new AsanaCredentials { UserId = "1165052513964881", AsanaToken = null },
            ["NikhilAggarwal"] = new AsanaCredentials { UserId = "34377429456995", AsanaToken = null },
            ["Mridul"] = new AsanaCredentials { UserId = "1192084099836247", AsanaToken = "***************************************************" },
            ["Divyansh"] = new AsanaCredentials { UserId = "1200558694599701", AsanaToken = null },
            ["Haritha"] = new AsanaCredentials { UserId = "1201092936877562", AsanaToken = null },
            ["Riya"] = new AsanaCredentials { UserId = "1201667880634412", AsanaToken = null },
            ["Pranshu"] = new AsanaCredentials { UserId = "1201600007924904", AsanaToken = null },
            ["KunalTaneja"] = new AsanaCredentials { UserId = "1205802520196342", AsanaToken = null },
            ["AryanSinghChauhan"] = new AsanaCredentials { UserId = "1205498950669022", AsanaToken = null },
            ["subhampandey"] = new AsanaCredentials { UserId = "1203531082979451", AsanaToken = null },
            ["RohanNandal"] = new AsanaCredentials { UserId = "1203612213371516", AsanaToken = null },
            ["AmitUpadhyay"] = new AsanaCredentials { UserId = "1202680569684667", AsanaToken = null },
            ["TanyaGarg"] = new AsanaCredentials { UserId = "1202969247674488", AsanaToken = null },
            ["ShubhamSaurav"] = new AsanaCredentials { UserId = "1203283744919912", AsanaToken = null },
        };
    }

    public static AsanaCredentials GetCredentialsForUser(string username)
    {
        return users[username];
    }

    public string UserId { get; private set; }
    public string AsanaToken { get; private set; }
}
