﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class FieldUserDailyDataPointersModel
{
    public string AssignedBeat { get; set; }
    public string AssignedJointWorkingEmployee { get; set; }
    public string AssignedReason { get; set; }
    public string AssignedReasonCategory { get; set; }
    public string AssignedRoute { get; set; }
    public int CAP { get; set; }
    public string DayStartLocation { get; set; }
    public string DayStartReasonCategory { get; set; }
    public string DayStartTypeDescription { get; set; }
    public DayStartType? DayStartTypeEnum { get; set; }
    public string Distributor { get; set; }
    public long EmployeeId { get; set; }
    public long PositionId { get; set; }
    public string EmployeeName { get; set; }
    public PortalUserRole EmployeeRole { get; set; }
    public DateTime? FirstCallTime { get; set; }
    public string FirstPCTime { get; set; }
    public bool IsJourneyViolated { get; set; }
    public int JointWorkingCalls { get; set; }
    public string JointWorkingEmployee { get; set; }
    public DateTime? LastCallTime { get; set; }
    public string Login { get; set; }
    public string Logout { get; set; }
    public double MTDValue { get; set; }
    public double NetValue { get; set; }
    public double NewOutletSalesInRevenue { get; set; }
    public int NewOutletsCreated { get; set; }
    public int NoOfOtherActivities { get; set; }
    public double OrderInStdUnits { get; set; }
    public int PC { get; set; }
    public double PercentOVC { get; set; }
    public double PercentOVT { get; set; }
    public int PhycialCalls { get; set; }
    public double Productivity { get; set; }
    public string Reason { get; set; }
    public string ReasonCategory { get; set; }
    public int SC { get; set; }
    public double SchProductivity { get; set; }
    public string SelectedBeat { get; set; }
    public int? SelectedJourneyOutlets { get; set; }
    public string SelectedRoute { get; set; }
    public int TC { get; set; }
    public int TelephonicOrders { get; set; }
    public double TotalSchemeQty { get; set; }
    public string TotalTime { get; set; }
    public double? FillratePer { get; set; }
    public int? DC { get; set; }
    public double OrderInSuperUnits { get; set; }
    public string LogOutTime { get; set; }
    public double? CAPAdherancePer { get; set; }
}
