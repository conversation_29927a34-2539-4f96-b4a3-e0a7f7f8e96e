﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class DynamicCarouselBannerService(IDynamicCarouselBannerRepository dynamicCarouselBannerRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<DynamicBanners> GetDynamicCarouselById(long dynamicBannerId) => await dynamicCarouselBannerRepository.GetDynamicCarouselById(dynamicBannerId, currentUser.CompanyId);
    public async Task<List<DynamicBanners>> GetDynamicCarouselInfo(bool includeDeactive = true) => await dynamicCarouselBannerRepository.GetDynamicCarouselInfo(currentUser.CompanyId, includeDeactive);
    public async Task<RepositoryResponse> CreateUpdateDynamicCarouselBanners(DynamicBanners dynamicBanner) => await dynamicCarouselBannerRepository.CreateUpdateDynamicCarouselBanners(dynamicBanner, currentUser.CompanyId);
    public async Task<RepositoryResponse> ActivateDeactivateDynamicCarouselBanner(long bannerId, bool action) => await dynamicCarouselBannerRepository.ActivateDeactivateDynamicCarouselBanner(bannerId, action, currentUser.CompanyId);
}
