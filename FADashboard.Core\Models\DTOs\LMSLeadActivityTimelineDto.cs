﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadActivityTimelineDto
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long LeadId { get; set; }
        public LMSActivityType ActivityType { get; set; }
        public LMSActivitySource Source { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Remark { get; set; }
        public List<string> Attachment { get; set; }
        public DateTime? MeetingStartTime { get; set; }
        public DateTime? MeetingEndTime { get; set; }
        public int? MeetingStatus { get; set; }
        public LMSMeetingLocation? MeetingLocation { get; set; }
        public long? CallDuration { get; set; }
        public LMSCallType? CallType { get; set; }
        public long? LeadContactId { get; set; }
        public long? ActivityMasterId { get; set; }
        public long? TaskOwner { get; set; }
        public string TaskOwnerName { get; set; }
        public long? PositionCode { get; set; }
        public LMSTaskStatus? TaskStatus { get; set; }
        public LMSTaskPriority? Priority { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ClosedDateTime { get; set; }
        public List<AuditTrailDetailDto> AuditTrail { get; set; }
        public bool? isOVC { get; set; }
        public long CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
