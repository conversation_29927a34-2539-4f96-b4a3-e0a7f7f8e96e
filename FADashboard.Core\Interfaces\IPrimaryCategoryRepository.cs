﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IPrimaryCategoryRepository
{
    Task<RepositoryResponse> ActivateDeactivatePrimaryCategory(long id, long companyId, bool action);

    Task<RepositoryResponse> CreatePrimaryCat(ProductCategoryDivision pc, long companyId);

    Task<List<ProductCategoryDivision>> GetDetailedPrimaryCategories(long companyId, bool includeDeactivate = false);

    Task<List<EntityMinWithErp>> GetPrimaryCategoriesErpMin(long companyId, bool includeDeactivate = false);
    Task<List<EntityMinWithStatus>> GetPrimaryCategoriesMin(long companyId, bool includeDeactivate);

    Task<ProductCategoryDivision> GetPrimaryCategoryById(long companyId, long id);

    Task<List<ProductCategoryDivision>> GetPrimaryCategoryForDivisionId(long companyId, long divId);

    Task<List<ProductCategoryDivision>> GetProductsForSecCatId(long companyId, long secCatId);

    Task<List<ProductCategoryDivision>> GetSecondaryCategoryForPrimaryCatId(long companyId, long primCatId);

    Task<RepositoryResponse> UpdatePrimaryCat(ProductCategoryDivision pc, long companyId);
    Task<List<EntityMinGuidWithErp>> GetProductPrimaryCategoriesByErpCodes(long companyId, List<string> productErpCodes);
    Task UpdatePrimaryCatImages(List<EntityMinGuidWithErp> dbProduct, long companyId);

}
