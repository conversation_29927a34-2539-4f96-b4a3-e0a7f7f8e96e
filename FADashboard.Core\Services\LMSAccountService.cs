﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.Enums;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSAccountService(ILMSAccountRepository lmsAccountRepository, ILMSLeadRepository lmsLeadRepository, ILMSCustomFieldValueService customFieldValueService) : ILMSAccountService
    {
        public async Task<LMSAccountDto> GetAccountByIdAsync(long id) => await lmsAccountRepository.GetByIdAsync(id);

        public async Task<LMSAccountDto> GetAccountDetailByIdAsync(long id) => await lmsAccountRepository.GetAccountDetailByIdAsync(id);

        public async Task<IEnumerable<LMSAccountDto>> GetAccountsByCompanyIdAsync(long companyId) => await lmsAccountRepository.GetByCompanyIdAsync(companyId);

        public async Task<LMSAccountDto> CreateAccountAsync(LMSAccountInput accountInput, long companyId, long createdByUserId)
        {
            ArgumentNullException.ThrowIfNull(accountInput);

            if (!await lmsAccountRepository.IsAccountNameUniqueAsync(accountInput.AccountName, companyId))
            {
                throw new InvalidOperationException("An account with this name already exists.");
            }

            if (!await lmsAccountRepository.IsEmailUniqueAsync(accountInput.Email, companyId))
            {
                throw new InvalidOperationException("An account with this email already exists.");
            }

            var accountDto = new LMSAccountDto
            {
                CompanyId = companyId,
                AccountName = accountInput.AccountName,
                AssignedTo = accountInput.AssignedTo,
                PositionCode = accountInput.PositionCodeId,
                Email = accountInput.Email,
                Website = accountInput.Website,
                AnnualRevenue = accountInput.AnnualRevenue,
                Description = accountInput.Description,
                AccountTemplateId = accountInput.AccountTemplateId,
                AccountImage = accountInput.AccountImage,
                IsActive = true,
                IsDeleted = false,
                CreatedBy = createdByUserId,
                CreatedAt = DateTime.UtcNow
            };

            var createdAccount = await lmsAccountRepository.AddAsync(accountDto);

            if (accountInput.CustomFieldValues != null && accountInput.CustomFieldValues.Count != 0)
            {
                await customFieldValueService.UpsertRangeForEntityAsync((int)LMSCustomFieldsEntityType.Account, createdAccount.Id, accountInput.CustomFieldValues, createdByUserId);
            }

            return createdAccount;
        }

        public async Task<LMSAccountDto> UpdateAccountAsync(long id, LMSAccountInput accountInput, long companyId, long? updatedByUserId)
        {
            var existingAccount = await lmsAccountRepository.GetByIdAsync(id);
            if (existingAccount == null)
            {
                throw new KeyNotFoundException($"Account with ID {id} not found.");
            }

            if (!await lmsAccountRepository.IsAccountNameUniqueAsync(accountInput.AccountName, companyId, id))
            {
                throw new InvalidOperationException("An account with this name already exists.");
            }

            if (!await lmsAccountRepository.IsEmailUniqueAsync(accountInput.Email, companyId, id))
            {
                throw new InvalidOperationException("An account with this email already exists.");
            }

            // Map fields from input to existing DTO
            existingAccount.AccountName = accountInput.AccountName;
            existingAccount.AssignedTo = accountInput.AssignedTo;
            existingAccount.Email = accountInput.Email;
            existingAccount.Website = accountInput.Website;
            existingAccount.AnnualRevenue = accountInput.AnnualRevenue;
            existingAccount.Description = accountInput.Description;
            existingAccount.AccountTemplateId = accountInput.AccountTemplateId;
            existingAccount.AccountImage = accountInput.AccountImage;
            existingAccount.UpdatedBy = updatedByUserId;
            existingAccount.UpdatedAt = DateTime.UtcNow;

            var updatedAccount = await lmsAccountRepository.UpdateAsync(existingAccount);

            if (accountInput.CustomFieldValues != null)
            {
                await customFieldValueService.UpsertRangeForEntityAsync((int)LMSCustomFieldsEntityType.Account, id, accountInput.CustomFieldValues, updatedByUserId ?? 0);
            }

            return updatedAccount;
        }

        public async Task<bool> DeleteAccountAsync(long id, long? updatedByUserId)
        {
            if (await lmsLeadRepository.HasLeadsAsync(id))
            {
                throw new InvalidOperationException("Cannot delete account with active leads. Please remove or reassign them first.");
            }

            var accountDto = await lmsAccountRepository.GetByIdAsync(id);
            if (accountDto == null)
            {
                return false;
            }

            accountDto.IsDeleted = true;
            accountDto.UpdatedBy = updatedByUserId;
            accountDto.UpdatedAt = DateTime.UtcNow;

            await lmsAccountRepository.UpdateAsync(accountDto);
            return true;
        }

        public async Task<IEnumerable<LMSAccountDto>> GetAccountsByAccountOwnerAsync(long accountOwnerId) =>
            await lmsAccountRepository.GetByAccountOwnerAsync(accountOwnerId);

        public async Task<IEnumerable<LMSAccountDto>> GetAccountsByPositionCodeAsync(long positionCode) =>
            await lmsAccountRepository.GetByPositionCodeAsync(positionCode);

        public async Task<PagedResult<LMSAccountDto>> GetAccountsAsync(long companyId, LMSAccountQueryParameters queryParameters) =>
            await lmsAccountRepository.GetAccountsAsync(companyId, queryParameters);
    }
}
