﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IAssetReallocationTransactionRepository
{
    Task<List<Requests>> GetAllAssetReallocationRequests(long userId, PortalUserRole userRole, long companyId, bool showArchived);
    Task<AssetReallocation> GetAssetReallocationRequestById(long id, long companyId);
    Task<RepositoryResponse> ApproveRejectAssetReallocationRequest(long requestId, bool isApproved);
}
