﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class DayStartMin
{
    public Guid? SessionId { get; set; }

    public long EmployeeId { get; set; }

    public DayStartType DayStartType { get; set; }

    public DateTimeOffset DayStartTime { get; set; }
    public DateTimeOffset? DayEndTime { get; set; }
    public string QualifiedDateKey { get; set; }
    public int SC { get; set; }
    public long? JWEmployeeId { get; set; }
    public string ReasonCategory { get; set; }
    public PortalUserRole UserRole { get; set; }
    public long? SelectedBeatId { get; set; }
    public DayEndType? DayEndType { get; set; }
    public string DayStartAddress { get; set; }
    public string DayEndAddress { get; set; }
    public string Reason { get; set; }
    public long? AssignedJWEmployeeId { get; set; }
    public string TourPlanReason { get; set; }
    public string TourPlanReasonCategory { get; set; }
    public long? AssignedBeatId { get; set; }
    public long? AssignedRouteId { get; set; }
    public long? RouteId { get; set; }
    public bool IsNormallyEnded { get; set; }
    public decimal? DayStartLatitude { get; set; }
    public decimal? DayStartLongitude { get; set; }
    public decimal? DayEndLatitude { get; set; }
    public decimal? DayEndLongitude { get; set; }
    public bool IsTourPlanAdhered { get; set; }
    public bool IsSwitched { get; set; }
    public int? SelectedJourneyOutlets { get; set; }
    public bool IsWFH { get; set; }
    public long? PositionCodeId { get; set; }
    public string DaySelfiImage { get; set; }
}

public class DayRecordsMin
{
    public Guid? SessionId { get; set; }
    public string EmployeeName { get; set; }
    public string PositionName { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
    public string SelectedBeatName { get; set; }
    public long EmployeeId { get; set; }
    public long PositionCodeId { get; set; }
    public long? ParentId { get; set; }
    public string ParentName { get; set; }
    public long SelectedBeatId { get; set; }
    public string DayStartType { get; set; }
    public DateTime CreatedAt { get; set; }
    public string ReasonCategory { get; set; }
    public bool IsDayStart { get; set; }
}

public class DayRecordsMinWithCount
{
    public List<DayRecordsMin> DayRecords { get; set; }
    public int Count { get; set; }
}
