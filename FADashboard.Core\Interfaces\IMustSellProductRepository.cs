﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IMustSellProductRepository
{
    Task<RepositoryResponse> CreateMustSellProduct(MustSellProductInput mustSellProduct, long companyId);

    Task<RepositoryResponse> DeactivateMustSellProduct(long mustSellProductId, long companyId);

    Task<MustSellProductInput> GetMustSellProductById(long mustSellProductId, long companyId);

    Task<List<MustSellProductList>> GetMustSellProducts(long companyId, bool includeDeactivate);

    Task<RepositoryResponse> UpdateMustSellProduct(MustSellProductInput mustSellProduct, long companyId);
}
