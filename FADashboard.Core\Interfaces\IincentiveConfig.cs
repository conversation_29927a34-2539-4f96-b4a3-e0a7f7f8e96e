﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces
{
    public interface IincentiveConfig
    {
        Task<List<IncentiveConfig>> GetRules(long companyId, bool includeDeactivate, CancellationToken ct = default); 
        Task<List<Cohorts>> GetCohorts(long companyId, CancellationToken ct = default);
        Task<List<QualifiersDetails>> GetQualifierDetails(long companyId, CancellationToken ct = default);
        Task<List<FaRewardsModel>> GetRewards(long companyId, CancellationToken ct = default);

        Task<RepositoryResponse> CreateRule(CreateIncentiveConfig rule, long CompanyId,CancellationToken ct = default);
        Task<RepositoryResponse> UpdateRule(PerfectEntityRule rule, CancellationToken ct);

    }
}
