﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FAProductGroups")]
public class ProductGroup
{
    [Key]
    public long Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public long CompanyId { get; set; }
    public string ERPId { get; set; }
    public string AttributeText1 { get; set; }
    public int Sequence { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
