﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class AssetDefinitionsList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string AssetType { get; set; }
    public long ValueCapacity { get; set; }
    public long VolumeCapacity { get; set; }
    public bool? IsActive { get; set; }
    public string ShortName { get; set; }
    public bool IsIRAsset { get; set; }
    public bool IsOutletAsset { get; set; }
    public FactoryProductType? EntityType { get; set; }
    public List<long?> EntityIds { get; set; }
    public long? AssetTypeId { get; set; }
    public bool AutoGenerateReferenceNumber { get; set; }
}

public class AssetRuleDefinitionsView
{
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public long HighLimit { get; set; }
    public long MediumLimit { get; set; }
}
