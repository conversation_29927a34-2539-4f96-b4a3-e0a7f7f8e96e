﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IPositionBeatRepository
{
    Task<RepositoryResponse> AttachBeats(long companyId, long positionCodeId, List<long> beatIds, long? productDivisionId, CompanySettings companySettings);
    Task<RepositoryResponse> AttachSecondaryPositionBeatMapping(long companyId, long positionCodeId, List<long> beatIds, long? productDivisionId);

    Task<List<EntityMin>> GetBeatForPosition(long positionId, long companyId);
    Task<List<EntityMin>> GetSecondaryBeatForPosition(long positionId, long companyId);
    Task<Dictionary<long, List<PositionBeatMapping>>> GetPositionBeatMappingDic(long companyId, List<long> positionIds);

    Task<List<EntityMin>> GetPositionForBeats(long beatId, long companyId);

    Task<List<EntityMin>> GetProductDivisionsForPositions(List<long> positionCodeIds, long companyId);
    Task<List<PositionBeatMapping>> GetPositionBeatMappings(long companyId, List<long> beatIds, long? productDivisionId);

    Task<RepositoryResponse> AttachTemporaryBeats(long companyId, long positionCodeId, List<long> beatIds, long? productDivisionId, CompanySettings companySettings);
    Task<long> GetPositionBeatMappingCount(long companyId, PaginationFilter validFilter);
}
