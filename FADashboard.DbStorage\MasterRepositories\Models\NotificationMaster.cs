﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Encodings.Web;
using System.Text.Json;
using AuditHelper;
using EntityHelper;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FaEngageNotificationMasters")]
public class NotificationMaster : IAuditedEntity, IDeletable
{
    public long Id { get; set; }

    [StringLength(128)]
    [Audited]
    public string Name { get; set; }

    [StringLength(512)]
    public string Description { get; set; }

    public long CompanyId { get; set; }

    [Audited]
    public NotificationCondition NotificationType { get; set; }

    [Audited]
    public bool Deleted { get; set; }

    [Audited]
    public UserPlatform UserPlatform { get; set; }

    [StringLength(512)]
    [Audited]
    public string CohortIds { get; set; }

    [StringLength(32)]
    [Audited]
    public string Cron { get; set; }

    [Audited]
    public DateTime StartDate { get; set; }

    [Audited]
    public DateTime EndDate { get; set; }

    [Audited]
    public bool IsAggregated { get; set; }

    [StringLength(32)]
    [Audited]
    public string AggregationCron { get; set; }
    [Audited]
    public int? AggregationDelay { get; set; }

    [StringLength(32)]
    [Audited]
    public string ManagerLevel { get; set; }

    public Guid Guid { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public ICollection<KPITriggers> KPITriggers { set; get; }
    public ICollection<NotificationMessage> NotificationMessages { set; get; }

    public static NotificationMaster CreateDbModel(NotificationCreateInput notification, Guid newGuid, long companyId)
    {
        var notificationMessagesList = new List<NotificationMessage>();
        var options = new JsonSerializerOptions { Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping};

        // Notification Message object of user
        if (notification.NotificationMessages is { MessageText.Count: > 0 })
        {
            notificationMessagesList.Add(
                new NotificationMessage
                {
                    CompanyId = companyId,
                    NotificationApp = notification.NotificationMessages.NotificationApp,
                    Title = notification.NotificationMessages.Title,
                    NotificationType = notification.NotificationMessages.AppNotificationType,
                    AppScreen = notification.NotificationMessages.AppScreen,
                    MessageText = JsonSerializer.Serialize(notification.NotificationMessages.MessageText, options),
                    Uri = notification.NotificationMessages.Uri,
                    OnClickScreen = notification.NotificationMessages.OnClickScreen,
                    Emoji = notification.NotificationMessages.Emoji,
                    IsDeactive = false,
                    IsAggregated = false,
                    ForWhatsapp = notification.NotificationMessages.ForWhatsapp,
                    TemplateID = notification.NotificationMessages.TemplateID,
                    WhatsappVariables = JsonSerializer.Serialize(notification.NotificationMessages.WhatsappVariables),
                    Image = notification.NotificationMessages.Image,
                }
            );
        }

        // Notification Message object of Aggregated manager
        if (notification.IsAggregated && notification.AggregatedMessages is { MessageText.Count: > 0 })
        {
            notificationMessagesList.Add(
                new NotificationMessage
                {
                    CompanyId = companyId,
                    NotificationApp = notification.AggregatedMessages.NotificationApp,
                    Title = notification.AggregatedMessages.Title,
                    NotificationType = notification.AggregatedMessages.AppNotificationType,
                    AppScreen = notification.AggregatedMessages.AppScreen,
                    MessageText = JsonSerializer.Serialize(notification.AggregatedMessages.MessageText, options),
                    Uri = notification.AggregatedMessages.Uri,
                    OnClickScreen = notification.AggregatedMessages.OnClickScreen,
                    Emoji = notification.AggregatedMessages.Emoji,
                    IsDeactive = false,
                    IsAggregated = true,
                    ForWhatsapp = notification.AggregatedMessages.ForWhatsapp,
                    TemplateID = notification.AggregatedMessages.TemplateID,
                    WhatsappVariables = JsonSerializer.Serialize(notification.AggregatedMessages.WhatsappVariables),
                }
            );
        }

        var createModel = new NotificationMaster
        {
            Name = notification.Name,
            Description = notification.Description,
            CompanyId = companyId,
            NotificationType = notification.NotificationType,
            Deleted = false,
            UserPlatform = notification.UserPlatform,
            CohortIds = JsonSerializer.Serialize(notification.CohortIds),
            Cron = notification.Cron,
            StartDate = notification.StartDate,
            EndDate = notification.EndDate.AddDays(1).AddMinutes(-1),
            Guid = newGuid,
            IsAggregated = notification.IsAggregated,
            AggregationCron = notification.AggregationCron,
            AggregationDelay = notification.AggregationDelay,
            ManagerLevel = notification.ManagerLevel is { Count: > 0 } ? JsonSerializer.Serialize(notification.ManagerLevel) : null,

            KPITriggers = notification.KPITriggers.Select(s => new KPITriggers
            {
                KpiId = s.KpiId,
                ComparisonOperator = s.ComparisonOperator,
                Value = s.Value,
                CompanyId = companyId,
                IsDeactive = false,
            }).ToList(),
            NotificationMessages = notificationMessagesList,
        };

        return createModel;
    }
}

[Table("FaEngageKPITriggers")]
public class KPITriggers : IAuditedEntity, IDeactivatable
{
    public long Id { get; set; }
    public long KpiId { get; set; }

    [Audited]
    public ComparisonOperator ComparisonOperator { get; set; }

    [StringLength(512)]
    public string Value { get; set; }

    public long NotificationId { get; set; }
    public long CompanyId { get; set; }
    public virtual NotificationMaster Notification { get; set; }
    public virtual KPI Kpi { get; set; }

    [Audited]
    public bool IsDeactive { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}

[Table("FaEngageNotificationMessages")]
public class NotificationMessage : IAuditedEntity, IDeactivatable
{
    public long Id { get; set; }
    public long NotificationId { get; set; }
    public long CompanyId { get; set; }

    [Audited]
    public UserPlatform NotificationApp { get; set; }

    [StringLength(64)]
    [Audited]
    public string Title { get; set; }

    [Audited]
    public AppNotificationType? NotificationType { get; set; }

    [Audited]
    public int? AppScreen { get; set; }

    [StringLength(3072)]
    [Audited]
    public string MessageText { get; set; }

    [StringLength(512)]
    [Audited]
    public string Uri { get; set; }

    [Audited]
    public int? OnClickScreen { get; set; }

    [StringLength(16)]
    [Audited]
    public string Emoji { get; set; }
    [Audited]
    public bool IsAggregated { get; set; }

    public virtual NotificationMaster Notification { get; set; }

    [Audited]
    public bool IsDeactive { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool ForWhatsapp { get; set; }

    [StringLength(1024)]
    public string TemplateID { get; set; }

    [StringLength(1024)]
    public string WhatsappVariables { get; set; }
    public string Image { get; set; }

}
