using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.ApiModels;

public class PmsRuleInput
{
    public long Id { get; set; }

    [Required(ErrorMessage = "Name is required")]
    public string Name { get; set; }

    [Required(ErrorMessage = "Cron expression is required")]
    public string Cron { get; set; }

    [Required(ErrorMessage = "Message is required")]
    public string Message { get; set; }

    public long CompanyId { get; set; }

    [Required(ErrorMessage = "Manager message is required")]
    public string ManagerMessage { get; set; }

    [Required(ErrorMessage = "Field user message is required")]
    public string FieldUserMessage { get; set; }

    public string RuleVariables { get; set; }
}
