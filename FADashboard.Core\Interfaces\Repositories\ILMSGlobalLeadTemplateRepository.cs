﻿using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.Enums;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSGlobalLeadTemplateRepository
    {
        Task<LMSGlobalLeadTemplateDto> GetByIdAsync(long id);
        Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetAllAsync();
        Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetByCompanyIdAsync(long companyId);
        Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetByCompanyIdAndTypeAsync(long companyId, LMSTemplateType templateType);
        Task<LMSGlobalLeadTemplateDto> AddAsync(LMSGlobalLeadTemplateDto dto);
        Task<LMSGlobalLeadTemplateDto> UpdateAsync(LMSGlobalLeadTemplateDto dto);
        Task<bool> DeleteAsync(long id, long? updatedByUserId);
    }
}
