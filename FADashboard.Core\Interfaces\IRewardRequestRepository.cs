﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IRewardRequestRepository
{
    Task<List<EnhancedRewardRequestDto>> GetRewardRequestList(bool showArchived, long companyId, PortalUserRole userRole, TypeofDistributorMapping distributorMappingType);
    Task<List<RewardRequestDetailDto>> GetRewardRequestDetailsByDistributors(List<long> distributorIds, long companyId, TypeofDistributorMapping distributorMappingType);
}
