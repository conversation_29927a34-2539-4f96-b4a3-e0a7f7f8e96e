﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class AttendanceOutletDetails
{
    public double Discount { get; set; }
    public string DiscountWithUnits { get; set; }
    public double NetOrderInRevenue { get; set; }
    public string NetOrderInRevenueWithUnits { get; set; }
    public double OrderInRevenue { get; set; }
    public string OrderInRevenueWithUnits { get; set; }
    public double OrderInUnits { get; set; }
    public string OrderWithUnits { get; set; }
    public double OrderInStdUnits { get; set; }
    public string OrderWithStdUnits { get; set; }
    public string PrimaryCategory { get; set; }
    public string Product { get; set; }
    public string Unit { get; set; }
    public string SecondaryCategory { get; set; }
    public string StandardUnit { get; set; }
}

public class AttendanceSummaryTransaction
{
    public string BeatName { get; set; }
    public string Description { get; set; }
    public string Discount { get; set; }
    public double Discount_double { get; set; }
    public DateTime EndTime { get; set; }
    public string EndTimeDisplay { get; set; }
    public bool IsOvc { get; set; }
    public bool IsOvt { get; set; }
    public bool IsProductive { get; set; }
    public bool IsTelephonic { get; set; }
    public long LocationId { get; set; }
    public string NetValue { get; set; }
    public double NetValue_double { get; set; }
    public string NetValue_DisplayName { get; set; }
    public long? OrderId { get; set; }
    public string OwnersName { get; set; }
    public string PhoneNo { get; set; }
    public string Qty { get; set; }
    public string QtyStdUnit { get; set; }
    public string QtyStdUnit_DisplayName { get; set; }
    public string ShopName { get; set; }
    public DateTime StartTime { get; set; }
    public string StartTimeDisplay { get; set; }
    public long? SurveyId { get; set; }
    public string SurveyName { get; set; }
    public DateTime Time { get; set; }
    public string TimeSpent { get; set; }
    public TimeSpan TimeSpentTimeSpan { get; set; }
    public string Type { get; set; }
    public string Value { get; set; }
    public double Value_double { get; set; }
    public string Value_DisplayName { get; set; }
}

public class EmployeeSummaryDetails
{
    public string BeatName { get; set; }
    public DateTime Date { get; set; }
    public string DiscountedSales { get; set; }
    public long EmployeeId { get; set; }
    public long LocationId { get; set; }
    public string OutletName { get; set; }
    public string OverallDiscount { get; set; }

    public bool OVT { get; set; }
    public string SalesQuantity { get; set; }
    public string SalesStdUnit { get; set; }
    public string SalesValues { get; set; }
    public bool Telephonic { get; set; }

    public string Time { get; set; }
}

public class TotalAttendanceOutletDetails
{
    public List<AttendanceOutletDetails> AttendanceOutletDetails { get; set; }
    public string TotalDiscount { get; set; }
    public string TotalNetOrderInRevenue { get; set; }
    public string TotalOrderInRevenue { get; set; }
    public string TotalOrderInUnits { get; set; }
    public string TotalOrderInStdUnits { get; set; }
}

public class TotalEmployeeSummaryDetails
{
    public List<EmployeeSummaryDetails> EmployeeSummaryDetails { get; set; }
    public string TotalDiscountedSales { get; set; }
    public string TotalOverallDiscount { get; set; }
    public string TotalUnitQuantity { get; set; }
    public string TotalStdUnitQuantiy { get; set; }
    public string TotalSalesValues { get; set; }
}

public class EmployeeSummaryWithPositions
{
    public string ContactNo { get; set; }
    public string ErpId { get; set; }
    public long EmployeeId { get; set; }
    public bool IsActive { get; set; }
    public bool IsBillable { set; get; }
    public bool IsFieldAppuser { get; set; }
    public string EmployeeName { get; set; }
    public string RegionName { set; get; }
    public EmployeeType UserType { set; get; }
    public bool IsTrainingUser { get; set; }
    public string PositionName { get; set; }
    public PositionCodeLevel? PositionLevel { get; set; }
}
