﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class AuditService(ICurrentUser currentUser, IUserLoginActivitiesRepository userLoginActivitiesRepository)
{
    public async Task<RepositoryResponse> AuditManagerLogin(Guid? sessionId, string ipAddress, ClientPlatform clientPlatform = ClientPlatform.Dashboard) => await userLoginActivitiesRepository.AddUserLoginActivity(currentUser.CompanyId,
        currentUser.LocalId, currentUser.UserRole,
        sessionId, ipAddress, clientPlatform);

    public async Task<RepositoryResponse> EndExistingSessions(long userId) => await userLoginActivitiesRepository.EndExistingSessions(currentUser.CompanyId, userId);
}
