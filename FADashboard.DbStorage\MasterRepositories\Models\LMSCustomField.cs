using System;

namespace FADashboard.DbStorage.MasterRepositories.Models
{
    public class LMSCustomField
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public int EntityType { get; set; }
        public long EntityId { get; set; }
        public string Name { get; set; }
        public int FieldType { get; set; }
        public string Placeholder { get; set; }
        public string Description { get; set; }
        public string Options { get; set; }
        public string MinValue { get; set; }
        public string MaxValue { get; set; }
        public int Sequence { get; set; }
        public bool IsRequired { get; set; }
        public bool IsActive { get; set; }
        public long? CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
