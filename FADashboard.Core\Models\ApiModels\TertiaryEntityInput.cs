﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace FADashboard.Core.Models.ApiModels;
public class TertiaryEntityInput
{
    public string ImageId { set; get; }
    public string Address { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }

    [StringLength(50)]
    public string City { set; get; }

    [StringLength(50)]
    [RegularExpression("^[0-9]{7,10}$", ErrorMessage = "Invalid Contact No.")]
    public string ContactNo { get; set; }

    [StringLength(50)]
    public string Email { get; set; }

    [StringLength(50)]
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { set; get; }

    [DisplayName("Tertiary Entity Name"), Required(ErrorMessage = "Please provide a name")]
    public string TertiaryEntityName { get; set; }

    [StringLength(50)]
    public string OwnersName { get; set; }

    [StringLength(6)]
    [RegularExpression(@"^[1-9]\d{4,5}$", ErrorMessage = "Pincode should Contain only Digits")]
    public string PinCode { get; set; }

    [StringLength(50)]
    public string State { set; get; }
    public List<long> OutletIds { get; set; }
}
public class TertiaryEntityWithOutletId
{
    public long Id { get; set; }
    public string Name { get; set; }
    public long OutletId { get; set; }
}
