﻿using Libraries.CommonEnums;
namespace FADashboard.Core.Models.DTOs;

public class CohortDTO
{
    public long Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public long CompanyId { get; set; }
    public bool Deleted { get; set; }

    public UserPlatform UserPlatform { get; set; }
    public ExtraInfoJson ExtraInfoJson { get; set; }
}

public class ExtraInfoJson
{
    public List<UserInfo> UserInfos { get; set; }
    public List<PositionInfo> PositionInfos { get; set; }
    public List<PositionCodeLevel> PositionCodeLevels { get; set; }
    public List<HierarchyLevel> EmployeeRanks { get; set; }
    public List<EmployeeType> EmployeeTypes { get; set; }
    public List<long> ProductDivisions { get; set; }
    public List<GeographyInfo> GeographyInfos { get; set; }
    public OutletAttributes OutletAttributes { get; set; }
}

public class UserInfo
{
    public List<long> Id { get; set; }
    public HierarchyLevel Rank { get; set; }
}

public class PositionInfo
{
    public List<long> PositionId { get; set; }
    public PositionCodeLevel Level { get; set; }
}
public class GeographyInfo
{
    public List<long> GeographyIds { get; set; }
    public GeographyHierarchy Level { get; set; }
}
public class OutletAttributes
{
    public List<OutletChannel> OutletChannel { get; set; }
    public List<string> OutletShopType { get; set; }
    public List<OutletSegmentation> OutletSegmentation { get; set; }
    public bool IsFocused { get; set; }
}
