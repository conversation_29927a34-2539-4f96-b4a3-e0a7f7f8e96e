﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class StockLoadoutRequestModel
{
    public long DayStockId { get; set; }
    public string UserName { get; set; }
    public double? UserCreditLimit { get; set; }
    public double UserCreditOutstanding { get; set; }
    public string DistributorName { get; set; }
    public string DistributorBeat { get; set; }
    public string DistributorContact { get; set; }
    public List<string> SkuNames { get; set; }
    public double PTR { get; set; }
    public double FOC { get; set; }
    public double Value { get; set; }
    public ApprovedStatus? ApprovedStatus { get; set; }
    public double FOCValue { get; set; }

    public long NetQuantity { get; set; }

    public int UniqueSku { get; set; }

    public string Currency { get; set; }

    public double TotalSchemeDiscount { get; set; }
    public double NetAmount { get; set; }
    public string NetValueStringConverted { get; set; }
    public List<SKUDetails> SKUDetails { get; set; }

}

public class StockReconcilationModel
{
    public double NetAmount { get; set; }
    public long DayStockId { get; set; }
    public string UserName { get; set; }
    public double StockValue { get; set; }
    public double SalesValue { get; set; }
    public double AmountCollected { get; set; }
    public string DistributorName { get; set; }
    public string DistributorBeatName { get; set; }
    public string DistributorContact { get; set; }
    public double AmountPaidToDistributor { get; set; }
    public double UserCreditOutstanding { get; set; }
    public List<string> SKUNames { get; set; }
    public List<SKUDetails> SKUDetails { get; set; }
    public long NetReconcilationQty { get; set; }
    public double CalculatedReconcilationQty { get; set; }
    public ApprovedStatus? ApprovedStatus { get; set; }
    public double DeltaStockDifference { get; set; }
    public double DeltaAmountDifference { get; set; }
    public string ReasonForDiscrepancy { get; set; }
    public double? UserCreditLimit { get; set; }
    public double PaymentDifferenceAmount { get; set; }
    public string Currency { get; set; }
    public int UniqueSku { get; set; }
    public double TotalSchemeDiscount { get; set; }
    public string NetQuantityString { get; set; }
}

public class DistributorBeatNameMapping
{
    public string Name { get; set; }
    public string BeatName { get; set; }
    public string ContactNo { get; set; }
}

public class StockLoadOutUserDetails
{
    public string UserName { get; set; }
    public double? UserCreditLimit { get; set; }
    public double UserCreditOutstanding { get; set; }

}

public class SKUDetails
{
    public long ProductId { get; set; }
    public string SKUName { get; set; }
    public double PTR { get; set; }
    public double FOCQty { get; set; }
    public double Value { get; set; }

    public int Boxs { get; set; }
    public int Pcs { get; set; }

    public int FocBoxs { get; set; }

    public int FocPcs { get; set; }

    public double FocValue { get; set; }

    public long Stock { get; set; }
    public int CalculatedStockBoxs { get; set; }
    public int CalculatedStockPcs { get; set; }
    public int CalculatedFOCStockBoxs { get; set; }
    public int CalculatedFOCStockPcs { get; set; }
    public double CalculatedStockValue { get; set; }
    public double CalculatedFOCValue { get; set; }
    public int NetReconcilationQtyInPcs { get; set; }
    public long UnloadedStock { get; set; }
}
public class UpdateStockRequest
{
    public ApprovedStatus ApprovalStatus { get; set; }
    public long OpenMarketDayStockId { get; set; }
}
