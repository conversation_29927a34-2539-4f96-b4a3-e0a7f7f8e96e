﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IOutletVerificationRequestRepository
{
    Task<RepositoryResponse> ApproveOutletVerificationRequest(OutletVerificationRequestInput requestId);
    Task<RepositoryResponse> DisapproveOutletVerificationRequest(long requestId, string reasonForRejection);
    Task<OutletVerificationRequestInput> GetOutletVerificationRequestById(long requestId, long companyId);
    Task<List<Requests>> GetOutletVerificationRequests(long userId, PortalUserRole userRole, long companyId, bool includeArchieved);
}
