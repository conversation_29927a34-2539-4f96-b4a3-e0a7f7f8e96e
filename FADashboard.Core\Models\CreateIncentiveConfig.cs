﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models
{
    public class CreateIncentiveConfig
    {
        public int Id { get; set; }

        public string Name { get; set; }
        public int RuleType { get; set; }
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public string Visibility { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
        public int Frequency { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public string CreationContext { get; set; } = "Creation Context";
        public bool IsDeactive { get; set; } = false;
        public bool Deleted { get; set; } = false;

        public long FilterConstraintId { get; set; }
        public double Weightage { get; set; } = 100;
        public bool IsQualifier { get; set; } = false;
        public List<long> QualifierIds { get; set; }
        public QualifierRelationModel QualifierRelation { get; set; }
        public string CriteriaRelation { get; set; }
        public int SubRuleType { get; set; }
        public List<Criteria> Criterias { get; set; } = new();
    }

    public class QualifierGroup
    {
        public List<long>? QualifierIds { get; set; }
        public string Operation { get; set; } = "AND"; // "AND" or "OR"
    }
    public class QualifierRelationModel
    {
        public string Operation { get; set; } = "AND"; // Root operation: "AND" or "OR"
        public List<QualifierGroup>? QualifiersRelations { get; set; }
    }

    public class Criteria
    {
        public TaskManagementFocusAreaType CriteriaType { get; set; }
        public long? CriteriaEntityId { get; set; }
        public string Name { get; set; }
        public bool IsDeactive { get; set; }
        public bool Deleted { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public string CreationContext { get; set; } = "CreationContext";
        public double? Weightage { get; set; }
        public List<long> QualifierIds { get; set; }

        public QualifierRelationModel QualifierRelation { get; set; }
        public long ParentId { get; set; }
        public string DisplayShortName { get; set; }
        public List<CriteriaSlabDetails> CriteriaSlabDetails { get; set; } = new();

    }

    public class CriteriaSlabDetails
    {
        public float SlabStartPercentage { get; set; }

        public float? SlabEndPercentage { get; set; }

        public int Sequence { get; set; }

        public float SlabWeightage { get; set; }

        public long RewardId { get; set; }

        public float RewardQuantity { get; set; }

        public SlabCalculationType SlabCalculationType { get; set; }
    }
}

