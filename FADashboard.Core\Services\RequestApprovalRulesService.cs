﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Interfaces;
using Libraries.CommonEnums;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
namespace FADashboard.Core.Services;
public class RequestApprovalRulesService(IRequestApprovalRulesRepository requestApprovalRulesRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<RequestApprovalRule>> GetRequestApprovalRulesList(bool includeDeactivate, CancellationToken ct) => await requestApprovalRulesRepository.GetRequestApprovalRulesList(currentUser.CompanyId, includeDeactivate, ct);
    public async Task<List<RequestApprovalRule>> GetRequestApprovalRules(ApprovalEngineRequesType approvalEngineRequestType, bool includeDeactivate, CancellationToken ct) => await requestApprovalRulesRepository.GetRequestApprovalRules(approvalEngineRequestType, currentUser.CompanyId, includeDeactivate, ct);
    public async Task<RepositoryResponse> CreateUpdateRequestApprovalRule(RequestApprovalRule requestApprovalRule, CancellationToken ct)
    {
        if (requestApprovalRule.Id == 0)
        {
            var existingRequestCount = await requestApprovalRulesRepository.GetExistingRequestCountAsync(requestApprovalRule, currentUser.CompanyId, ct);
            if (existingRequestCount > 0)
            {
                return new RepositoryResponse
                {
                    IsSuccess = false,
                    Message = "Request Name already exists",
                    ExceptionMessage = "Request Name already exists"
                };
            }
            return await requestApprovalRulesRepository.CreateRequestApprovalRule(requestApprovalRule, currentUser.CompanyId, ct);
        }

        return await requestApprovalRulesRepository.UpdateRequestApprovalRule(requestApprovalRule, currentUser.CompanyId, ct);
    }

    public async Task<RepositoryResponse> ActivateDeactivateRequestApprovalRule(long requestId, bool action, CancellationToken ct) => await requestApprovalRulesRepository.ActivateDeactivateRequestApprovalRule(requestId, action, currentUser.CompanyId, ct);
    public async Task<RequestApprovalRule> GetRequestApprovalRuleById(long requestId, CancellationToken ct) => await requestApprovalRulesRepository.GetRequestApprovalRuleById(requestId, currentUser.CompanyId, ct);

    public async Task<RepositoryResponse> UpdatePriorities(List<PriorityUpdateModel> priorityUpdates, CancellationToken ct) => await requestApprovalRulesRepository.UpdatePriorities(priorityUpdates, currentUser.CompanyId, ct);

    public async Task<List<ApprovalEntityApiModel>> GetApprovalEntitiesWithConfigs(CancellationToken ct)
    {
        var approvalEntitiesWithConfigs = await requestApprovalRulesRepository.GetApprovalEntitiesWithConfigs(currentUser, ct);
        return approvalEntitiesWithConfigs;
    }
    public async Task<RepositoryResponse> CreateUpdateApprovalEntityConfigs(List<ApprovalEntityConfigModel> models, CancellationToken ct)
    {
        var parts = new List<string>();

        foreach (var model in models)
        {
            var res = await requestApprovalRulesRepository.CreateOrUpdateApprovalEntityConfig(
                model, currentUser, ct);

            if (res.IsSuccess)
                parts.Add($"EntityId {model.EntityId}: {res.Message}");
        }

        var detail = string.Join(" | ", parts);
        return GetSuccessResponse(0, detail);
    }

}
