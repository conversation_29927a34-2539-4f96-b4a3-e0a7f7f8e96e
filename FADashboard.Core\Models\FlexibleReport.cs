﻿using Libraries.CommonEnums;
using static Libraries.CommonEnums.TypesUsedInReports;

namespace FADashboard.Core.Models;

public class FlexibleReport
{
    public FlexibleReportRequestCreateModel ExtraInfoJson { get; set; }
    public long Id { get; set; }
    public string ReportName { get; set; }
    public bool SaveFlexibleReport { get; set; }
    public string SubscribedRoles { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }

    // Required parameters
    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }
}

public class FlexibleReportRequestCreateModel
{
    public List<long> Stockists { get; set; }
    public DistributorChannel? DistributorChannel { get; set; }
    public List<string> AdditionalMeasures { get; set; }
    public string ChildRow { get; set; }
    public List<string> Columns { get; set; }
    public long CompanyId { get; set; }
    public DateRangePreset DateRangePreset { get; set; }
    public List<string> Dimensions { get; set; }
    public DateTime? EndDate { get; set; }
    public string FileName { get; set; }
    public bool IsNewDashboard { get; set; }
    public Dictionary<string, PerspectiveMeasure> MeasurePersDictionary { get; set; }
    public List<string> Measures { get; set; }
    public string ParentRow { get; set; }
    public ViewPerspective PerspectiveType { get; set; }
    public string PivotColumnName { get; set; }
    public List<string> PivotValues { get; set; }
    public string QueryId { get; set; }
    public string ReportName { get; set; }
    public bool SaveFlexibleReport { get; set; }
    public string SecondaryPivot { get; set; }
    public bool ShowSubtotals { get; set; }
    public DateTime? StartDate { get; set; }
    public StockistType? StockistType { get; set; }
    public string SubscribedUserRoles { get; set; }
    public bool UseAttendancePivot { get; set; }
    public bool UseGrouping { get; set; }
    public long UserId { get; set; }
    public List<long> UserIds { get; set; }
    public List<long> UserPositionIds { get; set; }
    public PositionCodeLevel UserPositionLevel { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string ViewName { get; set; }
    public List<long> PositionUserIds { get; set; }
    public PositionCodeLevel PositionUserLevel { get; set; }
    public List<long> ProductIds { get; set; }
    public ProductHierarchyEnum ProductType { get; set; }
    public GeographicalHierarchy? GeoFilter { get; set; }
    public List<long> GeographyFilterIds { get; set; }
    public long? TargetId { get; set; }
}


public class FlexibleReportBuilderRequest
{
    public FlexibleReportBuilderRequestCreateModel ExtraInfoJson { get; set; }
    public long Id { get; set; }
    public string ReportName { get; set; }
    public bool SaveFlexibleReport { get; set; }
    public string SubscribedRoles { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }

    // Required parameters
    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }
}


public class FlexibleReportBuilderRequestCreateModel
{
    public List<long> Stockists { get; set; }
    public DistributorChannel? DistributorChannel { get; set; }
    public long CompanyId { get; set; }
    public DateRangePreset DateRangePreset { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? StartDate { get; set; }
    public StockistType? StockistType { get; set; }
    public string SubscribedUserRoles { get; set; }
    public long UserId { get; set; }
    public List<long> UserIds { get; set; }
    public List<long> PositionHierachyIds { get; set; }
    public PositionCodeLevel PositionHierarchyLevel { get; set; }
    public PortalUserRole UserRole { get; set; }
    public List<long> PositionUserIds { get; set; }
    public PositionCodeLevel PositionUserLevel { get; set; }
    public List<long> ProductFilterIds { get; set; }
    public ProductHierarchyEnum ProductFilterType { get; set; }
    public GeographicalHierarchy? GeoFilter { get; set; }
    public List<long> GeographyFilterIds { get; set; }
    public string ReportId { get; set; }
    public string ReportName { get; set; }
}
