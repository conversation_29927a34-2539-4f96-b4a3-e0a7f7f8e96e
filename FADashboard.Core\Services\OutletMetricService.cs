﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class OutletMetricService(IOutletMetricRepository outletMetricRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<ExternalOutletMetricInput>> GetExternalOutletMetrics(bool includeDeactive)
    {
        var cueCards = await outletMetricRepository.GetExternalOutletMetrics(currentUser.CompanyId, includeDeactive);
        return cueCards;
    }

    public async Task<RepositoryResponse> CreateUpdateExternalOutletMetric(ExternalOutletMetricInput externalOutletMetric)
    {
        if (externalOutletMetric.Id == 0)
        {
            return await outletMetricRepository.CreateExternalOutletMetric(externalOutletMetric, currentUser.CompanyId);
        }

        return await outletMetricRepository.UpdateExternalOutletMetric(externalOutletMetric);
    }

    public async Task<RepositoryResponse> ActivateDeactivateExternalOutletMetric(long externalOutletMetricId, bool action) => await outletMetricRepository.ActivateDeactivateExternalOutletMetric(externalOutletMetricId, action);

    public async Task<ExternalOutletMetricInput> GetExternalOutletMetricById(long externalOutletMetricId)
    {
        var globalOutletMetric = await outletMetricRepository.GetExternalOutletMetricById(externalOutletMetricId);
        return globalOutletMetric;
    }
}
