﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.Core.Models.ApiModels;

public class ProductInput
{
    public string Category1 { get; set; }
    public List<long> ChannelIds { get; set; }
    public string ColorName { get; set; }
    public string Description { get; set; }
    public string DisplayMRP { get; set; }
    public int? DisplayOrder { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public string ImageId { get; set; }
    public bool IsActive { set; get; }
    public bool IsPromoted { get; set; }
    public string LocalName { get; set; }
    public int MBQ { get; set; }
    public decimal MRP { get; set; }
    public string Name { get; set; }
    public string PackagingType { get; set; }
    public string PackSize { get; set; }
    public string PcName { get; set; }
    public double Price { get; set; }
    public long? PrimaryCategoryId { get; set; }
    public long? ProductDisplayCategoryId { get; set; }
    public double ProductWeightinGm { get; set; }

    //public double? PTRDelta { get; set; }
    public decimal PTD { get; set; }

    public string Schemes { get; set; }
    public string ScName { get; set; }
    public long SecondaryCategoryId { get; set; }
    public double StandardUnitConversionFactor { set; get; }
    public double? SuperUnitConversionFactor { get; set; }
    public string Unit { get; set; }
    public string VariantName { get; set; }

    //src: https://stackoverflow.com/a/29611577
    [DefaultValue("true")]
    [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
    public bool IsSaleable { get; set; }

    public string HSNCode { get; set; }
    public double? PTRDelta { get; set; }
    public int? ExpiryInDays { get; set; }
    public long? ProductGSTCategoryId { get; set; }
    public long? ProductCESSCategoryId { get; set; }
    public decimal? PTRMT { get; set; }
    public decimal? PTDSub { get; set; }
    public decimal? PTDSuper { get; set; }
    public decimal? NetWeight { get; set; }
    public decimal? GrossWeight { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    [StringLength(20)] public string Barcode { get; set; }
    public string AdditionalUnit { get; set; }
    public double? AdditionalUnitConversionFactor { get; set; }
    public decimal? PTRTertiary { get; set; }
    public ProductVisibilityTag ProductVisibilityTag { get; set; }
    public string P1 { get; set; }
    public string P2 { get; set; }
    public string P3 { get; set; }
    public long? ProductGroupId { get; set; }
}
