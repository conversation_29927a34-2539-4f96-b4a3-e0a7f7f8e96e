﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IReportRepository
{

    Task<List<Report>> GetAllReports(long companyId, PortalUserRole userRole, bool includeDeactivate = false);

    Task<CustomReport> GetCustomReport(long reportId);

    Task<Report> GetReportByType(ReportCategory category, EnumForReportAssembly type);

    Task<ReportSubscription> GetReportSubscription(long companyId, Guid subscriptionKey);
    Task<List<Report>> GetAllRoleWiseSubscribedReports(long companyId, List<long> roleIds, bool includeDeactivate = false);
    Task<List<SubscribedReports>> GetSubscribedReports(long companyId);
    Task<RepositoryResponse> AddRoleBasedReportSubscriptions(long companyId, long roleId, List<ReportSubscriptionDetails> reportSubscriptionDetails);
    Task<RepositoryResponse> UpdateRoleBasedReportSubscriptions(long companyId, long roleId, List<ReportSubscriptionDetails> reportSubscriptionDetails);
    Task<List<ReportSubscriptionDetails>> GetSubscribedReportsForRole(long companyId, long roleId, bool includeDeactivate = false);
}
