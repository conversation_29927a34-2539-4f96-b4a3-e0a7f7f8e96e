﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models; // Added for LeadStageCategory enum

namespace FADashboard.Core.Services
{
    public class LMSCompanyLeadStageService(ILMSCompanyLeadStageRepository lmsCompanyLeadStageRepository) : ILMSCompanyLeadStageService
    {


        public async Task<LMSCompanyLeadStageDto> GetCompanyLeadStageByIdAsync(long id) =>
            // Repository now returns DTO and handles IsDeleted check for single GETs
            await lmsCompanyLeadStageRepository.GetByIdAsync(id);

        public async Task<List<LMSCompanyLeadStageDto>> GetCompanyLeadStageByTemplateIdAsync(long leadTemplateId) =>
            // Repository now returns DTO and handles IsDeleted check for single GETs
            await lmsCompanyLeadStageRepository.GetByTemplateIdAsync(leadTemplateId);

        public async Task<IEnumerable<LMSCompanyLeadStageDto>> GetAllCompanyLeadStagesAsync(long companyId) =>
            // Repository now returns IEnumerable<LMSCompanyLeadStageDto> and handles IsDeleted filter
            await lmsCompanyLeadStageRepository.GetAllByCompanyIdAsync(companyId);

        public async Task<IEnumerable<LMSCompanyLeadStageDto>> BulkUpsertStagesAsync(LMSCompanyLeadStageBulkInputDto inputDto, long companyId, long userId)
        {
            ArgumentNullException.ThrowIfNull(inputDto, nameof(inputDto));
            ArgumentNullException.ThrowIfNull(inputDto.LeadStages, nameof(inputDto.LeadStages));

            // Validate the final state of categories for the template.
            ValidateMandatoryStageCategories(inputDto.LeadStages);

            // The repository will handle the database transaction.
            return await lmsCompanyLeadStageRepository.BulkUpsertAsync(inputDto, companyId, userId);
        }

        private static void ValidateMandatoryStageCategories(List<LMSCompanyLeadStageInput> stages)
        {
            var mandatoryCategories = new HashSet<LeadStageCategory>
            {
                LeadStageCategory.Open,
                LeadStageCategory.Working,
                LeadStageCategory.ClosedWon,
                LeadStageCategory.ClosedLost
            };

            var providedCategories = stages.Where(s => s.IsActive).Select(s => s.StageCategory).ToHashSet();

            if (!mandatoryCategories.IsSubsetOf(providedCategories))
            {
                var missing = mandatoryCategories.Except(providedCategories);
                throw new InvalidOperationException($"Operation failed: Each template must have at least one active stage for each mandatory category. Missing categories: {string.Join(", ", missing)}");
            }
        }


    }
}
