﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class DesignationService(IDesignationRepository designationRepository, ICurrentUser currentUser) : RepositoryResponse
{
    //TODO
    private async Task<RepositoryResponse> IsValidDesignation(Designation designation)
    {
        //Private should not be calling public but other way round
        var designations = await GetDesignations(true);
        designations = designations.Where(p => p.Id != designation.Id && p.Level.ToString() == designation.Level.ToString()).ToList();
        var designationNameList = designations.Select(p => p.Name?.NormalizeCaps()).ToList();

        if (designationNameList.Contains(designation.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = designation.Id, ExceptionMessage = "Designation Name for Level is not unique", Message = "Designation Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = designation.Id, Message = "Designation Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateDesignations(long id, bool action)
    {
        //Only required to check in case of deactivation
        if (!action)
        {
            var errorCount = await designationRepository.GetEmployeeCountForDesignation(currentUser.CompanyId, id);


            if (errorCount > 0)
                return new RepositoryResponse { Id = id, Message = $"{errorCount} Users attached, Please detach them before deactivating", IsSuccess = false };
        }

        return await designationRepository.ActivateDeactivateDesignation(currentUser.CompanyId, id, action);
    }

    public async Task<RepositoryResponse> CreateUpdateDesignation(Designation designation)
    {
        var checkValid = await IsValidDesignation(designation);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (designation.Id == 0)
        {
            return await designationRepository.CreateDesignation(designation, currentUser.CompanyId);
        }

        return await designationRepository.UpdateDesignation(designation, currentUser.CompanyId);
    }

    public async Task<List<Designation>> GetDesignations(bool includeDeactivate = false)
    {
        var designation = await designationRepository.GetDesignation(currentUser.CompanyId, includeDeactivate);
        return designation;
    }
}
