﻿using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSAccountService
    {
        Task<LMSAccountDto> GetAccountByIdAsync(long id);
        Task<LMSAccountDto> GetAccountDetailByIdAsync(long id);
        Task<IEnumerable<LMSAccountDto>> GetAccountsByCompanyIdAsync(long companyId);
        Task<LMSAccountDto> CreateAccountAsync(LMSAccountInput accountInput, long companyId, long createdByUserId);
        Task<LMSAccountDto> UpdateAccountAsync(long id, LMSAccountInput accountInput, long companyId, long? updatedByUserId);
        Task<bool> DeleteAccountAsync(long id, long? updatedByUserId);
        Task<IEnumerable<LMSAccountDto>> GetAccountsByAccountOwnerAsync(long accountOwnerId);
        Task<IEnumerable<LMSAccountDto>> GetAccountsByPositionCodeAsync(long positionCode);
        Task<PagedResult<LMSAccountDto>> GetAccountsAsync(long companyId, LMSAccountQueryParameters queryParameters);
    }
}
