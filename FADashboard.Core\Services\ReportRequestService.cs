﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Library.CommonHelpers;
using Library.DateTimeHelpers;
using Microsoft.PowerBI.Api.Models;

namespace FADashboard.Core.Services;

public class ReportRequestService(
    IReportRequestRepository reportRequestRepository,
    ICurrentUser currentUser,
    IReportRepository reportRepository,ISlackService slackService) : RepositoryResponse
{
    public async Task<List<DumpReportRequestView>> GetReportRequests()
    {
        var requests = await reportRequestRepository.GetReportRequests(currentUser.LocalId, currentUser.EmailId, currentUser.UserRole, currentUser.CompanyId);
        return requests;
    }

    public async Task<RepositoryResponse> RequestDownloadReportDataForDateRange(DumpRequestReportCreateInput data, RequestMode requestMode)
    {
        DumpRequestReportCreateModel? createData = null;
        ReportSubscription? subscription = null;

        try
        {
            subscription = await reportRepository.GetReportSubscription(currentUser.CompanyId, data.SubscriptionKey.Value);
            if (subscription == null)
            {
                return new RepositoryResponse { ExceptionMessage = $"Report subscription with {data.SubscriptionKey} Not Found!", Message = "Report Request failed!", IsSuccess = false, };
            }

            createData = new DumpRequestReportCreateModel
            {
                SubscriptionKey = data.SubscriptionKey,
                StartDate = data.StartDate ?? DateTime.UtcNow,
                EndDate = data.EndDate ?? DateTime.UtcNow,
                ExtraJson = new DumpRequestReportCreateExtraJson
                {
                    UserId = data.UserId,
                    UserRole = data.UserRole,
                    UserPositionCodeIds = (data.UserPositionCodeIds != null && data.UserPositionCodeIds.Count != 0) ? data.UserPositionCodeIds : null,
                    PCIds = (data.PCIds != null && data.PCIds.Count != 0) ? string.Join(",", data.PCIds) : "-1", // store -1 (for All)
                    PositionCodeLevel = data.PositionCodeLevel.HasValue ? (int)data.PositionCodeLevel : -1,
                    PCUserIds = (data.PCUserIds != null && data.PCUserIds.Count != 0) ? string.Join(",", data.PCUserIds) : "-1", // store -1 (for all)
                    PCUserLevel = data.PCUserLevel.HasValue ? (int)data.PCUserLevel : -1,
                    IsNewDashboard = true,
                    DistributorId = data.DistributorId,
                    DistributorIds = (data.DistributorIds != null && data.DistributorIds.Count != 0) ? string.Join(",", data.DistributorIds) : "-1",
                    ProductHierarchy = data.Producthierarchy.HasValue ? (int)data.Producthierarchy : -1,
                    ShowAllColumns = data.ShowAllColumns,
                    ShowActiveDataOnly = data.ShowActiveDataOnly,
                    DateInvoiceOrder = data.DateInvoiceOrder,
                    ProductFilterIds = (data.ProductFilterIds != null && data.ProductFilterIds.Count != 0) ? data.ProductFilterIds : null, // TODO : keep same format as other arrays
                    ProductFilterLevel = data.ProductFilterLevel,
                    ShowDataFor = data.ShowDataFor ?? PSODShowDataForEnum.All,
                    FieldUserType = data.FieldUserType ?? FieldUserType.All,
                    BasisPCLevel = data.BasisPCLevel.HasValue ? (int)data.BasisPCLevel : -1,
                    Rank = data.Rank.HasValue ? (int)data.Rank : -1,
                    StartMonth = data.StartMonth,
                    EndMonth = data.EndMonth,
                    StartYear = data.StartYear,
                    EndYear = data.EndYear,
                    Month = data.Month,
                    Year = data.Year,
                    FromDateTimeStamp = data.StartDate.HasValue ? data.StartDate.Value.ToUnixTime() : DateTime.UtcNow.ToUnixTime(),
                    ToDateTimeStamp = data.EndDate.HasValue ? data.EndDate.Value.ToUnixTime() : DateTime.UtcNow.ToUnixTime(),
                    UsesGeographyMultiSelect = true,
                    GeoFilterIds = data.GeoFilterIds != null && data.GeoFilterIds.Count != 0 ? string.Join(",", data.GeoFilterIds) : "-1",
                    GeoFilter = data.GeoFilter,
                    SurveyId = data.SurveyId,
                    StockistType = data.StockistType ?? 0,
                    SchemeIds = (data.SchemeIds != null && data.SchemeIds.Count != 0) ? data.SchemeIds : [],
                    OutletStatus = data.OutletStatus ?? 0,
                    CampaignTaskStatus = data.CampaignTaskStatus,
                    CampaignName = data.CampaignName,
                    CampaignNames = data.CampaignNames,
                    IsIncludeAll = data.IsIncludeAll,
                    GameId = data.GameId ?? 0,
                    VariantName = data.VariantName,
                    UserIds = data.ReportType == EnumForReportAssembly.GILRePrimarySalesDump ? (data.PCUserIds != null && data.PCUserIds.Count != 0) ? string.Join(",", data.PCUserIds) : "-1" : null,
                    BeatometerRuleId = data.BeatometerRuleId,
                    ShowHierarchy = data.ShowHierarchy,
                    ShowPosHierarchy = data.ShowPosHierarchy,
                    ShowBlankForLowerHierarchy = data.ShowBlankForLowerHierarchy,
                    ViewPositionHierarchy = data.ViewPositionHierarchy,
                    ShowLiveData = data.ShowLiveData
                }
            };

            var res = await reportRequestRepository.RequestDownloadReportDataForDateRange(createData, subscription, requestMode);
            if (res.IsSuccess)
            {
                return res;
            }

            return new RepositoryResponse
            {
                Id = res.Id,
                ExceptionMessage = $"{res.ExceptionMessage}",
                Message = "Some Error Occurred, Please Go Back and Retry",
                IsSuccess = false,
            };
        }
        catch (Exception ex)
        {
            await slackService.SendSlackErrorAsync(ex, createData, subscription);
            return new RepositoryResponse
            {
                Id = 0,
                ExceptionMessage = ex.Message,
                Message = "some error occurred. please try again.",
                IsSuccess = false
            };
        }
    }

    public async Task<RepositoryResponse> RequestMasterReportDataDownload(EnumForReportAssembly reportType, RequestMode requestMode)
    {
        try
        {
            var createData = new DumpRequestReportCreateModel
            {
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow,
                ExtraJson = new DumpRequestReportCreateExtraJson
                {
                    UserId = currentUser.LocalId,
                    UserRole = currentUser.UserRole,
                    IsNewDashboard = true,
                    ReportType = reportType,
                    ReportName = reportType.GetDisplayName(),
                    StockistType = 0,
                    OutletStatus = 0,
                }
            };

            var res = await reportRequestRepository.RequestMasterReportDataDownload(createData, requestMode);
            if (res.IsSuccess)
            {
                return res;
            }

            return new RepositoryResponse
            {
                Id = res.Id,
                ExceptionMessage = $"{res.ExceptionMessage}",
                Message = "Some Error Occurred, Please Go Back and Retry",
                IsSuccess = false,
            };
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = 0, ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }

    public async Task<RepositoryResponse> RequestMasterReportDataDownloadDailyStats(DumpRequestReportCreateInput data, RequestMode requestMode)
    {
        try
        {
            var createData = new DumpRequestReportCreateModel
            {
                StartDate = (DateTime)data.StartDate,
                EndDate = (DateTime)data.StartDate,
                ExtraJson = new DumpRequestReportCreateExtraJson
                {
                    UserId = currentUser.LocalId,
                    UserRole = currentUser.UserRole,
                    PCIds = (data.PCIds != null && data.PCIds.Count != 0) ? string.Join(",", data.PCIds) : "-1",
                    PositionCodeLevel = data.PositionCodeLevel.HasValue ? (int)data.PositionCodeLevel : -1,
                    PCUserIds = (data.PCUserIds != null && data.PCUserIds.Count != 0) ? string.Join(",", data.PCUserIds) : "-1",
                    IsNewDashboard = true,
                    ReportType = data.ReportType,
                    ReportName = data.ReportName,
                    OutletStatus = data.OutletStatus ?? 0
                }
            };

            var res = await reportRequestRepository.RequestMasterReportDataDownload(createData, requestMode);
            if (res.IsSuccess)
            {
                return res;
            }

            return new RepositoryResponse
            {
                Id = res.Id,
                ExceptionMessage = $"{res.ExceptionMessage}",
                Message = "Some Error Occurred, Please Go Back and Retry",
                IsSuccess = false,
            };
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = 0, ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }
}
