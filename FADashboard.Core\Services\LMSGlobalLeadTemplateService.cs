using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using FADashboard.Core.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSGlobalLeadTemplateService : ILMSGlobalLeadTemplateService
    {
        private readonly ILMSGlobalLeadTemplateRepository _lmsGlobalLeadTemplateRepository;

        public LMSGlobalLeadTemplateService(ILMSGlobalLeadTemplateRepository lmsGlobalLeadTemplateRepository)
        {
            _lmsGlobalLeadTemplateRepository = lmsGlobalLeadTemplateRepository ?? throw new ArgumentNullException(nameof(lmsGlobalLeadTemplateRepository));
        }

        public async Task<LMSGlobalLeadTemplateDto> GetGlobalLeadTemplateByIdAsync(long id)
        {
            return await _lmsGlobalLeadTemplateRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetAllGlobalLeadTemplatesAsync()
        {
            return await _lmsGlobalLeadTemplateRepository.GetAllAsync();
        }

        public async Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetGlobalLeadTemplatesByCompanyIdAsync(long companyId)
        {
            return await _lmsGlobalLeadTemplateRepository.GetByCompanyIdAsync(companyId);
        }

        public async Task<IEnumerable<LMSGlobalLeadTemplateDto>> GetGlobalLeadTemplatesByCompanyIdAndTypeAsync(long companyId, LMSTemplateType templateType)
        {
            return await _lmsGlobalLeadTemplateRepository.GetByCompanyIdAndTypeAsync(companyId, templateType);
        }

        public async Task<LMSGlobalLeadTemplateDto> CreateGlobalLeadTemplateAsync(LMSGlobalLeadTemplateDto templateDto, long createdByUserId)
        {
            if (templateDto == null) throw new ArgumentNullException(nameof(templateDto));

            templateDto.CreatedBy = createdByUserId;

            return await _lmsGlobalLeadTemplateRepository.AddAsync(templateDto);
        }

        public async Task<LMSGlobalLeadTemplateDto> UpdateGlobalLeadTemplateAsync(long id, LMSGlobalLeadTemplateDto templateDto, long? updatedByUserId)
        {
            if (templateDto == null) throw new ArgumentNullException(nameof(templateDto));

            var existingTemplate = await _lmsGlobalLeadTemplateRepository.GetByIdAsync(id);
            if (existingTemplate == null)
            {
                throw new KeyNotFoundException($"Global Lead Template with ID {id} not found.");
            }

            // Update mutable properties from DTO
            existingTemplate.TemplateName = templateDto.TemplateName;
            existingTemplate.TemplateContent = templateDto.TemplateContent;
            existingTemplate.TemplateType = templateDto.TemplateType;
            
            // Set audit fields
            existingTemplate.UpdatedBy = updatedByUserId;

            return await _lmsGlobalLeadTemplateRepository.UpdateAsync(existingTemplate);
        }

        public async Task<bool> DeleteGlobalLeadTemplateAsync(long id, long? updatedByUserId)
        {
            return await _lmsGlobalLeadTemplateRepository.DeleteAsync(id, updatedByUserId);
        }
    }
}
