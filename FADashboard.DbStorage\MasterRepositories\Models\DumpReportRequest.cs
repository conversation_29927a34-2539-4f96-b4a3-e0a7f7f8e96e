﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class DumpReportRequest : ICreatedEntity
{
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string EmailId { get; set; }

    [Column("EndTimeUTC")] public DateTime EndDate { get; set; }

    public string EndMonthYear { get; set; }
    public DateTime? ExecutedAt { get; set; }
    public long ExecutionTimeInSecs { get; set; }

    public int ExpectedDeliveryDurationMinutes
    {
        get
        {
            switch (RequestMode)
            {
                case RequestMode.ScheduledEmail:
                default:
                    return 720;

                case RequestMode.LiveEmail:
                    return 30;

                case RequestMode.Download:
                    return 30;
            }
        }
    }

    public string ExtraInfoJson { get; set; }
    public long Id { get; set; }

    [Obsolete("Use Request Mode If Accessing from Db or ShouldRunLive", true)]
    public bool IsLiveRequest { get; set; }

    public string OutputLink { get; set; }

    public string RequestedRange { get; set; }

    public RequestMode RequestMode { get; set; }

    public bool ShouldEmail => RequestMode != RequestMode.Download;

    public bool ShouldRunLive => RequestMode is RequestMode.LiveEmail or RequestMode.Download;

    [Column("StartTimeUTC")] public DateTime StartDate { get; set; }

    public string StartMonthYear { get; set; }

    public ReportRequestStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public virtual ReportSubscription Subscription { get; set; }

    [ForeignKey("Subscription")] public long? SubscriptionId { get; set; }

    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public bool IsNewFlexibleReportRequest { get; set; }

    public static List<DumpReportRequestView> CreateFromDB(List<DumpReportRequest> dumpRequests, TimeSpan? offset)
    {
        offset ??= new TimeSpan();
        return dumpRequests.Select(dumpRequest => new DumpReportRequestView
        {
            Id = dumpRequest.Id,
            EmailId = dumpRequest.EmailId,
            ReportType = dumpRequest.Subscription?.Name ?? JsonConvert.DeserializeObject<FlexibleReportRequestCreate>(dumpRequest.ExtraInfoJson).ReportName,
            EndTime = dumpRequest.EndMonthYear ?? dumpRequest.EndDate.Add(offset.Value).ToShortDateString(),
            ExecutedAt = dumpRequest.ExecutedAt?.Add(offset.Value).ToString("MMM dd HH:mm"),
            ExecutionTimeInSecs = dumpRequest.ExecutionTimeInSecs,
            Status = dumpRequest.Status,
            StatusRemark = dumpRequest.StatusRemark,
            RequestedRange = dumpRequest.RequestedRange ?? $@"{dumpRequest.StartDate:dd-MM-yy} to {dumpRequest.EndDate:dd-MM-yy}",
            OutputLink = dumpRequest.OutputLink,
            StartTime = dumpRequest.StartMonthYear ?? dumpRequest.StartDate.Add(offset.Value).ToShortDateString(),
            ExpectedDeliveryTime = dumpRequest.CreatedAt.Add(offset.Value).AddMinutes(dumpRequest.ExpectedDeliveryDurationMinutes).ToString("HH:mm,MMM dd")
        }).ToList();
    }
}
