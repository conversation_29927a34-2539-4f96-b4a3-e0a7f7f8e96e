﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class AttendanceSummary
{
    public double NetOrderInRevenue { get; set; }
    public string NetOrderInRevenueDisplay { get; set; }
    public double OrderInRevenue { get; set; }
    public string OrderInRevenueDisplay { get; set; }
    public double OrderInStdUnits { get; set; }
    public string OrderInStdUnitsDisplay { get; set; }
    public double OrderInSuperUnits { get; set; }
    public string OrderInSuperUnitsDisplay { get; set; }
    public int PC { get; set; }
    public double Productivity { get; set; }
    public int TC { get; set; }
    public int SC { get; set; }
    public int OVC { get; set; }
}

public class DayStartTypeSummary
{
    #region important

    public int Regular { get; set; }
    public int JWCalls { get; set; }
    public int Others { get; set; }
    public int Leave { get; set; }
    public int Total { get; set; }
    public int Inactive { get; set; }
    public int InTimeUsers { get; set; }
    public int Holiday { get; set; }
    public int WeeklyOff { get; set; }
    public int ManagerJW { get; set; }

    #endregion important

    #region Extra without the Display Var

    public int CompliantFirstCalls { get; set; }
    public double NewOutletSalesInRevenue { get; set; }
    public int NoOfOtherActivities { get; set; }
    public long SC { get; set; }
    public double dispatchedValue { get; set; }
    public int dispatchedOutlets { get; set; }

    #endregion Extra without the Display Var
}

public class ManagerWiseOutletTargetsVsAchievements
{
    public double Achieved { get; set; }
    public string AchievedDisplay { get; set; }
    public string CalculationType { get; set; }
    public long Id { get; set; }
    public string Name { get; set; }
    public double RRR { get; set; }
    public string RRRDisplay { get; set; }
    public double Target { get; set; }
    public string TargetDisplay { get; set; }
    public string UserRole { get; set; }
    public string UserRoleString { get; set; }
}

public class MTDLMTD
{
    public RepositoryResponse ErrorResponse { get; set; }
    public double LMTDSalesSummary { get; set; }
    public string LMTDSalesSummaryDisplay { get; set; }
    public double MTDSalesSummary { get; set; }
    public string MTDSalesSummaryDisplay { get; set; }
    public string ValueHeader { get; set; }
}

public class MTDCategorySales
{
    public long CategoryId { get; set; }

    public string CategoryName { get; set; }

    public bool IsAmount { get; set; }
    public double MTDSales { get; set; }
    public string MTDSalesDisplay { get; set; }
    public string Unit { get; set; }
}

public class MTDLMTDTarget
{
    public RepositoryResponse ErrorResponse { get; set; }
    public double mtdTarget { get; set; }
    public string mtdTargetDisplay { get; set; }
    public double lmtdTarget { get; set; }
    public string lmtdTargetDisplay { get; set; }
}

public class MTDCategorySalesTotal
{
    public bool IsAmount { get; set; }
    public List<MTDCategorySales> MTDCategorySales { get; set; }
    public double MTDSales { get; set; }
    public string MTDSalesDisplay { get; set; }
    public string Unit { get; set; }

    public string OthersDisplay { get; set; }

    public double Others { get; set; }
}

public class MTDOutletSummary
{
    public double Covered { get; set; }
    public string CoveredDisplay { get; set; }
    public int NoSalesOutlet { get; set; }
    public string NoSalesOutletDisplay { get; set; }
    public double Ordered { get; set; }
    public string OrderedDisplay { get; set; }
    public int PC { get; set; }
    public string PCDisplay { get; set; }
    public double Productivity { get; set; }
    public string ProductivityDisplay { get; set; }
    public int TC { get; set; }
    public string TCDisplay { get; set; }
    public string TotalDisplay { get; set; }
    public int TotalOutlets { get; set; }
    public string TotalOutletsDisplay { get; set; }
    public int UnAttendedOutlets { get; set; }
    public string UnAttendedOutletsDisplay { get; set; }
    public int UPC { get; set; }
    public string UPCDisplay { get; set; }
    public int UTC { get; set; }
    public string UTCDisplay { get; set; }
    public int UBO { get; set; }
    public string UBODisplay { get; set; }
    public decimal? DropSize { get; set; }
    public string DropSizeDisplay { get; set; }
    public decimal? ThroughPut { get; set; }
    public string ThroughPutDisplay { get; set; }
    public double Billed { get; set; }
    public string BilledDisplay { get; set; }
    public int ZeroOrder { get; set; }
    public string ZeroOrderDisplay { get; set; }
    public int NotBilled { get; set; }
    public string NotBilledDisplay { get; set; }
}

public class OrderValidation
{
    public bool IsAmount { get; set; }
    public double? LmtdOrder { get; set; }
    public string LmtdOrderDisplay { get; set; }
    public double? LmtdValidation { get; set; }
    public string LmtdValidationDisplay { get; set; }
    public double? Order { get; set; }
    public string OrderDisplay { get; set; }
    public string Unit { get; set; }
    public long UserId { get; set; }
    public string UserName { get; set; }
    public string UserRole { get; set; }
    public double? Validation { get; set; }
    public string ValidationDisplay { get; set; }
}

public class PCWiseTargetAndAchList
{
    public double Achievements { get; set; }
    public string AchievementsDisplay { get; set; }
    public string PrimaryCategory { get; set; }

    public long PrimaryCategoryId { get; set; }

    public double RequiredRate { get; set; }
    public string RequiredRateDisplay { get; set; }
    public double Target { get; set; }
    public string TargetDisplay { get; set; }
}

public class PDWiseTargetAndAchList
{
    public double Achievements { get; set; }
    public string AchievementsDisplay { get; set; }
    public string ProductDivision { get; set; }

    public long ProductDivisionId { get; set; }

    public double RequiredRate { get; set; }
    public string RequiredRateDisplay { get; set; }
    public double Target { get; set; }
    public string TargetDisplay { get; set; }
}

public class PositionOrderValidation
{
    public bool IsAmount { get; set; }
    public double? Order { get; set; }
    public string OrderDisplay { get; set; }
    public long PositionId { get; set; }
    public string PositionName { get; set; }
    public string Unit { get; set; }
    public long UserId { get; set; }
    public string UserName { get; set; }
    public string UserRole { get; set; }
    public double? Validation { get; set; }
    public string ValidationDisplay { get; set; }
}

public class GeographyOrderValidation
{
    public bool IsAmount { get; set; }
    public double? Order { get; set; }
    public string OrderDisplay { get; set; }
    public string Unit { get; set; }
    public long GeographyId { get; set; }
    public string GeographyName { get; set; }
    public double? Validation { get; set; }
    public string ValidationDisplay { get; set; }
}

//public class PositionOrderValidationWithTotal
//{
//    public string ForHierarchy { get; set; }
//    public bool IsAmount { get; set; }
//    public double? Order { get; set; }
//    public string PositionOrder_Display { get; set; }
//    public List<PositionOrderValidation> SecondaryOrderValidations { get; set; }
//    public string Unit { get; set; }
//    public double? Validation { get; set; }
//    public string PositionValidation_Display { get; set; }
//}

public class PositionOrderValidationWithTotal
{
    public string ForHierarchy { get; set; }
    public bool IsAmount { get; set; }
    public double? Order { get; set; }
    public string PositionOrderDisplay { get; set; }
    public List<PositionOrderValidation> SecondaryOrderValidations { get; set; }
    public string Unit { get; set; }
    public double? Validation { get; set; }
    public string PositionValidationDisplay { get; set; }
}

public class GeographyOrderValidationWithTotal
{
    public bool IsAmount { get; set; }
    public double? Order { get; set; }
    public string GeographyOrderDisplay { get; set; }
    public List<GeographyOrderValidation> SecondaryOrderValidations { get; set; }
    public string Unit { get; set; }
    public double? Validation { get; set; }
    public string GeographyValidationDisplay { get; set; }
}

public class SecondaryOrderValidation
{
    public PositionOrderValidationWithTotal SecondaryOrderValidationsPositions { get; set; }
    public GeographyOrderValidationWithTotal SecondaryOrderValidationGeography { get; set; }
}

public class TargetAndAchieved
{
    public decimal Achieved { get; set; }
    public string AchievedDisplay { get; set; }
    public decimal PercentAchieved { get; set; }

    //just to make common model
    public decimal RequiredRate => RR;

    public string RequiredRateDisplay => RRDisplay;
    public decimal RR { get; set; }
    public string RRDisplay { get; set; }
    public decimal RRR => RR;
    public string RRRDisplay => RRDisplay;
    public decimal Target { get; set; }

    public string TargetDisplay { get; set; }
}

public class TargetVsAch
{
    public TargetAndAchieved MTDValue { get; set; }
    public long UserId { get; set; }
    public string UserName { get; set; }
    public PortalUserRole UserRole { get; set; }

    public string UserRoleString { get; set; }
}

public class TargetVsAchWithDaysRemaining
{
    public int DaysInMonth { get; set; }
    public List<TargetVsAch> HierarchyTargetsAndAch { get; set; }
    public int RemainingDaysInMonth { get; set; }
    public string RevenueOrStdUnit { get; set; }
    public TargetAndAchieved Total { get; set; }
}

public class UserWisePCWiseTargetAchivement
{
    public List<PCWiseTargetAndAchList> PCWiseTargetAndAchList { get; set; }
    public string RevenueOrStdUnit { get; set; }
    public string User { get; set; }
    public long UserId { get; set; }
    public string UserRoleString { get; set; }
}

public class UserWisePCWiseTargetVsAchievementWithTotal
{
    public TargetAndAchieved Total { get; set; }
    public List<UserWisePCWiseTargetAchivement> UserWisePCWiseTargetAchievements { get; set; }
}

public class UserWisePDWiseTargetAchivement
{
    public List<PDWiseTargetAndAchList> PDWiseTargetAndAchList { get; set; }
    public string RevenueOrStdUnit { get; set; }
    public string User { get; set; }
    public long UserId { get; set; }
    public string UserRoleString { get; set; }
}

public class UserWisePDWiseTargetVsAchievementWithTotal
{
    public TargetAndAchieved Total { get; set; }
    public List<UserWisePDWiseTargetAchivement> UserWisePDWiseTargetAchievements { get; set; }
}

public class MtdDistributorSummaryData
{
    public string DistributorName { get; set; }
    public decimal ClosingStockValue { get; set; }
    public decimal PrimaryTargetsAchievementCalculation { get; set; }
    public decimal PrimaryInvoiceTotalValue { get; set; }
    public decimal SecondarySOTotalValue { get; set; }
    public decimal SecondarySITotalValue { get; set; }
    public decimal FullfillmentPercentageValue { get; set; }
    public StockistType StockistType { get; set; }
}

public class MtdDistributorSummary
{
    public List<MtdDistributorSummaryData> Data { get; set; }
    public int TotalRecords { get; set; }
}

public class PositionWiseMTD
{
    public double? Sales { get; set; }
    public double? Demand { get; set; }
    public double? Targets { get; set; }
    public long PositionId { get; set; }
    public string PositionName { get; set; }
    public double? SalesAsPerDateRangePreset { get; set; }
    public double? Growth { get; set; }
}

public class PositionDetailsRecordsWithTotal
{
    public int Total { get; set; }
    public List<PositionWiseMTD> PositionWiseMTD { get; set; }
}

public class GeographyWiseMTD
{
    public double? Sales { get; set; }
    public double? Demand { get; set; }
    public double? Targets { get; set; }
    public long GeographyId { get; set; }
    public string GeographyName { get; set; }
    public double? SalesAsPerDateRangePreset { get; set; }
    public double? Growth { get; set; }
}

public class GeographyDetailsRecordsWithTotal
{
    public int Total { get; set; }
    public List<GeographyWiseMTD> GeographyWiseMTD { get; set; }
}

public class TargetAndAchievementMTDLMTD
{
    public double? Sales { get; set; }
    public double? Targets { get; set; }
}

public class ProductCategoryWiseMTD
{
    public long CategoryId { get; set; }
    public string CategoryName { get; set; }
    public decimal? Sales { get; set; }
    public decimal? SalesAsPerDateRangePreset { get; set; }
    public decimal? Demand { get; set; }
    public decimal? Targets { get; set; }
    public long UniqueBilledOutlet { get; set; }
    public decimal Growth { get; set; }
}
