﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class FAISExecutionLogService(
    ICurrentUser currentUser,
    IFAISExecutionLogRepository fAISExecutionLogRepository) : RepositoryResponse
{
    public async Task<List<FAISExecutionLogListView>> GetAllExecutionLogs()
    {
        var companyId = currentUser.CompanyId;
        var executionLogs = await fAISExecutionLogRepository.GetAllExecutionLogs(companyId);
        return executionLogs;
    }

}
