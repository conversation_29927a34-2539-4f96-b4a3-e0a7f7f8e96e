﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IRoleMasterRepository
{
    Task<List<RoleMastersView>> GetAllRoles(long companyId, bool showDeleted = true);
    Task<RoleDetailsById> GetRoleDetailsById(long companyId, long id);
    Task<RepositoryResponse> CreateUserRole(long companyId, RoleDetailsById roleDetails);
    Task<RepositoryResponse> UpdateUserRole(long companyId, RoleDetailsById roleDetails);
    Task<List<RoleDetailsById>> GetAllRoleDetails(long companyId);
    Task<RepositoryResponse> ActivateDeactivateRole(long companyId, long Id, bool action);
}
