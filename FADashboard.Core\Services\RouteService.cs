﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class RouteService(ICurrentUser currentUser, IRouteRepository routeRepository, IPositionCodeRepository positionCodeRepository)
{
    private async Task<RepositoryResponse> IsValidRoute(RoutePositionsModel route)
    {
        var routes = await GetAllRoutes(false);
        if (route.Id != 0)
        {
            routes = routes.Where(p => p.Id != route.Id).ToList();
        }

        var routeErpList = routes.Where(p => !string.IsNullOrEmpty(p.ErpId)).Select(p => p.ErpId.NormalizeCaps()).ToList();

        if (routeErpList.Contains(route.ErpId.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = route.Id, ExceptionMessage = $"Route ERPId : {route.ErpId} is not unique", Message = "Route Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = route.Id, Message = "Unique ErpId", IsSuccess = true, };
    }

    public async Task<List<RouteList>> GetAllRoutes(bool includeDeactivate)
    {
        var routes = await routeRepository.GetAllRoutes(currentUser.CompanyId, includeDeactivate);
        return routes;
    }

    public async Task<PagedResponse<List<RouteList>>> GetAllRoutes(PaginationFilter validFilter)
    {
        var routes = await routeRepository.GetAllRoutes(currentUser.CompanyId, validFilter);
        var totalRecords = routes.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(routes.Route, validFilter, totalRecords);
        return pagedReponse;
    }

    public async Task<RoutePositionsModel> GetRouteById(long id)
    {
        var route = await routeRepository.GetRouteById(currentUser.CompanyId, id);
        return route;
    }

    public async Task<RepositoryResponse> ActivateDeactivateRoute(long routeId, bool activateAction) => await routeRepository.ActivateDeactivateRoute(currentUser.CompanyId, routeId, activateAction);

    public async Task<RepositoryResponse> CreateUpdateRoute(RoutePositionsModel route)
    {
        var checkValid = await IsValidRoute(route);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (route.Id == 0)
        {
            return await routeRepository.CreateRoute(currentUser.CompanyId, route);
        }

        return await routeRepository.UpdateRoute(currentUser.CompanyId, route);
    }

    public async Task<List<EntityMin>> GetRoutesUnderAndOfPositions(List<long> positionIds)
    {
        var childPosCodes = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionIds, currentUser.CompanyId);
        var childPosCodeIds = childPosCodes.Select(p => p.Id).ToList();
        positionIds.AddRange(childPosCodeIds);
        return (await routeRepository.GetRoutesOfPositions(positionIds, currentUser.CompanyId)).DistinctBy(b => b.Id).ToList();
    }

    public async Task<List<EntityMinWithErp>> GetOutletsOfRoutes(List<long> routeIds) => (await routeRepository.GetOutletsOfRoutes(routeIds, currentUser.CompanyId)).DistinctBy(b => b.Id).ToList();
    public async Task<ApiResponse> ModifyRouteOutletMapping(List<RouteOutlets> routeOutlets) => await routeRepository.ModifyRouteOutletMapping(currentUser.CompanyId, routeOutlets);
    public async Task<List<EntityMinWithErp>> GetRoutesOfBeats(List<long> beatIds) => (await routeRepository.GetRoutesOfBeats(beatIds, currentUser.CompanyId)).DistinctBy(b => b.Id).ToList();
}
