using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSCustomFieldService
    {
        Task<LMSCustomFieldDto> GetByIdAsync(long id);
        Task<List<LMSCustomFieldDto>> GetByEntityAsync(int entityType, long entityId);
        Task<LMSCustomFieldDto> CreateAsync(LMSCustomFieldDto customField, long createdByUserId);
        Task UpdateAsync(long id, LMSCustomFieldDto customField, long updatedByUserId);
        Task DeleteAsync(long id);
        Task AddRangeAsync(List<LMSCustomFieldDto> customFields, long createdByUserId);
        Task DeleteByEntityAsync(int entityType, long entityId);
    }
}
