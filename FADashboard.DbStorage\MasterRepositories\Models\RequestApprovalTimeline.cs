﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class RequestApprovalTimeline : IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long RequestId { get; set; }
    public ApprovalEngineRequesType RequestType { get; set; }
    public long RuleId { get; set; }
    public long RequesterUserId { get; set; }
    public long RequesterPositionId { get; set; }
    public PositionCodeLevel RequesterPositionLevel { get; set; }
    public long ApproverPositionId { get; set; }
    public long? ApproverUserId { get; set; }
    public PositionCodeLevel ApproverPositionLevel { get; set; }
    public ApprovalEngineRequestStatus RequestStatus { get; set; }
    public int Sequence { get; set; }
    public PortalUserRole? ActionTakenByUserRole { get; set; }
    public long? ActionTakenById { get; set; }
    [StringLength(1000)]
    public string Remarks { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    [StringLength(1000)]
    public string CreationContext { get; set; }
}
