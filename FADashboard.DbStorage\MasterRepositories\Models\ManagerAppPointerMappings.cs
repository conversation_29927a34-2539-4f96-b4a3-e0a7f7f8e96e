﻿using EntityHelper;
using Libraries.CommonEnums.Helpers;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ManagerAppPointerMappings : ICreatedEntity, IUpdatableEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public List<DailyDataPointers> PointerListForManager => JsonConvert.DeserializeObject<List<DailyDataPointers>>(PointersForManager.Replace(" ", string.Empty));
    public List<DailyDataPointers> PointerListForUser => JsonConvert.DeserializeObject<List<DailyDataPointers>>(PointersForUser.Replace(" ", string.Empty));
    public List<DailyDataPointers> PointerListForDashboard => PointersForDashboard == null
    ? new List<DailyDataPointers>() : JsonConvert.DeserializeObject<List<DailyDataPointers>>(PointersForDashboard.Replace(" ", string.Empty));
    public string PointersForManager { get; set; }
    public string PointersForUser { get; set; }
    public string PointersForDashboard { get; set; }
}
