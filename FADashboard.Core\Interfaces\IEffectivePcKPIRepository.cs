﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IEffectivePcKPIRepository
{
    Task<EffectivePcKpiInput> GetRuleById(long ruleId, long companyId);
    Task<List<EffectivePcKpiInput>> GetAllRules(long companyId, bool getDeactivated = false);
    Task<RepositoryResponse> CreateRule(EffectivePcKpiInput effectivePcKpi, long companyId);
    Task<RepositoryResponse> UpdateRule(EffectivePcKpiInput effectivePcKpi, long companyId);
    Task<RepositoryResponse> ActivateDeactivateRule(long companyId, long ruleId, bool deactivate);
    Task<RepositoryResponse> SetDefaultRule(long companyId, long ruleId, bool isDefault);
}
