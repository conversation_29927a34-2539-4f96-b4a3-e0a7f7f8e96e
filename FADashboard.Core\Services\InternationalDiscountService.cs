﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class InternationalDiscountService(IInternationalDiscountRepository internationalDiscountRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<RepositoryResponse> ActivateDeactivateDiscount(long discountId, bool action) => await internationalDiscountRepository.ActivateDeactivateDiscount(discountId, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> CreateUpdateInternationalDiscount(InternationalDiscountInput discount)
    {
        if (discount.Id == 0)
        {
            return await internationalDiscountRepository.CreateInternationalDiscount(discount, currentUser.CompanyId);
        }

        return await internationalDiscountRepository.UpdateInternationalDiscount(discount, currentUser.CompanyId);
    }

    public async Task<InternationalDiscountInput> GetDiscountById(long discountId)
    {
        var discount = await internationalDiscountRepository.GetDiscountById(discountId, currentUser.CompanyId);
        return discount;
    }

    public async Task<List<InternationalDiscount>> GetInactiveInternationalDiscounts(int page, string searchTerm = null)
    {
        var discounts = await internationalDiscountRepository.GetInactiveInternationalDiscounts(currentUser.CompanyId, searchTerm);
        return discounts;
    }

    public async Task<int> GetInactiveInternationalDiscountsCount(string searchTerm = null) => await internationalDiscountRepository.GetInactiveInternationalDiscountsCount(currentUser.CompanyId, searchTerm);

    public async Task<List<InternationalDiscount>> GetInternationalDiscounts(bool inludeDeactive)
    {
        var discounts = await internationalDiscountRepository.GetInternationalDiscounts(currentUser.CompanyId, inludeDeactive);
        return discounts;
    }

    public async Task<int> GetInternationalDiscountsCount(string searchTerm = null) => await internationalDiscountRepository.GetInternationalDiscountsCount(currentUser.CompanyId, searchTerm);

    public async Task<List<SegmentationList>> GetSegmentation()
    {
        var segmentations = await internationalDiscountRepository.GetSegmentation(currentUser.CompanyId);
        return segmentations;
    }
}
