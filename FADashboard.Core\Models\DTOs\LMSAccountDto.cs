using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountDto
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long AssignedTo { get; set; } // Renamed from AccountOwner
        public long PositionCode { get; set; }
        [StringLength(255)]
        public string AccountName { get; set; }
        public string Description { get; set; }

        public List<LMSCustomFieldValueDto> CustomFieldValues { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Website { get; set; }
        public decimal? AnnualRevenue { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public string AccountImage { get; set; } // Added
        public long AccountTemplateId { get; set; } // Added

        // Auditing fields
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // User names for display
        public string CreatedByName { get; set; }
        public string UpdatedByName { get; set; }
        public string AssignedToName { get; set; }
    }

    public class LMSAccountInput
    {
        public string AccountImage { get; set; }

        [Required]
        [StringLength(255)]
        public string AccountName { get; set; }

        [Required]
        public long AssignedTo { get; set; }
        [Required]
        public long PositionCodeId { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        [StringLength(255)]
        public string Website { get; set; }

        public decimal? AnnualRevenue { get; set; }

        public string Description { get; set; }

        [Required]
        public long AccountTemplateId { get; set; }

        public List<LMSCustomFieldValueDto> CustomFieldValues { get; set; }
    }
}
