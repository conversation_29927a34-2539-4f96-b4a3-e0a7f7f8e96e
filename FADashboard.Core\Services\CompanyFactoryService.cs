﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class CompanyFactoryService(
    ICurrentUser currentUser,
    ICompanyFactoryRepository companyFactoryRepository
) : RepositoryResponse
{
    //Piece of code to get all Company Factories
    public async Task<List<CompanyFactories>> GetAllCompanyFactories(bool showDeactive = false) => await companyFactoryRepository.GetAllCompanyFactories(currentUser.CompanyId, showDeactive);

    //Piece of code to get Company Factory by Id
    public async Task<CompanyFactoryIO> GetCompanyFactoryById(long id) => await companyFactoryRepository.GetCompanyFactoryById(id, currentUser.CompanyId);

    //Piece of code to Activate/Deactivate Company Factory
    public async Task<RepositoryResponse> DeactivateCompanyFactory(long id) => await companyFactoryRepository.DeactivateCompanyFactory(id, currentUser.CompanyId);

    //Piece of code to check if Company Factory is valid or not
    private async Task<RepositoryResponse> IsValidCompanyFactory(CompanyFactoryIO companyFactoryIO)
    {
        var companyFactories = await GetAllCompanyFactories(true);

        // If Id is provided and not equal to 0
        if (companyFactoryIO.Id != 0)
        {
            var existingFactory = companyFactories.Find(f => f.Id == companyFactoryIO.Id);

            // Check if a factory with the provided Id exists
            if (existingFactory == null)
            {
                return new RepositoryResponse { Id = companyFactoryIO.Id, ExceptionMessage = "Company Factory does not exist", IsSuccess = false, Message = "Company Factory does not exist" };
            }
        }

        // Check if Name, ERPID, and ContactNumber are present or not
        if (!(string.IsNullOrEmpty(companyFactoryIO.Name) || string.IsNullOrEmpty(companyFactoryIO.ERPID)
                                                          || string.IsNullOrEmpty(companyFactoryIO.PhoneNumber)))
        {
            // Check if name is valid or not
            if (!string.IsNullOrEmpty(companyFactoryIO.Name))
            {
                var existingName = companyFactories
                    .Exists(f => f.Id != companyFactoryIO.Id && f.Name?.NormalizeCaps() == companyFactoryIO.Name?.NormalizeCaps());
                if (existingName)
                {
                    return new RepositoryResponse { Id = companyFactoryIO.Id, ExceptionMessage = "Company Factory Name already exists", IsSuccess = false, Message = "Company Factory Name already exists" };
                }
            }

            // Check if ERPID is valid or not
            if (!string.IsNullOrEmpty(companyFactoryIO.ERPID))
            {
                var existingERPID = companyFactories
                    .Exists(f => f.Id != companyFactoryIO.Id && f.ERPID?.NormalizeCaps() == companyFactoryIO.ERPID?.NormalizeCaps());
                if (existingERPID)
                {
                    return new RepositoryResponse { Id = companyFactoryIO.Id, ExceptionMessage = "Company Factory ERPID already exists", IsSuccess = false, Message = "Company Factory ERPID already exists" };
                }
            }

            // Check if ContactNumber is unique
            if (!string.IsNullOrEmpty(companyFactoryIO.PhoneNumber))
            {
                var existingContactNumber = companyFactories
                    .Exists(f => f.Id != companyFactoryIO.Id && f.PhoneNumber == companyFactoryIO.PhoneNumber);
                if (existingContactNumber)
                {
                    return new RepositoryResponse { Id = companyFactoryIO.Id, ExceptionMessage = "Company Factory Contact Number already exists", IsSuccess = false, Message = "Company Factory Contact Number already exists" };
                }
            }
        }
        else
        {
            return new RepositoryResponse { Id = companyFactoryIO.Id, ExceptionMessage = "Company Factory Name, ERPID and Contact Number are required", IsSuccess = false, Message = "Company Factory Name, ERPID and Contact Number are required" };
        }

        return new RepositoryResponse { Id = companyFactoryIO.Id, Message = "Company Factory Unique", IsSuccess = true, };
    }

    //Piece of code to Create/Update Company Factory
    public async Task<RepositoryResponse> CreateUpdateCompanyFactory(CompanyFactoryIO CompanyFactoryIO)
    {
        var isValidCompanyFactory = await IsValidCompanyFactory(CompanyFactoryIO);
        if (!isValidCompanyFactory.IsSuccess)
        {
            return isValidCompanyFactory;
        }

        if (CompanyFactoryIO.Id == 0)
        {
            return await companyFactoryRepository.CreateCompanyFactory(CompanyFactoryIO, currentUser.CompanyId);
        }

        return await companyFactoryRepository.UpdateCompanyFactory(CompanyFactoryIO, currentUser.CompanyId);
    }
}
