﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class PerfectEntityRuleCriteria : ICompanyEntity, IDeactivatable, ICreatedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long RuleId { get; set; }

    [ForeignKey(nameof(RuleId))]
    public PerfectEntityRule Rule { get; set; }
    public TaskManagementFocusAreaType CriteriaType { get; set; }
    public long? CriteriaEntityId { get; set; }
    [StringLength(64)] public string Name { get; set; }
    public bool IsDeactive { get; set; }
    public bool Deleted { get; set; }
    public double? Weightage { get; set; }
    public long ParentId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    [Column("QualifierIds")]
    public List<long> QualifierIds { get; set; }

    [Column("QualifierRelation")]
    public string QualifierRelation { get; set; }
    public string DisplayShortName { get; set; }

    public string ColorCode { get; set; }

    public bool ShowAchievementForUser { get; set; }
    public List<PerfectCriteriaSlabDetail> PerfectCriteriaSlabDetails { get; set; } = new List<PerfectCriteriaSlabDetail>();

}
