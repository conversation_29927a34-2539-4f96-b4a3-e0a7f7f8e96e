﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IAdvanceLeaveRepository
{
    Task<RepositoryResponse> ApproveRejectAdvanceLeaveRequest(long id, long companyId, bool action, long userId);

    Task<int> GetAdvanceLeaveCountUnderManager(long companyId, List<long> ids, DateTime date);

    Task<string> GetAdvanceLeaveForUser(long companyId, long id, DateTime date);

    Task<AdvanceLeaveSubmission> GetAdvanceLeaveRequestById(long id, long companyId);

    Task<List<Requests>> GetAdvanceLeaveRequests(long userId, PortalUserRole userRole, long companyId, bool includeArchieved = false);

    Task<List<EmployeeAdvanceLeave>> GetEmployeesAdvanceLeaveForDate(long companyId, DateTime date);
}
