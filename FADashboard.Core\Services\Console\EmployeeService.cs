﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.DateTimeHelpers;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services.Console;

public class EmployeeService(IEmployeeRepository employeeRepository, ICurrentUser currentUser, IDayStartRepository dayStartRepository, FAResilientHttpClient resilientHttpClient, AppConfigSettings appConfigSettings = null) : RepositoryResponse
{
    public async Task<List<EntityMin>> GetTokenForEmp(long userId)
    {
        var empTokens = await employeeRepository.GetTokenForEmp(userId, currentUser.CompanyId);
        return empTokens;
    }

    public async Task<List<Fedns>> MarkEndDayForAllSessionsNS(long userId)
    {
        var allOpenSessionForEmployee = await dayStartRepository.GetAllOpenDayStartForEmployee(currentUser.CompanyId, userId);
        var empLatestToken = await employeeRepository.GetLatestEmployeeToken(userId, currentUser.CompanyId);
        var rows = new List<Fedns>();
        if (allOpenSessionForEmployee.Count > 0)
        {
            foreach (var session in allOpenSessionForEmployee)
            {
                if (empLatestToken == null)
                {
                    return default;
                }

                rows.Add(await ForceEndDayNS(session.SessionId, empLatestToken.Name));
            }
        }

        return rows;
    }

    private async Task<Fedns> ForceEndDayNS(Guid? sessionId, string employeeToken)
    {
        var sessionIdString = sessionId?.ToString().Replace("{", "").Replace("}", "");
        if (sessionIdString == null)
        {
            return new Fedns { SessionId = null, Error = "App Api error", Message = "Session Not Found" };
        }

        var dayEndTime = DateTime.UtcNow;

        //creating model for NS DayEnd.
        var model = new
        {
            SessionId = sessionIdString,
            IsForcedDayEnd = true,
            Time = dayEndTime.ToUnixTime(),
            DayEndType = (int)DayEndType.ForcedViaURL,
            IgnoreNSErrors = true
        };
        try
        {
            var dataUrl = $"{appConfigSettings.NsAppApiBaseUrl}/api/Day/forceEnd";
            await resilientHttpClient.PostJsonAsync<RepositoryResponse>(dataUrl, employeeToken, model);

            return new Fedns { SessionId = sessionIdString, Error = "", Message = "SuccessFully Marked end Day" };
        }
        catch (Exception ex)
        {
            return new Fedns { SessionId = sessionIdString, Error = "App Api Error", Message = ex.Message, };
        }
    }
}
