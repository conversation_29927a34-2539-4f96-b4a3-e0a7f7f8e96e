﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ITradeCtaRepository
{
    Task<RepositoryResponse> CreateTradeCta(TradeCtaRequest tradeCtaRequest, long companyId);

    Task<RepositoryResponse> UpdateTradeCta(TradeCtaRequest tradeCtaRequest, long companyId);

    Task<RepositoryResponse> UpdateTradeCtaStatus(long id, long companyId, bool isDeleted);

    Task<List<TradeCtaResponse>> GetAllTradeCta(long companyId, bool includeDeactivate = false);

    Task<TradeCtaResponse> GetTradeCtaById(long id, long companyId);
}
