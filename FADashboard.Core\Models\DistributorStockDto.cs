﻿namespace FADashboard.Core.Models;
public class DistributorStockDto
{
    public long DistributorId { get; set; }
    public string DistributorName { get; set; }
    public int StockistType { get; set; }
    public long Time { get; set; }
    public int? Week { get; set; }
    public int Month { get; set; }
    public int Year { get; set; }
    public Guid? Guid { get; set; }
    public long? PositionCodeId { get; set; }
    public List<DistributorStockItemDto> DistributorStockItems { get; set; }
}

public class DistributorStockItemDto
{
    public long ProductId { get; set; }
    public string ProductName { get; set; }
    public double SaleValue { get; set; }
    public double? PTR { get; set; }
    public double? PTD { get; set; }
    public long? ProductDivisionId { get; set; }
    public string AlternateCategory { get; set; }
    public string MRP { get; set; }
    public DateTime? ManufacturingDate { get; set; }
}
