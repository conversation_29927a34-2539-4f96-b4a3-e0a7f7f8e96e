﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class CompanyInfo : CompanyNameAndLogo
{
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string AccountManagerEmail { set; get; }
    public string AccountManagerName { set; get; }
    public string KeyAccountManagerEmail { set; get; }
    public string KeyAccountManagerName { set; get; }
    public string ProjectManagerEmail { set; get; }
    public string ProjectManagerName { set; get; }

    public string BeatShortName { get; set; }
    public string? AssetShortName { get; set; }

    public int? DistributorErpIncrementalNumber { get; set; }

    public int? DistributorErpLength { get; set; }

    public string DistributorShortName { get; set; }

    public string EmployeeShortName { get; set; }

    public string LegalName { get; set; }

    public long OutletIncrementalId { get; set; }

    public string PreloaderBgImageId { get; set; }

    public string PreloaderIcon { get; set; }

    public string ProjectCoordinatorEmail { set; get; }

    public string ProjectCoordinatorName { set; get; }

    public string ProjectStage { set; get; }

    public string Sector { get; set; }

    public string ShortName { get; set; }
    public string AppVariantName { get; set; }

    public int AppVersionNumber { get; set; }
    public int? MinRequiredAppVersion { set; get; }
    public string CompanyShortCode { get; set; }
    public ClientCategory? ClientCategory { get; set; }
    public bool? IsSMBHAV { get; set; }

}

public class CompanyNameAndLogo
{
    public string ImageId { get; set; }
    public string Name { get; set; }
}
