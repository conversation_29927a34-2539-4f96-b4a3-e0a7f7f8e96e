﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FAOutletUpdationRequests")]
public class FAOutletUpdationRequest
{
    [StringLength(12)]
    public string Aadhar { get; set; }

    public string AccountHoldersName { get; set; }
    public DateTime AddedOn { set; get; }

    [StringLength(1000)]
    public string Address { get; set; }

    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public string AttributeImage3 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }

    [StringLength(18)]
    public string BankAccountNumber { get; set; }

    [StringLength(50)]
    public string City { set; get; }

    public long CompanyId { get; set; }
    public bool? Disapproved { set; get; }

    [StringLength(500)]
    public string Email { get; set; }

    public virtual ClientEmployee Employee { set; get; }

    [Column("Employee")]
    public long EmployeeId { set; get; }

    [StringLength(20)]
    public string GSTIN { get; set; }

    public bool GSTRegistered { get; set; }
    public long Id { get; set; }

    [StringLength(11)]
    public string IFSCCode { get; set; }

    public Guid? ImageId { set; get; }

    public virtual Location Location { get; set; }

    public long LocationId { set; get; }

    [StringLength(100)]
    public string MarketName { set; get; }

    public OutletChannel? OutletChannel { get; set; }

    [StringLength(50)]
    public string OwnersName { get; set; }

    [StringLength(50)]
    public string OwnersNo { get; set; }

    [StringLength(10)]
    public string PAN { get; set; }

    [StringLength(6)]
    public string PinCode { get; set; }

    [StringLength(50)]
    public string PlaceOfDelivery { set; get; }

    public long? PositionCodeId { get; set; }

    public string RejectionReason { get; set; }

    public string RequestId { set; get; }

    [Column("Approved")]
    public bool Reviewed { set; get; }

    [Column("ApprovedBy")]
    public long? ReviewedBy { set; get; }

    [Column("ApprovedByUserRole")]
    public PortalUserRole? ReviewedByUserRole { set; get; }

    [Column("ApprovedOn")]
    public DateTime? ReviewedOn { set; get; }

    public OutletSegmentation? Segmentation { set; get; }

    [StringLength(500)]
    public string ShopName { get; set; }

    [StringLength(50)]
    public string ShopType { get; set; }

    public long? ShopTypeId { get; set; }

    [StringLength(50)]
    public string State { set; get; }

    [StringLength(50)]
    public string TIN { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string FSSAINumber { get; set; }

    public UserPlatform UserPlatform { get; set; }
    public long? BeatId { get; set; }
    public string CustomTags { get; set; }
    public long? SubShopTypeId { get; set; }
}
