﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IAppConfigRepository
{
    Task<List<AppConfigView>> GetAllAppConfig(long companyId);
    Task<AppConfigView> GetAppConfigById(long companyId);
    Task<RepositoryResponse> CreateAppConfig(long companyId, AppConfigView appConfig);
    Task<RepositoryResponse> UpdateAppConfig(long companyId, AppConfigView appConfig);
}
