﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models
{
    [Table("LMSGlobalLeadStages")]
    public class LMSGlobalLeadStage
    {
        [Key]
        public long Id { get; set; }

        public long CompanyId { get; set; } // Can be used to scope global stages or for company-specific ones

        [Required]
        [StringLength(100)]
        public string StageName { get; set; }

        public int Order { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
