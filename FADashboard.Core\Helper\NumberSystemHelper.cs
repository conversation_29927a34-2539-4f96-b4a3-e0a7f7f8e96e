﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Services;
using Libraries.CommonEnums;
using Library.NumberSystem;

namespace FADashboard.Core.Helper;

public class NumberSystemHelper(
    CompanySettingService companySettingService,
    ICurrentUser currentUser,
    ICompanySettingsRepository companySettingsRepository)
{
    private readonly NumberSystem numberSystem = new(companySettingService.UsesInternationalNumberSystem().Result);

    //TODO

    public FormattedData FormatData(object val, string measure = null) => numberSystem.FormatData(val, measure);

    public string FormatDataValue(object val, string measure = null) => numberSystem.FormatData(val, measure).StringVal;

    public FormattedData GetFormattedData(long val) => numberSystem.GetFormattedValue(val);

    public FormattedData GetFormattedData(double val) => numberSystem.GetFormattedValue(val);

    public string GetFormattedValue(long val) => numberSystem.GetFormattedValue(val).StringVal;

    public string GetFormattedValue(double val, bool isSales = false)
    {
        var calculateInMT = false;
        if (isSales)
        {
            //TODO
            var companySettingsDict = companySettingsRepository.GetSettingsCompany(currentUser.CompanyId).Result;
            var companySettings = new CompanySettings(companySettingsDict);
            var usesSalesIn = companySettings.CompanySeesDataIn == CompanySeesDataInEnum.SuperUnit;
            var isUnitCalculateInMetricTon = companySettings.UsesUnitCalculateInMetricTon;
            calculateInMT = usesSalesIn && isUnitCalculateInMetricTon;
        }

        return numberSystem.GetFormattedValue(val, calculateInMT).StringVal;
    }

    public string GetFormattedValue(decimal val) => numberSystem.GetFormattedValue((double)val).StringVal;

    public string GetFormattedValue(double val, string unit, bool isAmount = false) => isAmount
        ? unit + " " + numberSystem.GetFormattedValue(val).StringVal
        : numberSystem.GetFormattedValue(val).StringVal + " " + unit;

    public string GetFormattedValue(long? val) => val.HasValue ? numberSystem.GetFormattedValue(val.Value).StringVal : string.Empty;

    public string GetFormattedValue(double? val) => val.HasValue ? numberSystem.GetFormattedValue(val.Value).StringVal : string.Empty;

    public string GetFormattedValue(decimal? val) => val.HasValue ? numberSystem.GetFormattedValue((double)val.Value).StringVal : string.Empty;

    public string GetFormattedValue(double? val, string unit, bool isAmount = false)
    {
        if (!val.HasValue)
        {
            return string.Empty;
        }

        return isAmount
            ? unit + " " + numberSystem.GetFormattedValue(val.Value).StringVal
            : numberSystem.GetFormattedValue(val.Value).StringVal + " " + unit;
    }
}
