﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IRouteRepository
{
    Task<List<RouteList>> GetAllRoutes(long companyId, bool includeDeactivate);
    Task<RouteWithTotal> GetAllRoutes(long companyId, PaginationFilter validFilter);

    Task<RoutePositionsModel> GetRouteById(long companyId, long id);

    Task<RepositoryResponse> ActivateDeactivateRoute(long companyId, long routeId, bool activateAction);

    Task<RepositoryResponse> CreateRoute(long companyId, RoutePositionsModel route);

    Task<RepositoryResponse> UpdateRoute(long companyId, RoutePositionsModel route);

    Task<List<EntityMin>> GetRoutesOfPositions(List<long> positionIds, long companyId);

    Task<List<EntityMinWithErp>> GetOutletsOfRoutes(List<long> routeIds, long companyId);
    Task<ApiResponse> ModifyRouteOutletMapping(long companyId, List<RouteOutlets> routeOutlets);
    Task<List<EntityMinWithErp>> GetRoutesOfBeats(List<long> routeIds, long companyId);
    Task<Dictionary<long, int>> GetDistinctLocationCountPerRoute(long companyId, List<long> routeIds);
}
