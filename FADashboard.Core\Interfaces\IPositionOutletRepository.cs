﻿using FADashboard.Core.Models;

namespace FADashboard.Core.Interfaces;

public interface IPositionOutletRepository
{
    Task<RepositoryResponse> SavePositionOutletMapping(long positionId, List<long> outletId, long companyId);
    Task<List<PositionOutletMapping>> GetPositionOutletMappingUsingOutletIds(List<long> outletIds, long companyId);
    Task<List<PositionOutletMapping>> GetPositionOutletMappingUsingPositionId(long positionId, long companyId);
}
