﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IDeadOutletRequestRepository
{
    Task<RepositoryResponse> ActionTakenOnRequest(long requestId, bool approved);

    Task<List<DeadOutletRequests>> GetArchivedRequests(long CompanyId, string searchTerm = null);

    Task<int> GetArchivedRequestsCount(long CompanyId, string searchTerm = null);

    Task<List<DeadOutletRequestItems>> GetOutletRequestItems(long requestItemId, long CompanyId);

    Task<int> GetOutletRequestItemsCount(long requestItemId);

    Task<List<Requests>> GetOutletRequests(long userId, PortalUserRole userRole, long companyId, bool includeArchieved = false);

    Task<int> GetOutletRequestsCount(long CompanyId, string searchTerm = null);
}
