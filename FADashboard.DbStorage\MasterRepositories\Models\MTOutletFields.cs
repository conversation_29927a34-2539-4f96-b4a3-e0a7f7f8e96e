﻿using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class MTOutletFields
{
    public long companyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public NewMTFields Field { get; set; }
    public long Id { get; set; }
    public bool Mandatory { get; set; }

    public DateTime UpdatedAt { get; set; }
    public bool Visible { get; set; }

    public static MTOutletFields ToCoreModel(MTConfigurationDTO model, long campanyId) => new()
    {
        Field = model.Field,
        Mandatory = model.Mandatory,
        Visible = model.Visible,
        CreatedAt = model.CreatedAt,
        UpdatedAt = model.UpdatedAt,
        companyId = campanyId
    };
}
