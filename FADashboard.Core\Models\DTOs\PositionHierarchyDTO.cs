﻿namespace FADashboard.Core.Models.DTOs;
public class PositionHierarchyDTO
{
    public string Level1UserName { get; set; }
    public string Level2UserName { get; set; }
    public string Level3UserName { get; set; }
    public string Level4UserName { get; set; }
    public string Level5UserName { get; set; }
    public string Level6UserName { get; set; }
    public string Level7UserName { get; set; }
    public string Level8UserName { get; set; }
    public string Level1Code { get; set; }
    public string Level2Code { get; set; }
    public string Level3Code { get; set; }
    public string Level4Code { get; set; }
    public string Level5Code { get; set; }
    public string Level6Code { get; set; }
    public string Level7Code { get; set; }
    public string Level8Code { get; set; }
    public string Level1ErpId { get; set; }
    public string Level2ErpId { get; set; }
    public string Level3ErpId { get; set; }
    public string Level4ErpId { get; set; }
    public string Level5ErpId { get; set; }
    public string Level6ErpId { get; set; }
    public string Level7ErpId { get; set; }
    public string Level8ErpId { get; set; }

}
