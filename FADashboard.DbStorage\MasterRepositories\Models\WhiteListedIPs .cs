﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class WhiteListedIP: IAuditedEntity
{
    public WhiteListedIP()
    {
        IsDeleted = false;
    }
    [Key] public long Id { get; set; }
    public bool IsDeleted { get; set; }
    [ForeignKey("Company")] public long CompanyId { get; set; }
    public virtual Company Company { get; set; }
    public string RuleName { get; set; }
    public string StartIPAddress { get; set; }
    public string EndIPAddress { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }

}

