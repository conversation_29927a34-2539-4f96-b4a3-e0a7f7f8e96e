﻿using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class SchemesList
{
    public ConstraintType ConstraintType { set; get; }

    [Column(TypeName = "datetime2")]
    public DateTime EndTime { get; set; }

    public string ErpId { get; set; }
    public Guid Guid { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsQPS { get; set; }
    public string Name { get; set; }
    public int? SchemeSequence { set; get; }
    public PayoutCalculationType PayoutCalculationType { set; get; }

    public PayoutType PayoutType { set; get; }

    [Column(TypeName = "datetime2")]
    public DateTime StartTime { get; set; }

    public DateTime CreatedAt { get; set; }

    public int? IsSchemeApproved { get; set; }
    public string RejectReason { get; set; }
    public DateTime? ActionTakenTime { get; set; }
    public string ActionTakenBy { get; set; }
    public long? AlternateScheme { get; set; }
    public long? ZoneId { get; set; }
    public long? RegionId { get; set; }
}

public class SchemeTotal
{
    public int Total { get; set; }
    public List<SchemesList> SchemeRecords { get; set; }
}
