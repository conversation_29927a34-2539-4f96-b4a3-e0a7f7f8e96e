﻿using System.Security.Cryptography;

namespace FADashboard.Core.Helper;
public class EncryptionHelper
{
    public static string EncryptString(string plainText)
    {
        var keyString = "0dcMvHX2f/Mlm44vyD5HPoCgoj4fBP0XbiT/rn9+4z0=";
        var key = Convert.FromBase64String(keyString);
        if (key.Length != 32)
        {
            throw new ArgumentException("Key length must be 32 bytes for AES-256 encryption.");
        }

        using (var aes = Aes.Create())
        {
            aes.Key = key;
            aes.GenerateIV();
            var iv = aes.IV;

            using (var encryptor = aes.CreateEncryptor(aes.Key, iv))
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                using (var sw = new StreamWriter(cs))
                {
                    sw.Write(plainText);
                }

                var encrypted = ms.ToArray();
                var result = new byte[iv.Length + encrypted.Length];
                Buffer.BlockCopy(iv, 0, result, 0, iv.Length);
                Buffer.BlockCopy(encrypted, 0, result, iv.Length, encrypted.Length);

                return Convert.ToBase64String(result);
            }
        }
    }
}
