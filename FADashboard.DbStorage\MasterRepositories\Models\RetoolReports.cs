﻿using System.ComponentModel.DataAnnotations;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class RetoolReports : IAuditedEntity, IDeactivatable
{
    public long Id { get; set; }

    [Audited] [StringLength(256)] public string Name { set; get; }

    [Audited] [Required] public Guid LandingPageUuid { get; set; }

    [Audited] public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
}
