﻿using Libraries.CommonEnums;
namespace FADashboard.DbStorage.DbContexts;

public class NonFAInvoiceDetail
{
    public long Id { set; get; }
    public long? FAInvoiceId { set; get; }
    public string InvoiceNumber { set; get; }
    public DateTime? InvoiceDate { set; get; }
    public DateTime AddedOn { set; get; }
    public long InvoiceDateKey { get; set; }
    public bool IsReceived { set; get; }
    public OrderType OrderType { get; set; }
    public long? OrderNumber { get; set; }
    public string ReferenceNumber { get; set; }
    public double Quantity { set; get; }
    public double OrderInStdUnits { get; set; }
    public double? BilledPTR { get; set; }
    public double OrderInRevenue { get; set; }
    public long ProductId { set; get; }
    public long SecondaryCategoryId { get; set; }
    public long PrimaryCategoryId { get; set; }
    public long? ProductDivisionId { get; set; }
    public double StandardUnitConversionFactor { get; set; }
    public string StandardUnit { get; set; }
    public string Unit { get; set; }
    public int InvoiceYear { get; set; }
    public int InvoiceMonth { get; set; }
    public long LocationId { set; get; }
    public long CompanyId { set; get; }
    public long? DistributorId { get; set; }
    public long EmployeeId { set; get; }
    public string Rank { set; get; }
    public long? ASMId { set; get; }
    public long? RSMId { set; get; }
    public long? ZSMId { set; get; }
    public long? NSMId { set; get; }
    public long? GSMId { set; get; }
    public long? ZoneId { get; set; }
    public long? RegionId { get; set; }
    public long? ASMUserId { set; get; }
    public long? RSMUserId { set; get; }
    public long? ZSMUserId { set; get; }
    public long? NSMUserId { set; get; }
    public long? GSMUserId { set; get; }
    public string SaleType { get; set; }
    public long? GeographyLevel5 { get; set; }
    public long? GeographyLevel6 { get; set; }
    public long? GeographyLevel7 { get; set; }
    public double? GrossRevenueValue { get; set; }
    public double? NetRevenueValue { get; set; }
    public double? DiscountValue { get; set; }
    public bool? IsSFAOrder { set; get; }
    public string ExternalOrderNumber { set; get; }
    public InvoiceType? InvoiceType { set; get; }
    public double? TotalTaxValue { set; get; }
    public long? FreeQuantity { set; get; }
    public string AttributeText1 { set; get; }
    public string AttributeText2 { set; get; }
}
