﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class CarouselBannerService(ICarouselBannerRepository carouselBannerRepsoitory, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<RepositoryResponse> CreateUpdateCarouselBanners(List<Banners> banner) => await carouselBannerRepsoitory.UpdateCarouselBanners(banner, currentUser.CompanyId);

    public async Task<List<Banners>> GetCarouselInfo() => await carouselBannerRepsoitory.GetCarouselBannersList(currentUser.CompanyId);
}
