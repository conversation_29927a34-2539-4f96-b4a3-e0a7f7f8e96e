﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IDerivedKPIRepository
{
    Task<List<DerivedKpi>> GetDerivedKPIs(long companyId, bool showDeactive = false);

    Task<RepositoryResponse> CreateDerivedKPI(DerivedKPIInput kpi, long companyId);

    Task<RepositoryResponse> UpdateDerivedKPI(DerivedKPIInput kpi, long companyId);

    Task<DerivedKPIInput> GetDerivedKPIById(long id, long companyId);

    Task<RepositoryResponse> ActivateDeactivateDerivedKPI(long kpiId, bool action, long companyId);

    Task<List<DerivedKpiInputMeasure>> GetDerivedKPIAsInput(long companyId);

    Task<List<DerivedKpiInputMeasure>> GetDerivedKPIInputMeasures();

    Task<List<DerivedKpiMin>> GetDerivedKPIs(ViewPerspective viewPerspective, long companyId);
}
