﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Game(long companyId) : IEntity, IAuditedEntity
{
    public long Id { get; set; }
    [StringLength(1000)] public string Name { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public GameRewardType RewardType { get; set; }
    public long CompanyId { get; set; } = companyId;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    [StringLength(1000)] public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public ICollection<CoinsforKpi> CoinsforKpi { get; set; }
    public ICollection<TargetForTeams> TargetsforTeams { get; set; }
}

public class TargetForTeams
{
    public long Id { get; set; }
    public long GameId { get; set; }
    public long TeamId { get; set; }
    public long KpiId { get; set; }
    [StringLength(1000)] public string Target { get; set; }
    public bool IsQualifier { get; set; }
    public bool IsContinuous { get; set; }
    public bool IsActive { get; set; }
    public Game Game { get; set; }
    public ICollection<KPISlab> KPISlabs { get; set; }
}

public class CoinsforKpi
{
    public long Id { get; set; }
    public long GameId { get; set; }
    public long KpiId { get; set; }
    public long Coins { get; set; }
    public Game Game { get; set; }
}
