﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class OutletTagService(
    ICurrentUser currentUser,
    IOutletTagRepository outletTagRepository
) : RepositoryResponse
{
    public async Task<List<OutletTagFlat>> GetAllOutletTags(bool showDeactive = false) => await outletTagRepository.GetAllOutletTags(currentUser.CompanyId, showDeactive);

    public Task<RepositoryResponse> ActivateDeactivateOutletTag(long id, bool action) => outletTagRepository.ActivateDeactivateOutletTag(id, action, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateUpdateOutletTag(OutletTagFlat outletTagFlat)
    {
        var isValidOutletTag = await IsValidOutletTag(outletTagFlat);
        if (!isValidOutletTag.IsSuccess)
        {
            return isValidOutletTag;
        }

        if (outletTagFlat.Id == 0)
        {
            return await outletTagRepository.CreateOutletTag(outletTagFlat, currentUser.CompanyId);
        }

        return await outletTagRepository.UpdateOutletTag(outletTagFlat, currentUser.CompanyId);
    }

    private async Task<RepositoryResponse> IsValidOutletTag(OutletTagFlat outletTagFlat)
    {
        var outletTags = await GetAllOutletTags();

        if (outletTagFlat.Id != 0)
        {
            var existingTag = outletTags.FirstOrDefault(f => f.Id == outletTagFlat.Id);

            if (existingTag == null)
            {
                return new RepositoryResponse { Id = outletTagFlat.Id, ExceptionMessage = "Outlet Tag does not exist", IsSuccess = false, Message = "Outlet Tag does not exist" };
            }

            var existingName = outletTags
                .Any(f => f.Id != outletTagFlat.Id && f.Name?.NormalizeCaps() == outletTagFlat.Name?.NormalizeCaps());
            if (existingName)
            {
                return new RepositoryResponse { Id = outletTagFlat.Id, ExceptionMessage = "Outlet Tag Name already exists", IsSuccess = false, Message = "Outlet Tag Name already exists" };
            }
        }
        else
        {
            if (string.IsNullOrEmpty(outletTagFlat.Name))
            {
                return new RepositoryResponse { Id = outletTagFlat.Id, ExceptionMessage = "Outlet Tag Name is required", IsSuccess = false, Message = "Outlet Tag Name is required" };
            }

            var existingName = outletTags
                .Any(f => f.Name?.NormalizeCaps() == outletTagFlat.Name?.NormalizeCaps());
            if (existingName)
            {
                return new RepositoryResponse { Id = outletTagFlat.Id, ExceptionMessage = "Outlet Tag Name already exists", IsSuccess = false, Message = "Outlet Tag Name already exists" };
            }
        }

        return new RepositoryResponse { Id = outletTagFlat.Id, Message = "Outlet Tag Unique", IsSuccess = true, };
    }

    public async Task<OutletTagFlat> GetOutletTagById(long id) => await outletTagRepository.GetOutletTagById(id, currentUser.CompanyId);
}
