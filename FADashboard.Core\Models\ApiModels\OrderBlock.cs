﻿namespace FADashboard.Core.Models.ApiModels;
public class OrderBlockList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public bool IsDeactive { get; set; }

}
public class OrderBlockModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public bool IsDeactive { get; set; }
    public List<GeographyConstraint> GeographyConstraints { get; set; }
    public List<OutletConstraint> OutletConstraints { get; set; }
    public List<OrderBlockItemModel> OrderBlockItems { set; get; }
}
public class OutletConstraint
{
    public string Property { get; set; }

    public List<string> PropertyValue { get; set; }

    public long? ProductDivisionId { get; set; }
}
public class GeographyConstraint
{
    public string GeographyEntityType { get; set; }

    public List<long> GeographyEntityIds { get; set; }
}

public class OrderBlockItemModel
{
    public long Id { get; set; }
    public DateTime ItemDate { get; set; }
    public bool Deleted { get; set; }
}

