﻿namespace FADashboard.Core.Models;

public class HCCBFileMetadata
{
    public string Name { get; set; }
    public DateTimeOffset LastModified { get; set; }
    public long Size { get; set; }
    public string Status { get; set; }
}

public class HCCBFileMetadataResponse
{
    public List<HCCBFileMetadata> Files { get; set; } = new();
}

public enum HCCBFileType
{
    PromotionMaster = 1,
    StockMaster = 2,
    PriceList = 3,
    SalesOrder = 4
}
public enum OutputEntityType
{
    Distributors,
    Routes,
    Outlets,
    Products
}
