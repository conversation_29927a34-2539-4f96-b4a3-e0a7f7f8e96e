﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IProductRecommendationRepository
{
    Task<List<ProductRecommendationList>> GetProductRecommendations(long companyId, bool includeDeactivate);
    Task<RepositoryResponse> ActivateDeactivateProductRecommendation(long Id, long companyId, bool action);
    Task<ProductRecommendationInput> GetProductRecommendationById(long Id, long companyId);
    Task<RepositoryResponse> CreateProductRecommendation(ProductRecommendationInput productRecommendationInput, long companyId);
    Task<RepositoryResponse> UpdateProductRecommendation(ProductRecommendationInput productRecommendationInput, long companyId);
}
