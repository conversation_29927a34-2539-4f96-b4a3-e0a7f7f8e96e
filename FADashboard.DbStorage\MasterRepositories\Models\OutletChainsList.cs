﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("Chains")]
public class Chains
{
    public long Id { get; set; }
    public string ChainName { get; set; }
    public long CompanyId { get; set; }
    public string ChainErpId { get; set; }
    public bool IsDeactivated { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
