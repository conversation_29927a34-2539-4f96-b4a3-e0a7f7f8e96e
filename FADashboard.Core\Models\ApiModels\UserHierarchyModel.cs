﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class UserHierarchyModel
{
    public long Id { get; set; }

    public bool IsFieldUser { get; set; }
    public string LocalName { get; set; }
    public string Name { get; set; }

    public long? ParentId { get; set; }

    //Parentid 1 includes manager id if he is field user
    public long? ParentId1 => IsFieldUser && UserRole != PortalUserRole.ClientEmployee ? Id : ParentId;

    public string PhoneNumber { get; set; }
    public PortalUserRole UserRole { get; set; }
    public EmployeeType UserType { get; set; }
}
