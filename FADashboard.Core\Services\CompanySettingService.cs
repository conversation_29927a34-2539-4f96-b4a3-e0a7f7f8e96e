﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Library.NumberSystem;

namespace FADashboard.Core.Services;

public class CompanySettingService(ICompanySettingsRepository companySettingsRepository, ICurrentUser currentUser) : RepositoryResponse
{
    #region Create and Update Company Settings

    //To Be used for new Company Creation Only
    public async Task<RepositoryResponse> CompanyNewSettingsSave(List<CompanySettingsValues> settingsValues, long companyId)
    {
        var res = await companySettingsRepository.UpdateSettingValues(settingsValues, companyId);
        return res;
    }

    public async Task<RepositoryResponse> CompanySettingsSave(List<CompanySettingsValues> settingsValues)
    {
        if (settingsValues.Select(v => v.CompanyId).ToList().Distinct().Count() == 1 && settingsValues.First().CompanyId == currentUser.CompanyId)
        {
            var res = await companySettingsRepository.UpdateSettingValues(settingsValues, currentUser.CompanyId);
            return res;
        }

        return new RepositoryResponse { Id = currentUser.CompanyId, Message = $"Current User Company ID {currentUser.CompanyId} does not match with {settingsValues.First().CompanyId} in the Input for the Current User", IsSuccess = false };
    }

    public async Task<Dictionary<string, object>> GetCompanySettings() => await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);

    public async Task<List<CompanySettingsValues>> GetSettingForCreate()
    {
        var set = await companySettingsRepository.GetAllSettingsAsync();
        var requiredSettings = new List<CompanySetting>();
        foreach (var setting in set)
        {
            if (setting.SettingKey is "PricePerUser" or "BillingType" or "MinimumBillingUser" or "IsInvoiceHardcopyRequired" or "GracePeriodCount" or
                "PaymentRecoveryEmailIDs" or "PaymentRecoveryMobileNumber" or "LimitSalesHierarchyUserCount" or "UsesCappingonEmployeeCreation")
            {
                requiredSettings.Add(setting);
            }
        }

        var values = await companySettingsRepository.GetAllConfigs(currentUser.CompanyId);
        var listToReturn = new List<CompanySettingsValues>();
        foreach (var item in requiredSettings)
        {
            var isSettingExist = values.Exists(v => v.SettingId == item.Id);
            CompanySettingsValues val;
            if (isSettingExist)
            {
                val = new CompanySettingsValues(item, values.Where(v => v.SettingId == item.Id).Select(v => v.SettingValue).SingleOrDefault());
                listToReturn.Add(val);
            }
            else
            {
                val = new CompanySettingsValues(item);
                listToReturn.Add(val);
            }
        }

        return listToReturn;
    }

    public async Task<List<CompanySetting>> GetSettings() => [.. (await companySettingsRepository.GetAllSettingsAsync()).OrderBy(o => o.Section).ThenBy(t => t.Order)];

    #endregion Create and Update Company Settings

    public async Task<NumberSystems> UsesInternationalNumberSystem()
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        return companySettings.NumberSystem;
    }
}
