﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class MustSellProductService(IMustSellProductRepository mustSellProductRepository, ICurrentUser currentUser) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidMustSellProduct(MustSellProductInput mustSellProduct)
    {
        var mustSellProducts = await GetMustSellProducts(true);
        if (mustSellProduct.Id != 0)
        {
            mustSellProducts = mustSellProducts.Where(p => p.Id != mustSellProduct.Id).ToList();
        }

        var mustSellProdNameList = mustSellProducts.Select(p => p.Name.NormalizeCaps()).ToList();

        if (mustSellProdNameList.Contains(mustSellProduct.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = mustSellProduct.Id, ExceptionMessage = "Must Sell Product Rule Name is not unique", Message = "Must Sell Product Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = mustSellProduct.Id, Message = "Must Sell Product Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> CreateUpdateMustSellProduct(MustSellProductInput mustSellProduct)
    {
        var checkValid = await IsValidMustSellProduct(mustSellProduct);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (mustSellProduct.Id == 0)
        {
            return await mustSellProductRepository.CreateMustSellProduct(mustSellProduct, currentUser.CompanyId);
        }

        return await mustSellProductRepository.UpdateMustSellProduct(mustSellProduct, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> DeactivateMustSellProduct(long mustSellProductId) => await mustSellProductRepository.DeactivateMustSellProduct(mustSellProductId, currentUser.CompanyId);

    public async Task<MustSellProductInput> GetMustSellProductById(long mustSellProductId)
    {
        var mustSellProduct = await mustSellProductRepository.GetMustSellProductById(mustSellProductId, currentUser.CompanyId);
        return mustSellProduct;
    }

    public async Task<List<MustSellProductList>> GetMustSellProducts(bool includeDeactivate)
    {
        var products = await mustSellProductRepository.GetMustSellProducts(currentUser.CompanyId, includeDeactivate);
        return products;
    }
}
