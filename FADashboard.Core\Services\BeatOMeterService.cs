﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class BeatOMeterService(
    ICurrentUser currentUser,
    IBeatOMeterRepository beatOMeterRepository
) : RepositoryResponse
{
    public async Task<List<BeatOMeterFlat>> GetAllBeatOMeterRules(bool showDeactive = false) => await beatOMeterRepository.GetAllBeatOMeterRules(currentUser.CompanyId, showDeactive);

    public async Task<List<EntityMin>> GetAllNewBeatOMeterRulesWithinDateRange(DateTime startDate, DateTime endDate) => await beatOMeterRepository.GetAllNewBeatoMeterRulesWithinDateRange(currentUser.CompanyId, startDate, endDate);

    public async Task<BeatOMeterById> GetBeatOMeterById(long id) => await beatOMeterRepository.GetBeatOMeterById(id, currentUser.CompanyId);

    public async Task<RepositoryResponse> DeactivateBeatOMeterRule(long id) => await beatOMeterRepository.DeactivateBeatOMeterRule(id, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateBeatOMeterRule(BeatOMeterById beatOMeterData) => await beatOMeterRepository.CreateBeatOMeterRule(beatOMeterData, currentUser.CompanyId);
    public async Task<List<BeatOMeterKpiBasedFlat>> GetAllKpiBasedBeatOMeterRules(bool showDeactive = false) => await beatOMeterRepository.GetAllKpiBasedBeatOMeterRules(currentUser.CompanyId, showDeactive);
    public async Task<RepositoryResponse> DeactivateKpiBasedBeatOMeterRule(long id) => await beatOMeterRepository.DeactivateKpiBasedBeatOMeterRule(id, currentUser.CompanyId);
    public async Task<KpiBasedBeatOMeterById> GetKpiBasedBeatOMeterById(long id) => await beatOMeterRepository.GetKpiBasedBeatOMeterById(id, currentUser.CompanyId);
    public async Task<RepositoryResponse> CreateKpiBasedBeatOMeterRule(KpiBasedBeatOMeterById beatOMeterData)
    {
        foreach (var tagDetail in beatOMeterData.TagDetails)
        {
            if (tagDetail.VisitType)
            {
                var conditionsForVisitType = new List<MetricDetail>();

                foreach (var condition in tagDetail.TagConstraintDetails.Conditions)
                {
                    conditionsForVisitType.Add(new MetricDetail
                    {
                        Metric = condition.Metric,
                        IsVisited = condition.IsVisited,
                        ConstraintDays = condition.ConstraintDays,
                    });
                }
                tagDetail.TagConstraintDetails = new TagConstraint
                {
                    Conditions = conditionsForVisitType,
                    Operation = tagDetail.TagConstraintDetails.Operation
                };
            }
            else
            {
                var conditionsForNonVisitType = new List<MetricDetail>();

                foreach (var condition in tagDetail.TagConstraintDetails.Conditions)
                {
                    conditionsForNonVisitType.Add(new MetricDetail
                    {
                        MetricId = condition.MetricId,
                        MetricFrequency = condition.MetricFrequency,
                        NumberOfDays = condition.NumberOfDays,
                        ParameterValue = condition.ParameterValue,
                        Condition = condition.Condition,
                        ConstraintValue = condition.ConstraintValue
                    });
                }

                tagDetail.TagConstraintDetails = new TagConstraint
                {
                    Conditions = conditionsForNonVisitType,
                    Operation = tagDetail.TagConstraintDetails.Operation
                };
            }
        }
        return await beatOMeterRepository.CreateKpiBasedBeatOMeterRule(beatOMeterData, currentUser.CompanyId);
    }
}
