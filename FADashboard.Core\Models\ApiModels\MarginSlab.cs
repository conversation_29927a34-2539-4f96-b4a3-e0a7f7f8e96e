﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class MarginSlab
{
    public long Id { get; set; }
    public string Name { get; set; }
    public MarginEntity EntityType { get; set; }
    public bool Deleted { get; set; }
    public int OutletCount { get; set; }
}
public class EntityMarginSlabModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public double Value { get; set; }
}
public class MarginSlabModel
{
    public long Id { get; set; }
    public string Name { get; set; }
    public MarginEntity EntityType { get; set; }
    public MarginSlabAttributeConstraint AttributeFilterConstraints { set; get; }

    public List<EntityMarginSlabModel> EntityMargin { get; set; }
}
public class MarginSlabAttributeConstraint
{
    public List<string> MRP { set; get; }

    public List<string> AttributeText1 { set; get; }

    public List<string> AttributeText2 { set; get; }
}


public class MarginSlabAttributeConstraintToBeSaved
{
    public List<string> MRP { set; get; }
    public List<string> AttributeText1 { set; get; }
    public List<string> AttributeText2 { set; get; }

}
