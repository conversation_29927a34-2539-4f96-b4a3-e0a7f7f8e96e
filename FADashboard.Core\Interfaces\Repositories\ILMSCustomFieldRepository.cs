﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSCustomFieldRepository
    {
        Task<LMSCustomFieldDto> GetByIdAsync(long id);
        Task<List<LMSCustomFieldDto>> GetByEntityAsync(int entityType, long entityId);
        Task<LMSCustomFieldDto> AddAsync(LMSCustomFieldDto customField);
        Task UpdateAsync(LMSCustomFieldDto customField);
        Task DeleteAsync(long id);
        Task AddRangeAsync(List<LMSCustomFieldDto> customFields);
        Task DeleteByEntityAsync(int entityType, long entityId);

    }
}
