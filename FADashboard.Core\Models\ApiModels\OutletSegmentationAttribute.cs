﻿namespace FADashboard.Core.Models.ApiModels;

public class OutletSegmentationAttribute
{
    public string DisplayName { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsValid { get; set; }
    public double MaxOutletOrderPotential { get; set; }
    public decimal MaxOutletPotential { get; set; }
    public decimal MinOutletPotential { get; set; }
    public int Segmentation { get; set; }
    public int VisitsPerMonth { get; set; }
    public long? ProductDivisionId { get; set; }
    public decimal MaxOutletPotentialInStdUnit { get; set; }
    public decimal MinOutletPotentialInStdUnit { get; set; }
    public decimal MinOrderValue { get; set; }
    public DateTime LastSubmissionDate { get; set; }
    public DateTime LastRefreshDate { get; set; }
}

public class OutletSegmentationInput
{
    public string DisplayName { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public double MaxOutletOrderPotential { get; set; }
    public decimal MaxOutletPotential { get; set; }
    public decimal MinOutletPotential { get; set; }
    public int Segmentation { get; set; }
    public int VisitsPerMonth { get; set; }
    public long? ProductDivisionId { get; set; }
    public decimal MaxOutletPotentialInStdUnit { get; set; }
    public decimal MinOutletPotentialInStdUnit { get; set; }
    public decimal MinOrderValue { get; set; }
}
