﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;

namespace FADashboard.Core.Services;

public class CompanyModuleService(ICompanyModuleRepository companyModuleRepsoitory, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<bool> IsModernTrade()
    {
        var moduleData = await companyModuleRepsoitory.GetCompanyModule(currentUser.CompanyId);
        var modernTrade = moduleData.Find(s => s.Name == "Modern Trade");
        if (modernTrade != null)
        {
            return true;
        }

        return false;
    }

    public async Task<RepositoryResponse> SetCompulsoryModulesForNewCompany(long companyId)
    {
        var res = await companyModuleRepsoitory.SetCompulsoryModulesForNewCompany(companyId);
        return res;
    }
}
