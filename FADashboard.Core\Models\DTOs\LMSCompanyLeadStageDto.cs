﻿using System;
using System.ComponentModel.DataAnnotations;
using FADashboard.Core.Models;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSCompanyLeadStageDto
    {
        public long Id { get; set; }

        [Required]
        public long CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        public string StageName { get; set; }

        [Required]
        public LeadStageCategory StageCategory { get; set; }

        [Range(0.0, 100.0, ErrorMessage = "Closure percentage must be between 0 and 100.")]
        public decimal ClosurePercentage { get; set; }
        public long LeadTemplateId { get; set; }

        public int Sequence { get; set; }

        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; }

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }

    public class LMSCompanyLeadStageInput
    {
        public long Id { get; set; } // 0 for new stages

        [Required]
        [StringLength(100)]
        public string StageName { get; set; }

        [Required]
        public LeadStageCategory StageCategory { get; set; }

        [Range(0.0, 100.0)]
        public decimal ClosurePercentage { get; set; }

        public int Sequence { get; set; }

        public bool IsActive { get; set; }
    }

    public class LMSCompanyLeadStageBulkInputDto
    {
        public long LeadTemplateId { get; set; } // 0 for new template

        [Required]
        public List<LMSCompanyLeadStageInput> LeadStages { get; set; }
    }
}
