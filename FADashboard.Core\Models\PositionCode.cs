﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class PositionCodeDetails
{
    public string CodeId { get; set; }
    public long? EmployeeId { get; set; }
    public string EmployeeLocalName { get; set; }
    public string EmployeeName { get; set; }
    public string EmployeeErpId { get; set; }
    public PortalUserRole? EmployeeRole { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool? IsFieldAppuser { get; set; }
    public bool IsVacant { get; set; }
    public PositionCodeLevel Level { get; set; }
    public string LevelName { get; set; }
    public string Name { get; set; }
    public long? ParentId { get; set; }
    public PositionCodeLevel? ParentLevel { get; set; }
    public string ParentName { get; set; } // reporting to
    public long? RegionId { get; set; }
}

public class PositionCodeRecord
{
    public string AttachedTo { get; set; }
    public long? AttachedToId { get; set; }
    public string LastAttachedTo { get; set; }
    public long? LastAttachedToId { get; set; }
    public string CodeId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsVacant { get; set; }
    public PositionCodeLevel Level { get; set; }
    public string LevelName { get; set; }
    public string Name { get; set; }
    public long? ParentId { get; set; }
    public EmployeeType  PositionType { get; set; }
// reporting to
public PositionCodeLevel? ParentLevel { get; set; }

    // reporting to
    public string ParentLevelName { get; set; }

    public string ParentName { get; set; } // reporting to

    public List<long> AttachedToProductDivisionIds { get; set; }
    public string AttachedToProductDivisionNames { get; set; }
}

public class PositionCodeRecordsWithTotal
{
    public int Total { get; set; }
    public List<PositionCodeRecord> PositionCodeRecords { get; set; }
}

public class ProductDivisionAttachedPositionInfo
{
    public long PositionId { get; set; }
    public long ProductDivisionId { get; set; }
    public string ProductDivisionName { get; set; }
}

public class PositionCodeEntityMappingsInfo
{
    public long PositionId { get; set; }
    public long ClientEmployeeId { get; set; }
    public string ClientEmployeeName { get; set; }
    public string ClientEmployeeContactNo { get; set; }
}
public class PositionsInfo
{
    public long Id { get; set; }
    public PositionCodeLevel Level { get; set; }
}

