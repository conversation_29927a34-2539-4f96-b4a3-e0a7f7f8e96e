﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonModels;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class LandingPageV2Service(ICurrentUser currentUser, FAResilientHttpClient resilientHttpClient, AppConfigSettings appConfigSettings) : RepositoryResponse
{
    public async Task<MTDOutletSummary> GetOutletSummary(DateTime date, string level, List<long> pcIds)
    {
        pcIds ??= [];
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/landingPageDashboard/OutletSummary?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}";
        var data = await resilientHttpClient.PostJsonAsync<MTDOutletSummary>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<MtdDistributorSummary> GetMtdDistributorSummary(DateTime date, string level, List<long> pcIds, int pageNumber, int pageSize, string querySearch)
    {
        pcIds ??= [];
        var dataUrl =
            $"{appConfigSettings.reportApiBaseUrl}api/landingPageDashboard/DistributorSummary?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}&pageNumber={pageNumber}&pageSize={pageSize}&querySearch={querySearch}";
        var data = await resilientHttpClient.PostJsonAsync<MtdDistributorSummary>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<PositionDetailsRecordsWithTotal> GetMtdDemandVsSalesPosition(DateTime date, string level, int dateFilterType, List<long> pcIds, int pageNumber, int pageSize, string querySearch)
    {
        pcIds ??= [];
        var dataUrl =
            $"{appConfigSettings.reportApiBaseUrl}api/landingPageDashboard/PositionSummaryMTDShort?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}&dateFilterType={dateFilterType}&pageNumber={pageNumber}&pageSize={pageSize}&querySearch={querySearch}";
        var data = await resilientHttpClient.PostJsonAsync<PositionDetailsRecordsWithTotal>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<GeographyDetailsRecordsWithTotal> GetMtdDemandVsSalesGeography(DateTime date, string level, int dateFilterType, List<long> pcIds, int pageNumber, int pageSize, string querySearch)
    {
        pcIds ??= [];
        var dataUrl =
            $"{appConfigSettings.reportApiBaseUrl}api/landingPageDashboard/GeographyMTDSummary?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&dateFilterType={dateFilterType}&level={level}&pageNumber={pageNumber}&pageSize={pageSize}&querySearch={querySearch}";
        var data = await resilientHttpClient.PostJsonAsync<GeographyDetailsRecordsWithTotal>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<List<TargetAndAchievementMTDLMTD>> GetTargetVsAchievementSummaryData(DateTime date, string level, List<long> pcIds)
    {
        pcIds ??= [];
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/landingPageDashboard/TargetVsAchievement?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}";
        var data = await resilientHttpClient.PostJsonAsync<List<TargetAndAchievementMTDLMTD>>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<AttendanceSummary> GetCallSummaryData(DateTime date, string level, List<long> pcIds)
    {
        pcIds ??= [];
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/call/callSummaryUsingPositions?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}";
        var data = await resilientHttpClient.PostJsonAsync<AttendanceSummary>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<DayStartTypeSummary> GetUserActivitySummaryData(DateTime date, string level, List<long> pcIds)
    {
        pcIds ??= [];
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/ds/dayUserSummaryPositions?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}";
        var data = await resilientHttpClient.PostJsonAsync<DayStartTypeSummary>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }

    public async Task<List<ProductCategoryWiseMTD>> GetPrimaryCategorySummaryData(DateTime date, string level, int dateFilter, List<long> pcIds)
    {
        pcIds ??= [];
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/landingPageDashboard/ProductCategorySummary?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&level={level}";
        var data = await resilientHttpClient.PostJsonAsync<List<ProductCategoryWiseMTD>>(dataUrl, appConfigSettings.reportApiToken, pcIds);
        return data;
    }
}
