﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class QueryView : IAuditedEntity, IDeletable
{
    [NotMapped]
    public List<string> Columns
    {
        get => ColumnsStr == null ? [] : JsonConvert.DeserializeObject<List<string>>(ColumnsStr);
        set => ColumnsStr = value == null ? "[]" : JsonConvert.SerializeObject(value);
    }

    [Obsolete("Only for DbAccess, Use Columns Instead!")]
    [StringLength(2048)]
    public string ColumnsStr { get; set; }

    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateRangePreset PresetTimePeriod { get; set; }
    public bool ShowInUserApp { get; set; }
    public virtual CompanyAdmin User { get; set; }
    public long UserId { get; set; }
    public string ViewName { get; set; }
    public int ViewPerspective { get; set; }
}
