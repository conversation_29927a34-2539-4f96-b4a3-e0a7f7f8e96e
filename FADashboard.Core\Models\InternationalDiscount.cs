﻿namespace FADashboard.Core.Models;

public class InternationalDiscount
{
    public bool Deleted { get; set; }
    public string DiscountName { get; set; }
    public string EndDate { get; set; }
    public long? FirstLevelDiscount { get; set; }
    public bool FirstLevelDiscountAutoApplied { get; set; }
    public bool FirstLevelDiscountIsEditable { get; set; }
    public bool FOCApplicable { get; set; }
    public bool FOCMandatory { get; set; }
    public long Id { get; set; }
    public long? SecondLevelDiscount { get; set; }
    public bool SecondLevelDiscountAutoApplied { get; set; }
    public bool SecondLevelDiscountIsEditable { get; set; }
    public string Segmentation { get; set; }
    public string StartDate { get; set; }
}
