﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class CustomReportService(
    ICustomReportRepository customReportRepository,
    IUserWiseCustomReportRepository userWiseCustomReportRepository) : RepositoryResponse
{
    public async Task<CustomReport> GetCustomReport(long reportId, long companyId, long employeeId, PortalUserRole userRole)
    {
        var customReport = await customReportRepository.GetCustomReport(reportId);
        var userWiseCustomReport = await userWiseCustomReportRepository.GetUserWiseCustomReportItems(customReport.Id, companyId, employeeId, userRole);
        var userWiseReportItems = userWiseCustomReport != null ? userWiseCustomReport.UserWiseCustomReportItems : [];
        var selectedPreferences = userWiseCustomReport != null ? userWiseCustomReport.SelectedReportPreferences : [];
        var itemIds = userWiseReportItems.Select(u => u.CustomReportItemId).Distinct().ToList();
        foreach (var report in customReport.CustomReportItems)
        {
            if (itemIds.Contains(report.Id))
            {
                report.IsSelected = true;
            }
        }

        foreach (var preference in customReport.ReportPreferences)
        {
            preference.Value = selectedPreferences.TryGetValue(preference.Name, out var value) && value != null ? value : preference.Value;
        }

        return customReport;
    }
}
