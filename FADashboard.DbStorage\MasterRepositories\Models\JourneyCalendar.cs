﻿using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class JourneyCalendar : IAuditedEntity
{
    public long Id { get; set; }

    [Audited]
    public string Name { get; set; }

    [Audited]
    public int ForMonths { get; set; }

    [Audited]
    public DateTime StartDate { get; set; }

    [Audited]
    public DateTime EndDate { get; set; }

    [Audited]
    public int Year { get; set; }

    public string SelectedZones { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }

    public bool Deleted { get; set; }

    public bool IsDeactive { get; set; }

    public long CompanyId { get; set; }

    public virtual Company Company { get; set; }

    public virtual ICollection<JourneyCycle> JourneyCycles { get; set; }
}
