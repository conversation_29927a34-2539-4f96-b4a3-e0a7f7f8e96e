﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class SchemeOnModel
{
    public string DiscountOnNames { set; get; }
    public string DiscountOnType { set; get; }
}

public class SchemeOutletConstraintDetails
{
    public List<string> RequiredChannels { set; get; }
    public bool? RequiredIsFocused { set; get; }
    public List<string> RequiredSegmentations { set; get; }
    public List<string> RequiredShopTypes { set; get; }
}

public class SchemesDetail
{
    public SchemeCategorization Category { set; get; }
    public ConstraintType ConstraintType { set; get; }
    public SchemeOnModel DiscountOn { set; get; }
    public string Distributor { set; get; }

    [Column(TypeName = "datetime2")] public DateTime EndTime { get; set; }

    public string ErpId { get; set; }
    public int SchemeStep { get; set; }
    public Guid Guid { get; set; }
    public bool HavePayoutProduct { set; get; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsOutletConstraint { set; get; }
    public string Name { get; set; }
    public Dictionary<OutletChannel, string> OutletChannelDict { get; set; }

    [DisplayName("Outlet Constraints")] public SchemeOutletConstraintDetails OutletConstraints { set; get; }

    public PayoutCalculationType PayoutCalculationType { set; get; }

    public PayoutType PayoutType { set; get; }
    public PayoutIn? PayoutIn { get; set; }

    public string Region { get; set; }

    public IEnumerable<SchemeSlabsDetails> SchemeSlabs { set; get; }

    public Dictionary<OutletSegmentation, string> SegmentationDict { get; set; }

    [Column(TypeName = "datetime2")] public DateTime StartTime { get; set; }

    public string StringDescription { get; set; }
    public string Zone { set; get; }
    public long? PreferredPayoutProduct { get; set; }
}

public class SchemeSlabsDetails
{
    public double Constraint { set; get; }

    public double Payout { set; get; }

    public string PayoutDescription { get; set; }

    public string PayoutProduct { set; get; }

    public int Priority { set; get; }
}
