﻿namespace FADashboard.DbStorage.HCCBRepositories.Models
{
    public class OrdersApiLogs
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public string OrderIdsJson { get; set; }
        public bool IsProcessed { get; set; }
        public bool IsSuccess { get; set; }
        public int TotalPages { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
}
