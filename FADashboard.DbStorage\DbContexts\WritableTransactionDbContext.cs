﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.DbStorage.TransactionRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace FADashboard.DbStorage.DbContexts;

public class WritableTransactionDbContext(DbContextOptions<WritableTransactionDbContext> options, ICurrentUser identity) : DbContextAbstract(options, identity)
{
    public DbSet<AttendanceRegulariseRequest> AttendanceRegulariseRequest { get; set; }
    public DbSet<DeadOutletRequestItem> DeadOutletRequestItems { get; set; }
    public DbSet<DeadOutletRequest> DeadOutletRequests { get; set; }
    public DbSet<OutletCreationRequestManagerEdit> OutletCreationRequestManagerEdits { get; set; }
    public DbSet<OutletCreationRequest> OutletCreationRequests { get; set; }
    public DbSet<OpenMarketDayStock> OpenMarketDayStocks { get; set; }
    public DbSet<UserCredentials> UserCredentials { get; set; }
    public DbSet<SMSRecipientDetails> SMSRecipientDetails { get; set; }
    public DbSet<AssetAllocations> AssetAllocations { get; set; }
    public DbSet<DistributorCreationRequests> DistributorCreationRequests { get; set; }
    public DbSet<DistributorCreationRequestManagerEdits> DistributorCreationRequestManagerEdits { get; set; }
    public DbSet<AssetAgreements> AssetAgreements { get; set; }
    public DbSet<AssetReallocation> AssetReallocationRequests { get; set; }

    public DbSet<Attendances> Attendances { get; set; }
    public DbSet<OutletVerification> OutletVerifications { get; set; }
    public DbSet<LMSLeadActivityMaster> LMSLeadActivitiesMaster { get; set; }
    public DbSet<LMSLeadActivityTransactions> LMSLeadActivitiesTransactions { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder) => modelBuilder.Entity<LMSLeadActivityTransactions>()
            .HasOne<LMSLeadActivityMaster>()
            .WithMany()
            .HasForeignKey(t => t.ActivityMasterId)
            .HasPrincipalKey(m => m.Id)
            .OnDelete(DeleteBehavior.Restrict); // or .NoAction, depending on desired behavior

    public void RejectChanges()
    {
        var entries = ChangeTracker.Entries().ToList();
        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                case EntityState.Deleted:
                    entry.State = EntityState.Modified; //Revert changes made to deleted entity.
                    entry.State = EntityState.Unchanged;
                    break;

                case EntityState.Added:
                    entry.State = EntityState.Detached;
                    break;
                case EntityState.Detached:
                    break;
                case EntityState.Unchanged:
                    break;
            }
        }
    }

    public override int SaveChanges(bool acceptAllChangesOnSuccess) => throw new InvalidOperationException("use save changes async instead");
}
