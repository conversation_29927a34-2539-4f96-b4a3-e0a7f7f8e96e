﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ProductToEmptyMapping")]
public class ProductToEmptyMapping
{
    public long Id { get; set; }
    public long EmptyId { get; set; }
    public long ProductId { get; set; }
    public long CompanyId { get; set; }
    public bool IsActive { get; set; } = false;
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [StringLength(32)]
    public string CreationContext { get; set; }

    [ForeignKey("EmptyId")]
    public virtual EmptyMaster EmptyMasters { get; set; }

    [ForeignKey("ProductId")]
    public virtual CompanyProduct CompanyProduct { get; set; }

    public virtual Company Company { get; set; }
}
