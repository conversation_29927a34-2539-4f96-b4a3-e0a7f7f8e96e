﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class MotorableDistanceCalculationAPIs : IAuditedEntity
{
    public long Id { get; set; }
    public string ApiType { get; set; }
    public string ApiLink { get; set; }
    public long CompanyId { get; set; }
    public string ApiUserName { get; set; }
    public string ApiKey { get; set; }
    public string Activity { get; set; }
    public string PositionLevel { get; set; }
    public string UserLevel { get; set; }
    public DateTime CreatedAt { get; set; }
    public string FloAPIToken { get; set; }
    public string WorkFlowDetails { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
