﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Services
{
    public class LMSLeadContactService(ILMSLeadContactRepository lmsLeadContactRepository) : ILMSLeadContactService
    {
        public async Task<LMSLeadContactDto> GetLeadContactAsync(long leadId, long contactId) =>
            await lmsLeadContactRepository.GetByIdAsync(leadId, contactId);

        public async Task<IEnumerable<LMSLeadContactDto>> GetLeadContactsAsync(long leadId) =>
            await lmsLeadContactRepository.GetAllByLeadIdAsync(leadId);

        public async Task<LMSLeadContactDto> CreateLeadContactAsync(long leadId, LMSLeadContactCreateInput input, long userId) =>
            await lmsLeadContactRepository.CreateAsync(leadId, input, userId);

        public async Task<LMSLeadContactDto> UpdateLeadContactAsync(long leadId, long contactId, LMSLeadContactUpdateInput input, long userId) =>
            await lmsLeadContactRepository.UpdateAsync(leadId, contactId, input, userId);

        public async Task<bool> DeleteLeadContactAsync(long leadId, long contactId, long userId) =>
            await lmsLeadContactRepository.DeleteAsync(leadId, contactId, userId);
    }
}
