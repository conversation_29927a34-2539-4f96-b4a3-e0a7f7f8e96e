﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class UserWiseCustomReportService(
    ICustomReportRepository customReportRepository,
    IUserWiseCustomReportRepository userWiseCustomReportRepository) : RepositoryResponse
{
    public async Task SaveUserWiseCustomReportRequest(long companyId, long employeeId, PortalUserRole userRole, UserWiseCustomReportRequest request)
    {
        var reportItems = await customReportRepository.GetCustomReportItems(request.CustomReportId);
        await userWiseCustomReportRepository.SaveUserWiseCustomReportRequest(companyId, employeeId, userRole, request, reportItems);
    }
}
