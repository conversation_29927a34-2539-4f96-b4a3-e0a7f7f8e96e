﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class Teamlist
{
    public long Id { get; set; }
    public string Name { get; set; }
}
public class NormsList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Status { get; set; }
    public long CompanyId { get; set; }
    public bool IsActive { get; set; }
}

public class AttendanceNorms
{
    public string Name { get; set; }
    public List<AttendanceNormPolicy> AttendanceNormPolicies { get; set; }
}

public class AttendanceNormPolicy
{
    public long TeamId { get; set; }
    public string TeamName { get; set; }
    public List<AttendanceTargetForTeam> AttendanceTargetForTeam { get; set; }
}

public class AttendanceTargetForTeam
{
    public KRAAttendanceNorm KRAType { get; set; }
    public string FullDayTarget { get; set; }
    public string HalfDayTarget { get; set; }
}
