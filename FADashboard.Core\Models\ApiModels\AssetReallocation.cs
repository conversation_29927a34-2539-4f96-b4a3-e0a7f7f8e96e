﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class AssetReallocation
{
    public long Id { get; set; }
    public long? NewOutletId { get; set; }
    public long CurrentOutletId { get; set; }
    public long EmployeeId { get; set; }
    public long? ApprovedBy { get; set; }
    public DateTime? ApprovedOn { get; set; }
    public PortalUserRole? ApprovalRole { get; set; }
    public long? FAEventId { set; get; }
    public Status Status { get; set; }
    public string Reason { get; set; }
    public long EquipmentId { set; get; }
    public string AssetReferenceNo { get; set; }
    public long AssetTypeId { get; set; }
}

public class AssetReallocationDetails : SurveyResponseOutput
{
    public long EquipmentId { set; get; }
    public string AssetReferenceNo { get; set; }
    public long AssetTypeId { get; set; }
    public string Reason { get; set; }
    public long? NewOutletId { get; set; }
}
