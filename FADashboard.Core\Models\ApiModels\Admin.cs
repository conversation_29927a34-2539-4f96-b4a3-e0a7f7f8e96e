﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class Admin
{
    public string EmailId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsBillable { set; get; }
    public bool TwoFactorEnabled { set; get; }
    public string Name { get; set; }
    public string PhoneNo { get; set; }
    public string PositionInCompany { get; set; }
    public long? RegionalParentId { get; set; }
    public PortalUserRole? RegionaParentUserRole { get; set; }
    public PortalUserRole UserRole { get; set; }
    public List<long?> RoleIds { get; set; }
    public List<long?> UserPositionIds { get; set; }
    public List<long> FactoryIds { get; set; }
    public string UserName {  get; set; }
    public Guid? LoginGuid { get; set; }
}
