﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class StateWithDistricts
{
    public string CityName { get; set; }

    [ForeignKey("CountryDetail")] public string CountryName { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public long Id { get; set; }
    public string StateName { get; set; }
}
