﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class JourneyWeek : IAuditedEntity
{
    public long Id { get; set; }
    public virtual JourneyCycle JourneyCycle { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long JourneyCycleId { get; set; }
    public int WeekForMonth { get; set; }
    public int QuarterNumber { get; set; }
    public int WeekForQuarter { get; set; }
    public int WeekForYear { get; set; }
    public DateTime WeekStartDate { get; set; }
    public DateTime WeekEndDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public long CompanyId { get; set; }
    public virtual Company Company { get; set; }
}
