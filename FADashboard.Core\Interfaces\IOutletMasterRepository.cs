﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IOutletMasterRepository
{
    Task<RepositoryResponse> ActivateOutlet(long companyId, long outletId);

    Task<RepositoryResponse> BlockOutletList(long compnayId, List<long> outletIds);

    Task<RepositoryResponse> CreateOutlet(OutletInput outlet, long companyId, string requestGuid);

    Task<RepositoryResponse> DeactivateOutlet(long companyId, long outletId, bool usesRoutePlan);

    Task<List<string>> GetAllFranchises(long companyId);

    Task<List<EntityMinWithStatus>> GetAllRoutes(long companyId, bool inludeDeactive);

    Task<List<string>> GetAllStates(string country);

    Task<BeatMinModel> GetBeatById(long companyId, long id);

    Task<List<BeatMinModel>> GetBeats(long companyId, bool inludeDeactive, List<long> regionIds = null);

    Task<MarginSlabMin> GetMarginSlabById(long companyId, long id);

    Task<List<MarginSlabMin>> GetMarginSlabs(long companyId, bool inludeDeactive);

    Task<OutletInput> GetOutletById(long companyId, long id);

    Task<List<OutletMasterList>> GetOutlets(long companyId, bool showDeactive);

    Task<OutletTotal> GetOutlets(long companyId, PaginationFilter validFilter, int? verificationStatusEnum, List<long> regionIds = null);
    Task<List<LocationModel>> GetAllOutletByRegion(long companyId, List<long> regionIds);

    Task<List<LocationModel>> GetAllOutletWithRegion(long companyId);

    Task<List<OutletSegmentationAttribute>> GetOutletSegmentationAttributes(long companyId, bool inludeDeactive);

    Task<EntityMinWithStatus> GetRouteById(long companyId, long id);

    Task<RepositoryResponse> ResetLocation(long companyId, long outletId);

    Task<RepositoryResponse> UpdateOutlet(OutletInput outlet, long companyId);

    Task<List<OutletMasterList>> GetOutletsOfBeats(long companyId, List<long> beatIds, bool inludeDeactive);
    Task<RepositoryResponse> ModifyBeatOutlets(List<BeatOutlets> beatOutlets, long companyId, CompanySettings companySettings);

    Task<List<LocationDTO>> GetOutletsByIds(long companyId, List<long> ids);
}
