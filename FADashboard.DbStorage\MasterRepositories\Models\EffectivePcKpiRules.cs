﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class EffectivePcKpiRules : IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Kpis { get; set; }
    public string Cohorts { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool IsDeactive { get; set; }
    public bool IsDefault { get; set; }
}
