﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models
{
    [Table("LMSAccountNotes")]
    public class LMSAccountNote
    {
        [Key]
        public long Id { get; set; }
        [ForeignKey("LMSAccount")]
        public long AccountId { get; set; }
        public virtual LMSAccount LMSAccount { get; set; }

        public long CompanyId { get; set; }

        [Required]
        [StringLength(1000)]
        [Column("Description")]
        public string Description { get; set; }

        [StringLength(500)]
        public string Attachment { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
