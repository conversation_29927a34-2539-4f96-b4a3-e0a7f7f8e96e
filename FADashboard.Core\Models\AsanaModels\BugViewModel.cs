﻿using FADashboard.Core.Helper.AsanaHelpers;
using Libraries.CommonEnums;
using Microsoft.AspNetCore.Http;

namespace FADashboard.Core.Models.AsanaModels;
public class BindPropertyAttribute : Attribute
{
}

public class BugViewModel : IRequirement
{
    public string IssueTitle { get; set; }

    public bool HCCB { get; set; }

    public Platform Platform { get; set; }

    public string AppVersion { get; set; }

    public long UserId { get; set; }

    public string UserName { get; set; }

    public string UserContactDetails { get; set; }

    public bool IsReproducedAtYourEnd { get; set; }

    public string ReportName { get; set; }

    public string ReportUserName { get; set; }

    public string DateRange { get; set; }

    public string HierarchyFilter { get; set; }
    //public string TokenUserName { get; set; }

    //public string Key { get; set; }
    //public string ApiLink { get; set; }
    public string IssueDescription { get; set; }

    public string StepsToReproduce { get; set; }

    public string ExpectedOutput { get; set; }

    public string Severity { get; set; }

    public string ReasonOfSeverity { get; set; }

    public string CompanyName { get; set; }

    public long ClientId { get; set; }

    public string? ClientCategory { get; set; }

    public string ListOfCompanies { get; set; }

    public string SelectedModule { get; set; }

    //[BindProperty]
    public List<IFormFile> Attachment { get; set; }

    public string GetTitle()
    {
        return IssueTitle;
    }

    public string GetDescription()
    {
        var content = $"{Platform}";
        switch ($"{Platform}")
        {
            case "Dashboard":
            case "NewDashboard":

                content += $" -->{SelectedModule}";
                break;

            case "NSApp":
            case "GTFlutter":
            case "VGTApp":
            case "MTApp":
            case "V8App":
                content += $" --> AppVersion:{AppVersion} --> UserName:{UserName} --> User Contact Details:{UserContactDetails} --> Issue Reproduced at your End:{IsReproducedAtYourEnd}";
                break;

            /*case ("ExternalAPI"):
                content += $" --> Api Credentials (Username:{TokenUserName} , Key:{Key}) --> API Link:{ApiLink}";
                break;*/

            case "AnalyticsApp":
                content += $" --> {SelectedModule}";
                break;

            case "Report":
            case "FloReports":
                content += $" --> UserName:{ReportUserName} --> Report Name:{ReportName}  --> Report Parameters(Date Range:{DateRange} , Hierarchy Filter:{HierarchyFilter}";
                break;

            case "FAFloApp":
            case "UploadProgram":
            case "DMS":
            case "FloDashboard":
            case "Other":
            default:
                break;
        }
        return $"<body><strong>{nameof(IssueTitle)}</strong>\r\n{IssueTitle}\r\n\r\n" +
                $"<strong>{nameof(Platform)}</strong>\r\n{content}\r\n\r\n" +
                $"<strong>{nameof(Severity)}</strong>\r\n{Severity}\r\n\r\n" +
                $"<strong>{nameof(ReasonOfSeverity)}</strong>\r\n{ReasonOfSeverity}\r\n\r\n" +
                $"<strong>{nameof(CompanyName)}</strong>\r\n{CompanyName}\r\n\r\n" +
                $"<strong>{nameof(ClientId)}</strong>\r\n{ClientId}\r\n\r\n" +
                $"<strong>{nameof(ClientCategory)}</strong>\r\n{ClientCategory}\r\n\r\n" +
                $"<strong>{nameof(IssueDescription)}</strong>\r\n{IssueDescription}\r\n\r\n" +
                $"<strong>{nameof(StepsToReproduce)}</strong>\r\n{StepsToReproduce}\r\n\r\n" +
                $"<strong>{nameof(ExpectedOutput)}</strong>\r\n{ExpectedOutput}\r\n\r\n</body>";
    }

    public List<IFormFile> GetAttachment()
    {
        return Attachment;
    }

    public List<string> GetFollowers()
    {
        if (Platform is Platform.DMS or Platform.ExternalAPI)
        {
            return new List<string> { "TanyaGarg", "PankajSharma", "Karishma", "KunalTaneja", "AryanSinghChauhan", "subhampandey", "RohanNandal", "Pranshu", "Laraib", "NikhilYadav", "RohanSalunkhe", "Nilesh", "Shiva", "ShrutiShama", "ShivangiSingh", "BhushanVerma", "ArvindSingh", "ChiragSoni", "AnandSamuel", "RakshitSharma", "ArijitRay", "Haritha", "Afzal" }
                   .Select(x => AsanaCredentials.GetCredentialsForUser(x).UserId).ToList();
        }
        else
            return new List<string> { "TanyaGarg", "PankajSharma", "Karishma", "KunalTaneja", "AryanSinghChauhan", "subhampandey", "RohanNandal", "Pranshu", "Laraib", "NikhilYadav", "RohanSalunkhe", "Nilesh", "Shiva", "ShrutiShama", "AnandSamuel", "RakshitSharma" }
                   .Select(x => AsanaCredentials.GetCredentialsForUser(x).UserId).ToList();
    }

    public List<string> GetProjects()
    {
        var projects = new List<string>()
            {
                "43051116721058",
                "1200206012231647"
            };
        if (Platform is Platform.Dashboard or Platform.ExternalAPI or Platform.BulkUpload)
        {
            projects.Add("1201671189368484");
        }
        if (Platform == Platform.NewDashboard)
        {
            projects.Add("1203071525101825");
        }
        if (Platform == Platform.MTApp)
        {
            projects.Add("1202531100820186");
        }
        if (Platform == Platform.AnalyticsApp)
        {
            projects.Add("1202108161513858");
        }
        if (Platform == Platform.DMS)
        {
            projects.Add("1204305685477592");
        }
        if (Platform is Platform.TADA or Platform.FAFloApp or Platform.FloDashboard or Platform.FloReports)
        {
            projects.Add("1174464695524145");
        }
        if (Platform is Platform.NSApp or Platform.GTFlutter or Platform.VGTApp or Platform.V8App)
        {
            projects.Add("1207589147409404");
        }
        return projects;
    }

    public List<string> GetTags()
    {
        var tags = new List<string>();
        switch (Severity)
        {
            case "x1":
                tags.Add("541680686458856");
                break;

            case "x2":
                tags.Add("404534051288982");
                break;

            case "x4":
                tags.Add("404534051288983");
                break;

            case "x8":
                tags.Add("404534051288984");
                break;

            case "x16":
                tags.Add("1205974909988465");
                break;

            case "x24":
                tags.Add("1205081849022759");
                break;

            case "x32":
                tags.Add("1201963708724504");
                break;
        }

        switch (Platform)
        {
            case Platform.AnalyticsApp:
                tags.Add("1136996958035009");
                break;

            case Platform.Dashboard:
                tags.Add("139097763031412");
                break;

            case Platform.NewDashboard:
                tags.Add("1204675422628893");
                break;

            case Platform.FAFloApp:
                tags.Add("1200015762002669");
                break;

            case Platform.FloReports:
                tags.Add("1208385478855300");
                break;

            case Platform.FloDashboard:
                tags.Add("1208385478855301");
                break;

            case Platform.MTApp:
                tags.Add("927419449337503");
                break;

            case Platform.Report:
                tags.Add("305436650865282");
                break;

            case Platform.NSApp:
                tags.Add("1184673386521573");
                break;

            case Platform.GTFlutter:
                tags.Add("1208385478855302");
                break;

            case Platform.V8App:
                tags.Add("952891692223879");
                break;

            case Platform.VGTApp:
                tags.Add("883735358176571");
                break;

            case Platform.DMS:
                tags.Add("1200015762002670");
                break;

            case Platform.ExternalAPI:
                tags.Add("1200015762002671");
                break;

            case Platform.TADA:
                tags.Add("1200180682142124");
                break;

            case Platform.Other:
                tags.Add("779100573429300");
                break;
        }

        if (HCCB)
        {
            tags.Add("1207343111159251");
        }
        return tags;
    }

    public string GetAssigneeName()
    {
        if (HCCB)
            return "AmitUpadhyay";
        else
        {
            switch (Severity)
            {
                case "x1":

                case "x2":

                case "x4":

                case "x8":
                    return "subhampandey";
            }

            switch (Platform)
            {
                case Platform.AnalyticsApp:
                    return "DeepaliSrivastava";

                case Platform.Dashboard:
                    return "SushantThakur";

                case Platform.NewDashboard:
                    return "SushantThakur";

                case Platform.MTApp:
                    return "RiyaSethi";

                case Platform.Report:
                    return "PoojaYadav";

                case Platform.NSApp:
                    return "RakshitSharma";

                case Platform.GTFlutter:
                    return "ShubhamSaurav";

                case Platform.DMS:
                    return "BhushanVerma";

                case Platform.ExternalAPI:
                    return "SushantThakur";

                case Platform.TADA:
                    return "ShriniwasTripathi";

                case Platform.FAFloApp:
                case Platform.FloDashboard:
                case Platform.FloReports:
                    return "ShriniwasTripathi";

                default:
                    return "RakshitSharma";
            }
        }
    }

    public string GetOwnerName()
    {
        return "AnandSamuel";
    }
}
