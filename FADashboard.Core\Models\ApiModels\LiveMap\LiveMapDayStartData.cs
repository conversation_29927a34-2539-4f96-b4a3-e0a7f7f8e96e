﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels.LiveMap;

public class EmployeeDayActivityWithOutletInfo
{
    public string Address { get; set; }
    public DateTime AttendanceDeviceTime { get; set; }
    public string AttendanceDeviceTimeString => AttendanceDeviceTime.ToString("hh:mm tt");
    public bool? AttendanceOutOfTurn { get; set; }
    public string Category { set; get; }
    public string Comment { set; get; }
    public string CommentCategory { set; get; }
    public long? DistributorId { get; set; }
    public string DistributorName { get; set; }
    public string GeoTaggingDistance { get; set; }
    public bool? IsGPSOff { get; set; }
    public bool? IsJointWorking { get; set; }
    public bool? IsOfficialWork { get; set; }
    public bool? IsOVC { get; set; }
    public bool? IsProductive { get; set; }
    public bool? IsTelephonic { get; set; }
    public decimal? Latitude { get; set; }
    public string LocAddress { get; set; }
    public string LocBeatName { get; set; }
    public string LocGUID { get; set; }
    public long? LocId { get; set; }
    public string LocImage { get; set; }
    public string LocMarketName { get; set; }
    public string LocName { get; set; }
    public decimal? Longitude { get; set; }
    public TimeSpan? TotalTime { set; get; }
    public VerificationStatus VerificationStatus { get; set; }
}

public class EmployeeLocationDumpModel
{
    public string Address { get; set; }
    public DateTime DeviceTime { get; set; }
    public decimal Latitude { get; set; }

    public decimal Longitude { get; set; }
    public long Order { get; set; }
    public string Status { get; set; }
    public string Time { get; set; }
}

public class LiveMapDayStartData
{
    public string AssignedBeat { get; set; }
    public string AssignedRoute { get; set; }
    public string DayEndAddress { get; set; }
    public decimal? DayEndLatitude { get; set; }
    public decimal? DayEndLongitude { get; set; }
    public long DayEndOrder { get; set; }
    public string DayEndTime { get; set; }
    public string DayStartAddress { get; set; }
    public DayStartType DayStartType { get; set; }
    public long EmployeeId { get; set; }
    public bool IsDayEnd { get; set; }
    public bool IsDayStart { get; set; }
    public bool IsJointWorking { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public long Order { get; set; }
    public int? OVC { get; set; }
    public int? OVT { get; set; }
    public int? PC { get; set; }
    public int? SC { get; set; }
    public string SelectedBeat { get; set; }
    public string SelectedRoute { get; set; }
    public int? TC { get; set; }
    public int TelephonicOrders { get; set; }
    public string Time { get; set; }
}

public class LiveMapEmployeeLocations
{
    public string LocAddress { get; set; }
    public string LocBeatName { get; set; }
    public string LocCodeId { get; set; }
    public string LocImage { get; set; }
    public decimal? LocLat { get; set; }
    public decimal? LocLon { get; set; }
    public string LocMarketName { get; set; }
    public string LocName { get; set; }
}

public class DayStartDistanceMin
{
    public long ESMId { get; set; }
    public double? UserTravelledDistance { get; set; }
}
