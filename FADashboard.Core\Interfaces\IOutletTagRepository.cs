﻿using FADashboard.Core.Models;

namespace FADashboard.Core.Interfaces;

public interface IOutletTagRepository
{
    Task<List<OutletTagFlat>> GetAllOutletTags(long companyId, bool includeDeactive);
    Task<RepositoryResponse> ActivateDeactivateOutletTag(long id, bool action, long companyId);
    Task<RepositoryResponse> CreateOutletTag(OutletTagFlat outletTagFlat, long companyId);
    Task<RepositoryResponse> UpdateOutletTag(OutletTagFlat outletTagFlat, long companyId);
    Task<OutletTagFlat> GetOutletTagById(long id, long companyId);
}
