﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class RouteList
{
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public List<string> PositionNames { get; set; }
}

public class Route
{
    public string ErpId { get; set; }

    public long CompanyId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public long PositionId { get; set; }
    public string PositionName { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
    public string LevelName { get; set; }
    public List<long> OutletIds { get; set; }
    public List<long> BeatIds { get; set; }
}

public class RouteWithTotal
{
    public int Total { get; set; }
    public List<RouteList> Route { get; set; }
}

public class RouteOutlets
{
    public long RouteId { get; set; }
    public List<long> OutletIds { get; set; }
}

public class RoutePositionsModel
{
    public string ErpId { get; set; }
    public long CompanyId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public List<long> PositionIds { get; set; }
    public List<string> PositionName { get; set; }
    public List<PositionCodeLevel> PositionLevel { get; set; }
    public List<long> OutletIds { get; set; }
    public List<long> BeatIds { get; set; }
}
