﻿using System.ComponentModel.DataAnnotations;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class RetoolReportSubscriptions : IAuditedEntity, IDeactivatable
{
    public long Id { get; set; }

    [Audited]
    [StringLength(256)]
    public string Name { set; get; }

    [Audited]
    [Required]
    public long RetoolReportId { get; set; }

    public RetoolReports RetoolReport { get; set; }

    [Audited]
    public PortalUserRole PortalUserRole { get; set; }

    public long CompanyId { get; set; }

    [Audited]
    public long UserId { get; set; }

    [Audited]
    public bool IsDeactive { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
}
