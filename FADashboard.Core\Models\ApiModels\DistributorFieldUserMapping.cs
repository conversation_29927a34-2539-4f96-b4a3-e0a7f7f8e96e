﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Models.ApiModels;

public class DistributorFieldUserMapping
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public virtual DistributorDTO Distributor { get; set; }
    public long DistributorId { get; set; }
    public long FieldUserId { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
