﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class ManagerDailyDataService(IAttendanceRepository attendanceRepository, IEmployeeRepository employeeRepository, ISchemeRepository schemeRepository, DayStartServicesReportApi dayStartServicesReportApi, MTDDateHelper mtdDateHelper)
    : RepositoryResponse
{
    public async Task<ManagerDailyDataModel> GetManagerDailyData(long companyId, long userId, PortalUserRole userRole, DateTime date, bool CompanyUsesOpenMarketOperations = false)
    {
        var alluserList = await employeeRepository.GetActiveFieldUsersListUnderManager(companyId, userId, userRole);
        var oldUsserId = await employeeRepository.GetOldIdForManager(userId, companyId);

        var daystartSummaryDataQuery = dayStartServicesReportApi.GetDailyDayStartSummary(companyId, oldUsserId, userRole, date);
        var attendanceDataQuery = attendanceRepository.GetUserDailyAttendanceDataForManager(companyId, alluserList, date);
        var schemeDataQuery = schemeRepository.GetSchemeQtyAndValueForManager(companyId, alluserList, date);
        var mtdValueQuery = mtdDateHelper.GetMTDValue(companyId, userId, userRole, date);

        await Task.WhenAll(daystartSummaryDataQuery, attendanceDataQuery, schemeDataQuery, mtdValueQuery);
        var daystartSummaryData = daystartSummaryDataQuery.Result;
        var attendanceData = attendanceDataQuery.Result;
        var schemeData = schemeDataQuery.Result;
        var mtdValue = mtdValueQuery.Result;


        var daystartData = new ManagerDailyDataModel();
        if (alluserList.Count == 0)
        {
            return new ManagerDailyDataModel();
        }

        daystartData.TotalUsers = daystartSummaryData.Total;
        daystartData.Retailing = daystartSummaryData.Regular;
        daystartData.Leave = daystartSummaryData.Leave;
        daystartData.OfficialWork = daystartSummaryData.Others;
        daystartData.Absent = daystartSummaryData.Inactive;
        daystartData.NoOfOtherActivities = daystartSummaryData.NoOfOtherActivities;
        daystartData.SC = daystartSummaryData.SC;

        daystartData.MTD = mtdValue;
        if (attendanceData != null)
        {
            daystartData.TC = attendanceData.TC;
            daystartData.PC = attendanceData.PC;
            daystartData.OVC = attendanceData.TC > 0 ? attendanceData.OVC / (double)attendanceData.TC * 100 : 0;
            daystartData.OVT = attendanceData.TC > 0 ? attendanceData.OVT / (double)attendanceData.TC * 100 : 0;
            daystartData.TelephonicOrders = attendanceData.TelephonicOrders;
            daystartData.OrderInStdUnits = attendanceData.OrderInStdUnits;
            daystartData.OrderInRevenue = attendanceData.OrderInRevenue;
            daystartData.LineWiseDiscount = attendanceData.LineWiseDiscount;
            daystartData.NewOutletsCreated = attendanceData.NewOutletsCreated;
            daystartData.CAP = attendanceData.CAP;
            daystartData.Productivity = attendanceData.TC > 0 ? attendanceData.PC / (double)attendanceData.TC * 100 : 0;
            daystartData.SchProductivity = attendanceData.TC > 0 ? attendanceData.SPC / (double)attendanceData.TC * 100 : 0;
            daystartData.JWCalls = daystartSummaryData.JWCalls;
            daystartData.NewOutletSalesInRevenue = attendanceData.NewOutletOrderInRevenue;
        }

        if (CompanyUsesOpenMarketOperations)
        {
            daystartData.FillratePer = daystartData.OrderInRevenue != 0 ? daystartSummaryData.dispatchedValue / daystartData.OrderInRevenue : 0;
            daystartData.DC = daystartSummaryData.dispatchedOutlets;
        }

        if (schemeData != null)
        {
            daystartData.SchemeCashDiscount = schemeData.SchemeCashDiscount;
            daystartData.TotalSchemeQty = schemeData.TotalSchemeQty;
        }

        daystartData.Absent = daystartData.TotalUsers - (daystartData.Retailing + daystartData.Leave + daystartData.PlannedLeave + daystartData.OfficialWork);
        daystartData.Absent = daystartData.Absent < 0 ? 0 : daystartData.Absent;
        daystartData.PhycialCalls = daystartData.TC - daystartData.TelephonicOrders;
        daystartData.NetValue = daystartData.OrderInRevenue - (daystartData.LineWiseDiscount + daystartData.SchemeCashDiscount);

        return daystartData;
    }
}
