﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class FocusProductService(
    IFocusProductRepository focusProductRepository,
    ISecondaryCategoryRepository secondaryCategoryRepository,
    IProductRepository productRepository,
    ICurrentUser currentUser) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidFocusProduct(FocusProductInput focusProduct)
    {
        var focusProductList = await GetFocusProducts(true);
        if (focusProduct.Id != 0)
        {
            focusProductList = focusProductList.Where(p => p.Id != focusProduct.Id).ToList();
        }

        if (!string.IsNullOrEmpty(focusProduct.Name))
        {
            var focusProductNameList = focusProductList.Select(p => p.Name.NormalizeCaps()).ToList();
            if (focusProductNameList.Contains(focusProduct.Name.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = focusProduct.Id, ExceptionMessage = "Focus Product Name is already present in the system!", Message = "Focus Product Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = focusProduct.Id, Message = "Focus Product Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> CreateUpdateFocusProduct(FocusProductInput focusProduct)
    {
        var checkValid = await IsValidFocusProduct(focusProduct);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (focusProduct.Id == 0)
        {
            return await focusProductRepository.CreateFocusProduct(focusProduct, currentUser.CompanyId);
        }

        return await focusProductRepository.UpdateFocusProduct(focusProduct, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> DeactivateFocusProduct(long focusProductId) => await focusProductRepository.DeactivateFocusProduct(focusProductId, currentUser.CompanyId);

    public async Task<FocusProductInput> GetFocusProductById(long focusProductId)
    {
        var focusProduct = await focusProductRepository.GetFocusProductById(focusProductId, currentUser.CompanyId);

        var sKUIds = focusProduct.SKUIds;
        var displayCategoryIds = focusProduct.DisplayCategoryIds;

        var secondaryCategories = await secondaryCategoryRepository.GetDetailedSecondaryCategories(currentUser.CompanyId, true);
        var secondaryCategoriesDict = secondaryCategories.ToDictionary(s => s.Id, s => s.ProductPrimaryCategoryId);

        if (sKUIds is { Count: > 0 })
        {
            var products = await productRepository.GetProducts(currentUser.CompanyId, true);
            var productDict = products.ToDictionary(s => s.Id, s => s.SecondaryCategoryId);
            var secondaryCategoryIds = sKUIds.Select(skuId => productDict[skuId]).Distinct().ToList();
            var primaryCategoryIds = secondaryCategoryIds.Select(scId => secondaryCategoriesDict[scId]).Distinct().ToList();
            focusProduct.PrimaryCategoryIds = primaryCategoryIds;
            focusProduct.SecondaryCategoryIds = secondaryCategoryIds;
        }
        else if (displayCategoryIds is { Count: > 0 })
        {
            var displayCategories = await productRepository.GetProductDisplayCategories(currentUser.CompanyId, true);
            var displayCategoryDict = displayCategories.ToDictionary(s => s.Id, s => s.ProductSecondaryCategoryId);
            var secondaryCategoryIds = displayCategoryIds.Select(displayCategoryId => displayCategoryDict[displayCategoryId]).Distinct().ToList();
            var primaryCategoryIds = secondaryCategoryIds.Select(scId => secondaryCategoriesDict[scId]).Distinct().ToList();
            focusProduct.PrimaryCategoryIds = primaryCategoryIds;
            focusProduct.SecondaryCategoryIds = secondaryCategoryIds;
        }

        return focusProduct;
    }

    public async Task<List<FocusProductList>> GetFocusProducts(bool includeDeactivate)
    {
        var products = await focusProductRepository.GetFocusProducts(currentUser.CompanyId, includeDeactivate);
        return products;
    }
}
