﻿using System.ComponentModel.Design;
using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class DistributorService(
    IDistributorRepository distributorRepository,
    IDistributorBeatRepository distributorBeatRepository,
    ICurrentUser currentUser,
    DistributorFieldUserService distributorFieldUserService,
    IDistributorAddressRepository distributorAddressRepository,
    IPositionCodeRepository positionCodeRepository,
    ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidDistributor(DistributorInput distributor)
    {
        var distributors = await distributorRepository.GetDistributorList(currentUser.CompanyId);
        if (distributor.Id != 0)
        {
            distributors = distributors.Where(p => p.Id != distributor.Id).ToList();
        }

        if (!string.IsNullOrEmpty(distributor.EmailId))
        {
            var distributorEmailList = distributors.Select(p => p.EmailId?.NormalizeCaps()).ToList();

            if (distributorEmailList.Contains(distributor.EmailId.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = distributor.Id, ExceptionMessage = "Distributor Email Id is not unique", Message = "Distributor Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        if (!string.IsNullOrEmpty(distributor.ErpId))
        {
            var distributorErpIdList = distributors.Select(p => p.ErpId?.NormalizeCaps()).ToList();
            if (distributorErpIdList.Contains(distributor.ErpId.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = distributor.Id, ExceptionMessage = "Distributor Erp Id is not unique", Message = "Distributor Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = distributor.Id, Message = "Distributor Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateDistributor(long distributorId, bool action)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var distributorMapping = companySettings.TypeofDistributorMapping;

        if (!action && distributorMapping == TypeofDistributorMapping.UserDistributor)
        {
            var employeeUnderDistributor = await distributorFieldUserService.GetFieldUserForDistributor(distributorId);
            if (employeeUnderDistributor.Count != 0)
            {
                return new RepositoryResponse
                {
                    Id = distributorId, ExceptionMessage = $"This Distributor is attached to {employeeUnderDistributor.Count} Field Users. Please detach them first.", Message = "Distributor Activation/Deactivation failed.", IsSuccess = false,
                };
            }
        }
        else if (!action && distributorMapping == TypeofDistributorMapping.BeatDistributor)
        {
            var beatsUnderDistributor = await GetBeatsForDistributor(distributorId);
            if (beatsUnderDistributor.Count != 0)
            {
                return new RepositoryResponse
                {
                    Id = distributorId, ExceptionMessage = $"This Distributor is attached to {beatsUnderDistributor.Count} Beats. Please detach them first.", Message = "Distributor Activation/Deactivation failed.", IsSuccess = false,
                };
            }
        }

        // Check if any substockist attached in case of deactivating super stockist.
        if (!action)
        {
            var getSubstockists = await distributorRepository.GetSubStockistsForSuperStockist(currentUser.CompanyId, distributorId);
            if (getSubstockists.Count != 0)
            {
                return new RepositoryResponse
                {
                    Id = distributorId,
                    ExceptionMessage = $"This Distributor(Super stockist) is attached to {getSubstockists.Count} Sub-Stockists. Please detach them first.",
                    Message = $"An Error Occurred while trying to {(action ? "Activate" : "Deactivate")} Distributor",
                    IsSuccess = false
                };
            }
        }

        return await distributorRepository.ActivateDeactivateDistributor(distributorId, currentUser.CompanyId, action, companySettings);
    }

    public async Task<RepositoryResponse> AttachBeatsForDistributor(long distributorId, List<long> beatIds)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        return await distributorBeatRepository.AttachBeatsForDistributor(distributorId, beatIds, currentUser.CompanyId, companySettings);
    }


    public async Task<RepositoryResponse> CreateUpdateDistributor(DistributorInput distributor, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var onlineDmsSetting = companySettings.IsOnlineDMS;
        var checkValid = await IsValidDistributor(distributor);
        var contactExists = await distributorRepository.DoesContactExist(currentUser.CompanyId, distributor.ContactNo, distributor.Id);

        if (contactExists)
        {
            return new RepositoryResponse
            {
                Id = 0,
                Message = "An Error Occurred while trying to create/update the Distributor",
                ExceptionMessage = "Phone number already associated with another distributor, please check",
                IsSuccess = false
            };
        }

        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (distributor.Id == 0)
        {
            var createDistributor = await distributorRepository.CreateDistributor(distributor, currentUser.CompanyId, companySettings, ct);
            if (onlineDmsSetting && createDistributor.Id != null)
            {
                await distributorAddressRepository.CreateDistributorAddress(distributor, createDistributor.Id.Value, currentUser.CompanyId, ct);
            }

            return createDistributor;
        }

        var updateDistributor = await distributorRepository.UpdateDistributor(distributor, currentUser.CompanyId, companySettings, ct);
        if (onlineDmsSetting && updateDistributor.Id != null)
        {
            await distributorAddressRepository.UpdateDistributorAddress(distributor, distributor.Id, currentUser.CompanyId);
        }

        return updateDistributor;
    }

    public async Task<List<Beat>> GetBeatsForDistributor(long distributorId)
    {
        var beats = await distributorBeatRepository.GetBeatsForDistributor(distributorId, currentUser.CompanyId);
        return beats;
    }

    public async Task<List<DistributorDTO>> GetDetailedDistributors(bool includeDeactivate)
    {
        var distributors = await distributorRepository.GetDistributors(currentUser.CompanyId, includeDeactivate);
        var distributorAddresses = await distributorAddressRepository.getDistributorAddressViaDistributorIds(distributors.Select(i => i.Id).ToList(), currentUser.CompanyId);
        distributors = distributors.Select(distributor =>
        {
            var filteredAddresses = distributorAddresses.Where(address => address.EntityId == distributor.Id);
            distributor.MappedWarehouseNamesWithErpId = filteredAddresses.Select(address => new WarehouseNamesWithErpId
            {
                WarehouseErpId = address.WarehouseERPID,
                WarehouseName = address.WareHouseName,
            }).ToList();
            return distributor;
        }).ToList();
        return distributors;
    }

    public async Task<List<DistributorOptionsList>> GetDistributorList()
    {
        var distributors = await distributorRepository.GetDistributorList(currentUser.CompanyId);
        return distributors;
    }

    public async Task<List<DistributorOptionsList>> GetDistributorListwithAdress()
    {
        return await distributorRepository.GetDistributorListwithAdress(currentUser.CompanyId);
    }

    public async Task<DistributorDTO> GetDistributorById(long distributorId)
    {
        var distributor = await distributorRepository.GetDistributorById(distributorId, currentUser.CompanyId);
        return distributor;
    }

    public async Task<List<DistributorDTO>> GetDistributors(List<long> regionIds) => await distributorRepository.GetDistributorByRegionIds(currentUser.CompanyId, regionIds);

    public async Task<PagedResponse<List<DistributorList>>> GetDistributors(PaginationFilter validFilter, int? stockistType, List<long> regionIds = null)
    {
        var distributer = await distributorRepository.GetDistributors(currentUser.CompanyId, validFilter, stockistType, regionIds);
        var totalRecords = distributer.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(distributer.DistributorRecords, validFilter, totalRecords);
        return pagedReponse;
    }

    public async Task<List<EntityMin>> GetDistributorsForProductDivision(long productDivisionId)
    {
        var distributors = await distributorRepository.GetDistributorsForProductDivision(productDivisionId, currentUser.CompanyId);
        return distributors;
    }

    public async Task<List<EntityMin>> GetProductDivisionsForDistributor(long distributorId)
    {
        var productDivisions = await distributorRepository.GetProductDivisionsForDistributor(distributorId, currentUser.CompanyId);
        return productDivisions;
    }

    public async Task<List<DistributorList>> GetDistributorsForTerritory(long territoryId)
    {
        var distributors = await distributorRepository.GetDistributorsForTerritory(territoryId, currentUser.CompanyId);
        return distributors;
    }

    public async Task<List<EntityMinWithStatus>> GetDistributorsList(bool inludeDeactive) => await distributorRepository.GetAllDistributorsMin(currentUser.CompanyId, inludeDeactive);

    public async Task<List<DistributorDTO>> GetDistributorsOfBeat(long beatId) => await distributorRepository.GetDistributorsOfBeat(currentUser.CompanyId, beatId);

    public async Task<List<DistributorDTO>> GetDistributorsForBeats(long companyId, List<long> beatIds) => await distributorRepository.GetDistributorsForBeats(companyId, beatIds);

    public async Task<List<DistributorDTO>> GetDistributorsOfPosition(long positionId) => await distributorRepository.GetDistributorsOfPosition(currentUser.CompanyId, positionId);

    public async Task<List<DistributorDTO>> GetDistributorsForPositions(long companyId, List<long> positionIds) => await distributorRepository.GetDistributorsForPositions(companyId, positionIds);

    public async Task<List<DistributorDTO>> GetDistinctDistributorUnderAndOfPositions(long companyId, List<long> positionIds)
    {
        var childPosCodes = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionIds, companyId);
        var childPosCodeIds = childPosCodes.Select(p => p.Id).ToList();
        positionIds.AddRange(childPosCodeIds);
        return (await distributorRepository.GetDistributorsForPositions(companyId, positionIds)).DistinctBy(b => b.Id).ToList();
    }
    public async Task<List<EntityMinWithErpStatus>> GetDistributorsOfStockistType(StockistType stockistType, bool includeDeactivate)
    {
        var distributors = await distributorRepository.GetDistributorsOfStockistType(stockistType, currentUser.CompanyId, includeDeactivate);
        return distributors;
    }

    public async Task<List<EntityMin>> GetDistributorSegmentations(bool inludeDeactive)
    {
        var segmentations = await distributorRepository.GetSegmentations(currentUser.CompanyId, inludeDeactive);
        return segmentations;
    }

    public async Task<List<EntityMin>> GetDistributorChannels(bool inludeDeactive)
    {
        var distChannels = await distributorRepository.GetAllDistributorChannels(currentUser.CompanyId, inludeDeactive);
        return distChannels;
    }

    //Get All Distributor Channels (Id, Name, Erpid, Active, Deleted)
    public async Task<List<DistChannels>> GetAllDistributorsChannels(bool inludeDeactive)
    {
        var distributorChannels = await distributorRepository.GetDistributorChannels(currentUser.CompanyId, inludeDeactive);
        return distributorChannels;
    }

    //Activate or Deactivate Distributor Channel
    public async Task<RepositoryResponse> ActivateDeactiveDistributorChannel(long id, long companyId, bool action) => await distributorRepository.ActivateDeactivateDistributorChannel(id, currentUser.CompanyId, action);


    //Get All Distributor Segmentation (Id, Name, Erpid, Active, Deleted)
    public async Task<List<DistSegmentations>> GetAllDistributorsSegmentations(bool inludeDeactive)
    {
        var distributorSegmentations = await distributorRepository.GetDistributorSegmentation(currentUser.CompanyId, inludeDeactive);
        return distributorSegmentations;
    }

    //Activate or Deactivate Distributor Segmentation
    public async Task<RepositoryResponse> ActivateDeactiveDistributorSegmentation(long id, long companyId, bool action) => await distributorRepository.ActivateDeactivateDistributorSegmentation(id, currentUser.CompanyId, action);

    /// <summary>
    /// This piece of code checks if the Distributor Channel Input is valid or not
    /// </summary>
    /// <param name="distributorChannels"></param>
    /// <returns></returns>
    private async Task<RepositoryResponse> IsValidDistributorChannel(DistributorChannelsInput distributorChannels)
    {
        var dbDistributorsChannels = await GetAllDistributorsChannels(true);
        if (distributorChannels.Id != 0)
        {
            dbDistributorsChannels = dbDistributorsChannels.Where(p => p.Id != distributorChannels.Id).ToList();
        }

        if (!string.IsNullOrEmpty(distributorChannels.ErpId))
        {
            var dbDistributorChannelErpIdList = dbDistributorsChannels.Select(p => p.ErpId?.NormalizeCaps()).ToList();
            if (dbDistributorChannelErpIdList.Contains(distributorChannels.ErpId.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = distributorChannels.Id, ExceptionMessage = "Distributor Channel ErpId is already present in the system!", Message = "Distributor Channel Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        if (!string.IsNullOrEmpty(distributorChannels.Name))
        {
            var dbDistributorChannelNameList = dbDistributorsChannels.Select(p => p.Name.NormalizeCaps()).ToList();
            if (dbDistributorChannelNameList.Contains(distributorChannels.Name.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = distributorChannels.Id, ExceptionMessage = "Distributor Channel Name is already present in the system!", Message = "Distributor Channel Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = distributorChannels.Id, Message = "Distributor Channel Unique", IsSuccess = true, };
    }

    /// <summary>
    /// This particular API is used to Create or Update Distributor Channel
    /// </summary>
    /// <param name="distributorChannel"></param>
    /// <returns></returns>
    public async Task<RepositoryResponse> CreateUpdateDistributorChannel(DistributorChannelsInput distributorChannel)
    {
        var checkValid = await IsValidDistributorChannel(distributorChannel);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (distributorChannel.Id == 0)
        {
            return await distributorRepository.CreateDistributorChannel(distributorChannel, currentUser.CompanyId);
        }

        return await distributorRepository.UpdateDistributorChannel(distributorChannel, currentUser.CompanyId);
    }

    /// <summary>
    ///  This particular validates Distributor segmentation
    /// </summary>
    /// <param name="distributorSegmentation"></param>
    /// <returns></returns>
    private async Task<RepositoryResponse> IsValidDistributorSegmentation(DistributorSegmentationInput distributorSegmentation)
    {
        var dbDistributorSegmentations = await GetAllDistributorsSegmentations(true);
        if (distributorSegmentation.Id != 0)
        {
            dbDistributorSegmentations = dbDistributorSegmentations.Where(p => p.Id != distributorSegmentation.Id).ToList();
        }

        if (!string.IsNullOrEmpty(distributorSegmentation.ErpId))
        {
            var distributorErpIdList = dbDistributorSegmentations.Select(p => p.ErpId?.NormalizeCaps()).ToList();
            if (distributorErpIdList.Contains(distributorSegmentation.ErpId.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = distributorSegmentation.Id, ExceptionMessage = "Distributor Segmentation ErpId is already present in the system!", Message = "Distributor Segmentation Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        if (!string.IsNullOrEmpty(distributorSegmentation.Name))
        {
            var distributorNameList = dbDistributorSegmentations.Select(p => p.Name.NormalizeCaps()).ToList();
            if (distributorNameList.Contains(distributorSegmentation.Name.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = distributorSegmentation.Id, ExceptionMessage = "Distributor Segmentation Name is already present in the system!", Message = "Distributor Segmentation Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = distributorSegmentation.Id, Message = "Distributor Segmentation Unique", IsSuccess = true, };
    }

    /// <summary>
    ///  This piece of code Creates and Updates Distributor Segmentation
    /// </summary>
    /// <param name="distributorSegmentation"></param>
    /// <returns></returns>
    public async Task<RepositoryResponse> CreateUpdateDistributorSegmentation(DistributorSegmentationInput distributorSegmentation)
    {
        var checkValid = await IsValidDistributorSegmentation(distributorSegmentation);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (distributorSegmentation.Id == 0)
        {
            return await distributorRepository.CreateDistributorSegmentation(distributorSegmentation, currentUser.CompanyId);
        }

        return await distributorRepository.UpdateDistributorSegmentation(distributorSegmentation, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> RegisterDistributor(long distributorId, bool action, string redirectUrl, bool twoFactorEnabled, string userName) => await distributorRepository.RegisterDistributor(distributorId, action, currentUser.CompanyId, redirectUrl, twoFactorEnabled, userName);

    public async Task<long> GetDistributorCount(long companyId, PaginationFilter validFilter)
    {
        return await distributorRepository.GetDistributerCount(companyId, validFilter);
    }
}
