﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class FlexibleTargetService(IFlexibleTargetRepository flexibleTargetRepository) : RepositoryResponse
{
    public async Task<List<FlexibleTargetList>> GetFlexibleTargetList()
    {
        var flexibleTargetList = await flexibleTargetRepository.GetFlexibleTargetList();
        return flexibleTargetList;
    }

    public async Task<FlexibleTargetModel> GetFlexibleTargetById(long id)
    {
        var flexibleTarget = await flexibleTargetRepository.GetFlexibleTargetById(id);
        return flexibleTarget;
    }

    public async Task<RepositoryResponse> CreateUpdateFlexibleTarget(FlexibleTargetModel flexibleTarget)
    {
        if (flexibleTarget.Id == 0)
        {
            return await flexibleTargetRepository.CreateFlexibleTarget(flexibleTarget);
        }

        return await flexibleTargetRepository.UpdateFlexibleTarget(flexibleTarget);
    }
}
