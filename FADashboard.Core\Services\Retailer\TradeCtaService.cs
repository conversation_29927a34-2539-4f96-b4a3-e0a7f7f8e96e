﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services.Retailer;

public class TradeCtaService(ITradeCtaRepository tradeCtaRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<RepositoryResponse> CreateUpdateTradeCta(TradeCtaRequest tradeCtaRequest)
    {
        if (tradeCtaRequest.Id == 0)
        {
            return await tradeCtaRepository.CreateTradeCta(tradeCtaRequest, currentUser.CompanyId);
        }

        return await tradeCtaRepository.UpdateTradeCta(tradeCtaRequest, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> UpdateTradeCtaStatus(long id, bool isDeleted) => await tradeCtaRepository.UpdateTradeCtaStatus(id, currentUser.CompanyId, isDeleted);

    public async Task<List<TradeCtaResponse>> GetAllTradeCta(bool includeDeactivate) => await tradeCtaRepository.GetAllTradeCta(currentUser.CompanyId, includeDeactivate);

    public async Task<TradeCtaResponse> GetTradeCtaById(long id) => await tradeCtaRepository.GetTradeCtaById(id, currentUser.CompanyId);
}
