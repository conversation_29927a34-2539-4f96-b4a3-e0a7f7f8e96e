﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FADashboard.Core.Models.ApiModels;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class TaxIntegrationDevice
{
    [Key]
    public long Id { get; set; }

    [Required]
    [StringLength(64)]
    public string DeviceName { get; set; }

    [Required]
    public IntegrationType IntegrationType { get; set; }

    [Required]
    [StringLength(64)]
    public string CountryName { get; set; }

    [Required]
    public string ConfigJson { get; set; }
    [NotMapped]
    public List<TaxIntegrationColumnMeta> TaxIntegrationColumns
    {
        get => !string.IsNullOrEmpty(ConfigJson) ? JsonConvert.DeserializeObject<List<TaxIntegrationColumnMeta>>(ConfigJson) : [];
        set => ConfigJson = JsonConvert.SerializeObject(value);
    }

    [Required]
    public bool IsDeleted { get; set; }
}
public enum IntegrationType
{
    TIMS = 0,
    ETIMS = 1,
    EINVOICING = 2,
}
