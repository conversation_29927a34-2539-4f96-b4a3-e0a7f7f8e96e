﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class FACompanyExternalAsset
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Url { get; set; }
    public long Company { get; set; }
    public long? UserId { get; set; }
    public bool VisibleToHierarchy { get; set; }
    public bool IsDeleted { get; set; }
    public FACompanyExternalAssetFilter FACompanyExternalAssetFilter { get; set; }
    public Guid? Image { get; set; }
    [StringLength(256)]
    public string UserPlatform { get; set; }
    [NotMapped]
    public List<string> UserPlatforms
    {
        get => !string.IsNullOrEmpty(UserPlatform) ? JsonConvert.DeserializeObject<List<string>>(UserPlatform) : null;
        set => UserPlatform = value == null ? null : JsonConvert.SerializeObject(value);
    }
}

public class FACompanyExternalAssetFilter
{
    [Key] public long Id { get; set; }

    [ForeignKey("FACompanyExternalAsset")] public long FACompanyExternalAssetId { get; set; }
    public FACompanyExternalAsset FACompanyExternalAsset { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ExternalAssetGeography FilterOn { get; set; }
    public string FilterIds { get; set; }
    public string Channel { get; set; }
    public string Chain { get; set; }
    public string ShopType { get; set; }
    public string Segmentation { get; set; }
    public string CustomTag { get; set; }
    public bool? IsFocused { get; set; }
    public bool ApplyAssetsOnNoTagOutlets { get; set; }
}
