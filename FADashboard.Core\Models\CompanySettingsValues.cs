﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class CompanySettingsValues
{
    public CompanySettingsValues()
    {
    }

    public CompanySettingsValues(long companyId, long settingId)
    {
        CompanyId = companyId;
        SettingId = settingId;
    }

    public CompanySettingsValues(CompanySetting key, string value)
    {
        SettingId = key.SettingId;
        SettingKey = key.SettingKey;
        SettingValue = string.IsNullOrWhiteSpace(value) ? key.DefaultValue : value;
    }

    public CompanySettingsValues(CompanySetting key)
    {
        SettingId = key.SettingId;
        SettingKey = key.SettingKey;
        SettingValue = key.DefaultValue;
    }

    public long CompanyId { get; set; }
    public string DefaultValue { get; set; }
    public long SettingId { get; set; }
    public string SettingKey { get; set; }
    public CompanySettingType SettingType { get; set; }
    public string SettingValue { get; set; }
    public List<string> SettingValues { get; set; }
}
