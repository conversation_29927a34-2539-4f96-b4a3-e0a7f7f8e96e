﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class DerivedKPIService(IDerivedKPIRepository derivedKpiRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<DerivedKpi>> GetDerivedKPIs(bool showDeactive)
    {
        var kpis = await derivedKpiRepository.GetDerivedKPIs(currentUser.CompanyId, showDeactive);
        var getMeasuresDict = (await GetDerivedKPIInputMeasures()).ToDictionary(p => p.InputMeasureKey + p.Perspective, p => p.InputMeasureDisplayName);
        foreach (var kpi in kpis)
        {
            kpi.InputMeasure1 = getMeasuresDict.ContainsKey(kpi.InputMeasure1 + kpi.Perspective) ? getMeasuresDict[kpi.InputMeasure1 + kpi.Perspective] : kpi.InputMeasure1;
            kpi.InputMeasure2 = getMeasuresDict.ContainsKey(kpi.InputMeasure2 + kpi.Perspective) ? getMeasuresDict[kpi.InputMeasure2 + kpi.Perspective] : kpi.InputMeasure2;
        }

        return kpis;
    }

    public async Task<List<DerivedKpiInputMeasure>> GetDerivedKPIInputMeasures()
    {
        var kpis = await derivedKpiRepository.GetDerivedKPIInputMeasures();
        var dkpis = await derivedKpiRepository.GetDerivedKPIAsInput(currentUser.CompanyId);
        kpis.AddRange(dkpis);

        return kpis;
    }

    public async Task<DerivedKPIInput> GetDerivedKPIById(long focusProductId)
    {
        var kpi = await derivedKpiRepository.GetDerivedKPIById(focusProductId, currentUser.CompanyId);
        return kpi;
    }

    public async Task<RepositoryResponse> CreateUpdateDerivedKPI(DerivedKPIInput kpi)
    {
        if (kpi.Id == 0)
        {
            return await derivedKpiRepository.CreateDerivedKPI(kpi, currentUser.CompanyId);
        }

        return await derivedKpiRepository.UpdateDerivedKPI(kpi, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> ActivateDeactivateDerivedKPI(long kpiId, bool action) => await derivedKpiRepository.ActivateDeactivateDerivedKPI(kpiId, action, currentUser.CompanyId);
}
