﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IKPIRepository
{
    Task<List<KPIList>> GetKPIs(bool showDeactive);
    Task<List<KPIsList>> GetKPIList(bool showDeactive);
    Task<KPIRequestInput> GetKPIById(long id);
    Task<RepositoryResponse> ActivateDeactivateKPI(long id, bool action);
    Task<RepositoryResponse> CreateKPI(KPIRequestInput kpi);
    Task<RepositoryResponse> UpdateKPI(KPIRequestInput kpi);
}
