﻿
namespace FADashboard.Core.Models.ApiModels;
public class TaxIntegrationDeviceResponse
{
    public long TaxIntegrationDeviceId { get; set; }
    public string DeviceName { get; set; }
}
public class TaxIntegrationColumnMeta
{
    public string ColumnName { get; set; }
    public string ColumnValue { get; set; }
    public string ColumnType { get; set; }
    public bool IsRequired { get; set; }
}
public class TaxIntegrationInputModel
{
    public long DistributorWiseTaxIntegrationId { get; set; }
    public long DistributorId { get; set; }
    public string DeviceName { get; set; }
    public long TaxIntegrationDeviceId { get; set; }
    public List<TaxIntegrationColumnMeta> TaxConfigMeta { get; set; }
}
public class DeviceConfiguredDistributorsMeta
{
    public long Id { get; set; }
    public string DeviceName { get; set; }
    public long DistributorId { get; set; }
    public bool IsDeleted { get; set; }
    public List<TaxIntegrationColumnMeta> TaxConfigMeta { get; set; }
    public long TaxIntegrationDeviceId { get; set; }

}
