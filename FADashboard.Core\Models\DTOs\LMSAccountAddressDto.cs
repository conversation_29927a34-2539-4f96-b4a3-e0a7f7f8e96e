﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountAddressDto
    {
        public long Id { get; set; }
        public long AccountId { get; set; }
        public long CompanyId { get; set; }
        public string AddressName { get; set; }
        public string Street { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PinCode { get; set; }
        public string Country { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool IsDeleted { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class LMSAccountAddressCreateInput
    {
        [Required]
        public long AccountId { get; set; }

        [Required]
        [StringLength(255)]
        public string AddressName { get; set; }

        [StringLength(255)]
        public string Street { get; set; }

        [StringLength(100)]
        public string City { get; set; }

        [StringLength(50)]
        public string State { get; set; }

        [StringLength(20)]
        public string PinCode { get; set; }

        [StringLength(50)]
        public string Country { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }
    }

    public class LMSAccountAddressUpdateInput
    {
        [Required]
        [StringLength(255)]
        public string AddressName { get; set; }

        [StringLength(255)]
        public string Street { get; set; }

        [StringLength(100)]
        public string City { get; set; }

        [StringLength(50)]
        public string State { get; set; }

        [StringLength(20)]
        public string PinCode { get; set; }

        [StringLength(50)]
        public string Country { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }
    }
}
