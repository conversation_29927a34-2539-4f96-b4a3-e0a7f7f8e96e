using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IDistributorBeatRepository
{
    Task<RepositoryResponse> AttachBeatsForDistributor(long distributorId, List<long> beatIds, long companyId, CompanySettings companySettings);

    Task<RepositoryResponse> AttachDistributorsandBeat(long companyId, long beatId, List<long> distributorIds);

    Task<List<Beat>> GetBeatsForDistributor(long distributorId, long companyId);

    Task<List<EntityMin>> GetDistributorForBeat(long beatId, long companyId, bool includeDeactivate = false);

    Task<List<EntityMin>> GetDistributorForBeats(List<long> beatIds, long companyId, bool includeDeactivate = false);

    Task<long> GetDistributorBeatMappingCount(long companyId, PaginationFilter validFilter);

    Task<Dictionary<long, long>> GetDistributorBeatMappings(long companyId, List<long> distributorIds);
}
