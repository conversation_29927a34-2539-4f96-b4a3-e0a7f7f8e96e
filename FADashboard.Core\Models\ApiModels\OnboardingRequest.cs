﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class OnboardingRequestInput
{
    public long Id { get; set; }
    public string OutletName { get; set; }
    public string OwnersName { get; set; }
    public string OwnersContactNumber { get; set; }
    public long AssignedUserId { get; set; }
    public long? AssignedToUserId { get; set; }
    public long? AssigneePositionId { get; set; }
    public long? AssignedToPositionId { get; set; }
    public Status Status { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public long? Market { get; set; }
    public string Address { get; set; }
    public string TIN { get; set; }
    public string Email { get; set; }
    public string PinCode { get; set; }
    public string MarketName { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string ImageId { get; set; }
    public string GSTIN { get; set; }
    public string PAN { get; set; }
    public string <PERSON><PERSON>har { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public bool GSTRegistered { get; set; }
    public string BankAccountNumber { get; set; }
    public string AccountHoldersName { get; set; }
    public string IFSCCode { get; set; }
    public string LandMark { get; set; }
    public string LandlineNumber { get; set; }
    public string AlternateImageId { get; set; }
    public string PhotoProofId { get; set; }
    public string TypeOfIdProof { get; set; }
    public string ModeOfDataCollection { get; set; }
    public string AttributeText4 { get; set; }
    public float? AttributeNumber1 { get; set; }
    public float? AttributeNumber2 { get; set; }
    public float? AttributeNumber3 { get; set; }
    public float? AttributeNumber4 { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public string AttributeImage3 { get; set; }
    public string District { get; set; }
    public string FSSAINumber { get; set; }
    public DateTime? FSSAIExpiryDate { get; set; }
    public List<long> SuggestedUserIds { get; set; }
    public List<long> SuggestedUserPositionIds { get; set; }
}

public class OnboardingRequest
{
    public long Id { get; set; }
    public string OutletName { get; set; }
    public string OwnersName { get; set; }
    public string OwnersContactNumber { get; set; }
    public long AssignedUserId { get; set; }
    public long? AssigneePositionId { get; set; }
    public long? AssignedToUserId { get; set; }
    public long? AssignedToPositionId { get; set; }
    public string AssignedUserName { get; set; }
    public string AssignedToUserName { get; set; }
    public string AssignedPositionName { get; set; }
    public string AssignedToPositionName { get; set; }
    public Status Status { get; set; }
}
