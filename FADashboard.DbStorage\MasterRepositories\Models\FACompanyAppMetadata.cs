﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FACompanyAppMetadata")]
public class CompanyAppMetadata : IAuditedEntity, ICompanyEntity
{
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public long Id { get; set; }
    public long CompanyId { get; set; }

    public DateTime LastUpdatedAt { get; set; }
    public AlertSource AppType { get; set; }
    public UpdateType UpdateType { get; set; }
    public int AppVersionNumber { get; set; }
    public string UpdateMessage {  get; set; }


}
