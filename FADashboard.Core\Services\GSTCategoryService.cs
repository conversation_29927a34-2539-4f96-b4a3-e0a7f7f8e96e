﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.DbStorage.MasterRepositories.Models;

namespace FADashboard.Core.Services;

public class GSTCategoryService(
    IGSTCategoryRepository gstCategoryRepository,
    ICurrentUser currentUser
) : RepositoryResponse
{
    /// <summary>
    /// This particular is used to get GST Categories from the tables - ProductGSTCategories and GSTCategoryTaxes
    /// </summary>
    /// <param name="companyId"></param>
    /// <param name="showDeactive"></param>
    /// <returns></returns>
    public async Task<List<ProductGSTCategoryFlat>> GetGSTCategories(bool showDeactive = false) => await gstCategoryRepository.GetGSTCategories(currentUser.CompanyId, showDeactive);

    /// <summary>
    /// This particular is used to create GST Categories. The data is saved in the tables - ProductGSTCategories and GSTCategoryTaxes
    /// </summary>
    /// <param name="gstCategoryInput"></param>
    /// <returns></returns>
    public async Task<RepositoryResponse> CreateGSTCategory(GSTCategoryInput gstCategoryInput)
    {
        if (gstCategoryInput.Id == 0)
        {
            return await gstCategoryRepository.CreateGSTCategory(gstCategoryInput, currentUser.CompanyId);
        }

        return await gstCategoryRepository.UpdateGSTCategory(gstCategoryInput, currentUser.CompanyId);
    }

    //Deactivate GST Category
    public async Task<RepositoryResponse> DeactiveGSTCategory(long id) => await gstCategoryRepository.DeactiveGSTCategory(id, currentUser.CompanyId);
}
