﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FADashboard.Core.Models.ApiModels;
public class ApprovalEntityConfigModel
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public int EntityId { get; set; }
    public bool Status { get; set; }
    //public DateTime CreatedAt { get; set; }
    //public string CreationContext { get; set; }
    //public DateTime LastUpdatedAt { get; set; }
}

