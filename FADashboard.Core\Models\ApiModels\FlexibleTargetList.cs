﻿
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class FlexibleTargetList
{
    public long Id { get; set; }
    public string TargetIdentifier { get; set; }
    public string TargetName { get; set; }
    public string TargetOn { get; set; }
    public TargetType? Target_type { get; set; }
}

public class FlexibleTargetModel
{
    public long Id { get; set; }
    public string TargetIdentifier { get; set; }
    public string TargetName { get; set; }
    public Heirarchy Hierarchy1 { get; set; }
    public Heirarchy? Hierarchy2 { get; set; }
    public Heirarchy? Hierarchy3 { get; set; }
    public string TargetOn { get; set; }
    public string Hierarchy1Query { get; set; }
    public string Hierarchy2Query { get; set; }
    public string Hierarchy3Query { get; set; }
    public string AchievementQuery { get; set; }
    public AchievementDb? AchievementDb { get; set; }
    public AchievementReturnType? AchievementReturnType { get; set; }
    public AppScreen? AppScreen { get; set; }
    public VisualizationType? VisualizationType { get; set; }
    public List<TargetFilters> TargetFilters { get; set; }
    public TargetFilters? DisplayAxis { get; set; }
    public bool? IsForApp { get; set; }
    public TargetType? Target_type { get; set; }
    public string AnalyticalQuery { get; set; }
    public string ReportDataQuery { get; set; }
    public int? ReportDataDb { get; set; }
}
