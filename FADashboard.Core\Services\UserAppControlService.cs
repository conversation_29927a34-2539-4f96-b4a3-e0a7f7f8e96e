﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class UserAppControlService(IUserAppControlRespository userAppRepository, ICurrentUser currentUser, IEmployeeRepository employeeRepository) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsRuleNameValid(UserAppControlInput rule)
    {
        var rules = await userAppRepository.GetRules(currentUser.CompanyId, true);
        if (rule.Id != 0)
        {
            rules = rules.Where(r => r.Id != rule.Id).ToList();
        }
        var activeRuleNames = rules
            .Where(r => !r.IsDeactive)
            .Select(r => r.RuleName.Trim().ToLower())
            .ToList();

        var inactiveRuleNames = rules
            .Where(r => r.IsDeactive)
            .Select(r => r.RuleName.Trim().ToLower())
            .ToList();

        if (activeRuleNames.Contains(rule.RuleName.Trim().ToLower()))
        {
            return new RepositoryResponse
            {
                Id = rule.Id,
                ExceptionMessage = "Rule name is not unique. An active rule with the same name already exists.",
                Message = "Rule Creation/Updation Failed!",
                IsSuccess = false
            };
        }
        if (inactiveRuleNames.Contains(rule.RuleName.Trim().ToLower()))
        {
            return new RepositoryResponse
            {
                Id = rule.Id,
                Message = "Rule name is valid (duplicate of an inactive rule).",
                IsSuccess = true
            };
        }
        return new RepositoryResponse
        {
            Id = rule.Id,
            Message = "Rule name is unique.",
            IsSuccess = true
        };
    }

    public async Task<RepositoryResponse> ActivateDeactivateRule(long ruleId, bool action, CancellationToken ct) => await userAppRepository.ActivateDeactivateRule(ruleId, currentUser.CompanyId, action, ct);

    public async Task<RepositoryResponse> CreateUpdateRule(UserAppControlInput rule, CancellationToken ct)
    {
        var checkValid = await IsRuleNameValid(rule);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }
        if (rule.Id == 0)
        {
            return await userAppRepository.CreateRule(rule, currentUser.CompanyId, ct);
        }

        return await userAppRepository.UpdateRule(rule, currentUser.CompanyId, ct);
    }

    public async Task<UserAppControlInput> GetRuleById(long ruleId, CancellationToken ct)
    {
        var rule = await userAppRepository.GetRuleById(ruleId, currentUser.CompanyId, ct);
        return rule;
    }

    public async Task<List<UserAppControl>> GetRules(bool includeDeactivate, CancellationToken ct)
    {
        var rules = await userAppRepository.GetRules(currentUser.CompanyId, includeDeactivate, ct);
        return rules;
    }
}
