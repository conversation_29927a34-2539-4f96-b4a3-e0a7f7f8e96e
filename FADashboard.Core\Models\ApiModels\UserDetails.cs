﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class IDSLogin
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string EmailId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long LocalId { get; set; }
    public Guid LoginGuid { get; set; }
    public string Name { get; set; }
    public string PhoneNo { get; set; }
    public string RoleIds { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string UserName { get; set; }
}

public class UserDetails
{
    public long CompanyId { get; set; }
    public IDSLogin User { get; set; }
}

public class IDSWithRoleIds
{
    public long Id { get; set; }
    public string RoleIds { get; set; }
    public string LoginGuid { get; set; }
    public string EmailId { get; set; }
}
