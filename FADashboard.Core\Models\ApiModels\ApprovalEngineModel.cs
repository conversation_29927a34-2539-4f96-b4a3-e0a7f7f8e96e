﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class ApprovalEngineModel
{
    public long UserId { get; set; }
    public long RequestId { get; set; }
    public long CompanyId { get; set; }
    public ApprovalEngineRequesType RequestType { get; set; }
    public PortalUserRole UserRole { get; set; }
    public bool IsApprovalRequest { get; set; }
    public bool IsRequestedByAdmin { get; set; }
    public string ReasonForRejection { get; set; }
    public List<long> AttachedPositionsIds { get; set; }
    public bool CompanyUsesHCCBUserFlows { get; set; }
    public OutletAdditionRequestInput OutletAdditionRequestInput { get; set; }
}
