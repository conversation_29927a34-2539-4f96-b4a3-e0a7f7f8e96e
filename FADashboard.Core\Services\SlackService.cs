﻿using System;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Services;
public class SlackService(Microsoft.Extensions.Configuration.IConfiguration configuration, IHttpClientFactory clientFactory, ICurrentUser currentUser) : ISlackService
{
    public async Task SendSlackErrorAsync(Exception ex, DumpRequestReportCreateModel data, ReportSubscription reportSubscription)
    {
        try
        {
            var slackWebhookUrl = configuration["Serilog:SpecialReportErrorWebhook"];
            if (string.IsNullOrWhiteSpace(slackWebhookUrl))
                return;

            var companyId = currentUser?.CompanyId ?? 0;
            var startDate = data.StartDate.ToString("yyyy-MM-dd") ?? "N/A";
            var endDate = data.EndDate.ToString("yyyy-MM-dd") ?? "N/A";
            var subscriptionId = reportSubscription.ReportId;
            var reportId = reportSubscription.Report.Id;
            var reportName = reportSubscription.Report.Name;
            var reportEnum = reportSubscription.Report.ReportType;

            var payload = new
            {
                text = $":warning: *Dashboard API Report Error* :warning:\n" +
                       $"*CompanyId:* {companyId}\n" +
                       $"*UserId:* {data.ExtraJson.UserId}\n" +
                       $"*Report Id:* {reportId}\n" +
                       $"*Report Enum:* {reportEnum}\n" +
                       $"*Report Name:* {reportName}\n" +
                       $"*Date Range:* {startDate} to {endDate}\n" +
                       $"*Error:* {ex.Message}\n" +
                       $"*Stack:* ```{ex.StackTrace}```"
            };

            var client = clientFactory.CreateClient();
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(payload), System.Text.Encoding.UTF8, "application/json");

            await client.PostAsync(slackWebhookUrl, content);
        }
        catch(Exception e)
        {
            System.Console.WriteLine($"Error sending Slack notification: {e.Message}");
        }
    }
}
