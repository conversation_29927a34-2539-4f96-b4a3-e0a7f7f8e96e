﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadContactDto
    {
        public long Id { get; set; }
        public long LeadId { get; set; }
        public long CompanyId { get; set; }
        public string Name { get; set; }
        public string MobileNumber { get; set; }
        public string Email { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Designation { get; set; }
        public string Description { get; set; }
        public string Photo { get; set; }
        public bool IsDecisionMaker { get; set; }
        public bool IsDeleted { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class LMSLeadContactCreateInput
    {
        [Required]
        public long CompanyId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        [StringLength(20)]
        public string MobileNumber { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        public DateTime? DateOfBirth { get; set; }

        [StringLength(100)]
        public string Designation { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        // For now, Photo is a string. File validation for type and size will be handled separately.
        public string Photo { get; set; }

        public bool IsDecisionMaker { get; set; }
    }

    public class LMSLeadContactUpdateInput : LMSLeadContactCreateInput
    {
    }

    public class LMSLeadContactDeleteInput
    {
        [Required]
        public long LeadId { get; set; }

        [Required]
        public long ContactId { get; set; }
    }
}
