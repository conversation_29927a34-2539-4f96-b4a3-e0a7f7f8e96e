﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IReportRequestRepository
{
    Task<List<DumpReportRequestView>> GetReportRequests(long userId, string userEmail, PortalUserRole portalRole, long companyId);

    Task<RepositoryResponse> RequestDownloadReportDataForDateRange(DumpRequestReportCreateModel data, ReportSubscription subscription, RequestMode requestMode);

    Task<RepositoryResponse> RequestMasterReportDataDownload(DumpRequestReportCreateModel data, RequestMode requestMode);
}
