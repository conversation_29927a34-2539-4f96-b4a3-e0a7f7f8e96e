﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ITeamUserMappingsRepository
{
    Task<RepositoryResponse> CreateUpdateTeamUserMapping(long companyId, TeamUserMappingsView teamUserMappingsView);
    Task<RepositoryResponse> CreateUpdateTeamUserMappingForUser(long companyId, List<TeamUserMappingsView> teamUserMappingsView);
    Task<TeamUserMappingsWithPlayers> GetTeamPlayers(long companyId, long teamId);
    Task<List<long>> EmployeeMappedToTeams(long companyId);
    Task<List<EntityMin>> EmployeeMappedToTeams(long companyId, long teamId);
    Task<RepositoryResponse> DeactivateTeamUserMappingForTeamPlayer(long companyId, long teamPlayerId);
    Task<Dictionary<TeamModuleType, long>> GetTeamPlayerTeamIDDict(long companyId, long employeeId);

    Task<Dictionary<long, List<long>>> GetTeamPlayerMappingsDict(long companyId);
}
