﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IPowerBIRepository
{
    Task<List<BIReportSubscriptions>> GetBIReportSubScriptions(long companyId, bool showDeactive);

    Task<RepositoryResponse> ActivateDeactivateBIReports(long id, long companyId, bool action);

    Task<RepositoryResponse> CreatBIReportSubscription(BIReportSubscriptionsInput biReport, long companyId, long userId);

    Task<RepositoryResponse> UpdateBIReportSubscription(BIReportSubscriptionsInput biReport, long companyId);

    Task<BIReportSubscriptions> GetBIReportSubscriptionById(long companyId, long id);

    Task<RepositoryResponse> PinUnpinBIReport(long reportId, SubscribedScreenType subscribedScreen, long companyId, bool action);
    Task<BIReportSubscriptions> GetBIReportSubscribedForUserRole(long companyId, PortalUserRole userRole);
}
