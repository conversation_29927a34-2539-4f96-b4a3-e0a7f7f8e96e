﻿using FADashboard.Core.Interfaces.Cache;
using Microsoft.Extensions.Caching.Distributed;

namespace FADashboard.DbStorage.Helpers;

internal sealed class CacheHelper(ICacheProvider cacheProvider)
{
    //src: https://github.com/turkelk/Quantic/blob/cd4773e95da6bcd291d3fc51eb78594f6355d11d/src/Quantic.Cache.Redis/Main/RedisCacheExtension.cs#L10
    internal async Task<T> GetResultAsync<T>(string cacheKey, int expiresIn, Func<Task<T>> methodProxy)
    {
        var cacheOptions = new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(expiresIn) };

        var result = await cacheProvider.GetAsJsonAsync<T>(cacheKey);
        if (result == null || result.Equals(default(T)))
        {
            result = await methodProxy();
            if (result != null && !result.Equals(default(T)))
                await cacheProvider.SetAsJsonAsync(cacheKey, result, options: cacheOptions).ConfigureAwait(false);
        }

        return result;
    }

    internal async Task ClearCache(string cacheKey) => await cacheProvider.ClearCache(cacheKey);
}
