﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface ITerritoryOptimizationRepository
{
    Task<List<TerritoryOptimizationDetailDto>> GetTerritoryOptimizationDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<List<TerritoryOptimizationDetailDto>> GetTerritoryOptimizationDetails(long id, CancellationToken ct = default);
    Task<RepositoryResponse> SaveTerritoryOptimizationDetail(long userId, long companyId, PortalUserRole userRole,
        string fileName, string emailId, TerritoryInputConstraints inputConstraints, CancellationToken ct = default);
    Task UpdateTerritoryDetailStatus(long id, long userId, CancellationToken cancellationToken = default);
}
