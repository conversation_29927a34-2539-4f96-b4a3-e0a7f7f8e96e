﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class DumpReportRequestView
{
    [Display(Name = "Requester Email")] public string EmailId { get; set; }

    [Display(Name = "Date/Month To")] public string EndTime { get; set; }

    public string ExecutedAt { get; set; }

    [Display(Name = "Execution Time in Secs")]
    public long ExecutionTimeInSecs { get; set; }

    [Display(Name = "Expected Delivery Time")]
    public string ExpectedDeliveryTime { get; set; }

    public long Id { get; set; }

    [Display(Name = "Output File Link")] public string OutputLink { get; set; }

    public string ReportType { get; set; }

    [Display(Name = "Requested Time Range")]
    public string RequestedRange { get; set; }

    [Display(Name = "Date/Month From")] public string StartTime { get; set; }

    //[Display(Name = "Requested Date for Execution")]
    //public DateTime? RequestExecutionDate { get; set; }
    public ReportRequestStatus Status { get; set; }

    [Display(Name = "Status Remark")] public string StatusRemark { get; set; }

    [Display(Name = "Request Status")] public string StatusStr => Status.ToString();
}
