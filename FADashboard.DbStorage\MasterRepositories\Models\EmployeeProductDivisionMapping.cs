﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("EmployeeProductDivisionMappings")]
public class EmployeeProductDivisionMapping : IDeletable, IAuditedEntity
{
    public EmployeeProductDivisionMapping()
    {
        Deleted = false;
    }

    public EmployeeProductDivisionMapping(long companyId) : this()
    {
        CompanyId = companyId;
    }

    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { set; get; }
    public virtual ClientEmployee Employee { get; set; }
    public long EmployeeId { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public virtual ProductDivision ProductDivision { get; set; }
    public long ProductDivisionId { get; set; }
}
