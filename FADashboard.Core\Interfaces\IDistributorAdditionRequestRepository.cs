﻿using FADashboard.Core.Helper;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IDistributorAdditionRequestRepository
{
    Task<RepositoryResponse> ApproveAdditionRequestByAdmin(DistributorAdditionRequestInput request, CompanySettings companySettings);

    Task<RepositoryResponse> DisapproveDistributorAdditionRequest(long requestId, string reasonForRejection);

    Task<DistributorAdditionRequestInput> GetDistributorAdditionRequestById(long schemeId, long companyId);
    Task<List<Requests>> GetDistributorAdditionRequestsForAdmin(long userId, PortalUserRole userRole, long companyId, TimeSpan timeZoneOffsetMinutes, bool includeArchieved);

    Task<List<Requests>> GetDistributorAdditionRequests(long userId, PortalUserRole userRole, long companyId, TimeSpan timeZoneOffsetMinutes, bool includeArchieved);
}
