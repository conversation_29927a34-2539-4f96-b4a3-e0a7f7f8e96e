﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ICohortRepository
{
    Task<RepositoryResponse> CreateCohort(CohortDTO cohort, long companyId);

    Task<RepositoryResponse> UpdateCohort(CohortDTO cohort, long companyId);

    Task<CohortMinCount> GetCohorts(long companyId, PaginationFilter validFilter);

    Task<RepositoryResponse> ActivateDeactivateCohorts(long cohortId, long companyId, bool action);

    Task<CohortViewModel> GetCohort(long cohortId, long companyId);
    Task<List<CohortViewModel>> GetCohorts(long companyId, bool showDeactive);
}
