﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ProductTagSuggestion : ICreatedEntity, ICompanyEntity, IDeactivatable
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long OutletId { get; set; }
    public long ProductId { get; set; }
    public long SuggestedQuantity { get; set; }
    public long ProductTagId { get; set; }
    public long MTDQty { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public ProductTagMaster ProductTag { get; set; }
    [StringLength(32)] public string CreationContext { get; set; }

    public bool IsDeactive { get; set; }
}
