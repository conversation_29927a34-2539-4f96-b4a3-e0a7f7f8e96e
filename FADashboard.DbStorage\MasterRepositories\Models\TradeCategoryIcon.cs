﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;
[Table("FATradeCategoryIcons")]
public class FATradeCategoryIcon : IAuditedEntity
{
    public long Id { get; set; }
    [Required]
    [StringLength(100)]
    public string Image { get; set; }
    public long CompanyId { get; set; }
    public virtual Company Company { get; set; }
    public bool IsDeleted { get; set; }
    public long ProductPrimaryCategoryId { get; set; }
    public ProductPrimaryCategory ProductPrimaryCategory { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { set; get; }
    public string CreationContext { get; set; }
}
