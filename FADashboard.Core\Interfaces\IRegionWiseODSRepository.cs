﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IRegionWiseODSRepository
{
    Task<RegionWiseODSMin> GetRegionODSByRegionId(long regionId, long companyId);
    Task<RepositoryResponse> CreateRegionWiseODS(long zoneId, long regionId, long companyId);
    Task<RepositoryResponse> UpdateRegionWiseODS(long regionId, long zoneId, long companyId, bool? odsAction);
}
