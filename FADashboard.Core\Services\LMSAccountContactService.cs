﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSAccountContactService(ILMSAccountContactRepository lmsAccountContactRepository) : ILMSAccountContactService
    {
        public async Task<LMSAccountContactDto> GetAccountContactByIdAsync(long accountId, long contactId) =>
            await lmsAccountContactRepository.GetByIdAsync(accountId, contactId);

        public async Task<IEnumerable<LMSAccountContactDto>> GetAccountContactsByAccountIdAsync(long accountId) =>
            await lmsAccountContactRepository.GetByAccountIdAsync(accountId);

        public async Task<LMSAccountContactDto> CreateAccountContactAsync(LMSAccountContactCreateInput contactDto, long createdByUserId, long companyId) =>
            await lmsAccountContactRepository.CreateContactAsync(contactDto, createdByUserId, companyId);

        public async Task<LMSAccountContactDto> UpdateAccountContactAsync(long contactId, LMSAccountContactUpdateInput contactDto, long updatedByUserId) =>
            await lmsAccountContactRepository.UpdateContactAsync(contactId, contactDto, updatedByUserId);

        public async Task<bool> DeleteAccountContactAsync(long contactId, long deletedByUserId) =>
            await lmsAccountContactRepository.DeleteContactAsync(contactId, deletedByUserId);
    }
}
