﻿namespace FADashboard.Core.Models.ApiModels;

public class ExternalAPITrackingModel
{
    public long Id { get; set; }
    public int RequestCount { get; set; }
    public string ApiName { get; set; }
    public string ApiUser { get; set; }
    public long DateKey { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public int? Status2xxcount { get; set; }
    public int? Status4xxcount { get; set; }
    public int? Status5xxcount { get; set; }
}
