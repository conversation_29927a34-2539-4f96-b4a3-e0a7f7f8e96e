﻿using FADashboard.Core.Models;

namespace FADashboard.Core.Interfaces;

public interface ICompanyFactoryRepository
{
    Task<List<CompanyFactories>> GetAllCompanyFactories(long companyId, bool includeDeactive);
    Task<CompanyFactoryIO> GetCompanyFactoryById(long id, long companyId);
    Task<RepositoryResponse> DeactivateCompanyFactory(long id, long companyId);
    Task<RepositoryResponse> CreateCompanyFactory(CompanyFactoryIO CompanyFactoryIO, long companyId);

    Task<RepositoryResponse> UpdateCompanyFactory(CompanyFactoryIO CompanyFactoryIO, long companyId);
}
