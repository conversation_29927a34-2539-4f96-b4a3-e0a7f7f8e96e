﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IAssetAllocationTransactionRepository
{
    Task<List<Requests>> GetAssetAllocationRequests(long userId, PortalUserRole userRole, long companyId, bool showArchived);

    Task<RepositoryResponse> ApproveRejectAssetAllocationRequest(AssetAllocationRequest requestDetails, bool isApproved, bool isAgreementRequired);
    Task<AssetAllocation> GetAssetAllocationRequestById(long id, long companyId);
    Task<List<AssetAllocation>> GetAssetAllocationPendingRequest(long companyId);
    Task<RepositoryResponse> UpdateAllocationRequestStatus(long companyId, long equipmentId);
}
