﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;
public interface ITradeCategoryIconRepository
{
    public Task<List<EntityMin>> GetProductPrimaryCategoryMin(long companyId);
    public Task<RepositoryResponse> UpdateTradeCategory(TradeCategoryIcon tradeCategoryIcon, long companyId);
    public Task<RepositoryResponse> CreateTradeCategory(TradeCategoryIcon tradeCategoryIcon, long companyId);
    public Task<RepositoryResponse> DeactivateTradeCategory(long categoryId, long companyId);
    public Task<TradeCategoryIcon> GetTradeCategoryById(long id, long companyId);

    public Task<List<TradeCategoryIcon>> GetTradeCategoryList(long companyId);

}
