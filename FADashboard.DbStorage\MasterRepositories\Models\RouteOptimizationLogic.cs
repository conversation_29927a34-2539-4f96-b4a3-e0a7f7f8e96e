﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class RouteOptimizationLogic(long companyId) : IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; } = companyId;
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string LogicName { get; set; }
    public string AsmId { get; set; }
    public string OutletConstraints { get; set; }

    public int MonthsToConsider { get; set; }
    public int MonthlyAvgOrderRevenueType { get; set; }
    public int VisitCountType { get; set; }
    public int RetailTimeType { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime StartDateForRoute { get; set; }
    public DateTime EndDateForRoute { get; set; }
    public int WorkingHrsInDay { get; set; }
    public long DistanceLimit { get; set; }
    public int StartingPointOfEmployeeType { get; set; }
    public int WeeklyOff { get; set; }
    public double? PrescribedRetailTime { get; set; }
    public bool IsProcessed { get; set; }
    public double? DistancePercentageThreshold { get; set; }
    public double? TimePercentageThreshold { get; set; }
    public bool? IsHomeToHomeEvaluation { get; set; }
    public long? MaxStores { get; set; }
    public long? MinStores { get; set; }
    public double? WorkUtilization { get; set; }
    public int? PreferredDayOfVisitType { get; set; }
    public int? Bufferbetweenvisit { get; set; }
}
