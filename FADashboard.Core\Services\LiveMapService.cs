﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels.LiveMap;
using Libraries.CommonModels;
using Library.ResilientHttpClient;
using Newtonsoft.Json;
using Library.DateTimeHelpers;

namespace FADashboard.Core.Services;

public class LiveMapService(
    ICurrentUser currentUser,
    ILiveMapRepository liveMapRepository,
    IDistributorRepository distributorRepository,
    ICompanySettingsRepository companySettingsRepository,
    FAResilientHttpClient resilientHttpClient,
    AppConfigSettings appConfigSettings) : RepositoryResponse
{
    private async Task<LiveMapDayStartData> GetNSLiveMapBeatsInfo(LiveMapDataSyncModel inputData)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/liveMapDashboard/getData";
        var result = await resilientHttpClient.PostJsonAsync<LiveMapDayStartData>(dataUrl, appConfigSettings.reportApiToken, inputData);
        return result;
    }

    private async Task<List<LiveMapEmployeeLocations>> LiveMapDumpLocation(LiveMapDataSyncModel inputData)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/liveMap/getLocationDataForEmployee";
        var result = await resilientHttpClient.PostJsonAsync<List<LiveMapEmployeeLocations>>(dataUrl, appConfigSettings.reportApiToken, inputData);
        return result;
    }

    private async Task<List<LiveMapThumbnail>> LiveMapThumbnailDetails(List<long> userIds, DateTime date)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/liveMapDashboard/getDataThumbnail?companyId={currentUser.CompanyId}&&date={date}";
        var result = await resilientHttpClient.PostJsonAsync<List<LiveMapThumbnail>>(dataUrl, appConfigSettings.reportApiToken, userIds);
        return result;
    }

    private async Task<List<EmployeeLocationDumpModel>> NSLiveEmployeeDump(LiveMapDataSyncModel inputData)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/liveMap/getDumpData";
        var result = await resilientHttpClient.PostJsonAsync<List<EmployeeLocationDumpModel>>(dataUrl, appConfigSettings.reportApiToken, inputData);
        return result;
    }

    private async Task<List<EmployeeDayActivityWithOutletInfo>> NSLiveMapDataSync(LiveMapDataSyncModel inputData)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/liveMapDashboard/LiveMapOutletAndActivities";
        var result = await resilientHttpClient.PostJsonAsync<List<EmployeeDayActivityWithOutletInfo>>(dataUrl, appConfigSettings.reportApiToken, inputData);
        return result;
    }

    public async Task<RepositoryResponse> AddWatchlistUsers(List<long> userIds) => await liveMapRepository.UpdateWatchlistUsers(userIds, currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole);

    public async Task<List<LiveMapThumbnail>> GetThumbnailDataForEmployee(List<long> userIds, DateTime date) => await LiveMapThumbnailDetails(userIds, date);

    public async Task<dynamic> GetUserLiveMapDetailed(LiveMapDataSyncModel inputData)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var isProductDiv = companySettings.UsesDistProdDivBeatMappings;
        var distributorDict = (await distributorRepository.GetAllDistributorsMin(currentUser.CompanyId)).ToDictionary(d => d.Id, d => d.Name);
        var liveMapDayStartData = await GetNSLiveMapBeatsInfo(inputData);
        var liveMapOutletAndActivities = await NSLiveMapDataSync(inputData);

        liveMapOutletAndActivities.ForEach(activity =>
        {
            if (isProductDiv)
            {
                activity.DistributorId = null;
            }
            else
            {
                activity.DistributorName = activity.DistributorId.HasValue && distributorDict.TryGetValue(activity.DistributorId.Value, out var value) ? value : string.Empty;
            }
        });
        var livemapEmployeeLocations = await NSLiveEmployeeDump(inputData);
        var timelineEmployeeforDay = await LiveMapDumpLocation(inputData);
        var totalDistanceKm = GetTotalDistanceKm(liveMapOutletAndActivities);
        return JsonConvert.SerializeObject(new
        {
            liveMapDayStartData,
            liveMapOutletAndActivities,
            livemapEmployeeLocations,
            timelineEmployeeforDay,
            totalDistanceKm
        });
    }

    public async Task<object> WatchlistCurrentUsers(long userId) => await liveMapRepository.WatchlistCurrentUsers(userId, currentUser.CompanyId);

    public async Task<DayStartDistanceMin> GetUserTravelledDistanceFromDayStarts(long employeeId, DateTime date, long companyId)
    {
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/DailyData/GetUserTravelledDistance?companyId={companyId}&userId={employeeId}&dayStartDateKey={date.GetDateKey()}";
        var result = await resilientHttpClient.GetJsonAsync<DayStartDistanceMin>(dataUrl, appConfigSettings.reportApiToken);
        return result;
    }

    public static double? GetTotalDistanceKm(List<EmployeeDayActivityWithOutletInfo> liveMapOutletAndActivities)
    {
        var TotalDistanceKm = (double?)0;
        var locationsNotNull = liveMapOutletAndActivities
            .Where(location => location.Latitude > 0 || location.Longitude > 0).ToList();

        var iLat = locationsNotNull.Count > 1 ? locationsNotNull[0].Latitude : 0;
        var iLon = locationsNotNull.Count > 1 ? locationsNotNull[0].Longitude : 0;

        foreach (var location in locationsNotNull.Skip(1))
        {
            var curLat = location.Latitude;
            var curLon = location.Longitude;

            var R = 6378137; // Earth’s mean radius in meters
            var dLat = rad(curLat - iLat ?? 0);
            var dLong = rad(curLon - iLon ?? 0);
            var a = (Math.Sin(dLat / 2) * Math.Sin(dLat / 2)) +
                    (Math.Cos(rad(iLat ?? 0)) * Math.Cos(rad(curLat ?? 0)) *
                     Math.Sin(dLong / 2) * Math.Sin(dLong / 2));

            var c = 0.0;
            if (a < 1)
                c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

            var d = R * c / 1000;
            TotalDistanceKm += d;

            iLat = curLat;
            iLon = curLon;
        }

        TotalDistanceKm = Math.Round(1.3 * (TotalDistanceKm ?? 0), 2);
        return TotalDistanceKm;
    }

    private static double rad(decimal x) => (double)x * Math.PI / 180;
}
