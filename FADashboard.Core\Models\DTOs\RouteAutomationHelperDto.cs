﻿namespace FADashboard.Core.Models.DTOs;

public class ExcelEmployeeRoutePlan
{
    public string OutletErpId { get; set; }
    public double? CumulativeDistance { get; set; }
    public double? ConsecutiveOutletDistance { get; set; }
    public double? CumulativeTime { get; set; }
    public string StartTime { get; set; }
    public string EndTime { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Week { get; set; }
    public string Day { get; set; }
    public string Date { get; set; }
    public string EmployeeName { get; set; }
    public int? Iteration { get; set; }
    public int? Sequence { get; set; }
    public string Label { get; set; }
    public string FlagA { get; set; }
    public string FlagB { get; set; }
    public string Flag { get; set; }
    public string ShopName { get; set; }
    public string Region { get; set; }
}

public class EmployeeLocation
{
    public string EmployeeName { get; set; }
    public double AvgLatitude { get; set; }
    public double AvgLongitude { get; set; }
    public List<LatLng> PolygonCoordinates { get; set; } = [];
}

public class LatLng
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
}

public class EmployeeDayLocation
{
    public string EmployeeName { get; set; }
    public int Day { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public List<LatLng> PolygonCoordinates { get; set; } = [];
    public DayStats Stats { get; set; }

    public EmployeeDayLocation()
    {
        PolygonCoordinates = [];
        Stats = new DayStats();
    }
}

public class DayStats
{
    public double TotalDistance { get; set; }
    public string TotalTime { get; set; } = "0";
    public int OutletCount { get; set; }
    public double AvgDistance { get; set; }
    public string AvgTime { get; set; } = "0";
}

public class OutletLocation
{
    public string OutletErpId { get; set; } = string.Empty;
    public int Sequence { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double CumulativeDistance { get; set; }
    public double ConsecutiveOutletDistance { get; set; }
    public string CumulativeTime { get; set; } = "0";
    public string StartTime { get; set; } = string.Empty;
    public string EndTime { get; set; } = string.Empty;
    public string ShopName { get; set; } = string.Empty;
}

public class DayOutlets
{
    public List<OutletLocation> Outlets { get; set; } = [];
    public DayStats Stats { get; set; } = new DayStats();
}

public class EmployeeOutletDaysResponse
{
    public Dictionary<string, DayOutlets> DayWiseOutlets { get; set; } = [];
    public DayStats AggregateStats { get; set; } = new DayStats();
}
public class EmployeeOutletDaysRequest
{
    public int[] Days { get; set; } = [];
}
