﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class EffectivePcKpiInput
{
    public long RuleId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public List<long> Cohorts { get; set; }
    public List<KpiDetails> Kpis { get; set; }
    public bool IsDeactive { get; set; }
    public bool IsDefault { get; set; }
}

public class KpiDetails
{
    public EffectivePcKpis KpiEnum { get; set; }
    public double MinValue { get; set; }
    public ApplicableOn? ApplicableOn { get; set; }
}
