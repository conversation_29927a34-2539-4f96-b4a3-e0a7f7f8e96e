﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class RoutePlanAutomationDetailDto
{
    public long Id { get; set; }

    public string InputFileName { get; set; }

    public string InputFilePublicPath { get; set; }

    public string OutputFilePublicPath { get; set; }

    public RouteAutomationStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public string EmailId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string EstimatedTime { get; set; }
}

public class RoutePlanManualDJPDetailDto
{
    public long Id { get; set; }

    public string InputFileName { get; set; }

    public string InputFilePublicPath { get; set; }

    public RouteAutomationStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public string EmailId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string EstimatedTime { get; set; }
}



public class RoutePlanIntegrationQueueDto
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public bool IsManual { get; set; }

    public string FilePath { get; set; }

    public DateTime StartDate { get; set; }
    public bool IsDJP { get; set; }
    public bool IsDJPIncremental { get; set; }
}
