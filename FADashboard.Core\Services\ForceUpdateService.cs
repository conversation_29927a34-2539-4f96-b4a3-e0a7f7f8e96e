﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;

namespace FADashboard.Core.Services;

public class ForceUpdateService(
    ICurrentUser currentUser,
    IForceUpdateRepository forceUpdateRepository) : RepositoryResponse
{
    public async Task<RepositoryResponse> CreateForceUpdate(CompanyAppMetaModel request)
    {
        return await forceUpdateRepository.CreateForceUpdate(currentUser.CompanyId, request);
    }

}
