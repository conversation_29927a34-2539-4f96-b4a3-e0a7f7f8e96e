﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class UserHierarchyService(
    IEmployeeRepository employeeRepository,
    ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<UserHierarchyModel>> GetUserHierarchyData()
    {
        var hierarchyList = await employeeRepository.GetActiveUserHierarchyUnder(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole);

        return hierarchyList.Where(s => s.UserRole != PortalUserRole.ClientEmployee || (
            s.UserType == EmployeeType.DSR && s.UserRole == PortalUserRole.ClientEmployee)).ToList();
    }
}
