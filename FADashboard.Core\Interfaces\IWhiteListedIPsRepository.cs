﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IWhiteListedIPsRepository
{
    Task<List<WhiteListedIPList>> GetAllWhiteListedIPs(long companyId, bool includeDeactive, CancellationToken ct);
    Task<RepositoryResponse> ActivateDeactivateWhiteListedIP(long companyId, long id, bool action, CancellationToken ct);
    Task<RepositoryResponse> CreateWhiteListedIPs(long companyId, string ruleName, List<IpAdress> IpAdresses, CancellationToken ct);

}
