﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class CompanyConfigService(
    ICurrentUser currentUser,
    ICompanyRepository companyRepository,
    IScreenListRepository screenListRepository
) : RepositoryResponse
{
    public async Task<List<ModuleScreenListView>> GetAllModuleScreenListDefault()
    {
        var moduleScreenListViews = await screenListRepository.GetAllModuleScreenLists();
        return moduleScreenListViews;
    }

    public async Task<RepositoryResponse> CreateUpdateCompanyConfig(List<CompanyConfigModules> companyConfigs)
    {
        var createCompanyConfig = await companyRepository.CreateUpdateCompanyConfig(currentUser.CompanyId, companyConfigs);
        return createCompanyConfig;
    }

    public async Task<List<CompanyConfigModules>> GetCompanySpecificConfig()
    {
        var companyConfigModules = await companyRepository.GetCompanyConfig(currentUser.CompanyId);
        return companyConfigModules;
    }
}
