﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class ProductRecommendationService(IProductRecommendationRepository productRecommendationRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<ProductRecommendationList>> GetProductRecommendations(bool includeDeactivate)
    {
        var productRecommendations = await productRecommendationRepository.GetProductRecommendations(currentUser.CompanyId, includeDeactivate);
        return productRecommendations;
    }

    public async Task<ProductRecommendationInput> GetProductRecommendationById(long Id)
    {
        var productRecommendation = await productRecommendationRepository.GetProductRecommendationById(Id, currentUser.CompanyId);
        return productRecommendation;
    }

    public async Task<RepositoryResponse> ActivateDeactivateProductRecommendation(long Id, bool action) => await productRecommendationRepository.ActivateDeactivateProductRecommendation(Id, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> UpdateProductRecommendation(ProductRecommendationInput productRecommendation) => await productRecommendationRepository.UpdateProductRecommendation(productRecommendation, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateProductRecommendation(ProductRecommendationInput productRecommendation) => await productRecommendationRepository.CreateProductRecommendation(productRecommendation, currentUser.CompanyId);
}
