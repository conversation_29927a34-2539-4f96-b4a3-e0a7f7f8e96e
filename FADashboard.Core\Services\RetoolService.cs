﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Library.ResilientHttpClient;
using Library.StringHelpers;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace FADashboard.Core.Services;

public class RetoolService(ICurrentUser currentUser, IRetoolRepository retoolRepository, IConfiguration configuration, FAResilientHttpClient resilientHttpClient)
{
    public async Task<string> GetRetoolEmbedUrl(RetoolRequest model)
    {
        var url = configuration["Retool:BaseUrl"];
        var auth = configuration["Retool:Authentication"];
        model.companyId = currentUser.CompanyId;
        var body = new { model.landingPageUuid, externalIdentifier = configuration["Retool:ExternalIdentifier"], groupIds = new[] { 92031 }, metadata = model };
        var embedUrlJson = await resilientHttpClient.PostJsonAsync(url, auth, body);
        var responseObj = JsonConvert.DeserializeObject<EmbedResponse>(embedUrlJson);
        responseObj.EmbedUrl += $"?companyId={currentUser.CompanyId}&name={model.reportName}";
        return JsonConvert.SerializeObject(responseObj);
    }

    public async Task<List<RetoolReport>> GetRetoolReports()
    {
        var retoolReports = await retoolRepository.GetRetoolReports();
        return retoolReports;
    }

    public async Task<List<RetoolReportSubscriptions>> GetRetoolReportSubScriptions(bool includeDeactivate)
    {
        var retoolReports = await retoolRepository.GetRetoolReportSubscriptions(currentUser.CompanyId, includeDeactivate);
        return retoolReports;
    }

    public async Task<RepositoryResponse> CreateUpdateRetoolReportSubscription(RetoolReportSubscriptionsInput retoolReport)
    {
        var isValid = await IsValidForCreateUpdate(retoolReport);

        if (!isValid.IsSuccess)
        {
            return isValid;
        }

        if (retoolReport.Id == 0)
        {
            return await retoolRepository.CreatRetoolReportSubscription(retoolReport, currentUser.CompanyId, currentUser.LocalId);
        }

        return await retoolRepository.UpdateRetoolReportSubscription(retoolReport, currentUser.CompanyId);
    }

    private async Task<RepositoryResponse> IsValidForCreateUpdate(RetoolReportSubscriptionsInput retoolReport)
    {
        var isvalid = false;
        var activeSubList = await retoolRepository.GetRetoolReportSubscriptions(currentUser.CompanyId, false);
        if (retoolReport.Id != 0 && retoolReport.Name == activeSubList.FirstOrDefault(p => p.Id == retoolReport.Id).Name)
        {
            isvalid = true;
        }

        if (retoolReport.Id != 0)
        {
            activeSubList = activeSubList.Where(p => p.Id != retoolReport.Id).ToList();
        }

        var inactiveSubList = await retoolRepository.GetRetoolReportSubscriptions(currentUser.CompanyId, true);

        //Duplicate Name Check
        var activeSubNameList = activeSubList.Select(p => p.Name.NormalizeCaps()).ToList();
        var subscribedUserRole = activeSubList.Where(p => p.RetoolReportId == retoolReport.RetoolReportId && retoolReport.PortalUserRoles.Contains(p.PortalUserRole)).ToList();
        if (!isvalid && activeSubNameList.Contains(retoolReport.Name.NormalizeCaps()) && subscribedUserRole.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = retoolReport.Id, ExceptionMessage = "Subscription Name is not unique", Message = "Subscription Creation/Updation Failed!", IsSuccess = false,
            };
        }

        // Check : Should not have same userrole and Retool Report
        var duplicateActiveSubs = activeSubList.Where(p => p.RetoolReportId == retoolReport.RetoolReportId && retoolReport.PortalUserRoles.Contains(p.PortalUserRole)).ToList();
        var duplicateInactiveSubs = inactiveSubList.Where(p => p.RetoolReportId == retoolReport.RetoolReportId && retoolReport.PortalUserRoles.Contains(p.PortalUserRole)).ToList();
        if (duplicateActiveSubs.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = retoolReport.Id,
                ExceptionMessage =
                    $"An active Report/s ({string.Join(", ", duplicateActiveSubs.Select(p => p.Name).ToList())}) already exists for the User role/s {string.Join(", ", duplicateActiveSubs.Select(p => p.PortalUserRole).ToList())} respectively",
                Message = "Subcription Creation/Updation failed",
                IsSuccess = false,
            };
        }

        if (duplicateInactiveSubs.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = retoolReport.Id,
                ExceptionMessage =
                    $"An Inactive Report/s ({string.Join(", ", duplicateActiveSubs.Select(p => p.Name).ToList())}) already exists for the User role/s {string.Join(", ", duplicateInactiveSubs.Select(p => p.PortalUserRole).ToList())} respectively",
                Message = "Subcription Creation/Updation failed",
                IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = 0, Message = "Valid to Create/Update", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateRetoolReports(long subscriptionId, bool action)
    {
        if (action)
        {
            var isValidToActivate = await IsValidToActivateSubscription(subscriptionId);
            if (!isValidToActivate.IsSuccess)
            {
                return isValidToActivate;
            }
        }

        return await retoolRepository.ActivateDeactivateRetoolReports(subscriptionId, currentUser.CompanyId, action);
    }

    private async Task<RepositoryResponse> IsValidToActivateSubscription(long subscriptionId)
    {
        var deactiveSubs = await retoolRepository.GetRetoolReportSubscriptions(currentUser.CompanyId, true);
        var activeSubs = await retoolRepository.GetRetoolReportSubscriptions(currentUser.CompanyId, false);
        var sub = deactiveSubs.FirstOrDefault(p => p.Id == subscriptionId);

        if (sub == null)
        {
            return new RepositoryResponse
            {
                Id = subscriptionId, ExceptionMessage = $"Subscription with the Id {subscriptionId} doesn't exist", Message = "Subcription Activation failed", IsSuccess = false,
            };
        }

        //Duplicate Name Check
        var activeSubNameList = activeSubs.Select(p => p.Name.NormalizeCaps()).ToList();
        var activeUserRoleList = activeSubs.Where(p => p.RetoolReportId == sub.RetoolReportId).Select(x => x.PortalUserRole).ToList();
        if (activeSubNameList.Any(subName => subName.Equals(sub.Name, StringComparison.OrdinalIgnoreCase)) && activeUserRoleList.Contains(sub.PortalUserRole))
        {
            return new RepositoryResponse
            {
                Id = sub.Id, ExceptionMessage = "Subscription Name is not unique", Message = "Subscription Activation Failed!", IsSuccess = false,
            };
        }

        var duplicateActiveSubs = activeSubs.Where(p => p.RetoolReportId == sub.RetoolReportId && p.PortalUserRole == sub.PortalUserRole).ToList();

        if (duplicateActiveSubs.Count != 0)
        {
            return new RepositoryResponse
            {
                Id = subscriptionId, ExceptionMessage = "This Report is already subscribed for this User role", Message = "Subcription Activation failed", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = 0, Message = "Valid to activate", IsSuccess = true, };
    }
}
