﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class EmployeeInput
{
    public bool AllowOrderBooking { get; set; }
    public long? CompanyZoneId { get; set; }
    public string ContactNo { get; set; }
    public DateTime? DateOfJoining { get; set; }
    public long? DesignationId { get; set; }
    public string EmailId { get; set; }
    public string ErpId { get; set; }
    public string LocalName { get; set; }
    public List<long> OutletIds { get; set; }
    public long Id { get; set; }
    public bool IsFieldAppuser { get; set; }
    public string Name { get; set; }
    public List<long> PositionCodes { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
    public string ProductDivision { get; set; }
    public EmployeeRank Rank { get; set; }
    public long? RegionId { get; set; }
    public EmployeeType UserType { set; get; }

    [StringLength(50)]
    public string EmployeeAttributeText1 { get; set; }

    [StringLength(50)]
    public string EmployeeAttributeText2 { get; set; }

    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
    public List<long?> RoleIds { get; set; }
    public bool IsTrainingUser { get; set; }
    public long? TeamId { get; set; }
    public long? BattleGroundTeamId { get; set; }

    public long? AttendanceNormsTeamId { get; set; }
}

public class EmployeeRoleIds
{
    public List<long?> RoleIds { get; set; }
}
