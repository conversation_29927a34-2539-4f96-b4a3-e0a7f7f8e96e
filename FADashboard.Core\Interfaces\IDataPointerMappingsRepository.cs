﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums.Helpers;

namespace FADashboard.Core.Interfaces;

public interface IDataPointerMappingsRepository
{
    Task<List<ManagerAppPointers>> GetManagerAppPointers(long companyId, List<ManagerAppPointers> viewModel);

    Task<RepositoryResponse> SaveManagerAppPointers(ManagerAppPointerInput inputPointers, long companyId);

    Task<List<DailyDataPointers>> GetManagerDataPointers(long companyId);

    Task<List<DailyDataPointers>> GetUserDataPointers(long companyId);

    Task<List<DailyDataPointers>> GetDashboardDataPointers(long companyId);
}
