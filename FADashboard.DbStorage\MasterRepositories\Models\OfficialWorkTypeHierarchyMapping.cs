﻿using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("OfficialWorkTypeHierarchyMappings")]
public class OfficialWorkTypeHierarchyMapping
{
    public long ComapnyId { get; set; }
    public PortalUserRole? HierarchyLevel { get; set; }
    public long Id { get; set; }
    public virtual OfficialWorkType OfficialWorkType { get; set; }
    public long OfficialWorkTypeId { get; set; }
    public PositionCodeLevel? PositionCodeLevel { get; set; }
}
