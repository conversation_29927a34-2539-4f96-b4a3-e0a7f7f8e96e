﻿using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.ApiModels;

public class Banners
{
    [Required(ErrorMessage = "Image is required."), RegularExpression(@"{?\w{8}-?\w{4}-?\w{4}-?\w{4}-?\w{12}}?")]
    public string BannerImageGUID { get; set; }

    public string BannerRedirectUrl { get; set; }

    public long Id { get; set; }
    public List<long> ProductDivisions { get; set; }
    public int Sequence { get; set; }
    public string BannerRedirectImageGUID { get; set; }
    public bool IsBannerRedirectUrl { get; set; }
    public bool IsActive { get; set; }
}
