﻿using System.Text.Json;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class MotorableDistanceCalculationAPIsService(IMotorableDistanceCalculationRepository motorableDistanceCalculationRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<MotorableDistanceCalculationAPIsModel> GetMotorableDistanceCalculationAPIs()
    {
        var apiConfig = await motorableDistanceCalculationRepository.GetMotorableDistanceCalculationAPIs(currentUser.CompanyId);
        if (apiConfig == null)
        {
            return null;
        }

        apiConfig.PositionLevelValues = string.IsNullOrEmpty(apiConfig.PositionLevel) ? [] : JsonSerializer.Deserialize<List<long>>(apiConfig.PositionLevel);
        apiConfig.UserLevelValues = string.IsNullOrEmpty(apiConfig.UserLevel) ? [] : JsonSerializer.Deserialize<List<long>>(apiConfig.UserLevel);
        apiConfig.ActivityValues = string.IsNullOrEmpty(apiConfig.Activity) ? [] : JsonSerializer.Deserialize<List<string>>(apiConfig.Activity);
        apiConfig.WorkFlowDetailsValues = string.IsNullOrEmpty(apiConfig.WorkFlowDetails) ? [] : JsonSerializer.Deserialize<List<WorkFlowDetail>>(apiConfig.WorkFlowDetails);
        return apiConfig;
    }

    public async Task<RepositoryResponse> CreateUpdateMotorableDistanceCalculationAPIs(MotorableDistanceCalculationAPIsModel motorableDistanceCalculation)
    {
        if (motorableDistanceCalculation.Id == 0)
        {
            return await motorableDistanceCalculationRepository.CreateMotorableDistanceCalculationApis(motorableDistanceCalculation, currentUser.CompanyId);
        }

        return await motorableDistanceCalculationRepository.UpdateMotorableDistanceCalculationApis(motorableDistanceCalculation);
    }
}
