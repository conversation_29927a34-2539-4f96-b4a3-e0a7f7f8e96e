﻿using System.Numerics;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Library.NumberSystem;

namespace FADashboard.Core.Helper;

public class CompanySettings(Dictionary<string, object> settings)
{
    private string GetSettingByKey(string key)
    {
        if (settings.TryGetValue(key, out var setting))
            return setting.ToString();

        return null;
    }

    private T GetSetting<T>(string key, T defaultValue)
    {
        try
        {
            var data = GetSettingByKey(key);
            if (!string.IsNullOrEmpty(data))
                return (T)Convert.ChangeType(Convert.ChangeType(data, data.GetType()), typeof(T));

            return defaultValue;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    private T GetSettingEnum<T>(string key, T defaultValue) where T : struct
    {
        try
        {
            var result = defaultValue;
            var value = GetSettingByKey(key).Replace(" ", "");
            if (!string.IsNullOrEmpty(value))
                Enum.TryParse(value, out result);

            return result;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    private PositionCodeLevel GetSettingEnumForPositionCodeLevel(string key, PositionCodeLevel defaultValue)
    {
        try
        {
            var result = defaultValue;
            var value = PositionRoleExtension.PositionCodeLevelFromPositionLevel(GetSettingByKey(key));
            if (!string.IsNullOrEmpty(value))
                Enum.TryParse(value, out result);

            return result;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    public JourneyPlanType GetJourneyPlanType => GetSettingEnum("JourneyPlanType", JourneyPlanType.Default);
    public bool IsUsesNewJourneyPlan => GetSetting("JourneyPlanVersion", "Default") == "New Journey Plan";
    public TimeSpan TimeZoneOffset => TimeSpan.FromMinutes(GetSetting("TimeZoneOffsetMinutes", 330));
    public bool TertiaryOffTakeManual => GetSetting("tertiaryOfftakeManual", false);
    public bool CompanyUsesMonthWiseOpening => GetSetting("UsesMonthWiseOpeningStockMT", false);
    public string GetCountryName => GetSetting("Country", string.Empty);
    public PortalUserRole GetCompanyHighestHierarchy => GetSettingEnum("CompanyUsesHighestHierarchy", PortalUserRole.GlobalSalesManager);
    public PositionCodeLevel GetCompanyHighestPositionLevel => GetSettingEnumForPositionCodeLevel("HighestPositionLevel", PositionCodeLevel.L4Position);
    public string TargetValueType => GetSetting("TargetValueType", "Revenue");
    public TargetValueType OutletTargetOn => GetSettingEnum("Outlettargeton", Libraries.CommonEnums.TargetValueType.Revenue);
    public string BeatTargetValueType => GetSetting("Beatwisetargetvaluetype", "Revenue");
    public CompanySeesDataInEnum CompanySeesDataIn => GetSettingEnum("SalesDataVisibilityMode", CompanySeesDataInEnum.SuperUnit);
    public int MonthStartDate => GetSetting("monthStartDate", 1);
    public int YearStartMonth => GetSetting("yearStartMonth", 4);
    public bool UsesSalesInAmount => GetSetting("UsesSalesinQuantity", false);
    public bool UsesSecondarySales => GetSetting("usesSecondarySales", false);
    public bool UsesOpenMarketOperations => GetSetting("CompanyUsesOpenMarketOperations", false);
    public bool CompanyUsesFaFlo => GetSetting("usesFaFlo", false);
    public string UsesOutletVerification => GetSetting("UsesOutletVerification", "Not Applicable");
    public bool CompanyDoesNotUsesDefaultProductPricing => GetSetting("CompanydoesnotusesDefaultProductPricing", false);
    public bool CompanyCreatesPlanOnPositionCodes => GetSetting("CompanyCreatesPlanOnPositionCodes", false);
    public bool IsRegionWiseOds => GetSetting("CompanyUsesRegionWiseOutletWiseVerification", false);
    public bool CompanyUsesVanSales => GetSetting("usesVanSales", false);
    public bool CompanyUsesDms => GetSetting("CompanyUsesDMS", false);
    public bool UsingIntelligentScheme => GetSetting("usesIntelligentSchemes", false);
    public bool UsesJourneyCalendar => GetSetting("UsesJourneyCalendar", false);
    public bool UsesProductDivision => GetSetting("usesDistProdDivBeatMappings", false);
    public bool UsesReverseGeocodes => GetSetting("usesReverseGeocodes", false);
    public string CurrencySymbol => GetSetting("CurrencySymbol", "₹");
    public string CalculateAchFrom => GetSetting("CalculateAchFrom", "NotApplicable");
    public bool FetchOutletPerformanceDataFrom3RdPartyDms => GetSetting("FetchOutletPerformancedatafrom3rdPartyDMS", false);
    public string OrderBookingScreenType => GetSetting("AppOrderBookingScreenType", "Main");
    public int AssortedProductFlagDays => GetSetting("assortedProductFlagDays", 30);
    public int MaximumNumberOfTrainingUsersAllowed => GetSetting("MaximumNumberOfTrainingUsersAllowed", 0);
    public bool UsesDistProdDivBeatMappings => GetSetting("usesDistProdDivBeatMappings", false);
    public bool UsesUnitCalculateInMetricTon => GetSetting("IsUnitCalculateInMetricTon", false);
    public bool CompaySeeAppVersionInUserManagement => GetSetting("CompaySeeAppVersionInUserManagement", false);
    public bool HideFullDayActivity => GetSetting("hideFullDayActivity", false);
    public bool UsesSecondaryPositionBeatMapping => GetSetting("UsesSecondaryPositionBeatMapping", false);
    public bool UsesModernTrade => GetSetting("usesModernTrade", false);
    public bool CompanyUsesMultipleManning => GetSetting("CompanyUsesMultipleManning", false);
    public bool CompanyUsesRoleBasedAccessRightsModule => GetSetting("CompanyUsesRoleBasedAccessRightsModule", false);
    public bool UsesScheduledCallProductivity => GetSetting("ScheduledCallProductivity", false);
    public int TargetType => GetSetting("TargetsType", 1);
    public bool UsesAddOutletRequest => GetSetting("CompanyUsesAddOutletRequest", false);
    public TypeofDistributorMapping TypeofDistributorMapping => GetSettingEnum("TypeofDistributorMapping", TypeofDistributorMapping.BeatDistributor);
    public string PrimaryTargetsOn => GetSetting("PrimaryTargetsOn", "No Targets");
    public List<string> GetRejectionReasons => GetSetting("ReasonForOutletRequestRejection", new List<string>());
    public int DigitsInPhNo => GetSetting("DigitsInPhNo", 10);
    public int NumberOfDaysFastMovingCalculation => GetSetting("numberofDaysFastMovingCalculation", 30);
    public NumberSystems NumberSystem => GetSettingEnum("numberSystem", NumberSystems.Indian);
    public EmployeeTargetsCalculationType CalculateAchievementAgainstEmployeeTargets => GetSettingEnum("CalculateAchievementAgainstEmployeeTargets", EmployeeTargetsCalculationType.Order);
    public bool ShouldCalculateBeatLength => GetSetting("allowcompaniestocalculateBeatLength", false);
    public double CompanyPricePerUser => GetSetting("PricePerUser", 0.0);
    public int MinimumBillingUser => GetSetting("MinimumBillingUser", 0);
    public bool UserUsesNsApp => GetSetting("UserUsesNSApp", false);
    public bool UsesSingleApprovalForOutletAdditionRequest => GetSetting("UsesSingleApprovalForOutletAdditionRequest", false);
    public string FaBattleGround => GetSetting("FABattleGround", "Not Applicable");
    public string BillingType => GetSetting("BillingType", "Monthly");
    public string TargetOn => GetSetting("TargetOn", "Overall");
    public string BeatTargetOn => GetSetting("Beatwisetargeton", "Overall");
    public string OldJourneyPlanType => GetSetting("JourneyPlanType", "Default");
    public bool UsesPaymentFeature => GetSetting("usesPaymentFeature", false);
    public bool IsOnlineDMS => GetSetting("IsOnlineDMS", false);
    public bool UsesPositionCodes => GetSetting("UsesPositionCodes", false);
    public bool UsesNewDashboard => GetSetting("CompanyUsesNewDashboard", false);
    public bool SeeReportOnBasisOfPositionCode => GetSetting("SeeReportOnBasisOfPositionCode", false);
    public bool UseGeographicalMappingOfOutlets => GetSetting("AllowGeographicalMappingOfOutlets", false);
    public GeographyLevel CompanyHighestGeography => GetSettingEnum("HighestGeoHierarchy", GeographyLevel.Level4);
    public bool UsesLast10Invoices => GetSetting("usesLastThirtyDayInvoice", false);
    public long? WfhSurveyId => GetSetting("WFHSurveyId", (long?)0);
    public bool UsesThirdPartyApiForInvoiceAchievement => GetSetting("UsethirdPartyAPIforInvoiceAchievement", false);
    public bool UsesZonalJourneyCycle => GetSetting("UsesZonalJourneyCycle", false);
    public JourneyPlanVersion JourneyPlanVersion => GetSettingEnum("JourneyPlanVersion", JourneyPlanVersion.OldJourneyPlan);
    public bool IsUsingNewJourneyPlanStructure => JourneyPlanVersion == JourneyPlanVersion.NewJourneyPlan;
    public bool IsBeatForNewJourneyPlanStructure => JourneyPlanningEntity == JourneyPlanningEntity.Beat;
    public bool IsRouteForNewJourneyPlanStructure => JourneyPlanningEntity == JourneyPlanningEntity.Route;
    public JourneyPlanningEntity JourneyPlanningEntity => GetSettingEnum("JourneyPlanningEntity", JourneyPlanningEntity.Beat);
    public bool IsCompanyUsesAttendanceBasedTada => GetSetting("CompanyUsesAttendanceBasedTADA", false);
    public bool IsCompanyUsesNormBasedAttendance => GetSetting("UsesNewAttendanceModule", false);
    public ShowEmployeeSalesIn ShowEmployeeSalesIn => GetSettingEnum("ShowEmployeeSalesIn", ShowEmployeeSalesIn.NetValue);
    public bool IsCallReviewAllowed => GetSetting("callReviewAllowed", false);
    public bool DownloadReportInExcel => GetSetting("DownloadReportInExcel", false);
    public bool IsUnifyEnabled => GetSetting("UsesFAUnify", false);
    public bool UsesEngageWhatsappIntegration => GetSetting("CompanyUsesEngageWhatsappIntegration", false);
    public bool E11Company => GetSetting("E11Company", false);
    public bool CompanyUsesHCCBUserFlows => GetSetting("CompanyUsesHCCBUserFlows", false);
    public bool CompanyUsesGamification => GetSetting("CompanyUsesGamification", false);
    public int NumberOfDaysForDormantUsers => GetSetting("NumberOfDaysForDormantUsers", 0);
    public bool CompanyUsesPrimaryPositionCodeOnly => GetSetting("UsesPrimaryPositionCodeOnly", false);
    public bool SyncOutletMasterfromSFAtoFLO => GetSetting("SyncOutletMasterfromSFAtoFLO", false);
    public bool SyncDistributorMasterfromSFAtoFLO => GetSetting("SyncDistributorMasterfromSFAtoFLO", false);
    public bool SyncProductMasterfromSFAtoFLO => GetSetting("SyncProductMasterfromSFAtoFLO", false);
    public bool CompanyUsesNewApprovalMechanism => GetSetting("NewApprovalMechanism", false);
    public bool CompanyUsesFlexibleTargetModule => GetSetting("CompanyUsesFlexibleTargetModule", false);
    public bool CompanyUsesAdminFlowInAssetAllocation => GetSetting("CompanyUsesAdminFlowInAssetAllocation", false);
    public int ExpectedDeliveryTimeOfOrders => GetSetting("ExpectedDeliveryTimeOfOrders(inDays)", 2);
    public bool AllowUserToInputPOandDateOfDelivery => GetSetting("AllowUserToInputPOandDateOfDelivery", false);
    public bool CompanyUsesCESS => GetSetting("CompanyUsesCESS", false);
    public bool CompanyUsesExciseDutyOnProducts => GetSetting("CompanyUsesExciseDutyOnProducts", false);
    public bool CompanyUsesTaxVerification => GetSetting("CompanyUsesTaxVerification", false);
    public string ConversionFactorForSecondaryCurrencyV2 => GetSetting("ConversionFactorForSecondaryCurrencyV2", string.Empty);
}
