﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("GlobalOutletMetrices")]
public class GlobalOutletMetric : IEntity, IAuditedEntity
{
    public long Id { get; set; }
    [StringLength(2000)]
    public string Name { get; set; }
    [StringLength(2000)]
    public string Description { get; set; }
    public bool IsDeleted { get; set; }
    [StringLength(2000)]
    public string TransactionSqlQuery { get; set; }
    [StringLength(2000)]
    public string ReportSqlQuery { get; set; }
    [StringLength(2000)]
    public string MasterSqlQuery { get; set; }
    public OutletMetricQueryRelation QueriesRelation { get; set; }
    public OutletMetricDataType DataType { get; set; }
    public CueCardPerspective Perspective { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string ParameterReferences { get; set; }
    [StringLength(2000)]
    public string HCCBSQLQuery { get; set; }
    [StringLength(2000)]
    public string ClickHouseQuery { get; set; }
    [StringLength(800)]
    public string? ColumnFields {  get; set; }
}
