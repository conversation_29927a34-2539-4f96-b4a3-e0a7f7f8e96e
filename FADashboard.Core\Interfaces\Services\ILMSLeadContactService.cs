﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSLeadContactService
    {
        Task<LMSLeadContactDto> GetLeadContactAsync(long leadId, long contactId);
        Task<IEnumerable<LMSLeadContactDto>> GetLeadContactsAsync(long leadId);
        Task<LMSLeadContactDto> CreateLeadContactAsync(long leadId, LMSLeadContactCreateInput input, long userId);
        Task<LMSLeadContactDto> UpdateLeadContactAsync(long leadId, long contactId, LMSLeadContactUpdateInput input, long userId);
        Task<bool> DeleteLeadContactAsync(long leadId, long contactId, long userId);
    }
}
