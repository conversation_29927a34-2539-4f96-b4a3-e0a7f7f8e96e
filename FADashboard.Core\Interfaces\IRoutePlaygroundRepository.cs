﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IRoutePlaygroundRepository
{
    Task<List<RoutePlaygroundDetailDto>> GetRoutePlaygroundDetails(long companyId, long userId, PortalUserRole userRole, CancellationToken ct = default);
    Task<List<RoutePlaygroundDetailDto>> GetRoutePlaygroundDetails(long id, CancellationToken ct = default);
    Task<RepositoryResponse> SaveRoutePlaygroundDetails(long userId, long companyId, PortalUserRole userRole,
        string fileName, string emailId, RoutePlaygroundInputConstraints inputConstraints, CancellationToken ct = default);
    Task UpdateRoutePlaygroundDetail(long id, long userId, CancellationToken cancellationToken = default);
}
