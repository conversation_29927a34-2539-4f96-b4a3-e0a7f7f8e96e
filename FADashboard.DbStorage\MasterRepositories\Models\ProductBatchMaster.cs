﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ProductBatchMasters")]
public class ProductBatchMaster : ICreatedEntity
{
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    public long Id { get; set; }

    public string BatchNumber { get; set; }

    public long ProductId { get; set; }

    public long CompanyId { get; set; }

    public decimal MRP { get; set; }

    public DateTime MFGDate { get; set; }

    public DateTime ExpiryDate { get; set; }

    public virtual CompanyProduct Product { get; set; }

    public virtual Company Company { get; set; }
    public decimal PTD { get; set; }
    public decimal PTR { get; set; }
}
