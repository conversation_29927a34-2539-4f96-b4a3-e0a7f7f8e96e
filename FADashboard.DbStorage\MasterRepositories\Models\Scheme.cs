﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using EntityHelper;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public enum DiscountConstraintType
{
    Amount = 1,

    [Display(Name = "Quantity (Unit)")] Quantity = 2,

    [Display(Name = "Standard Unit")] StandardUnit = 3,
}

[Obsolete]
public enum DiscountType
{
    Unknown = 0,
    Cash_Discount = 1, // Can Avail bill Amount reduction equal to the Cash Discount
    FOC = 2, //Allows to use the Discount Amount only for Purchase in smae Block
    Article = 3,
    OpenFOC = 4,
    OtherDiscount = 100
}

public enum SchemeFor
{
    Company = 1,
    Zone = 2,
    Distributor = 3,
    Region = 4
}

public enum SchemeOn
{
    Primary = 1,
    Secondary = 2,
    Product = 3
}

public enum SchemeSubType1
{
    Extendable = 1,
    NonExtendable = 2,
}

public enum SchemeSubType2
{
    Claimable = 3,
    NonClaimable = 4
}

[Table("QualifierSchemeSlabs")]
public class QualifierSchemeSlab
{
    public long? BasketId { get; set; }
    public long Id { get; set; }
    public bool IsBasketMandatory { get; set; }
    public long? ProductId { get; set; }
    public long? Quantity { get; set; }
    public virtual SchemeSlab SchemeSlab { get; set; }
    public long SchemeSlabId { set; get; }
}

[Table("Schemes")]
public class Scheme : IAuditedEntity, IDeletable, ICompanyEntity, IERPEntity
{
    public Scheme()
    {
        IsActive = true;
        Deleted = false;
        SchemeSlabs = [];
        IsMarkup = false;
    }

    #region Entity Enforced Fields

    [Column(TypeName = "datetime2")] public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public Guid Guid { get; set; }

    [Key] public long Id { get; set; }

    [Column(TypeName = "datetime2")] public DateTime LastUpdatedAt { get; set; }

    #endregion Entity Enforced Fields

    #region GenericFields

    [Column("IsDeleted")] public bool Deleted { get; set; }

    public bool IsActive { get; set; }

    #endregion GenericFields

    public SchemeCategorization Category { set; get; }
    public int SchemeStep { set; get; }
    public int? SchemeSequence { set; get; }
    public virtual Company Company { get; set; }

    //Foregign Keys
    [ForeignKey("Company")] public long CompanyId { get; set; }

    public ConstraintType ConstraintType { set; get; }

    [StringLength(4096)] public string DiscountBlockArray { get; set; }

    public DiscountBlock DiscountOn { get; set; }

    [Obsolete("Use Slabs with Payout Type", true)]
    public DiscountType DiscountType { get; set; }

    /// <summary>
    /// Is In Percentage of Purchase Amount or Article Value as per the Discount Type
    /// </summary>
    [Obsolete("Use Slabs", true)]
    public decimal DiscountValue { get; set; }

    public virtual Distributor Distributor { set; get; }

    [Obsolete("Use distributorid block instead")]
    public long? DistributorId { get; set; }

    [StringLength(4096)] public string DistributorIdBlock { set; get; }
    [StringLength(4096)] public string BeatIdBlock { set; get; }
    [StringLength(4096)] public string TerritoryIdBlock { set; get; }

    [Column(TypeName = "datetime2")] public DateTime EndTime { get; set; }

    [StringLength(32)] public string ErpId { get; set; }

    public bool IsMRPBilling { get; set; }

    public bool IsNew { set; get; }

    public bool? MandatoryScheme { get; set; }

    [Obsolete("Use Slabs", true)] public decimal MinAmountConstraint { get; set; }

    [Obsolete("Use Slabs", true)] public int MinQtyConstraint { get; set; }

    [StringLength(100)] public string Name { set; get; }

    [StringLength(2000)] public string OutletConstraints { set; get; }

    [StringLength(2000)]
    [Column("FilterConstraints")]
    public string ProductFilterConstraints { set; get; }

    [StringLength(2000)] public string DistributorConstraints { set; get; }

    [StringLength(2000)] public string UserConstraints { set; get; }

    [StringLength(1024)] public string AttributeText1 { set; get; }

    public PayoutCalculationType PayoutCalculationType { set; get; }

    public DiscountBlock? PayoutOn { get; set; }

    public PayoutType PayoutType { set; get; }

    public virtual Regions Region { set; get; }

    public long? RegionId { get; set; }

    public double? SchemeBudget { get; set; }
    public BudgetType? SchemeBudgetType { get; set; }

    //[Obsolete]
    //public double MinConstraintValue { set; get; }
    [Obsolete("Use Slabs", true)]
    [StringLength(128)]
    public string SchemeOffer { get; set; }

    public ICollection<SchemeSlab> SchemeSlabs { set; get; }

    public SchemeSubType? SchemeSubType { get; set; }

    public SchemeSubType? SchemeSubType2 { get; set; }

    public SchemeType? SchemeType { get; set; }

    public string SelectedProductBlockArray { get; set; }

    [Column(TypeName = "datetime2")] public DateTime StartTime { get; set; }

    [StringLength(1024)] public string StringDescription { get; set; }

    public virtual Zone Zone { set; get; }
    public long? ZoneId { get; set; }
    public PayoutIn? PayoutIn { get; set; }
    public bool IsQPS { get; set; }
    public bool? IsPreferred { get; set; }
    public bool? IsInclusive { get; set; }
    public bool? IsIndividual { get; set; }
    public bool AllowOnNonTrackedSales { set; get; }
    public int? IsSchemeApproved { get; set; }
    public string States { set; get; }
    [StringLength(300)] public string RejectReason { get; set; }
    [StringLength(300)] public string ActionTakenBy { get; set; }

    public SchemeDisbursementMethods? SchemeDisbursementMethod { get; set; }
    public bool UserAllocation { get; set; }
    public long? AlternateScheme { get; set; }
    public CappingType? CappingType { get; set; }
    public double? CappingValue { get; set; }
    public int? QpsType { get; set; }
    public bool IsMarkup { get; set; }
    public AdditionalCalculationLogic? AdditionalCalculationLogic { get; set; }
    public long? DefaultPayoutProduct { get; set; }
    public bool? SingleProductUsesEntireBudget { get; set; }
    public bool? AllowExtraUnitIfBudgetLeft { get; set; }
    public bool CalculationOnCompleteNumber { get; set; }

    public static Scheme CreateDbModel(SchemesInput scheme, Guid schemeGuid, long companyId, string outletConstraint, string distributorConstraint, string productFilterConstraint, string userConstraint)
    {
        var schemeNew = new Scheme
        {
            SchemeType = scheme.SchemeType,
            SchemeSubType = scheme.SchemeSubType,
            SchemeSubType2 = scheme.SchemeSubType2,
            PayoutType = scheme.PayoutType,
            Category = scheme.Category,
            DistributorIdBlock = scheme.DistributorIdBlock,
            OutletConstraints = scheme.IsOutletConstraint ? outletConstraint : null,
            DistributorConstraints = scheme.IsDistributorConstraint ? distributorConstraint : null,
            UserConstraints = scheme.IsUserConstraint ? userConstraint : null,
            ProductFilterConstraints = productFilterConstraint,
            PayoutCalculationType = scheme.PayoutCalculationType,
            ConstraintType = scheme.ConstraintType,
            DiscountOn = scheme.DiscountOn,
            DiscountBlockArray = scheme.DiscountBlockArray,
            StartTime = scheme.StartTime,
            EndTime = scheme.EndTime.AddDays(1).AddMilliseconds(-1),
            Name = scheme.Name,
            AttributeText1 = string.IsNullOrEmpty(scheme.AttributeText1) ? null : scheme.AttributeText1,
            ErpId = scheme.ErpId,
            SchemeBudget = scheme.SchemeBudget,
            SchemeBudgetType = scheme.SchemeBudgetType,
            StringDescription = scheme.StringDescription,
            //   MandatoryScheme = scheme.PayoutType is PayoutType.TopUpDiscount or PayoutType.FOC ? scheme.MandatoryScheme : null,
            MandatoryScheme = scheme.MandatoryScheme,
            PayoutOn = scheme.PayoutOn,
            PayoutIn = scheme.PayoutType == PayoutType.FOC ? scheme.PayoutIn : null,
            SelectedProductBlockArray = scheme.SelectedProductBlockArray,
            SchemeStep = scheme.SchemeStep,
            SchemeSequence = scheme.SchemeSequence,
            CompanyId = companyId,
            Guid = schemeGuid,
            IsNew = true,
            IsQPS = scheme.IsQPS,
            IsPreferred = scheme.IsPreferred,
            IsInclusive = scheme.IsInclusive,
            IsIndividual = scheme.IsIndividual,
            IsMRPBilling = (scheme.PayoutType == PayoutType.FOC || scheme.PayoutType == PayoutType.Discount) && scheme.IsMRPBilling,
            AllowOnNonTrackedSales = scheme.AllowOnNonTrackedSales,
            States = scheme.States,
            UserAllocation = scheme.UserAllocation,
            IsMarkup = scheme.IsMarkup,
            AlternateScheme = scheme.AlternateScheme,
            CappingType = scheme.CappingType,
            CappingValue = scheme.CappingValue,
            AdditionalCalculationLogic = scheme.IsDiscountCalculationOnPtr ? Libraries.CommonEnums.AdditionalCalculationLogic.Discount_On_Ptr : null,
            DefaultPayoutProduct = scheme.PreferredPayoutProduct,
            SingleProductUsesEntireBudget = scheme.SingleProductUsesEntireBudget,
            AllowExtraUnitIfBudgetLeft = scheme.AllowExtraUnitIfBudgetLeft,
            CalculationOnCompleteNumber = scheme.CalculationOnCompleteNumber,
            SchemeSlabs = scheme.SchemeSlabs.Select(s => new SchemeSlab
            {
                Constraint = s.Constraint ?? 0,
                Payout = s.Payout ?? 0,
                MRPPayout = s.MRPPayout,
                QualifierPayoutType = s.QualifierPayoutType,
                PayoutDescription = s.PayoutDescription,
                ProductId = scheme.PayoutType is PayoutType.Product or PayoutType.Coupon ? s.ProductId : null,
                QualifierSchemeSlab = s.QualifierSchemeSlab.Select(ss => new QualifierSchemeSlab { ProductId = ss.ProductId, Quantity = ss.Quantity, BasketId = ss.BasketId, IsBasketMandatory = ss.IsBasketMandatory }).ToList(),
                BasketInvoicePayout = s.BasketInvoicePayout,
                MaxBasketPayout = s.MaxBasketPayout,
                BasketConstraintType = s.BasketConstraintType,
                NominalPrice = s.NominalPrice,
                BatchMasterNumber = s.BatchMasterNumber,
                Priority = s.Priority ?? 0,
                MultiplierStepSize = s.MultiplierStepSize ?? 0
            }).ToList()
        };
        return schemeNew;
    }
}

public class SchemeBucket : IAuditedEntity, IDeletable, ICompanyEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }

    [StringLength(1024)] public string ErpId { get; set; }

    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [StringLength(1024)] public string Name { get; set; }

    public string ProductIds { get; set; }
    public SchemeBasketEntityType? EntityType { get; set; }

    [StringLength(8000)]
    public string EntityValue { get; set; }
}

public class SchemeOutletConstraintDetails
{
    public List<string> RequiredChannels { set; get; }
    public bool? RequiredIsFocused { set; get; }
    public List<string> RequiredSegmentations { set; get; }
    public List<string> RequiredShopTypes { set; get; }
}

[Table("SchemeSlabs")]
public class SchemeSlab
{
    //Asana :- https://app.asana.com/0/home/<USER>/1201154360572471
    //Date:- 21-10-2021
    //Issue:- Setting Default value Empty and not null
    public SchemeSlab()
    {
        QualifierSchemeSlab = [];
    }

    public ConstraintType? BasketConstraintType { get; set; }
    public double? BasketInvoicePayout { get; set; }
    public double Constraint { set; get; }
    public long Id { get; set; }
    public double? MaxBasketPayout { get; set; }
    public double? MRPPayout { get; set; }
    public double? NominalPrice { get; set; }
    public double Payout { set; get; }

    [StringLength(128)] public string PayoutDescription { get; set; }

    public int Priority { set; get; }
    public virtual CompanyProduct Product { set; get; }
    public long? ProductId { set; get; }

    public QualifierPayoutType? QualifierPayoutType { get; set; }

    //public virtual Scheme Scheme { private set; get; }
    public ICollection<QualifierSchemeSlab> QualifierSchemeSlab { get; set; }

    public virtual Scheme Scheme { set; get; }
    public long SchemeId { set; get; }
    [StringLength(2000)] public string BatchMasterNumber { set; get; }
    public int? SlabNumber { set; get; }
    public long? GroupId { set; get; }
    public bool MultipleProductPayout { set; get; }
    [StringLength(4000)]
    public string PayoutProductList { set; get; }
    public int? MultiplierStepSize { set; get; }
}

/*
 Scheme can be defined at different levels in terms of Product i.e.
    Primary Category (PC), Secondary Category (SC), Product/SKU.
*/
