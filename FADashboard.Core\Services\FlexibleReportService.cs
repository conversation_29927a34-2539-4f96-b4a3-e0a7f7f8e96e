﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.ReportBuilderHelper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Libraries.PerspectiveColumns;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class FlexibleReportService(
    ICurrentUser currentUser,
    IFlexibleReportRepository flexibleReportRepository,
    QuickVizService quickVizService,
    IDerivedKPIRepository derivedKPIRepository,
    AppConfigSettings appConfigSettings,
    FAResilientHttpClient resilientHttpClient,
    MTDDateHelper mtdDateHelper,
    IFlexibleReportBuilderRepository flexibleReportBuilderRepository) : RepositoryResponse
{
    public async Task<List<FlexibleReports>> GetAllFlexibleReports(long userId)
    {
        var reportList = await flexibleReportRepository.GetAllFlexibleReports(currentUser.CompanyId, currentUser.UserRole, userId);
        return reportList;
    }

    public async Task<FlexibleReport> GetFlexibleReportByID(long id)
    {
        var flexibleReport = await flexibleReportRepository.GetFlexibleReportById(id, currentUser.UserRole);
        return flexibleReport;
    }

    public async Task<RepositoryResponse> RequestDownloadFlexibleReport(long reportId, FlexibleReportRequestCreateInput data, RequestMode requestMode)
    {
        try
        {
            var report = await flexibleReportRepository.GetFlexibleReportById(reportId, currentUser.UserRole);

            if (report == null)
            {
                return new RepositoryResponse { ExceptionMessage = $"Report with Id {reportId} Not Found!", Message = "Report Request failed!", IsSuccess = false, };
            }

            var createData = new FlexibleReport
            {
                //SubscriptionKey = data.SubscriptionKey, check not required
                StartDate = data.StartDate ?? DateTime.UtcNow,
                EndDate = data.EndDate ?? DateTime.UtcNow,
                ExtraInfoJson = new FlexibleReportRequestCreateModel
                {
                    UserId = data.UserId,
                    UserRole = data.UserRole,
                    UserPositionIds = (data.PCIds != null && data.PCIds.Count != 0) ? data.PCIds : null,
                    UserPositionLevel = data.PositionCodeLevel,
                    PositionUserIds = (data.PCUserIds != null && data.PCUserIds.Count != 0) ? data.PCUserIds : null,
                    PositionUserLevel = data.PCUserLevel,
                    IsNewDashboard = true,
                    Stockists = (data.DistributorIds != null && data.DistributorIds.Count != 0) ? data.DistributorIds : null,
                    StockistType = data.StockistType ?? StockistType.Unknown,
                    DistributorChannel = data.DistributorChannel ?? DistributorChannel.All,
                    //UsesGeographyMultiSelect = data.UsesGeographyMultiSelect,
                    ReportName = report.ReportName,
                    FileName = report.ExtraInfoJson.FileName,
                    PerspectiveType = report.ExtraInfoJson.PerspectiveType,
                    Measures = report.ExtraInfoJson.Measures,
                    AdditionalMeasures = report.ExtraInfoJson.AdditionalMeasures,
                    Dimensions = report.ExtraInfoJson.Dimensions,
                    Columns = report.ExtraInfoJson.Columns,
                    MeasurePersDictionary = report.ExtraInfoJson.MeasurePersDictionary,
                    UseGrouping = report.ExtraInfoJson.UseGrouping,
                    ParentRow = report.ExtraInfoJson.ParentRow,
                    ChildRow = report.ExtraInfoJson.ChildRow,
                    ShowSubtotals = report.ExtraInfoJson.ShowSubtotals,
                    SubscribedUserRoles = report.ExtraInfoJson.SubscribedUserRoles,
                    UseAttendancePivot = report.ExtraInfoJson.UseAttendancePivot,
                    PivotColumnName = report.ExtraInfoJson.PivotColumnName,
                    PivotValues = report.ExtraInfoJson.PivotValues,
                    SecondaryPivot = report.ExtraInfoJson.SecondaryPivot,
                    CompanyId = currentUser.CompanyId,
                    QueryId = report.ExtraInfoJson.QueryId,
                    ViewName = report.ExtraInfoJson.ViewName,
                    DateRangePreset = report.ExtraInfoJson.DateRangePreset,
                    ProductIds = (data.ProductFilterIds != null && data.ProductFilterIds.Count != 0) ? data.ProductFilterIds : null,
                    ProductType = data.ProductFilterLevel,
                    GeoFilter = data.GeoFilter,
                    GeographyFilterIds = data.GeoFilterIds,
                    TargetId = report.ExtraInfoJson.TargetId,
                }
            };

            //if (data.UsesGeographyMultiSelect)
            //{
            //    createData.ExtraJson.GeoFilterIds =
            //        data.RegionIds != null && data.RegionIds.Any() ? data.RegionIds :
            //        (data.ZoneIds != null && data.ZoneIds.Any() ? data.ZoneIds :
            //         (data.GeoLevel5Ids != null && data.GeoLevel5Ids.Any() ? data.GeoLevel5Ids :
            //          (data.GeoLevel6Ids != null && data.GeoLevel6Ids.Any() ? data.GeoLevel6Ids :
            //           (data.GeoLevel7Ids != null && data.GeoLevel7Ids.Any() ? data.GeoLevel7Ids :
            //            null
            //           ))));

            //    createData.ExtraJson.GeoFilter =
            //    data.RegionIds != null && data.RegionIds.Any() ? GeographicalHierarchy.regionId :
            //    (data.ZoneIds != null && data.ZoneIds.Any() ? GeographicalHierarchy.zoneId :
            //    (data.GeoLevel5Ids != null && data.GeoLevel5Ids.Any() ? GeographicalHierarchy.level5 :
            //    (data.GeoLevel6Ids != null && data.GeoLevel6Ids.Any() ? GeographicalHierarchy.level6 :
            //    (data.GeoLevel7Ids != null && data.GeoLevel7Ids.Any() ? GeographicalHierarchy.level7 :
            //                               GeographicalHierarchy.nofilter
            //    ))));
            //}

            if (report.ExtraInfoJson.PerspectiveType is ViewPerspective.TrendReportNLT or ViewPerspective.TrendReportLT)
            {
                if (data.StartDate != null)
                {
                    createData.ExtraInfoJson.StartDate = data.StartDate;
                }

                if (data.EndDate != null)
                {
                    createData.ExtraInfoJson.EndDate = data.EndDate;
                }
            }
            else if (data.StartDate != null && data.EndDate != null)
            {
                createData.ExtraInfoJson.StartDate = data.StartDate;
                createData.ExtraInfoJson.EndDate = data.EndDate;

                if (report.ExtraInfoJson.DateRangePreset == DateRangePreset.MTD)
                {
                    var mtdData = await mtdDateHelper.GetMTDDateData(data.EndDate.Value);
                    createData.ExtraInfoJson.StartDate = mtdData.MTD.StartDate;
                }
            }
            var res = createData.ExtraInfoJson.PerspectiveType != ViewPerspective.FlexibleTargetVsAchievement
                ? await flexibleReportRepository.AddFlexibleReportToQueue(createData, requestMode, reportId)
                : await flexibleReportRepository.AddTargetAchievementReportToQueue(createData, requestMode, reportId);
            if (res.IsSuccess)
            {
                return res;
            }

            return new RepositoryResponse
            {
                Id = res.Id, ExceptionMessage = $"{res.ExceptionMessage}", Message = "Some Error Occurred, Please Go Back and Retry", IsSuccess = false,
            };
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = 0, ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }


    public async Task<RepositoryResponse> RequestDownloadFlexibleBuilderReport(string reportId, FlexibleReportRequestCreateInput data, RequestMode requestMode)
    {
        try
        {
            var report = await flexibleReportBuilderRepository.GetByIdAsync(reportId, currentUser.CompanyId);

            var createData = new FlexibleReportBuilderRequest
            {
                //SubscriptionKey = data.SubscriptionKey, check not required
                StartDate = data.StartDate ?? DateTime.UtcNow,
                EndDate = data.EndDate ?? DateTime.UtcNow,
                ExtraInfoJson = new FlexibleReportBuilderRequestCreateModel
                {
                    UserId = data.UserId,
                    UserRole = data.UserRole,
                    PositionHierachyIds = (data.PCIds != null && data.PCIds.Count != 0) ? data.PCIds : null,
                    PositionHierarchyLevel = data.PositionCodeLevel,
                    PositionUserIds = (data.PCUserIds != null && data.PCUserIds.Count != 0) ? data.PCUserIds : null,
                    PositionUserLevel = data.PCUserLevel,
                    Stockists = (data.DistributorIds != null && data.DistributorIds.Count != 0) ? data.DistributorIds : null,
                    StockistType = data.StockistType ?? StockistType.Unknown,
                    DistributorChannel = data.DistributorChannel ?? DistributorChannel.All,
                    CompanyId = currentUser.CompanyId,
                    ProductFilterIds = (data.ProductFilterIds != null && data.ProductFilterIds.Count != 0) ? data.ProductFilterIds : null,
                    ProductFilterType = data.ProductFilterLevel,
                    GeoFilter = data.GeoFilter,
                    GeographyFilterIds = data.GeoFilterIds,
                    ReportId = reportId,
                    ReportName = report?.Metadata?.ReportName,
                    StartDate = data.StartDate ?? DateTime.UtcNow,
                    EndDate = data.EndDate ?? DateTime.UtcNow,
                }
            };

            var res = await flexibleReportRepository.AddToFlexibleReportBuilderQueue(createData, requestMode);
            if (res.IsSuccess)
            {
                return res;
            }

            return new RepositoryResponse
            {
                Id = res.Id,
                ExceptionMessage = $"{res.ExceptionMessage}",
                Message = "Some Error Occurred, Please Go Back and Retry",
                IsSuccess = false,
            };
        }
        catch (Exception ex)
        {
            return new RepositoryResponse { Id = 0, ExceptionMessage = ex.Message, Message = "some error occurred. please try again.", IsSuccess = false };
        }
    }

    public async Task<List<PerspectiveColumnModel>> GetPerspectiveColumns(ViewPerspective viewPerspective)
    {
        var cols = await quickVizService.GetPerspectiveColumnsAsPerSettings(viewPerspective);
        if (viewPerspective is ViewPerspective.EmpGeoPerformanceData or
            ViewPerspective.ProductWiseSales or
            ViewPerspective.NoSalesReason or
            ViewPerspective.DayStart)
        {
            var derivedKPIsList = await derivedKPIRepository.GetDerivedKPIs(viewPerspective, currentUser.CompanyId);
            foreach (var kpi in derivedKPIsList)
            {
                if (kpi.IsNomenclature)
                {
                    cols.RemoveAll(c => c.Name == kpi.Measure1);
                }

                cols.Add(new PerspectiveColumnModel
                {
                    Attribute = "Sales Measure",
                    DisplayName = kpi.Name,
                    Name = "Derived_" + kpi.Id,
                    IsMeasure = true,
                    IsDimension = false,
                    PerspectiveMeasure = PerspectiveMeasure.Value
                });
            }
        }

        return cols;
    }

    public async Task<RepositoryResponse> CreateFlexibleReports(FlexibleReport data)
    {
        if (data.SaveFlexibleReport)
        {
            return await flexibleReportRepository.SaveFlexibleReport(currentUser.CompanyId, data);
        }

        return new RepositoryResponse { Id = currentUser.CompanyId, Message = "SaveFlexibleReport is false", IsSuccess = false, };
    }

    public async Task<RepositoryResponse> FlexibleReportDownloadWithCreateInput(FlexibleReport data)
    {
        if (!data.SaveFlexibleReport || !data.ExtraInfoJson.SaveFlexibleReport)
        {
            data.ExtraInfoJson.IsNewDashboard = true;
            var result = await flexibleReportRepository.AddFlexibleReportToQueue(data, RequestMode.Download);
            return result;
        }

        return new RepositoryResponse { Message = "Error! SaveFlexibleReport value received as true", IsSuccess = false, };
    }

    public async Task<List<FlexibleReportsMin>> GetAllFlexibleReportsList(long userId)
    {
        var reportList = await flexibleReportRepository.GetAllFlexibleReportsList(currentUser.CompanyId, currentUser.UserRole, userId);
        return reportList;
    }

    public async Task<RepositoryResponse> ActivateDeactivateFlexibleReport(long reportId, bool action) => await flexibleReportRepository.ActivateDeactivateFlexibleReport(reportId, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> UpdateFlexibleReportSubscription(long flexibleReportId, string subscriptionRoles) =>
        await flexibleReportRepository.UpdateFlexibleReportSubscription(flexibleReportId, currentUser.CompanyId, subscriptionRoles);

    public async Task<RepositoryResponse> UpdateFlexibleReportEmailSubscription(long flexibleReportId, string subscriptionRoles, string subscriptionToAdmins, bool subscriptionIsActive, EmailFrequency frequency) =>
        await flexibleReportRepository.UpdateFlexibleReportEmailSubscription(currentUser.CompanyId, flexibleReportId, subscriptionRoles, subscriptionToAdmins, subscriptionIsActive, frequency);

    public async Task<RepositoryResponse> TargetAchievementReportDownloadWithCreateInput(FlexibleReport data)
    {
        if (!data.SaveFlexibleReport || !data.ExtraInfoJson.SaveFlexibleReport)
        {
            data.ExtraInfoJson.IsNewDashboard = true;
            var result = await flexibleReportRepository.AddTargetAchievementReportToQueue(data, RequestMode.Download);
            return result;
        }

        return new RepositoryResponse { Message = "Error! SaveFlexibleReport value received as true", IsSuccess = false, };
    }

    public async Task<List<PerspectiveColumnModel>> GetTargetMasterDimensions(long targetId)
    {
        var targetMaster = await flexibleReportRepository.GetTargetMaster(targetId);
        var targetHierarchyList = new List<Heirarchy> { targetMaster.Hierarchy1 ?? 0, targetMaster.Hierarchy2 ?? 0, targetMaster.Hierarchy3 ?? 0 };
        var cols = await quickVizService.GetPerspectiveColumnsAsPerSettings(ViewPerspective.FlexibleTargetVsAchievement);
        var dimensions = new List<string> { "Performance" }; //will contain only the required dimensional attribute
        if (targetHierarchyList.Contains(Heirarchy.Position) || targetHierarchyList.Contains(Heirarchy.User))
        {
            dimensions.Add("Position");
        }
        if (targetHierarchyList.Contains(Heirarchy.Beat)
            || targetHierarchyList.Contains(Heirarchy.Route)
            || targetHierarchyList.Contains(Heirarchy.Outlet)
            || targetHierarchyList.Contains(Heirarchy.Territories)
            || targetHierarchyList.Contains(Heirarchy.Region)
            || targetHierarchyList.Contains(Heirarchy.Zone)
            || targetHierarchyList.Contains(Heirarchy.OutletChannel)
            || targetHierarchyList.Contains(Heirarchy.OutletSegmentation)
            || targetHierarchyList.Contains(Heirarchy.ShopTypes)
            || targetHierarchyList.Contains(Heirarchy.DistributorChannel)
            || targetHierarchyList.Contains(Heirarchy.DistributorSegmentation)
            )
        {
            dimensions.Add("Sales Territory");
        }
        if (targetHierarchyList.Contains(Heirarchy.Outlet))
            dimensions.Add("Outlet Attributes");
        if (targetHierarchyList.Contains(Heirarchy.Product)
            || targetHierarchyList.Contains(Heirarchy.PrimaryCategory)
            || targetHierarchyList.Contains(Heirarchy.SecondaryCategory)
            || targetHierarchyList.Contains(Heirarchy.ProductDivision)
            || targetHierarchyList.Contains(Heirarchy.DisplayCategory))
        {
            dimensions.Add("Product");
        }
        if (targetHierarchyList.Contains(Heirarchy.MustSellRule))
        {
            dimensions.Add("Must Sell Rule");
        }
        if (targetHierarchyList.Contains(Heirarchy.FocusedProductRule))
        {
            dimensions.Add("Focussed Product Rule");
        }
        if (targetHierarchyList.Contains(Heirarchy.ProductGroup))
        {
            dimensions.Add("Product Group");
        }
        if (targetHierarchyList.Contains(Heirarchy.Distributors))
            dimensions.Add("Channel Partner");
        cols = cols.Where(x => dimensions.Contains(x.Attribute)).ToList();

        //handle visibility of route dimension when target is on beat and vice-versa
        cols.RemoveAll(x =>
            (x.Name == "Route" && targetHierarchyList.Contains(Heirarchy.Beat)) ||
            (x.Name == "Beat" && targetHierarchyList.Contains(Heirarchy.Route)));

        //remove lower-levels dimension when target is defined at a higher level
        var hierarchyOrderCols = targetHierarchyList.Where(HierarchyOrderDict.ContainsKey).Select(target => HierarchyOrderDict[target]).ToList();
        foreach (var att in hierarchyOrderCols)
        {
            cols.RemoveAll(x => hierarchyOrderCols.Any(targetHierarchyOrder => x.Attribute == att.Item2 && x.HierarchyOrder > att.Item1));
        }

        //freeze mandatory columns
        targetHierarchyList
            .Where(MasterEntityFreezedColumnsDict.ContainsKey)
            .SelectMany(hierarchy => MasterEntityFreezedColumnsDict[hierarchy])
        .ToList()
        .ForEach(col =>
        {
            var targetColumn = cols.FirstOrDefault(c => c.Name == col);
            if (targetColumn != null)
            {
                targetColumn.IsMandatory = true;
            }
        });

        return cols;
    }

    public async Task<bool> GetValidDateRange(long targetId, DateTime startDate, DateTime endDate)
    {
        var companyId = currentUser.CompanyId;
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/target-achievement/flexible-target-ach-date-validation?companyId={companyId}&targetId={targetId}&startDate={startDate:MM/dd/yyyy}&endDate={endDate:MM/dd/yyyy}";
        var result = await resilientHttpClient.GetJsonAsync<bool>(dataUrl, appConfigSettings.reportApiToken);
        return result;
    }

    public static readonly Dictionary<Heirarchy, List<string>> MasterEntityFreezedColumnsDict = new()
        {
            {Heirarchy.Beat, new List<string> { "Beat" } },
            {Heirarchy.Route, new List<string> { "Route" } },
            {Heirarchy.Outlet, new List<string> {"Shop"} },
            {Heirarchy.Territories, new List<string> {"Territory"} },
            {Heirarchy.Region, new List<string> {"Region"} },
            {Heirarchy.Zone, new List<string> {"Zone"} },
            {Heirarchy.Product, new List<string> { "ProductName", "ProductErpId"} },
            {Heirarchy.PrimaryCategory, new List<string> { "PrimaryCategory" } },
            {Heirarchy.SecondaryCategory, new List<string> { "SecondaryCategory" } },
            {Heirarchy.ProductDivision, new List<string> { "ProductDivision" } },
            {Heirarchy.DisplayCategory, new List<string> { "DisplayCategory" } },
            {Heirarchy.Distributors, new List<string> { "Distributor", "DistributorErpId" } },
        };

    public static readonly Dictionary<Heirarchy, (int, string)> HierarchyOrderDict = new()
    {
        //{key: target entity enum, value: target entity hierarchy order}
        {Heirarchy.Route, (4, "Sales Territory" ) },
        {Heirarchy.Territories, (3, "Sales Territory") },
        {Heirarchy.Region, (2, "Sales Territory") },
        {Heirarchy.Zone, (1, "Sales Territory") },
        {Heirarchy.Product, (4, "Product") },
        {Heirarchy.SecondaryCategory, (3, "Product")},
        {Heirarchy.PrimaryCategory, (2, "Product") },
        {Heirarchy.ProductDivision, (1, "Product") },
    };


    public async Task<IEnumerable<ReportDefinitionMin>> GetAllReportBuilderReportsAsync()
    {
        return await flexibleReportBuilderRepository.GetAllAsync(currentUser.CompanyId);
    }

    public async Task<ReportDefinitionTemplate> GetReportBuilderReportsByIdAsync(string id)
    {
        return await flexibleReportBuilderRepository.GetByIdAsync(id, currentUser.CompanyId);
    }
}
