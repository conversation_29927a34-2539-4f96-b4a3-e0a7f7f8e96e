﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("GSTCategoryTaxes")]
public class GSTCategoryTax : ICreatedEntity
{
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    public long Id { get; set; }

    [Audited] public bool IsEnded { get; set; }

    public DateTime? EndedAt { get; set; }


    public decimal IGST { get; set; }

    public decimal CGST { get; set; }

    public decimal SGST { get; set; }

    public decimal VAT { get; set; }

    public ProductGSTCategory CompanyGSTCategory { get; set; }

    public long CompanyGSTCategoryId { get; set; }


    public long CompanyId { get; set; }

    public Company Company { get; set; }
}

[Table("ProductGSTCategories")]
public class ProductGSTCategory : ICreatedEntity, IDeletable
{
    public Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    public bool Deleted { get; set; }
    public IEnumerable<GSTCategoryTax> GSTCategoryTaxes { get; set; }
    public long Id { get; set; }

    [StringLength(256)] public string Name { get; set; }
}
