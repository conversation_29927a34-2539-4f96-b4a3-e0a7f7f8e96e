﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("FocusedProductRulePositionCodeMappings")]
public class FocusedProductRulePositionCodeMapping
{
    public virtual FocusedProductRule FocusedProductRule { get; set; }

    [ForeignKey("FocusedProductRule")] public long FocusedProductRuleId { get; set; }

    public long Id { get; set; }

    public virtual PositionCode PositionCode { get; set; }

    [ForeignKey("PositionCode")] public long PositionCodeId { get; set; }
}
