﻿using FADashboard.DbStorage.HCCBRepositories.Models;
using FADashboard.DbStorage.Models;
using Microsoft.EntityFrameworkCore;

namespace FADashboard.DbStorage.DbContexts;

public class HCCBDbContext(DbContextOptions<HCCBDbContext> options) : DbContext(options)
{
    public DbSet<HCCBIntegrationLog> HCCBIntegrationLogs { get; set; }
    public DbSet<HCCBIntegrationAPILog> HCCBIntegrationAPILogs { get; set; }
    public DbSet<CustomerMaster> CustomerMaster { get; set; }
    public DbSet<CustomerMasterSalesArea> CustomerMasterSalesArea { get; set; }
    public DbSet<OrdersApiLogs> OrdersApiLogs { get; set; }
    public DbSet<OrderSyncFallbackProcessorAttendanceId> OrderSyncFallbackProcessorAttendanceIds { get; set; }

    public DbSet<WeeklyRouteUpload> WeeklyRouteUpload { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.Entity<HCCBIntegrationLog>().ToTable("hccbintegrationlogs");
    }
}
