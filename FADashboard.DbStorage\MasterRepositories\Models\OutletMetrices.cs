﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("OutletMetrices")]
public class OutletMetric(long companyId) : IEntity, ICompanyEntity, IAuditedEntity
{
    public long Id { get; set; }
    public string Name { get; set; }

    [Required]
    [StringLength(1000)]
    public string Description { get; set; }

    public long CompanyId { get; set; } = companyId;
    public Company Company { get; set; }
    public bool IsExternal { get; set; }
    public bool IsActive { get; set; } = true;
    public long CueCardId { get; set; }
    public CueCardsMaster CueCard { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public OutletMetricFrequency? Frequency { get; set; }
    public OutletMetricCurrentDayOfWeek? WeekStartDay { get; set; }
    public string ParameterValue { get; set; }
    public long? GlobalMetricId { get; set; }
    public long? ExternalMetricId { get; set; }
    public virtual GlobalOutletMetric GlobalMetric { get; set; }
    public virtual CompanyExternalMetrices ExternalMetric { get; set; }
    public bool? DateFilter { get; set; }

    [StringLength(int.MaxValue)] public string ColourConstraints { get; set; }
}
