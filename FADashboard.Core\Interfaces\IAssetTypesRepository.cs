﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IAssetTypesRepository
{
    Task<List<AssetTypeList>> GetAllAssetTypes(long companyId, bool includeDeactive);
    Task<AssetTypeList> GetAssetTypeById(long id, long companyId);
    Task<RepositoryResponse> CreateAssetType(long companyId, AssetTypeList assetTypeInput);
    Task<RepositoryResponse> UpdateAssetType(long companyId, AssetTypeList assetTypeInput);
    Task<RepositoryResponse> DeactivateAssetType(long companyId, long id);
}
