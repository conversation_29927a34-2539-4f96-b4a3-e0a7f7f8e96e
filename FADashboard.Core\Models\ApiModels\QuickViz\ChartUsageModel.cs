﻿namespace FADashboard.Core.Models.ApiModels.QuickViz;

public class ChartUsageModel
{
    public long Id { get; set; }
    public long? ChartId { get; set; }
    public bool IsFailure { get; set; }
    public int ResponseTime { get; set; }
    public bool DateManipulated { get; set; }
    public DateTime? QueryStartDate { get; set; }
    public DateTime? QueryEndDate { get; set; }
    public string FailureMessageTrimmed { get; set; }
}
