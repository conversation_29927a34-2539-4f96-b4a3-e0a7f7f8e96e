﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IProductWinbackLogicRepository
{
    Task<List<ProductWinbackList>> GetAllProductWinbackLogics(long companyId, bool showDeactive);
    Task<ProductWinbackInput> GetProductWinbackLogicById(long companyId, long id);
    Task<RepositoryResponse> ActivateDeactivateWinbackLogic(long Id, bool action, long companyId);
    Task<RepositoryResponse> CreateProductWinbackLogic(ProductWinbackInput productWinbackInput, long companyId);
    Task<RepositoryResponse> UpdateProductWinbackLogic(ProductWinbackInput productWinbackInput, long companyId);
}
