﻿using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class JourneyCycle : IAuditedEntity
{
    public long Id { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string CreationContext { get; set; }

    [Audited] public int MonthNumber { get; set; }

    [Audited] public string MonthName { get; set; }

    [Audited] public DateTime MonthStartDate { get; set; }

    [Audited] public DateTime MonthEndDate { get; set; }

    public long JourneyCalendarId { get; set; }

    public long CompanyId { get; set; }

    public virtual Company Company { get; set; }

    public virtual JourneyCalendar JourneyCalendar { get; set; }

    public virtual ICollection<JourneyWeek> JourneyWeeks { get; set; }
}
