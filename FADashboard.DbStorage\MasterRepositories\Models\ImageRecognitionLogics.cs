﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ImageRecognitionLogic : IAuditedEntity
{
    public ImageRecognitionLogic()
    {
        IsDeactive = false;
    }

    [Key] public long Id { get; set; }
    public string RuleName { get; set; }
    [ForeignKey("Company")] public long CompanyId { get; set; }
    public virtual Company Company { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime StartDate { get; set; }
    public bool IsMandatory { get; set; }
    public DateTime EndDate { get; set; }
    public string OutletHierarchyConstraints { get; set; }
    public string OutletConstraints { get; set; }
    public string ProductConstraints { get; set; }
    public string Assets { get; set; }
    public string KPIWeightage { get; set; }
    public int? Positioning { get; set; }
    public bool BeforeSeenEnabled { get; set; }
}
