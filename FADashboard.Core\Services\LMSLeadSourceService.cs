using Amazon.Auth.AccessControlPolicy;
using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSLeadSourceService(ILMSLeadSourceRepository lmsLeadSourceRepository) : ILMSLeadSourceService
    {
        public async Task<LMSLeadSourceDto> GetLeadSourceByIdAsync(long id) => await lmsLeadSourceRepository.GetByIdAsync(id);

        public async Task<PagedResult<LMSLeadSourceDto>> GetAllLeadSourcesByCompanyAsync(long companyId, LMSLeadSourceQueryParameters queryParameters) =>
            await lmsLeadSourceRepository.GetAllByCompanyIdAsync(companyId, queryParameters);

        public async Task<LMSLeadSourceDto> CreateLeadSourceAsync(long companyId, LMSLeadSourceInput source, long createdByUserId)
        {
            ArgumentNullException.ThrowIfNull(source);

            if (await lmsLeadSourceRepository.SourceNameExistsAsync(companyId, source.SourceName))
            {
                throw new InvalidOperationException($"A lead source with the name '{source.SourceName}' already exists for this company.");
            }

            var sourceDto = new LMSLeadSourceDto
            {
                CompanyId = companyId,
                SourceName = source.SourceName,
                IsActive = source.IsActive,
                Description = source.Description,
                IsDeleted = source.IsDeleted,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = createdByUserId
            };

            return await lmsLeadSourceRepository.AddAsync(sourceDto);
        }

        public async Task<LMSLeadSourceDto> UpdateLeadSourceAsync(long id, long companyId, LMSLeadSourceInput source, long updatedByUserId)
        {
            ArgumentNullException.ThrowIfNull(source);

            if (await lmsLeadSourceRepository.SourceNameExistsAsync(companyId, source.SourceName, id))
            {
                throw new InvalidOperationException($"A lead source with the name '{source.SourceName}' already exists for this company.");
            }

            source.Id = id;
            //source.UpdatedBy = updatedByUserId;
            var sourceDto = new LMSLeadSourceDto
            {
                Id = id,
                CompanyId = companyId,
                SourceName = source.SourceName,
                UpdatedBy = updatedByUserId,
                UpdatedAt = DateTime.UtcNow,
                IsActive = source.IsActive,
                Description = source.Description,
                IsDeleted = source.IsDeleted
            };

            return await lmsLeadSourceRepository.UpdateAsync(sourceDto);
        }

        public async Task<bool> DeleteLeadSourceAsync(long id, long updatedByUserId) =>
            await lmsLeadSourceRepository.DeleteAsync(id, updatedByUserId);
    }
}
