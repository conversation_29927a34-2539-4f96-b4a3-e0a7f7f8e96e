﻿using FADashboard.Core.Models;
using FADashboard.DbStorage.MasterRepositories.Models;

namespace FADashboard.Core.Interfaces;

public interface IGSTCategoryRepository
{
    Task<List<ProductGSTCategoryFlat>> GetGSTCategories(long companyId, bool showDeactive = false);
    Task<RepositoryResponse> CreateGSTCategory(GSTCategoryInput gstCategoryInput, long companyId);
    Task<RepositoryResponse> UpdateGSTCategory(GSTCategoryInput gstCategoryInput, long companyId);
    Task<RepositoryResponse> DeactiveGSTCategory(long id, long companyId);
}
