﻿using System.Diagnostics;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ApiModels.QuickViz;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Libraries.PerspectiveColumns;
using Libraries.PerspectiveColumns.Interface;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class QuickVizService(
    IQueryViewRepository queryViewRepository,
    ICurrentUser currentUser,
    IDerivedKPIRepository derivedKpiRepository,
    NomenclatureService nomenclatureService,
    FAResilientHttpClient resilientHttpClient,
    IChartLogRepository chartLogRepository,
    AppConfigSettings appConfigSettings,
    ICompanySettingsRepository companySettingsRepository,
    IFlexiblePerspectiveColumnsRepository flexiblePerspectiveColumnsRepository) : RepositoryResponse
{
    public async Task<List<QuickVizList>> GetQuickVizList()
    {
        var quickWizLists = await queryViewRepository.GetQuicViz(currentUser.CompanyId);
        return quickWizLists;
    }

    public async Task<List<ChartDataMin>> GetChartsForView(long viewId)
    {
        var charts = await queryViewRepository.GetChartsForView(viewId);
        return charts;
    }

    private async Task<ChartDataViewModel> GetDataForChart(ChartDataInput inputData)
    {
        var newChartLog = new ChartUsageModel()
        {
            ChartId = inputData.ChartId,
            QueryStartDate = inputData.DateRangeModel?.StartDate,
            QueryEndDate = inputData.DateRangeModel?.EndDate,
            DateManipulated = inputData.DateRangeModel != null && inputData.DateRangeModel.EndDate.HasValue && (inputData.DateRangeModel.EndDate.Value.Date != DateTime.UtcNow.AddMinutes(330).Date.AddDays(-1)),
        };

        var stopWatch = new Stopwatch();
        stopWatch.Start();

        try
        {
            var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/qd/chart";
            var result = await resilientHttpClient.PostJsonAsync<ChartDataViewModel>(dataUrl, appConfigSettings.reportApiToken, inputData);
            stopWatch.Stop();
            newChartLog.ResponseTime = (int)stopWatch.Elapsed.TotalMilliseconds;
            return result;
        }
        catch (Exception ex)
        {
            stopWatch.Stop();
            newChartLog.IsFailure = true;
            newChartLog.ResponseTime = (int)stopWatch.Elapsed.TotalMilliseconds;
            newChartLog.FailureMessageTrimmed = ex.InnerException.Message[..256];
            throw;
        }
        finally
        {
            await chartLogRepository.AddLog(newChartLog, currentUser);
        }
    }

    public async Task<ChartDataViewModel> GetChartData(ChartDataInput inputData)
    {
        inputData.CompanyId = currentUser.CompanyId;
        inputData.IsNewDashboard = true;
        var result = await GetDataForChart(inputData);
        return result;
    }

    public async Task<ChartDataViewModel> GetPreviewChartData(ChartVizDetailModel inputData)
    {
        inputData.CompanyId = currentUser.CompanyId;
        inputData.IsNewDashboard = true;

        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/qd/chartdata";
        var result = await resilientHttpClient.PostJsonAsync<ChartDataViewModel>(dataUrl, appConfigSettings.reportApiToken, inputData);
        return result;
    }

    private static bool ChartsColumnsValidation(ChartVizDetailModel chartViz)
    {
        var columns = chartViz.Dimension != null ? chartViz.Measure.Concat(chartViz.Dimension) : chartViz.Measure;
        return columns.GroupBy(c => c).Any(c => c.Count() > 1);
    }

    public async Task<RepositoryResponse> CreateChart(ChartVizDetailModel chartViz)
    {
        var isInValid = ChartsColumnsValidation(chartViz);
        if (isInValid)
        {
            return new RepositoryResponse { ExceptionMessage = "Error! Two or many selected colums can not be same.", Message = "Chart Creation Request failed!", IsSuccess = false, };
        }

        var isDimensionMeasureCombinationValid = ChartsDimensionMeasureValidation(chartViz);
        if (!isDimensionMeasureCombinationValid.IsSuccess)
        {
            return new RepositoryResponse { ExceptionMessage = isDimensionMeasureCombinationValid.ExceptionMessage, Message = "Chart Creation Request failed!", IsSuccess = false, };
        }

        var res = await queryViewRepository.SaveChart(chartViz, currentUser.CompanyId);
        if (res.IsSuccess)
        {
            return new RepositoryResponse
            {
                Id = res.Id, ExceptionMessage = "Chart Created Successfully", Message = "Chart Created Successfully", IsSuccess = true,
            };
        }

        return new RepositoryResponse
        {
            Id = res.Id, ExceptionMessage = $"{res.ExceptionMessage}", Message = "Some Error Occurred, Please Go Back and Retry", IsSuccess = false,
        };
    }

    public async Task<AxisDetail> GetPerspectiveColumns(ViewPerspective viewPerspective, bool isTrend = false, DateRangePreset dateRangePreset = DateRangePreset.MTD)
    {
        var cols = await GetPerspectiveColumnsAsPerSettings(viewPerspective);

        if (viewPerspective is ViewPerspective.EmpGeoPerformanceData or
            ViewPerspective.ProductWiseSales or
            ViewPerspective.NoSalesReason or
            ViewPerspective.TrendReportNLT or
            ViewPerspective.TrendReportNLTPosition or
            ViewPerspective.SecondaryDemandVsSales or
            ViewPerspective.ProductDemandVsSales or
            ViewPerspective.DistributorPerformance or ViewPerspective.LiveSalesDataPosition or
            ViewPerspective.DayStart or
            ViewPerspective.PrimaryOrders)
        {
            var derivedKPIs = await derivedKpiRepository.GetDerivedKPIs(viewPerspective, currentUser.CompanyId);
            foreach (var kpi in derivedKPIs)
            {
                if (kpi.IsNomenclature)
                {
                    cols.RemoveAll(c => c.Name == kpi.Measure1);
                }

                if (viewPerspective is ViewPerspective.TrendReportNLT or ViewPerspective.TrendReportNLTPosition)
                {
                    if (kpi.IsActive)
                    {
                        cols.Add(new PerspectiveColumnModel
                        {
                            Attribute = "Master Measure",
                            DisplayName = kpi.Name,
                            Name = kpi.Name,
                            IsMeasure = true,
                            IsDimension = false,
                            PerspectiveMeasure = PerspectiveMeasure.Value
                        });
                    }
                    else
                    {
                        cols.Add(new PerspectiveColumnModel
                        {
                            Attribute = "Sales Measure",
                            DisplayName = kpi.Name,
                            Name = kpi.Name,
                            IsMeasure = true,
                            IsDimension = false,
                            PerspectiveMeasure = PerspectiveMeasure.Value,
                            TimeFrames =
                            [
                                NLTFrames.MTD,
                                NLTFrames.LMTD,
                                NLTFrames.LM,
                                NLTFrames.L3MAvg,
                                NLTFrames.PerL3M
                            ]
                        });
                    }
                }
                else
                {
                    cols.Add(new PerspectiveColumnModel
                    {
                        Attribute = "Sales Measure",
                        DisplayName = kpi.Name,
                        Name = kpi.Name,
                        IsMeasure = true,
                        IsDimension = false,
                        PerspectiveMeasure = PerspectiveMeasure.Value
                    });
                }
            }
        }

        var axis = new AxisDetail { Dimension = cols.Where(s => s.IsDimension).ToList(), Measures = cols.Where(s => s.IsMeasure).ToList() };
        if (viewPerspective is ViewPerspective.TrendReportLT or ViewPerspective.TrendReportNLT)
        {
            axis.Measures = cols.Where(s => s.IsMeasure && s.Attribute != "Master Measure").ToList();
            axis.MasterMeasures = cols.Where(s => s.IsMeasure && s.Attribute == "Master Measure").ToList();
        }

        axis.Perspective = viewPerspective;
        return axis;
    }

    // This method is not specific to QuickViz (common for flexible as well)
    public async Task<List<PerspectiveColumnModel>> GetPerspectiveColumnsAsPerSettings(ViewPerspective viewPerspective, DateRangePreset dateRangePreset = DateRangePreset.MTD)
    {
        var nomenclatureDict = await nomenclatureService.GetCompanyNomenclatureDict();
        var displayNameDict = nomenclatureDict.ToDictionary(p => p.Key, p => p.Value.DisplayName);
        string definition = null;

        if (viewPerspective is ViewPerspective.ProductDemandVsSales or ViewPerspective.DistributorStockSale)
        {
            // Add this to fetch perspective columns json definition
            definition = await flexiblePerspectiveColumnsRepository.GetPerspectiveColumns(viewPerspective);
        }

        var perspectiveColumns = PerspectiveBuilder.GetPerspectiveFor(viewPerspective, currentUser.CompanyId, displayNameDict, definition).Columns;
        if (viewPerspective is ViewPerspective.ProductWiseSales or ViewPerspective.PrimaryOrders or ViewPerspective.NoSalesReason or
            ViewPerspective.TrendReportNLT or ViewPerspective.TrendReportLT or
            ViewPerspective.EmpGeoPerformanceData or ViewPerspective.DayStart or ViewPerspective.SecondaryDemandVsSales or
            ViewPerspective.ProductDemandVsSales or ViewPerspective.DistributorPerformance or ViewPerspective.LiveSalesDataPosition or ViewPerspective.DistributorStockSale or
            ViewPerspective.FlexibleTargetVsAchievement)
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var highestPosLevel = companySettings.GetCompanyHighestPositionLevel;
            var highestGeoLevel = companySettings.CompanyHighestGeography;

            // considering settings  (usesPositionCode && usePositionsForReporting) are used in new dashboard.
            var posLevels = Enum.GetValues(typeof(PositionCodeLevel)).Cast<PositionCodeLevel>();
            var posLevelsToRemove = posLevels.Where(l => l < highestPosLevel); //The highest enum value in this will be 1 + the company Highest
            if (posLevelsToRemove.Any())
            {
                var maxL = int.Parse(string.Join("", posLevelsToRemove.Max().ToString().Where(char.IsDigit)));
                perspectiveColumns.RemoveAll(c =>
                {
                    if (c.Attribute == "Position" && int.TryParse(string.Join("", c.Name.Where(char.IsDigit)), out var positionValue))
                        return positionValue >= maxL;
                    return false;
                });
            }

            perspectiveColumns.RemoveAll(c => c.Attribute == "Field User");
            //Remove Columns from L3M and MOM to make it like their Position versions on front end.
            if (viewPerspective is ViewPerspective.TrendReportNLT or ViewPerspective.TrendReportLT)
            {
                perspectiveColumns.RemoveAll(c => c.Attribute == "Sales Territory");
                perspectiveColumns.RemoveAll(c => c.Name == "Routes");
            }

            if (highestGeoLevel > GeographyLevel.Level4)
            {
                var geoLevels = Enum.GetValues(typeof(GeographyLevel)).Cast<GeographyLevel>();
                var geoLevelsToRemove = geoLevels.Where(l => l > highestGeoLevel);
                if (geoLevelsToRemove.Any())
                {
                    perspectiveColumns
                        .RemoveAll(c => c.Attribute == "Sales Territory" && c.Name.StartsWith("Level", StringComparison.OrdinalIgnoreCase)
                                                                            && string.Compare(c.Name, geoLevelsToRemove.Min().ToString(), StringComparison.Ordinal) >= 0);
                }
            }
            else
            {
                perspectiveColumns.RemoveAll(c => c.Attribute == "Sales Territory" && c.Name.StartsWith("Level", StringComparison.OrdinalIgnoreCase));
            }
        }

        if (viewPerspective == ViewPerspective.MTStockAndSales)
        {
            //var usesMonthWiseOpeningStockMT = await companySettingService.UsesMonthWiseOpeningStockMT(currentUser.CompanyId);
            //if (!usesMonthWiseOpeningStockMT || dateRangePreset != DateRangePreset.MTD)
            //{
            //    perspectiveColumns.RemoveAll(c => c.Name.StartsWith("Opening"));
            //    perspectiveColumns.RemoveAll(c => c.Name.StartsWith("Closing"));
            //    perspectiveColumns.RemoveAll(c => c.Name.StartsWith("Calculated"));
            //}
        }

        if (viewPerspective is not ViewPerspective.EmpGeoPerformanceData and not ViewPerspective.LiveSalesDataPosition and not ViewPerspective.FlexibleTargetVsAchievement) // This parameter is manually added in portal cshtml. Required in dimensions dropdown for all perspectives.
        {
            perspectiveColumns.Insert(0, new PerspectiveColumnModel { IsDimension = true, Name = "Hierarchy", DisplayName = "Hierarchy" });
        }

        if (viewPerspective == ViewPerspective.LiveSalesDataPosition)
        {
            perspectiveColumns.Insert(0, new PerspectiveColumnModel { IsDimension = true, Name = "UserHierarchy", DisplayName = "User Hierarchy" });
        }

        if (viewPerspective == ViewPerspective.MTMerchandiserStockAndSale)
        {
            perspectiveColumns.RemoveAll(c => c.Attribute == "Position"
            || c.Name == "FieldUserName"
            || c.Name == "POReferenceNumber"
            || c.Attribute == "Visit"
            || c.Name == "Time"
            || c.Name == "Week"
            || c.Name == "ReportingManager"
            );
        }

        return perspectiveColumns;
    }

    public async Task<RepositoryResponse> PinUnpinChart(long chartId, bool action) => await queryViewRepository.PinUnpinChart(chartId, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> DeleteChart(long chartId) => await queryViewRepository.DeleteChart(chartId, currentUser.CompanyId);

    public static RepositoryResponse ChartsDimensionMeasureValidation(ChartVizDetailModel chartViz)
    {
        if (chartViz.ChartPerspective == ViewPerspective.LiveSalesDataPosition)
        {
            var dimensions = new List<string>
            {
                "ProductName",
                "ProductERPID",
                "SecondaryCategory",
                "PrimaryCategory",
                "ProductDivision"
            };
            var measures = new List<string> { "TC", "PC", "SC", "LPC" };
            var selectedDimensions = chartViz.Dimension;
            var selectedMeasures = chartViz.Measure;
            if (selectedDimensions.Any(dimensions.Contains)
                && selectedMeasures.Any(measures.Contains))
            {
                return new RepositoryResponse { ExceptionMessage = "Error! Invalid Combination Of Dimension and Measures.", Message = "Invalid Combination of dimension and measures!", IsSuccess = false, };
            }

            return new RepositoryResponse { ExceptionMessage = "Valid Combination Of Dimension and Measures.", Message = "Valid Combination Of Dimension and Measures!", IsSuccess = true, };
        }

        return new RepositoryResponse { ExceptionMessage = "Valid Combination Of Dimension and Measures.", Message = "Valid Combination Of Dimension and Measures!", IsSuccess = true, };
    }
}
