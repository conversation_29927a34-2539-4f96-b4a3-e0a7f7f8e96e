﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Microsoft.Extensions.Logging;
using Libraries.CommonModels;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using Library.StorageWriter.Reader_Writer;
using FADashboard.Core.Models.DTOs;
using Newtonsoft.Json;
using FADashboard.Core.Models.HccbIntegrationLogs;
using FADashboard.Core.Helper;

namespace FADashboard.Core.Services;

public class HCCBIntegrationLogService
{
    private static class GtAppApiEndpoints
    {
        public const string GetStockForDistributors = "api/master/GetStockforDistributors";
        public const string GetDistributors = "api/master/distributorsV2?token=0";
        public const string GetPricingForDistributors = "api/ProductPricing/get";
    }

    private readonly IHCCBIntegrationLogRepository repository;
    private readonly ICurrentUser currentUser;
    private readonly ILogger<HCCBIntegrationLogService> logger;
    private readonly AppConfigSettings appConfigSettings;
    private readonly EmployeeService employeeService;
    private readonly DistributorService distributorService;
    private readonly BeatService beatService;
    private readonly PositionCodeService positionCodeService;
    private readonly GTAppApiService gtAppApiService;
    private readonly IPositionBeatRepository positionBeatRepository;
    private readonly IDistributorBeatRepository distributorBeatRepository;
    private readonly IOutletRepository outletRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IAssetRepository assetRepository;
    private readonly IRouteRepository routeRepository;
    private readonly IBeatRepository beatRepository;
    private readonly IRegionRepository regionRepository;
    private readonly IProductDivRepository productDivRepository;
    private readonly IProductRepository productRepository;

    public HCCBIntegrationLogService(
        IHCCBIntegrationLogRepository repository,
        ICurrentUser currentUser,
        ILogger<HCCBIntegrationLogService> logger,
        AppConfigSettings appConfigSettings,
        EmployeeService employeeService,
        DistributorService distributorService,
        BeatService beatService,
        PositionCodeService positionCodeService,
        IPositionBeatRepository positionBeatRepository,
        IDistributorBeatRepository distributorBeatRepository,
        IOutletRepository outletRepository,
        IEmployeeRepository employeeRepository,
        IAssetRepository assetRepository,
        GTAppApiService gtAppApiService,
        IRouteRepository routeRepository,
        IBeatRepository beatRepository,
        IRegionRepository regionRepository,
        IProductDivRepository productDivRepository,
        IProductRepository productRepository)
    {
        this.repository = repository;
        this.currentUser = currentUser;
        this.logger = logger;
        this.appConfigSettings = appConfigSettings;
        this.employeeService = employeeService;
        this.distributorService = distributorService;
        this.beatService = beatService;
        this.positionCodeService = positionCodeService;
        this.positionBeatRepository = positionBeatRepository;
        this.distributorBeatRepository = distributorBeatRepository;
        this.outletRepository = outletRepository;
        this.employeeRepository = employeeRepository;
        this.assetRepository = assetRepository;
        this.gtAppApiService = gtAppApiService;
        this.routeRepository = routeRepository;
        this.beatRepository = beatRepository;
        this.regionRepository = regionRepository;
        this.productDivRepository = productDivRepository;
        this.productRepository = productRepository;
    }

    /// <summary>
    /// Get HCCB integration details for a specific date
    /// </summary>
    /// <param name="date">Optional date filter</param>
    /// <returns>Integration details response</returns>
    public async Task<List<HCCBIntegrationLog>> GetHCCBIntegrationDetails(DateTime? date = null)
    {
        try
        {
            var searchDate = date ?? DateTime.Today;
            return await repository.GetHCCBIntegrationDetails(currentUser.CompanyId, searchDate);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting HCCB integration details");
            return new List<HCCBIntegrationLog>();
        }
    }

    /// <summary>
    /// Validate HCCB request based on request type
    /// </summary>
    /// <param name="request">HCCB console request containing inputs and request type</param>
    /// <returns>Validation results wrapped in ApiResponse</returns>
    public async Task<List<HCCBValidationResult>> ValidateHCCBRequest(HCCBConsoleRequest request)
    {
        try
        {
            if (request.RequestType != HCCBConsoleRequestType.OUTLET_BEAT_VALIDATION && request.RequestType != HCCBConsoleRequestType.OUTLET_BEAT_DISTRIBUTOR_EMPLOYEE)
            {
                if (request.Inputs == null || !request.Inputs.Any())
                {
                    throw new ArgumentException("Inputs cannot be empty");
                }

                if (request.Inputs.Count > 10)
                {
                    throw new ArgumentException("Maximum 10 inputs allowed");
                }
            }

            switch (request.RequestType)
            {
                case HCCBConsoleRequestType.OUTLET_BEAT_VALIDATION:
                    return await repository.ValidateOutletBeat(currentUser.CompanyId, request.Inputs);
                case HCCBConsoleRequestType.OUTLET_BEAT_DISTRIBUTOR_EMPLOYEE:
                    return await repository.ValidateOutletBeatDistributorEmployee(currentUser.CompanyId, request.Inputs);
                case HCCBConsoleRequestType.ORDER_VALIDATION:
                    var date = request.Inputs?.FirstOrDefault();
                    var validationDate = date != null ? DateTime.Parse(date) : DateTime.Today;
                    return await repository.ValidateOrders(currentUser.CompanyId, validationDate);
                case HCCBConsoleRequestType.ORDER_STATUS_VALIDATION:
                    var startDate = request.Inputs?.FirstOrDefault();
                    var startDateTime = startDate != null ? DateTime.Parse(startDate) : DateTime.Today;
                    return await repository.ValidateOrderStatus(currentUser.CompanyId, startDateTime);
                default:
                    throw new ArgumentException($"Validation type {request.RequestType} is not supported");
            }
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid HCCB validation request");
            throw new Exception(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing HCCB validation request");
            throw new Exception(ex.Message);
        }
    }

    public async Task<FAActiveEntitiesResponse> GetFAActiveEntities()
    {
        try
        {
            var companyId = currentUser.CompanyId;
            var filter = new PaginationFilter { ShowDeactive = false };

            var employeeCount = await employeeService.GetEmployeeCount(companyId, filter);
            var distributorCount = await distributorService.GetDistributorCount(companyId, filter);
            var geographyCount = await beatService.GetBeatCount(companyId, filter);
            var positionCount = await positionCodeService.GetPositionCount(companyId, filter);
            var positionBeatMappingCount = await positionBeatRepository.GetPositionBeatMappingCount(companyId, filter);
            var distributorBeatMappingCount = await distributorBeatRepository.GetDistributorBeatMappingCount(companyId, filter);
            var outletCount = await outletRepository.GetOutletCount(companyId, filter);
            var assetOutletMappingCount = await assetRepository.GetAssetOutletMappingCount(companyId, filter);

            return new FAActiveEntitiesResponse
            {
                EmployeeCount = employeeCount,
                DistributorCount = distributorCount,
                GeographyCount = geographyCount,
                PositionCount = positionCount,
                PositionBeatMappingCount = positionBeatMappingCount,
                DistributorBeatMappingCount = distributorBeatMappingCount,
                OutletCount = outletCount,
                AssetOutletMappingCount = assetOutletMappingCount
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting FA active entities");
            return new FAActiveEntitiesResponse
            {
                EmployeeCount = 0,
                DistributorCount = 0,
                GeographyCount = 0,
                PositionCount = 0,
                PositionBeatMappingCount = 0,
                DistributorBeatMappingCount = 0,
                OutletCount = 0,
                AssetOutletMappingCount = 0
            };
        }
    }

    /// <summary>
    /// Get metadata of files from HCCB storage account for specified file type and date
    /// </summary>
    /// <param name="fileType">Type of file to retrieve metadata for</param>
    /// <param name="date">Optional date filter, defaults to today</param>
    /// <returns>List of file metadata matching the criteria</returns>
    public async Task<HCCBFileMetadataResponse> GetHCCBFileMetadata(HCCBFileType fileType, DateTime? date = null)
    {
        try
        {
            var searchDate = date ?? DateTime.Today;
            var response = new HCCBFileMetadataResponse();

            // Get files from dms-prod container
            var prodFiles = await GetContainerFiles("dms-prod", fileType, searchDate);
            response.Files.AddRange(prodFiles);

            // Get files from dms-processed-files container
            var processedFiles = await GetContainerFiles("dms-processed-files", fileType, searchDate, "dms-prod");
            response.Files.AddRange(processedFiles);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving HCCB file metadata for type {FileType} and date {Date}",
                fileType, date);
            throw;
        }
    }

    public async Task<Stream> GetHCCBFileContent(string fileName, string status)
    {
        try
        {
            var containerName = status == "Processed" ? "dms-processed-files" : "dms-prod";
            var connectionString = appConfigSettings.HCCBStorageConnectionString;
            var reader = new HCCBBlobReader(connectionString, containerName);

            return await reader.ReadDatBlobContentAsync(fileName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving file {FileName} with status {Status}", fileName, status);
            throw;
        }
    }

    private async Task<List<HCCBFileMetadata>> GetContainerFiles(string containerName, HCCBFileType fileType, DateTime date, string subDirectory = null)
    {
        try
        {
            var prefix = GetFilePrefix(fileType);
            if (!string.IsNullOrEmpty(subDirectory))
            {
                prefix = $"{subDirectory}/{prefix}";
            }

            var connectionString = appConfigSettings.HCCBStorageConnectionString;
            var reader = new HCCBBlobReader(connectionString, containerName);
            var files = new List<HCCBFileMetadata>();

            var blobs = await reader.ListBlobsAsync(prefix);
            var startDateUtc = date.Date.AddHours(-5.5); // Adjusting for UTC storage
            var endDateUtc = startDateUtc.AddDays(1);

            foreach (var blob in blobs)
            {
                //  if (IsMatchingDate(blob.Name, date))
                if (blob.Properties.LastModified >= startDateUtc && blob.Properties.LastModified <= endDateUtc)
                {
                    files.Add(new HCCBFileMetadata
                    {
                        Name = blob.Name,
                        LastModified = blob.Properties.LastModified ?? DateTimeOffset.MinValue,
                        Size = blob.Properties.ContentLength ?? 0,
                        Status = containerName == "dms-prod" ? "Not Processed" : "Processed"
                    });
                }
                else
                {
                    logger.LogDebug("Blob {BlobName} does not match date {Date}", blob.Name, date.ToString("yyyy-MM-dd"));
                }
            }

            return files;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error listing files from container {ContainerName} with prefix {Prefix}",
                containerName);
            throw;
        }
    }

    private string GetFilePrefix(HCCBFileType fileType)
    {
        return fileType switch
        {
            HCCBFileType.PromotionMaster => "PromotionMaster_",
            HCCBFileType.StockMaster => "StockMaster_",
            HCCBFileType.PriceList => "PricingMaster_",
            HCCBFileType.SalesOrder => "SalesOrder",
            _ => throw new ArgumentException($"Invalid file type: {fileType}")
        };
    }

    public async Task<List<HCCBIntegrationAPILog>> GetIntegrationLogDetails(long integrationLogId)
    {
        try
        {
            return await repository.GetIntegrationLogDetails(currentUser.CompanyId, integrationLogId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting integration log details for log {LogId}", integrationLogId);
            throw;
        }
    }

    public async Task<long> GetEmployeeUserId(string userId)
    {
        if (string.IsNullOrWhiteSpace(userId))
            return 0;

        Employee? employee = null;

        if (!userId.StartsWith('0') && long.TryParse(userId, out var userIdLong))
        {
            employee = await employeeRepository.GetActiveEmployeeById(userIdLong, currentUser.CompanyId);
        }

        if (employee == null || employee.Id == 0)
        {
            employee = await employeeRepository.GetActiveEmployeeByErpId(userId, currentUser.CompanyId);
        }

        if (employee == null || employee.Id == 0)
        {
            return 0;
        }

        return employee.Id;
    }


    public async Task<object> GetTodaysJourneyData(string userId, OutputEntityType entityType, bool getAll)
    {
        var empId = await GetEmployeeUserId(userId);
        if (empId == 0)
        {
            return $"User with ID {userId} doesn't exist";
        }
        var token = await employeeRepository.GetLatestEmployeeToken(empId, currentUser.CompanyId);
        if (token == null)
            return $"User with UserId: {userId} has not logged in to the application yet";

        var (url, needsToken) = EntityEndpointConfig.GetEndpoint(entityType);
        var endPoint = needsToken ? $"{url}?token=0" : url;

        var rawResponse = await gtAppApiService.GetApiResponse(endPoint, token.Name);

        return await ProcessEntityDataAsync(entityType, rawResponse, token, getAll);
    }
    private async Task<object> ProcessEntityDataAsync(OutputEntityType entityType, string jsonResponse, EntityMin token, bool getAll)
    {
        switch (entityType)
        {
            case OutputEntityType.Distributors:
                var distributors = JsonConvert.DeserializeObject<List<DistributorReturn>>(jsonResponse);
                if (distributors == null || distributors.Count == 0)
                    return "No distributors found for the user.";
                return await HandleDistributors(distributors);

            case OutputEntityType.Routes:
                var routes = JsonConvert.DeserializeObject<List<RoutePlanDto>>(jsonResponse);
                if (routes == null || routes.Count == 0)
                    return "No routes found for the user.";
                return await HandleRoutes(routes);

            case OutputEntityType.Outlets:
                var outlets = JsonConvert.DeserializeObject<List<LocationWithBeatOrRoutes>>(jsonResponse);
                if (outlets == null || outlets.Count == 0)
                    return "No outlets found for the user.";
                return await HandleOutlets(outlets);

            case OutputEntityType.Products:
                var products = JsonConvert.DeserializeObject<List<ProductForNewAppDTO>>(jsonResponse);
                if (products == null || products.Count == 0)
                    return "No products found for the user.";
                return await HandleProducts(products, token, getAll);

            default:
                throw new Exception("Unknown entity type");
        }
    }
    private async Task<List<RoutePlanDto>> HandleRoutes(List<RoutePlanDto> routes)
    {
        if (routes == null || routes.Count == 0)
            return routes;

        routes = routes.Where(r => r.RouteId != null).ToList();

        routes = routes
            .GroupBy(r => r.RouteId)
            .Select(g => g.First())
            .ToList();

        var routeIds = routes
            .Select(r => r.RouteId!.Value)
            .ToList();

        var routeLocationDict = await routeRepository.GetDistinctLocationCountPerRoute(currentUser.CompanyId, routeIds);

        foreach (var route in routes)
        {
            route.OutletCount = routeLocationDict.TryGetValue(route.RouteId!.Value, out var count) ? count : 0;
            route.Day = route.Date.ToString("dddd");
        }

        return routes;
    }

    private async Task<List<LocationWithBeatOrRoutes>> HandleOutlets(
List<LocationWithBeatOrRoutes> outlets)
    {
        if (outlets == null || outlets.Count == 0)
        {
            return outlets;
        }

        try
        {
            await ProcessBeatsAsync(outlets);

            await ProcessRoutesAsync(outlets);

            return outlets;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing outlets for company {CompanyId}. Processed {Count} outlets.",
                currentUser.CompanyId, outlets?.Count ?? 0);
            throw;
        }
    }

    private async Task ProcessBeatsAsync(List<LocationWithBeatOrRoutes> outlets)
    {
        var beatIds = outlets
           .Where(o => o.BeatId.HasValue)
           .Select(o => o.BeatId.Value)
           .Distinct()
           .ToList();

        if (beatIds.Count == 0)
        {
            return;
        }

        var beatIdToErpIdMap = await beatRepository.GetBeatErpIdMapAsync(
            currentUser.CompanyId,
            beatIds);

        foreach (var outlet in outlets)
        {
            if (outlet.BeatId.HasValue &&
                beatIdToErpIdMap.TryGetValue(outlet.BeatId.Value, out var beat))
            {
                outlet.BeatErpid = beat.ErpId ?? string.Empty;
                outlet.RegionName = beat.RegionName ?? string.Empty;
            }
            else
            {
                outlet.BeatErpid = string.Empty;
                outlet.RegionName = string.Empty;
            }
        }
    }

    private async Task ProcessRoutesAsync(
List<LocationWithBeatOrRoutes> outlets)
    {
        if (outlets == null || outlets.Count == 0)
            return;

        var beatIds = outlets
            .Where(o => o.BeatId != null)
            .Select(o => o.BeatId ?? 0)
            .Distinct()
            .ToList();

        var routes = await routeRepository.GetRoutesOfBeats(beatIds, currentUser.CompanyId);
        var routeToNameMap = routes.ToDictionary(r => r.Id, r => r.Name ?? string.Empty);

        foreach (var outlet in outlets)
        {
            if (outlet.RouteIds == null || outlet.RouteIds.Count == 0)
            {
                outlet.RouteNames = string.Empty;
                continue;
            }

            var routeNames = new List<string>();
            foreach (var routeId in outlet.RouteIds)
            {
                if (routeToNameMap.TryGetValue(routeId, out var name) && !string.IsNullOrWhiteSpace(name))
                {
                    routeNames.Add(name);
                }
            }

            outlet.RouteNames = routeNames.Count > 0
                ? string.Join(", ", routeNames.Distinct())
                : string.Empty;
        }
    }
    private async Task<List<DistributorReturn>> HandleDistributors(List<DistributorReturn> distributors)
    {
        if (distributors == null || distributors.Count == 0)
            return distributors;

        var regionIds = distributors
            .Where(d => d.RegionId.HasValue)
            .Select(d => d.RegionId.Value)
            .Distinct()
            .ToList();

        var regions = await regionRepository.GetAllActiveRegions(currentUser.CompanyId, regionIds);
        var regionIdToNameDict = regions.ToDictionary(r => r.Id, r => r.Name ?? string.Empty);

        foreach (var distributor in distributors)
        {
            if (distributor.RegionId.HasValue && regionIdToNameDict.TryGetValue(distributor.RegionId.Value, out var regionName))
            {
                distributor.RegionName = regionName;
            }
            else
            {
                distributor.RegionName = string.Empty;
            }
        }

        return distributors;
    }

    private async Task<object> HandleProducts(List<ProductForNewAppDTO> products, EntityMin token, bool getAll = false)
    {
        var distributors = await GetDistributorsForCurrentUser(token.Name);
        var distributorIds = distributors.Select(d => d.Id).ToList();
        // Return error message if getAll is false and no distributors are found
        if (!getAll && distributorIds.Count == 0)
        {
            return "No distributors found for the current user. No products applicable";
        }
        var productIds = products.Select(p => p.Id).ToHashSet();

        // Fetch stock and pricing data in parallel
        var stockTask = GetStockInformationByDistributor(token.Name, distributorIds, productIds);
        var pricingTask = GetPricingInformation(token.Name, distributorIds);
        await Task.WhenAll(stockTask, pricingTask);

        return await EnrichProducts(products, stockTask.Result, pricingTask.Result, distributors, getAll);
    }

    private async Task<List<DistributorDTO>> GetDistributorsForCurrentUser(string token)
    {
        var response = await gtAppApiService.GetApiResponse(GtAppApiEndpoints.GetDistributors, token);
        return JsonConvert.DeserializeObject<List<DistributorDTO>>(response);
    }

    private async Task<Dictionary<long, Dictionary<long, decimal>>> GetPricingInformation(string token, List<long> distributors)
    {
        var response = await gtAppApiService.PostApiResponse(GtAppApiEndpoints.GetPricingForDistributors, token, JsonConvert.SerializeObject(distributors));
        var pricingData = JsonConvert.DeserializeObject<List<ProductPricingReturnModelV2>>(response) ?? new List<ProductPricingReturnModelV2>();

        return pricingData.ToDictionary(
            d => d.DistributorId,
            d => d.PricingDetails.Where(p => p.PTR > 0).ToDictionary(p => p.ProductId, p => p.PTR));
    }

    private async Task<Dictionary<long, Dictionary<long, double>>> GetStockInformationByDistributor(
        string token, List<long> distributorIds, HashSet<long> productIds)
    {
        var response = await gtAppApiService.PostApiResponse(GtAppApiEndpoints.GetStockForDistributors,
            token, JsonConvert.SerializeObject(distributorIds));
        var stockData = JsonConvert.DeserializeObject<List<InventoryStockDTO>>(response);

        var stockByDistributor = stockData.Where(item => productIds.Contains(item.ProductId) && item.Qty > 0).GroupBy(item => item.DistributorId);
        return stockByDistributor.ToDictionary(
            distributor => distributor.Key,
            distributor => distributor.ToDictionary(item => item.ProductId, item => Math.Max(0, (double)item.Qty))
        );
    }

    private async Task<List<ProductForNewAppDTO>> EnrichProducts(
        List<ProductForNewAppDTO> products,
        Dictionary<long, Dictionary<long, double>> stockByDistributor,
        Dictionary<long, Dictionary<long, decimal>> pricingByDistributor,
        List<DistributorDTO> distributors,
        bool getAll = false)
    {
        var divIds = products.Select(p => p.ProductDivisionId.Value).Distinct().ToList();
        var catIds = products.Select(p => p.SecondaryCategoryId.Value).Distinct().ToList();

        var divDict = await productDivRepository.GetProdDivDict(currentUser.CompanyId, divIds);
        var catDict = (await productRepository.GetSecondaryCategories(currentUser.CompanyId, catIds))
            .ToDictionary(d => d.Id, d => d.Name);
        var distLookup = distributors.ToDictionary(d => d.Id, d => d);

        var result = new List<ProductForNewAppDTO>();

        foreach (var product in products)
        {
            divDict.TryGetValue(product.ProductDivisionId.Value, out var divName);
            catDict.TryGetValue(product.SecondaryCategoryId.Value, out var catName);

            foreach (var distId in distLookup.Keys)
            {
                // skip distributor if not getAll and distributor doesn't exist
                if (!distLookup.TryGetValue(distId, out var distributor) || distributor == null)
                {
                    if (!getAll)
                        continue;
                }

                double stock = 0;
                decimal price = 0;

                var hasStock = stockByDistributor.TryGetValue(distId, out var stockDict) && stockDict.TryGetValue(product.Id, out stock);
                var hasPricing = pricingByDistributor.TryGetValue(distId, out var pricing) && pricing.TryGetValue(product.Id, out price);

                // Skip if not getAll and either stock or pricing is missing
                if (!getAll && (!hasStock || !hasPricing))
                    continue;

                result.Add(new ProductForNewAppDTO
                {
                    Id = product.Id,
                    Name = product.Name,
                    SecondaryCategoryId = product.SecondaryCategoryId,
                    ProductDivisionId = product.ProductDivisionId,
                    ProductDivisionName = divName ?? "",
                    SecondaryCategoryName = catName ?? "",
                    PricingValue = price,
                    DistributorId = distId,
                    DistributorName = distributor?.Name,
                    StockValue = stock
                });
            }
        }

        return result;
    }
}
