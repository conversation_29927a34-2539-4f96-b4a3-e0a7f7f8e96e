﻿using System.Security;
using FADashboard.Core.Models.PowerBI;
using Microsoft.Identity.Client;

namespace FADashboard.Core.Helper;

public class EntraService(AzureAd azureAd)
{
    public async Task<string> GetAccessTokenAsync()
    {
        AuthenticationResult authenticationResult = null;
        if (azureAd.AuthenticationMode.Equals("masteruser", StringComparison.OrdinalIgnoreCase))
        {
            // Create a public client to authorize the app with the AAD app
            var clientApp = PublicClientApplicationBuilder.Create(azureAd.ClientId).WithAuthority(azureAd.AuthorityUri).Build();
            var userAccounts = await clientApp.GetAccountsAsync().ConfigureAwait(false);
            try
            {
                // Retrieve Access token from cache if available
                authenticationResult = await clientApp.AcquireTokenSilent(azureAd.Scope, userAccounts.FirstOrDefault()).ExecuteAsync().ConfigureAwait(false);
            }
            catch (MsalUiRequiredException)
            {
                var password = new SecureString();
                foreach (var key in azureAd.PbiPassword)
                {
                    password.AppendChar(key);
                }

                authenticationResult = await clientApp.AcquireTokenByUsernamePassword(azureAd.Scope, azureAd.PbiUsername, password).ExecuteAsync().ConfigureAwait(false);
            }
        }

        // Service Principal auth is the recommended by Microsoft to achieve App Owns Data Power BI embedding
        else if (azureAd.AuthenticationMode.Equals("serviceprincipal", StringComparison.OrdinalIgnoreCase))
        {
            // For app only authentication, we need the specific tenant id in the authority url
            var tenantSpecificUrl = azureAd.AuthorityUri.Replace("organizations", azureAd.TenantId);

            // Create a confidential client to authorize the app with the AAD app
            var clientApp = ConfidentialClientApplicationBuilder
                .Create(azureAd.ClientId)
                .WithClientSecret(azureAd.ClientSecret)
                .WithAuthority(tenantSpecificUrl)
                .Build();
            // Make a client call if Access token is not available in cache
            authenticationResult = await clientApp.AcquireTokenForClient(azureAd.Scope).ExecuteAsync().ConfigureAwait(false);
        }

        return authenticationResult.AccessToken;
    }
}
