﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IAttendanceRepository
{
    Task<FieldUserDailyAttendanceSummaryDataModel> GetUserDailyAttendanceDataForFieldUser(long companyId, long userId, DateTime date);

    Task<ManagerDailyAttendanceModel> GetUserDailyAttendanceDataForManager(long companyId, List<long> ids, DateTime date);

    Task<List<AttendanceAudit>> GetDetails(long companyId, DateTime Startdate, DateTime Enddate, long beatid);
}
