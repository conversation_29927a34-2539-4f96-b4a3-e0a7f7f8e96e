﻿using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;
using Microsoft.AspNetCore.Http;

namespace FADashboard.Core.Services;

public class ProductService(
    IPrimaryCategoryRepository primaryCategoryRepository,
    ISecondaryCategoryRepository secondaryCategoryRepository,
    IProductRepository productRepository,
    IProductDivRepository productDivRepository,
    ICurrentUser currentUser) : RepositoryResponse
{
    // Checks if ProductGroup Name or ErpId is unique (active/inactive), similar to IsRuleNameValid in UserAppControlService
    private async Task<RepositoryResponse> IsNameErpIdValid(ProductGroupInput group)
    {
        var groups = await productRepository.GetProductGroups(currentUser.CompanyId, true);
        if (group.Id != 0)
            groups = groups.Where(g => g.Id != group.Id).ToList();

        // Normalize and build lookup sets for names and erpids
        var activeNames = new HashSet<string>(groups.Where(g => !g.IsDeleted && !string.IsNullOrWhiteSpace(g.Name)).Select(g => g.Name.Trim().ToLower()));
        var inactiveNames = new HashSet<string>(groups.Where(g => g.IsDeleted && !string.IsNullOrWhiteSpace(g.Name)).Select(g => g.Name.Trim().ToLower()));
        var activeErpIds = new HashSet<string>(groups.Where(g => !g.IsDeleted && !string.IsNullOrWhiteSpace(g.ErpId)).Select(g => g.ErpId.Trim().ToLower()));
        var inactiveErpIds = new HashSet<string>(groups.Where(g => g.IsDeleted && !string.IsNullOrWhiteSpace(g.ErpId)).Select(g => g.ErpId.Trim().ToLower()));

        string nameToCheck = group.Name?.Trim().ToLower();
        string erpIdToCheck = group.ErpId?.Trim().ToLower();

        if (!string.IsNullOrEmpty(nameToCheck))
        {
            if (activeNames.Contains(nameToCheck))
            {
                return new RepositoryResponse
                {
                    Id = group.Id,
                    ExceptionMessage = "Product Group Name is not unique. An active group with the same name already exists.",
                    Message = "Product Group Creation/Updation Failed!",
                    IsSuccess = false
                };
            }
            if (inactiveNames.Contains(nameToCheck))
            {
                return new RepositoryResponse
                {
                    Id = group.Id,
                    Message = "Product Group Name is valid (duplicate of an inactive group).",
                    IsSuccess = true
                };
            }
        }
        if (!string.IsNullOrEmpty(erpIdToCheck))
        {
            if (activeErpIds.Contains(erpIdToCheck))
            {
                return new RepositoryResponse
                {
                    Id = group.Id,
                    ExceptionMessage = "Product Group ERP ID is not unique. An active group with the same ERP ID already exists.",
                    Message = "Product Group Creation/Updation Failed!",
                    IsSuccess = false
                };
            }
            if (inactiveErpIds.Contains(erpIdToCheck))
            {
                return new RepositoryResponse
                {
                    Id = group.Id,
                    Message = "Product Group ERP ID is valid (duplicate of an inactive group).",
                    IsSuccess = true
                };
            }
        }
        return new RepositoryResponse
        {
            Id = group.Id,
            Message = "Product Group Name and ERP ID are unique.",
            IsSuccess = true
        };
    }


    private async Task<RepositoryResponse> IsValidProduct(ProductInput product)
    {
        var products = await GetProducts(true);
        if (product.Id != 0)
        {
            products = products.Where(p => p.Id != product.Id).ToList();
        }

        var productErpList = products.Where(p => !string.IsNullOrEmpty(p.ErpId)).Select(p => p.ErpId.NormalizeCaps()).ToList();

        if (productErpList.Contains(product.ErpId.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = product.Id, ExceptionMessage = $"Product ERPId : {product.ErpId} is not unique", Message = "Product Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (products.Any(p => p.SecondaryCategoryId == product.SecondaryCategoryId && p.Name.NormalizeCaps() == product.Name.NormalizeCaps() && p.VariantName.NormalizeCaps() == product.VariantName.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = product.Id, ExceptionMessage = "Product with Variant Name Already Exists for some other Product!", Message = "Product Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = product.Id, Message = "Product Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateProduct(long productId, bool action) => await productRepository.ActivateDeactivateProduct(productId, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> CreateUpdateProduct(ProductInput product)
    {
        var checkValid = await IsValidProduct(product);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }
        if (product.Id == 0)
        {
            return await productRepository.CreateProduct(product, currentUser.CompanyId);
        }

        return await productRepository.UpdateProduct(product, currentUser.CompanyId);
    }

    public async Task<List<EntityMinWithStatus>> GetAllPCForComapny(bool includeDeactivate)
    {
        var pCMins = await primaryCategoryRepository.GetPrimaryCategoriesMin(currentUser.CompanyId, includeDeactivate);
        return pCMins;
    }

    public async Task<List<EntityMinWithStatus>> GetAllSCForComapny(bool includeDeactivate)
    {
        var sCMins = await secondaryCategoryRepository.GetSecCategoriesMin(currentUser.CompanyId, includeDeactivate);
        return sCMins;
    }

    public async Task<List<EntityMin>> GetAllSCForPC(List<long> pcIds)
    {
        var sCMins = await secondaryCategoryRepository.GetSecCategoriesMin(currentUser.CompanyId, pcIds);
        return sCMins;
    }

    public async Task<List<EntityMin>> GetMins(ProductHierarchyEnum productHierarchyEnum, List<long> ids)
    {
        var mins = new List<EntityMin>();
        switch (productHierarchyEnum)
        {
            case ProductHierarchyEnum.PrimaryCategory:
                mins = await productRepository.GetPrimaryCategories(currentUser.CompanyId, ids);
                break;

            case ProductHierarchyEnum.SecondaryCategory:
                mins = await productRepository.GetSecondaryCategories(currentUser.CompanyId, ids);
                break;

            case ProductHierarchyEnum.Product:
                mins = await productRepository.GetProductMin(currentUser.CompanyId, ids);
                break;
            case ProductHierarchyEnum.FocussedProduct:
                break;
            case ProductHierarchyEnum.ProductMustSell:
                break;
            case ProductHierarchyEnum.Assorted:
                break;
            case ProductHierarchyEnum.All:
                break;
            case ProductHierarchyEnum.ProductDivision:
                break;
        }

        return mins;
    }

    public async Task<ProductInput> GetProductById(long productId)
    {
        var product = await productRepository.GetProductById(productId, currentUser.CompanyId);
        return product;
    }

    public async Task<List<ProductDisplayCategoryList>> GetProductDisplayCategories(bool includeDeactivate)
    {
        var prodDisplayCategories = await productRepository.GetProductDisplayCategories(currentUser.CompanyId, includeDeactivate);
        return prodDisplayCategories;
    }

    public async Task<List<EntityMinWithErp>> GetProductDivsionMin() => await productDivRepository.GetProductDivisionList(currentUser.CompanyId);

    public async Task<List<EntityMin>> GetProductMin(List<long> pcIds, List<long> scIds)
    {
        var mins = new List<EntityMin>();
        if ((pcIds == null && scIds == null) || (pcIds.Count == 0 && scIds.Count == 0))
        {
            mins = await productRepository.GetProductMin(currentUser.CompanyId);
        }
        else if (scIds is { Count: > 0 })
        {
            mins = await productRepository.GetProductForSCMin(currentUser.CompanyId, scIds);
        }
        else if (pcIds is { Count: > 0 })
        {
            mins = await productRepository.GetProductForPCMin(currentUser.CompanyId, scIds);
        }

        return mins;
    }

    public async Task<List<ProductList>> GetProducts(bool includeDeactivate)
    {
        var users = await productRepository.GetProducts(currentUser.CompanyId, includeDeactivate);
        return users;
    }

    public async Task<PagedResponse<List<ProductList>>> GetProductsList(PaginationFilter validFilter)
    {
        var products = await productRepository.GetProductsList(currentUser.CompanyId, validFilter: validFilter);
        var totalRecords = products.Total;
        var pagedReponse = PaginationHelper.CreatePagedReponse(products.ProductRecords, validFilter, totalRecords);
        return pagedReponse;
    }

    public async Task<ApiResponse> BulkUploadProductImages(string conainerName, IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            return ApiResponse.GetFailure("Zip File is Either Empty or not uploaded.");
        }

        var response = await productRepository.BulkUploadProductImages(currentUser.CompanyId, file, conainerName);
        response.CalculateResponse();
        return response;
    }

    public async Task<ApiResponse> BulkUploadProductThumbnails(string conainerName, IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            return ApiResponse.GetFailure("Zip File is Either Empty or not uploaded.");
        }

        var response = await productRepository.BulkUploadProductThumbnails(currentUser.CompanyId, file, conainerName);
        response.CalculateResponse();
        return response;
    }

    public async Task<ProductBatchNumberResponse> GetProductBatchNumbers(List<long> productIds)
    {
        var batchNumbers = await productRepository.GetProductBatchNumbers(currentUser.CompanyId, productIds);
        return batchNumbers;
    }
    public async Task<List<ProductBatchNumberDto>> GetProductsBatchNumber(List<long> productIds)
    {
        var batchNumbers = await productRepository.GetProductsBatchNumber(currentUser.CompanyId, productIds);
        return batchNumbers;
    }

    public async Task<List<ProductGroupList>> GetProductGroups(bool includeDeactivate)
    {
        var rules = await productRepository.GetProductGroups(currentUser.CompanyId, includeDeactivate);
        return rules;
    }

    public async Task<RepositoryResponse> CreateUpdateProductGroup(ProductGroupInput pg)
    {
        var checkValid = await IsNameErpIdValid(pg);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }
        if (pg.Id == 0)
        {
            return await productRepository.CreateProductGroup(pg, currentUser.CompanyId);
        }
        return await productRepository.UpdateProductGroup(pg, currentUser.CompanyId);
    }

    public async Task<ProductGroupInput> GetProductGroupById(long pgId)
    {
        var productGroup = await productRepository.GetProductGroupById(pgId, currentUser.CompanyId);
        return productGroup;
    }

    public async Task<RepositoryResponse> ActivateDeactivateProductGroup(long pgId, bool action) => await productRepository.ActivateDeactivateProductGroup(pgId, currentUser.CompanyId, action);

}
