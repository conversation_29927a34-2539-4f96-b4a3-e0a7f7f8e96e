﻿using System.Text;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Models;
using Libraries.CommonModels;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class OrderDetailsService(
    IOrderDetailsRepository orderDetailsRepository,
    AppConfigSettings appConfigSettings,
    IOutletRepository outletRepository,
    IExternalAssetRepository externalAssetRepository,
    FAResilientHttpClient resilientHttpClient)
{

    public async Task<DetailedVisit> GetDetailedVisitForOutlet(string encryptedString)
    {
        var decryptedString = Decrypt(encryptedString);
        var attendanceGuid = new Guid();
        try
        {
            attendanceGuid = new Guid(decryptedString);
        }
        catch (Exception ex)
        {
            throw;
        }

        var dataUrl = $" {appConfigSettings.reportApiBaseUrl}api/visit/detailedVisitForOutlet?attendanceGuid={attendanceGuid}";

        var data = await resilientHttpClient.GetJsonAsync<List<DetailedVisit>>(dataUrl, appConfigSettings.reportApiToken);
        var detailedVisitData = data.FirstOrDefault();
        return detailedVisitData;
    }

    public async Task<RepositoryResponse> MarkOrderAsVerified(Guid attendanceGuid, bool isVerified)
    {
        var res = await orderDetailsRepository.MarkOrderAsVerified(attendanceGuid, isVerified);
        return res;
    }

    public async Task<CustomPage> GetExternalsAssetDetails(string encryptedParam1, string encryptedParam2)
    {
        var companyId = long.Parse(Decrypt(encryptedParam1));
        var outletErpId = Decrypt(encryptedParam2);
        var outletData = await outletRepository.GetAllLocationInfoAsync(companyId, outletErpId);
        var retailerConnectPromotionConfigurations = (outletData != null) ? await externalAssetRepository.GetRetailerConnectPromotionConfigurations(outletData.CompanyId) : new RetailerConnectPromotionConfigurationModel();
        var externalAssetData = await externalAssetRepository.GetCorrespondingExternalAsset(outletData);
        if (externalAssetData.FACompanyExternalAssetFilter != null)
        {
            return new CustomPage
            {
                CompanyExternalAsset = externalAssetData,
                CompanyId = outletData?.CompanyId,
                IsHeaderVisible = retailerConnectPromotionConfigurations.IsHeaderVisible,
                IsFooterVisible = retailerConnectPromotionConfigurations.IsFooterVisible,
                FooterText = retailerConnectPromotionConfigurations.FooterText,
                HeaderText = retailerConnectPromotionConfigurations.HeaderText,
                ShouldDisplayCustomPage = true
            };
        }
        else if (retailerConnectPromotionConfigurations != null && (retailerConnectPromotionConfigurations.IsHeaderVisible || retailerConnectPromotionConfigurations.IsFooterVisible))
        {
            return new CustomPage
            {
                CompanyExternalAsset = new FACompanyExternalAssetList(),
                CompanyId = outletData?.CompanyId,
                IsHeaderVisible = retailerConnectPromotionConfigurations.IsHeaderVisible,
                IsFooterVisible = retailerConnectPromotionConfigurations.IsFooterVisible,
                FooterText = retailerConnectPromotionConfigurations.FooterText,
                HeaderText = retailerConnectPromotionConfigurations.HeaderText,
                ShouldDisplayCustomPage = true
            };
        }
        return new CustomPage
        {
            CompanyExternalAsset = new FACompanyExternalAssetList(),
            CompanyId = outletData?.CompanyId,
            IsHeaderVisible = false,
            IsFooterVisible = false,
            ShouldDisplayCustomPage = false
        };
    }

    private static string Decrypt(string encryptedValue)
    {
        var encryptedId = encryptedValue;
        var decodedBytes = Convert.FromBase64String(encryptedId);
        var decodedData = Encoding.UTF8.GetString(decodedBytes);
        return decodedData;
    }
}
