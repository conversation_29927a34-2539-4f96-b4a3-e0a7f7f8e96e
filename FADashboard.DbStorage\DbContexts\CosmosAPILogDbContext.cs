﻿using FADashboard.DbStorage.MasterRepositories.Models;
using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;

namespace FADashboard.DbStorage.DbContexts;

public class CosmosAPILogDbContext(DbContextOptions<CosmosAPILogDbContext> options, AppConfigSettings appConfigSettings) : DbContext(options)
{
    public DbSet<ApiLogRequests> ApiLogRequests { get; set; }


    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) => base.OnConfiguring(optionsBuilder);

    protected override void OnModelCreating(ModelBuilder modelBuilder) => modelBuilder.Entity<ApiLogRequests>()
        .HasNoDiscriminator()
        .ToContainer(appConfigSettings.CosmosApiLogsContainerName);
}
