﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;


namespace FADashboard.DbStorage.MasterRepositories.Models;

public class FAISExecutionLog : IAuditedEntity
{

    public long Id { get; set; }
    [ForeignKey("Company")] public long CompanyId { get; set; }

    public long WorkflowId { get; set; }
    public FAISProcessorStatus? Status { get; set; }
    public string? Exception { get; set; }
    public long? InputDataCount { get; set; }
    public long? SyncedDataCount { get; set; }
    public long? FailedDataCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }

}

