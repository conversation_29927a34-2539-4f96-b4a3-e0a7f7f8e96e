﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>
	<ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.25.0" />
    <PackageReference Include="EPPlus" Version="8.1.0" />
	  <PackageReference Include="Humanizer" Version="2.14.1" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.76.0" />
    <PackageReference Include="Microsoft.PowerBI.Api" Version="4.22.0" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\FA_Libraries\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Libraries.Cryptography\Libraries.Cryptography.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Libraries.PerspectiveColumns\Libraries.PerspectiveColumns.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.Infrastructure\Library.Infrastructure.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.NumberSystem\Library.NumberSystem.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.ResponseHelpers\Library.ResponseHelpers.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.StringHelpers\Library.StringHelpers.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.StorageWriter\Library.StorageWriter.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Using Include="Library.ResponseHelpers" />
	</ItemGroup>
	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Models\Enums\" />
	</ItemGroup>
</Project>
