﻿using Library.StorageWriter;

namespace FADashboard.Core.Helper.BlobHelper;
public class FlexibleBlobReader(string masterStorageConnectionString) : B<PERSON>bReader(masterStorageConnectionString,
    "flexibleperspectivecolumns")
{
    public async Task<string> GetPerspectiveColumns(string filename)
    {
        try
        {
            if (await IsExists(filename))
            {
                return await ReadBlobContentAsync(filename);
            }
            else
                return null;
        }
        catch (Exception)
        {
            return null;
        }
    }
}
