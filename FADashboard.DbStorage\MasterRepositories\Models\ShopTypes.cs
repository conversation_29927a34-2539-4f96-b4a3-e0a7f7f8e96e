﻿using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ShopTypes")]
public class ShopTypes : IAuditedEntity, ICreatedEntity
{
    public virtual Channels Channel { get; set; }

    [Audited] public long ChannelId { get; set; }

    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }

    public bool IsValid { get; set; }

    [Audited] public DateTime LastUpdatedAt { get; set; }

    [Audited] public string ShopTypeName { get; set; }

    public bool SkipOTPVerification { get; set; }
    public bool AllowInOutletFlow { get; set; }
}
