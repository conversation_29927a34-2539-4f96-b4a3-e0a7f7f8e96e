﻿using System;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountNoteDto
    {
        public long Id { get; set; }
        public long AccountId { get; set; }
        public long CompanyId { get; set; }
        public string Description { get; set; }
        public List<string> Attachment { get; set; }
        public bool IsDeleted { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class LMSAccountNoteCreateInput
    {
        [Required]
        public long AccountId { get; set; }

        [Required]
        [StringLength(1000, MinimumLength = 1)]
        public string Description { get; set; }

        public List<string> Attachment { get; set; }
    }

    public class LMSAccountNoteUpdateInput
    {
        [Required]
        [StringLength(1000, MinimumLength = 1)]
        public string Description { get; set; }

        public List<string> Attachment { get; set; }
    }
}
