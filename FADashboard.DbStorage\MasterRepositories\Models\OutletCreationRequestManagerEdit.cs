﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("OutletCreationRequestManagerEdits")]
public class OutletCreationRequestManagerEdit
{
    [StringLength(12)] public string Aadhar { get; set; }

    public string AccountHoldersName { get; set; }
    public virtual OutletCreationRequest AdditionRequest { get; set; }
    public long AdditionRequestId { get; set; }
    public string Address { get; set; }

    [StringLength(100)] public string AlternateImageId { set; get; }

    [StringLength(18)] public string BankAccountNumber { get; set; }

    [StringLength(50)] public string City { get; set; }

    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    [StringLength(2048)] public string DistributorIdsJsonString { get; set; }

    public long? EditedBy { set; get; }
    public PortalUserRole? EditedByUserRole { set; get; }

    [StringLength(500)] public string Email { get; set; }

    [StringLength(20)] public string GSTN { get; set; }

    public bool GSTRegistered { get; set; }

    [StringLength(42)] public string Guid { get; set; }

    public long Id { get; set; }

    [StringLength(11)] public string IFSCCode { get; set; }

    public Guid? ImageId { set; get; }

    public bool IsAdminEdited { get; set; }

    public bool IsKYC { get; set; }

    [StringLength(50)] public string LandlineNumber { get; set; }

    [StringLength(1000)] public string Landmark { get; set; }

    [StringLength(100)] public string MarketName { set; get; }

    [StringLength(500)] public string ModeOfDataCollection { get; set; }

    public OutletChannel? OutletChannel { get; set; }

    [StringLength(50)] public string OwnersName { get; set; }

    [StringLength(50)] public string OwnersNo { get; set; }

    [StringLength(10)] public string PAN { get; set; }

    [StringLength(100)] public string PhotoProofId { set; get; }

    [StringLength(6)] public string PinCode { get; set; }

    [StringLength(2048)] public string RouteIds { get; set; }

    public OutletSegmentation? Segmentation { set; get; }

    [StringLength(500)] public string ShopName { get; set; }

    [StringLength(200)] public string ShopNumber { get; set; }

    [StringLength(50)] public string ShopType { get; set; }

    public int SizeForAreaForCompany { get; set; }

    public int SizeOfShop { get; set; }

    [StringLength(50)] public string State { get; set; }

    [StringLength(100)] public string TypeOfIdProof { get; set; }
}
