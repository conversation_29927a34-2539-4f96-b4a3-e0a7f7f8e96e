﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class HolidayService(IHolidayRepository holidayRepository, ICurrentUser currentUser) : RepositoryResponse
{
    #region Private

    private async Task<RepositoryResponse> IsValidForCreate(RegionalHolidayCreateInput input)
    {
        if (input.Holidays.Count == 0)
        {
            return new RepositoryResponse { ExceptionMessage = "At least one holiday is required for creation.", Message = "Holidays Creation Failed!", IsSuccess = false, };
        }

        var distinctYears = input.Holidays.Select(h => h.Date.Year).Distinct().Count();
        if (distinctYears != 1 || input.Holidays.Any(h => h.Date.Year != input.Year))
        {
            return new RepositoryResponse { ExceptionMessage = "All holiday dates must have the same year and match the specified year.", Message = "Holidays Creation Failed!", IsSuccess = false, };
        }

        if (input.Holidays.Select(h => h.Date).Distinct().Count() != input.Holidays.Count)
        {
            return new RepositoryResponse { ExceptionMessage = "Holiday dates must be unique.", Message = "Holidays Creation Failed!", IsSuccess = false, };
        }

        var regionalHolidays = await holidayRepository.GetAllHolidays(currentUser.CompanyId, false);

        var regionalHolidaysDictionary = regionalHolidays
            .GroupBy(p => p.RegionId)
            .ToDictionary(
                group => group.Key,
                group => group.SelectMany(h => h.Holidays.Select(d => d.Date)).ToList()
            );

        var inputDates = input.Holidays.Select(h => h.Date).ToList();

        var overlappingRegions = input.RegionIds
            .Where(regionId => regionalHolidaysDictionary.ContainsKey(regionId) && regionalHolidaysDictionary[regionId].Intersect(inputDates).Any())
            .ToList();

        if (overlappingRegions.Count != 0)
        {
            var regionNames = regionalHolidays
                .Where(r => overlappingRegions.Contains(r.RegionId))
                .Select(r => r.RegionName)
                .Distinct();

            return new RepositoryResponse { ExceptionMessage = $"Holidays with conflicting dates already exist for Regions: {string.Join(", ", regionNames)}", Message = "Holiday Creation Failed!", IsSuccess = false, };
        }

        return new RepositoryResponse { Message = "Holidays are valid for creation", IsSuccess = true, };
    }


    private static RepositoryResponse IsValidForUpdate(RegionalHolidayUpdateInput input)
    {
        if (input.Holidays.Count != 0)
        {
            if (input.Holidays.Select(h => h.Date).Distinct().Count() != input.Holidays.Count)
            {
                return new RepositoryResponse { ExceptionMessage = "Holiday dates must be unique.", Message = "Holidays Updation Failed!", IsSuccess = false, };
            }


            var distinctYears = input.Holidays.Select(h => h.Date.Year).Distinct().Count();
            if (distinctYears != 1 || input.Holidays.Any(h => h.Date.Year != input.Year))
            {
                return new RepositoryResponse { ExceptionMessage = "All holiday dates must have the same year and match the specified year.", Message = "Holidays Creation Failed!", IsSuccess = false, };
            }
        }

        return new RepositoryResponse { Message = "Holidays are valid for updation", IsSuccess = true, };
    }

    #endregion Private

    public async Task<RepositoryResponse> CreateRegionalHolidays(RegionalHolidayCreateInput holiday)
    {
        var checkValid = await IsValidForCreate(holiday);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        return await holidayRepository.CreateRegionalHolidays(holiday, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> UpdateRegionalHolidays(RegionalHolidayUpdateInput holiday)
    {
        var checkValid = IsValidForUpdate(holiday);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        return await holidayRepository.UpdateRegionalHolidays(holiday, currentUser.CompanyId);
    }

    public async Task<RepositoryResponse> DeactivateAllHolidaysForRegion(long regionId, int year) => await holidayRepository.DeactivateAllHolidaysForRegion(regionId, year, currentUser.CompanyId);

    public async Task<RegionalHolidays> GetHolidaysForRegion(long regionId, int year) => await holidayRepository.GetHolidaysForRegion(regionId, year, currentUser.CompanyId);

    public async Task<List<RegionalHolidays>> GetAllHolidays(bool showInactive) => await holidayRepository.GetAllHolidays(currentUser.CompanyId, showInactive);
}
