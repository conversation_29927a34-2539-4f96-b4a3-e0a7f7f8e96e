﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Models;
public class RequestApprovalRule
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public ApprovalEngineRequesType RequestType { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public RuleConstraintType ConstraintType { get; set; }
    public int Priority { get; set; }
    public int ApprovalLevel { get; set; }
    public PositionLevelApprovalDefinition LevelDefinition { get; set; }
    public int? AdminApprovalLevel { get; set; }
    public string? AdminLevelDefinition { get; set; }
    public bool IsAdminRequired { get; set; }
    public bool IsDeactive { get; set; }
}

public class PriorityUpdateModel
{
    public long Id { get; set; }
    public int Priority { get; set; }
}

