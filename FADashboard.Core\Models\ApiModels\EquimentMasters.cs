﻿using Libraries.CommonModels;

namespace FADashboard.Core.Models.ApiModels;

public class EquipmentMasterView
{
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public string ErpId { get; set; }
    public string ReferenceNumber { get; set; }
    public DateTime? MFGDate { get; set; }
    public int WarrantyPeriod { get; set; }
    public EntityMinWithStatus AssetDefinitionId { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }
    public long? AttributeNumber1 { get; set; }
    public long? AttributeNumber2 { get; set; }
    public long? AttributeNumber3 { get; set; }
    public long? AttributeNumber4 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public bool? AttributeBool1 { get; set; }
    public bool? AttributeBool2 { get; set; }
    public bool GenerateReferenceNumber { get; set; }
}

public class EquipmentMasterListView
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string ErpId { get; set; }
    public string ReferenceNumber { get; set; }
    public bool IsActive { get; set; }
    public long AssetDefinitionId { get; set; }
}
