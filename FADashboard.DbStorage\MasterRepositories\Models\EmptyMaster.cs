﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("EmptyMaster")]
public class EmptyMaster
{
    public long Id { get; set; }
    public long CompanyId { get; set; }

    [StringLength(32)]
    public string ErpId { get; set; }

    [StringLength(32)]
    public string Name { get; set; }
    public UomType Uom { get; set; } = UomType.Unit;

    [Column(TypeName = "decimal(10,2)")]
    public decimal Price { get; set; } = 0.00m;
    public bool IsActive { get; set; } = false;
    public bool IsLinked { get; set; } = false;

    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [StringLength(32)]
    public string CreationContext { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; }
}
