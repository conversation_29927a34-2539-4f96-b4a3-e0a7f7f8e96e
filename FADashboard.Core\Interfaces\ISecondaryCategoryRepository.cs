﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface ISecondaryCategoryRepository
{
    Task<RepositoryResponse> ActivateDeactivateSecondaryCategory(long id, long companyId, bool action);

    Task<RepositoryResponse> CreateSecondaryCat(ProductCategoryDivision sc, long companyId);

    Task<List<ProductCategoryDivision>> GetDetailedSecondaryCategories(long companyId, bool includeDeactivate = false);

    Task<List<EntityMinWithErp>> GetSecCategoriesErpMin(long companyId, bool includeDeactivate = false);

    Task<List<EntityMinWithStatus>> GetSecCategoriesMin(long companyId, bool includeDeactivate);

    Task<List<EntityMin>> GetSecCategoriesMin(long companyId, List<long> pcIds);

    Task<List<EntityMin>> GetSecCategoriesWithPC(long companyId);

    Task<ProductCategoryDivision> GetSecondaryCategoryById(long companyId, long id);

    Task<RepositoryResponse> UpdateSecondaryCat(ProductCategoryDivision sc, long companyId);
    Task<List<EntityMinGuidWithErp>> GetProductSecondaryCategoriesByErpCodes(long companyId, List<string> productErpCodes);
    Task UpdateSecondaryCatImages(List<EntityMinGuidWithErp> dbProduct, long companyId);


}
