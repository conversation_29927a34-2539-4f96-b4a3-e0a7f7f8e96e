﻿namespace FADashboard.Core.Models.ApiModels;

public class ProductPricingResponse
{
    public ResponseStatus ResponseStatus { get; set; }
    public bool IsSuccess { get; set; }
    public string Message { get; set; }
    public List<string> UpdatedProducts { get; set; }
    public List<string> IgnoredProducts { get; set; }
    public static ProductPricingResponse GetRejectResponse(string error) => new() { IsSuccess = false, ResponseStatus = ResponseStatus.Failure, Message = error, };
}
