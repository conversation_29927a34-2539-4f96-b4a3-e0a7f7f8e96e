﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class OutletAdditionRequestInput
{
    public string Aadhar { set; get; }
    public string AccountHoldersName { get; set; }
    public string Address { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1Id { get; set; }
    public string AttributeImage2Id { get; set; }
    public string AttributeImage3Id { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }
    public string BankAccountNumber { get; set; }
    public long BeatId { get; set; }
    public long? ChannelId { get; set; }
    public string City { set; get; }
    public string ContactNo { get; set; }
    public string Email { get; set; }
    public long? EntityMarginSlabId { get; set; }
    public string ERPId { get; set; }
    public string Franchise { set; get; }
    public string GSTIN { get; set; }
    public bool GSTRegistered { get; set; }
    public long Id { get; set; }
    public string IFSCCode { get; set; }
    public string ImageId { set; get; }
    public bool IsBlocked { set; get; }
    public bool IsFocused { set; get; }
    public string MarketName { set; get; }
    public string OutletName { get; set; }
    public string OwnersName { get; set; }
    public string PAN { set; get; }
    public string PinCode { get; set; }
    public string PlaceOfDelivery { set; get; }
    public string RequestId { get; set; }
    public List<long> RouteIds { get; set; }
    public string SecondaryEmail { get; set; }
    public OutletSegmentation Segmentation { set; get; }
    public string SegmentationScope { set; get; }
    public long? ShopTypeId { get; set; }
    public string State { set; get; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }

    [StringLength(14, MinimumLength = 14, ErrorMessage = "The FSSAINumber should be in 14 characters")]
    public string FSSAINumber { get; set; }
    public long FAEventId { get; set; }
    public long? GeographicalMappingId { get; set; }
    public double? GeoAccuracy { get; set; }
    public long? RetailerOnboardingId { get; set; }
    public string CustomTags { get; set; }
    public long? SubShopTypeId { get; set; }
}
