﻿using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSAccountTemplateService
    {
        Task<PagedResult<LMSAccountTemplateDto>> GetAccountTemplatesAsync(long companyId, LMSAccountTemplateQueryParameters queryParameters);
        Task<LMSAccountTemplateDto> GetAccountTemplateByIdAsync(long id);
        Task<LMSAccountTemplateDto> CreateAccountTemplateAsync(long companyId, LMSAccountTemplateInput input, long createdByUserId);
        Task<LMSAccountTemplateDto> UpdateAccountTemplateAsync(long id, long companyId, LMSAccountTemplateInput input, long updatedByUserId);
        Task<bool> SetDefaultAsync(long id, long companyId, long updatedByUserId);
        Task<bool> SetActiveStatusAsync(long id, long companyId, bool isActive, long updatedByUserId);
    }
}
