﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class PositionsFlat
{
    public PositionCodeLevel PositionCodeLevel { get; set; }
    public string Level1 { get; set; }
    public string Level2 { get; set; }
    public string Level3 { get; set; }
    public string Level4 { get; set; }
    public string Level5 { get; set; }
    public string Level6 { get; set; }
    public string Level7 { get; set; }
    public string Level8 { get; set; }
    public string Level1Code { get; set; }
    public string Level2Code { get; set; }
    public string Level3Code { get; set; }
    public string Level4Code { get; set; }
    public string Level5Code { get; set; }
    public string Level6Code { get; set; }
    public string Level7Code { get; set; }
    public string Level8Code { get; set; }
    public string Level1User { get; set; }
    public string Level2User { get; set; }
    public string Level3User { get; set; }
    public string Level4User { get; set; }
    public string Level5User { get; set; }
    public string Level6User { get; set; }
    public string Level7User { get; set; }
    public string Level8User { get; set; }
    public long? Level1Id { get; set; }
    public long? Level2Id { get; set; }
    public long? Level3Id { get; set; }
    public long? Level4Id { get; set; }
    public long? Level5Id { get; set; }
    public long? Level6Id { get; set; }
    public long? Level7Id { get; set; }
    public long? Level8Id { get; set; }

    public PositionsFlat AddUserNameFromDictionary(Dictionary<long, string> posIdUserNameDict)
    {
        Level1User = Level1Id.HasValue && posIdUserNameDict.TryGetValue(Level1Id.Value, out var value) ? value : null;
        Level2User = Level2Id.HasValue && posIdUserNameDict.TryGetValue(Level2Id.Value, out var value1) ? value1 : null;
        Level3User = Level3Id.HasValue && posIdUserNameDict.TryGetValue(Level3Id.Value, out var value2) ? value2 : null;
        Level4User = Level4Id.HasValue && posIdUserNameDict.TryGetValue(Level4Id.Value, out var value3) ? value3 : null;
        Level5User = Level5Id.HasValue && posIdUserNameDict.TryGetValue(Level5Id.Value, out var value4) ? value4 : null;
        Level6User = Level6Id.HasValue && posIdUserNameDict.TryGetValue(Level6Id.Value, out var value5) ? value5 : null;
        Level7User = Level7Id.HasValue && posIdUserNameDict.TryGetValue(Level7Id.Value, out var value6) ? value6 : null;
        Level8User = Level8Id.HasValue && posIdUserNameDict.TryGetValue(Level8Id.Value, out var value7) ? value7 : null;
        return this;
    }
}
