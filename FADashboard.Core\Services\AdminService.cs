﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class AdminService(
    IAdminRepository adminRepository,
    IRegionRepository regionRepository,
    IEmployeeRepository employeeRepository,
    ICurrentUser currentUser,
    IIdsRepository idsRepository,
    ICompanySettingsRepository companySettingsRepository,
    IPositionCodeRepository positionCodeRepository) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidUser(Admin user)
    {
        var users = await GetUsers(currentUser.CompanyId, true);
        if (user.Id != 0)
        {
            users = users.Where(p => p.Id != user.Id).ToList();
        }

        var usermailList = users.Select(p => p.EmailId.NormalizeCaps()).ToList();
        var userPhoneNoList = users.Select(p => p.PhoneNo.NormalizeCaps()).ToList();

        if (usermailList.Contains(user.EmailId.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = user.Id, ExceptionMessage = "Admin with this Email Id already Exists!", Message = "Admin Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (userPhoneNoList.Contains(user.PhoneNo.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = user.Id, ExceptionMessage = "Phone number already exists in the company", Message = "Admin Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = user.Id, Message = "Admin Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateUser(long userId, long companyId, bool action, string portalUrl, bool twoFactorEnabled, string userName)
    {
        if (action)
            return await adminRepository.ActivateUser(userId, companyId, portalUrl, twoFactorEnabled, userName);
        return await adminRepository.DeactivateUser(userId, companyId);
    }

    public async Task<RepositoryResponse> CreateUpdateUser(Admin user, long companyId, string portalUrl)
    {
        var checkValid = await IsValidUser(user);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (user.Id == 0)
        {
            return await adminRepository.CreateUser(user, companyId, portalUrl);
        }

        return await adminRepository.UpdateUser(user, companyId, portalUrl);
    }

    public async Task<List<EntityMinUserWithStatus>> GetAllManagers(long companyId, bool includeDeactivate)
    {
        var managers = await employeeRepository.GetAllManagers(companyId, includeDeactivate);
        return managers;
    }

    public async Task<List<EntityMin>> GetRegionsUnderManager(long managerId, PortalUserRole managerRole, long companyId)
    {
        var regions = await regionRepository.GetRegionsUnderManager(managerId, managerRole, companyId);
        return regions;
    }

    public async Task<Admin> GetUserById(long userId, long companyId)
    {
        var user = await adminRepository.GetUserById(userId, companyId);
        var roleIds = await idsRepository.GetRoleIdsForUser(userId, companyId);
        user.RoleIds = roleIds.Select(id => (long?)id).ToList();
        return user;
    }

    public async Task<Admin> GetUserByIdForRBACAndNonRBAC(long userId, long companyId)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var usesRBAC = companySettings.CompanyUsesRoleBasedAccessRightsModule;
        var user = await adminRepository.GetUserById(userId, companyId);
        if (!usesRBAC)
        {
            var positionAttachedToUser = await positionCodeRepository.GetPositionCodesForEmployee(user.RegionalParentId ?? 0, currentUser.CompanyId);
            var positionIds = positionAttachedToUser.Select(p => p.Id).ToList();
            user.UserPositionIds = [.. positionIds.Select(id => (long?)id)];
        }
        var roleIds = await idsRepository.GetRoleIdsForUser(userId, companyId);
        user.RoleIds = roleIds.Select(id => (long?)id).ToList();
        return user;
    }

    public async Task<List<Admin>> GetUsers(long companyId, bool includeDeactivate)
    {
        var users = await adminRepository.GetUsers(companyId, includeDeactivate);
        return users;
    }

    public async Task<RegionalAdminDTO> GetRegionalAdminDetails(long id) => await adminRepository.GetRegionalAdminDetails(id, currentUser.CompanyId);

    public async Task<List<CompanyFactories>> GetCompanyFactoriesToAttach(long? adminId) => await adminRepository.GetCompanyFactoriesToAttach(adminId, currentUser.CompanyId);

    public async Task<RepositoryResponse> RegisterAdmin(long adminId, bool action, string redirectUrl, bool twoFactorEnabled, string userName, List<long> roleIds) => await adminRepository.RegisterAdmin(adminId, action, currentUser.CompanyId, redirectUrl, twoFactorEnabled, userName, roleIds);

}
