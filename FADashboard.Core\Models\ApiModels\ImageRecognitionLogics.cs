﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ImageRecognitionLogicList
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsMandatory { get; set; }
    public bool IsDeactive { get; set; }
}

public class ImageRecognitionLogicsView
{
    public long Id { get; set; }
    public string RuleName { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsMandatory { get; set; }
    public bool BeforeSeenEnabled { get; set; }
    public List<OutletHierarchyConstraints> OutletHierarchyConstraints { get; set; }
    public List<OutletConstraintsInput> OutletConstraints { get; set; }
    public List<ProductConstraintsInput> ProductConstraints { get; set; }
    public List<KPIWeightageInput> KPIWeightage { get; set; }
    public int? Positioning { get; set; }
    public List<long> Assets { get; set; }
}

public class OutletHierarchyConstraints
{
    public string OutletHierarchyEntityType { get; set; }

    public List<long> OutletHierarchyEntityIds { get; set; }
}

public class KPIWeightageInput
{
    public ImageRecognitionKPIs KPIEnum { get; set; }
    public decimal Weightage { get; set; }
    public decimal? Target { get; set; }
}

