﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ICueCardRepository
{
    Task<List<CueCardsList>> GetCueCards(long companyId, bool includeDeactive = false);
    Task<RepositoryResponse> CreateCueCard(long companyId, CueCardInput cueCardInput);
    Task<RepositoryResponse> UpdateCueCard(long companyId, CueCardInput cueCardInput);
    Task<CueCardInput> GetCueCardById(long cueCardId, long companyId);
    Task<RepositoryResponse> ActivateDeactivateCueCard(long id, long companyId, bool action);
    Task<RepositoryResponse> UpdateCueCardSequences(long companyId, List<CueCardSequence> cueCardSequences);
}
