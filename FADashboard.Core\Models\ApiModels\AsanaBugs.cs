﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class AsanaBugs
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string ClientCategory { get; set; }
    public Platform Platform { get; set; }
    public string Module { get; set; }
    public string Type { get; set; }
    public string ListOfCompany { get; set; }
    public string AsanaTitle { get; set; }
    public string IssueDescription { get; set; }
    public string AsanaLink { get; set; }
    public string CreatedBy { get; set; }
    public string RaisedSeverity { get; set; }
    public string ActualSeverity { get; set; }
    public bool IsSLAAdhere { get; set; }
    public DateTime CreatedAt { get; set; }
    public string ClosedBy { get; set; }
    public DateTime CloserDate { get; set; }
    public string CreationContext { get; set; }
}
