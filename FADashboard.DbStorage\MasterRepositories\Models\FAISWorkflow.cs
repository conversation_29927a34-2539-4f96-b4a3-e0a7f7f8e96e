﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class FAISWorkflow : IDeactivatable, ICompanyEntity
{
    public long Id { get; set; }
    public string WorkflowName { get; set; }
    public long CompanyId { get; set; }
    public int? Sequence { get; set; }
    public WorkFlowIntegrationType? IntegrationEntity { get; set; }
    public int? IntegrationType { get; set; }
    public string Configuration { get; set; }
    public DateTime? StartDate { get; set; }
    public string Cron { get; set; }
    public bool IsDeactive { get; set; }
}
