﻿using EntityHelper;
using FADashboard.Core.Interfaces;
using FADashboard.DbStorage.MasterRepositories.Models;

namespace FADashboard.DbStorage.Helpers.DataHelper;

public class AutoERPGenerate(ICompanyRepository companyRepository)
{
    public RepositoryResponse GenerateUpdateErpId<T>(T entity, long companyId, string shortRegionCode = null) where T : IERPEntity
    {
        var shortName = companyRepository.GetShortName(companyId);
        var shouldGenerateErpId = string.IsNullOrEmpty(entity.ErpId);
        if (shouldGenerateErpId)
        {
            if (typeof(T) == typeof(Location))
            {
                if (shortRegionCode != null)
                {
                    //Date: 12th Mar 2021
                    //Link: https://app.asana.com/0/1107745485828457/1200030242805967
                    //Reason: Changing the erpId logic as requested in asana.
                    entity.ErpId = $"{shortRegionCode}-{(entity.Id - 107) * 3}";
                    return RepositoryResponse.GetSuccessResponse(entity.Id);
                }
                //Date: 7th July 2021
                //Link: https://app.asana.com/0/139097763031412/1200556391876644
                //Reason: OutletShortName and shortRegionCode logics are independent of each other.

                if (!string.IsNullOrEmpty(shortName.OutletShortName))
                {
                    entity.ErpId = $"FO_{shortName.OutletShortName}_{(entity.Id - 107) * 3}";
                    return RepositoryResponse.GetSuccessResponse(entity.Id);
                }

                return RepositoryResponse.GetSuccessResponse(entity.Id, "Not Valid for ERPID Generation");
            }

            if (typeof(T) == typeof(LocationBeat) && !string.IsNullOrEmpty(shortName.BeatShortName))
            {
                entity.ErpId = $"FB_{shortName.BeatShortName}_{(entity.Id + 211) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            if (typeof(T) == typeof(ClientEmployee) && !string.IsNullOrEmpty(shortName.EmployeeShortName))
            {
                entity.ErpId = $"FU_{shortName.EmployeeShortName}_{(entity.Id - 191) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }
            if (typeof(T) == typeof(Scheme))
            {
                entity.ErpId = $"FA_SC_{(entity.Id - 107) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            if (typeof(T) == typeof(Distributor))
            {
                entity.ErpId = $"FD_{shortName.DistributorShortName}_{(entity.Id + 211) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            if (typeof(T) == typeof(ProductDivision))
            {
                entity.ErpId = $"FA_PD_{(entity.Id + 211) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            if (typeof(T) == typeof(ProductPrimaryCategory))
            {
                entity.ErpId = $"FA_PC_{(entity.Id + 211) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            if (typeof(T) == typeof(ProductSecondaryCategory))
            {
                entity.ErpId = $"FA_SC_{(entity.Id + 211) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            if (typeof(T) == typeof(ProductDisplayCategory))
            {
                entity.ErpId = $"FA_DC_{(entity.Id + 211) * 3}";
                return RepositoryResponse.GetSuccessResponse(entity.Id);
            }

            return RepositoryResponse.GetSuccessResponse(entity.Id);
        }

        return RepositoryResponse.GetSuccessResponse(entity.Id, "Not Valid for ERPID Generation");
    }
}
