﻿namespace FADashboard.Core.Models.ApiModels;
public class ProductPricingList : ProductList
{
    public decimal? PTSubStockist { get; set; }
    public decimal? PTSuperStockist { get; set; }
    public decimal? PTD { get; set; }
    public decimal? PTR { get; set; }
    public decimal? SuperStockistTradeDiscount { get; set; }
    public decimal? DistributorTradeDiscount { get; set; }
    public decimal? SubStockistTradeDiscount { get; set; }
    public decimal? RetailerTradeDiscount { get; set; }
    public string IsActivePricing { get; set; }
    public string Region { get; set; }
    public string Zone { get; set; }
    public string SuperStockistName { get; set; }
    public string SubDistributorName { get; set; }
    public string DistributorName { get; set; }
    public string DistributorState { get; set; }
    public string DistributorErpId { get; set; }
    public string PricingStockistSegmentation { get; set; }
    public string PricingStockistChannel { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string MRP { get; set; }
    public string PTRMT { get; set; }
    public string BatchNumber { get; set; }
}

public class ProductPricingMasterTotal
{
    public int Total { get; set; }
    public List<ProductPricingList> ProductPricingRecords { get; set; }
}
