﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;
using Microsoft.AspNetCore.Http;


namespace FADashboard.Core.Services;
public class ProductGroupService(
    IProductGroupRepository productGroupRepository,
    ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<ProductInput>> GetActiveProductsGroupName()
    {
        var groupNames = await productGroupRepository.GetActiveProductsGroupName(currentUser);
        return groupNames;
    }
}
