﻿using System.ComponentModel.DataAnnotations;
using AuditHelper;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class EmailSchedule : IAuditedEntity, IDeactivatable
{
    public virtual ChartVizDetails ChartVizDetails { get; set; }
    public long? ChartVizDetailsId { get; set; }
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public EmailFrequency EmailFrequency { get; set; }
    public virtual FlexibleReport FlexibleReport { get; set; }
    public long? FlexibleReportId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [StringLength(63)]
    public string SubscribedRoles { get; set; }

    [StringLength(63)]
    public string SubscribedAdmins { get; set; }
}

public class FlexibleReport : IAuditedEntity, IDeletable
{
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    [Audited]
    public bool Deleted { get; set; }
    public virtual List<EmailSchedule> EmailSchedules { get; set; }
    public string ExtraInfoJson { get; set; }
    public long Id { get; set; }
    [Audited]
    public string ReportName { get; set; }
    [Audited]
    [StringLength(63)]
    public string SubscribedRoles { get; set; }

    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
}
