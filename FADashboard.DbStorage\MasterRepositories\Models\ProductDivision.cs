﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ProductDivision : IAuditedEntity, IDeletable, IERPEntity
{
    public virtual Company Company { get; private set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { set; get; }
    public long Id { get; set; }
    public bool IsDeactive { set; get; }
    public DateTime LastUpdatedAt { get; set; }
    public string Name { get; set; }

    [Column("ERPId")] public string ErpId { get; set; }
    public int OrderPosition { set; get; }
    public string StandardUnit { get; set; }

    public string SuperUnit { get; set; }
}
