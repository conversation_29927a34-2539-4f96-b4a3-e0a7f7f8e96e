﻿using System.ComponentModel.DataAnnotations;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class SuperStockist : IAuditedEntity, IDeletable, IDeactivatable
{
    private SuperStockist()
    {
        Guid = new Guid();
        Distributors = [];
    }

    public SuperStockist(long companyId) : this()
    {
        CompanyId = companyId;
    }

    public virtual Company Company { get; private set; }
    public long CompanyId { private set; get; }

    [StringLength(15)] [Audited] public string ContactNo { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public virtual ICollection<Distributor> Distributors { get; set; }

    [StringLength(100)] [Audited] public string EmailId { get; set; }

    [StringLength(100)] [Audited] public string ErpId { get; set; }

    [Required] public Guid Guid { get; private set; }

    public long Id { get; set; }

    public bool IsDeactive { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    [Required]
    [StringLength(100)]
    [Audited]
    public string Name { get; set; }

    [StringLength(50)] [Audited] public string SecondaryEmailId { set; get; }
}
