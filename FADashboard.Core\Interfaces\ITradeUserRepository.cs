﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface ITradeUserRepository
{
    Task<RepositoryResponse> UpdateTradeUser(OutletInput outlet, string phoneNo, OutletChannel outletChannel);
    Task<bool> CheckTradeUserIfExist(string phoneNo, long companyId);
    Task<RepositoryResponse> ApproveTradeUserUpdation(long requestId, string phoneNo);
}
