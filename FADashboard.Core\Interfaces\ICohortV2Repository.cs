﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ICohortV2Repository
{
    Task<RepositoryResponse> CreateOutletCohort(FilterConstraintDetailInput cohort, long companyId, CancellationToken ct);
    Task<RepositoryResponse> UpdateOutletCohort(long companyId, FilterConstraintDetailInput cohort, CancellationToken ct);
    Task<RepositoryResponse> CreateDistributorCohort(FilterConstraintDetailInput cohort, long companyId, CancellationToken ct);
    Task<RepositoryResponse> UpdateDistributorCohort(long companyId, FilterConstraintDetailInput cohort, CancellationToken ct);
    Task<RepositoryResponse> CreateUserCohort(FilterConstraintDetailInput cohort, long companyId, CancellationToken ct);
    Task<RepositoryResponse> UpdateUserCohort(long companyId, FilterConstraintDetailInput cohort, CancellationToken ct);
    Task<List<CohortV2View>> GetAllCohorts(long companyId, bool showDeactive);
    Task<RepositoryResponse> ActivateDeactivateCohort(long requestId, bool action, long companyId, CancellationToken ct);
    Task<FilterConstraintById> GetById(long id, long companyId, CancellationToken ct);

}
