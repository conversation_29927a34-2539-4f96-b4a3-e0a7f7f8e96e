﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public enum IndividualFilterType
{
    Unknown = 0,
    EmployeeHierarchyMultiSelect = 1,
    PositionHierarchyMultiSelect = 2,
    ProductCategoryMultiSelect = 3,
    PSODShowDataForFilter = 4,
    //TODO: Add more as required
}

public enum InputType
{
    Checkbox = 0,
}

public class ColumnMin
{
    public string CategoryDisplayName { get; set; }
    public string CategoryName { get; set; }
    public string DisplayName { get; set; }
    public long Id { get; set; }
    public bool IsNewColumn { get; set; }
    public bool IsSelected { get; set; }
    public bool IsStandardColumn { get; set; }
    public string ReportColumnName { get; set; }
}

public class ReportDetails
{
    private static readonly Dictionary<FilterType, List<IndividualFilterType>> FilterDictionary = new()
    {
        [FilterType.EmployeeHierarchyMultiSelect] = [IndividualFilterType.EmployeeHierarchyMultiSelect, IndividualFilterType.PositionHierarchyMultiSelect],
        //[FilterType.EmployeeMSPositionMSCategoryMS] = new List<IndividualFilterType> { IndividualFilterType.EmployeeHierarchyMultiSelect, IndividualFilterType.PositionHierarchyMultiSelect, IndividualFilterType.ProductCategoryMultiSelect },
        //[FilterType.EmployeeMSPositionMSCategoryMSPSOD] = new List<IndividualFilterType> { IndividualFilterType.EmployeeHierarchyMultiSelect, IndividualFilterType.PositionHierarchyMultiSelect, IndividualFilterType.ProductCategoryMultiSelect, IndividualFilterType.PSODShowDataForFilter },
        //TODO: Add More as required
    };

    public bool CanCustomize { get; set; }

    public List<string> Categories => Columns.Select(c => c.CategoryName).Distinct().ToList();

    public List<ColumnMin> Columns { get; internal set; }
    public long CustomReportId { get; internal set; }
    public DateFiltersType DateFilterType { get; set; }
    public List<string> DatePresets { get; set; }

    public List<string> DisplayCategories => Columns.Select(c => c.CategoryDisplayName).Distinct().ToList();

    public List<IndividualFilterType> Filters { get; set; }
    public int MaxColumnsSelectable { get; internal set; }
    public int maxDaysAllowed { get; set; }
    public string Name { get; set; }
    public List<ReportPreferences> OtherPreferences { get; set; }
    public long ReportId { get; internal set; }
    public User UserDetails { get; set; }

    public static void GetFilters(ReportDetails res, FilterType filterType) => res.Filters = FilterDictionary.TryGetValue(filterType, out var value) ? value : [];
}

public class ReportPreferences
{
    public string DisplayName { get; set; }
    public InputType InputType { get; set; }
    public string Name { get; set; }
    public object Value { get; set; }
}

public class ReportSatus
{
    public string downloadLink { get; set; }
    public long requestId { get; set; }
    public ReportRequestStatus status { get; set; }
}

public class User
{
    public PositionCodeLevel HighestPosition { get; set; }
    public PortalUserRole HighestRole { get; set; }
    public long Id { get; set; }
    public long newId { get; set; }
    public List<long> PositionIds { get; set; }
    public PositionCodeLevel UserHighestPosition { get; set; }
    public PortalUserRole UserRole { get; set; }
    public bool UsesPositionCodes { get; set; }
}
