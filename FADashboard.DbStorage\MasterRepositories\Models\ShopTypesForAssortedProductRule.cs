﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ShopTypesForAssortedProductRules")]
public class ShopTypesForAssortedProductRule
{
    public AssortedProductRule AssortedProductRule { get; set; }
    public long AssortedProductRuleId { get; set; }
    public Channels Channel { get; set; }
    public long ChannelId { get; set; }
    public long Id { get; set; }
    public ShopTypes ShopType { get; set; }
    public long ShopTypeId { get; set; }
}
