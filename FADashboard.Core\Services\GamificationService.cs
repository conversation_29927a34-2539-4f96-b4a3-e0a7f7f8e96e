﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class GamificationService(
    ITeamsRepository teamsRepository,
    ICurrentUser currentUser,
    ITeamUserMappingsRepository teamUserMappingsRepository,
    IEmployeeRepository employeeRepository,
    IPositionCodeRepository positionCodeRepository
) : RepositoryResponse
{
    public async Task<List<SendTeamInfo>> GetAllTeams(bool includeDeactive)
    {
        var teams = await teamsRepository.GetAllTeams(currentUser.CompanyId, includeDeactive);
        return teams;
    }

    public async Task<SendTeamInfo> GetTeamById(long id)
    {
        var team = await teamsRepository.GetTeamById(currentUser.CompanyId, id);
        return team;
    }

    public async Task<RepositoryResponse> IsValidDeactivation(long teamId)
    {
        var teamPlayers = await teamUserMappingsRepository.EmployeeMappedToTeams(currentUser.CompanyId, teamId);

        if (teamPlayers != null && teamPlayers.Select(a => a.Id).ToList().Count > 0)
        {
            var playerNames = teamPlayers.Select(p => p.Name).ToList();
            return GetRejectResponse($"Team has players, please remove players:  {string.Join(", ", playerNames)} from the team before deactivating.");
        }

        return new RepositoryResponse { IsSuccess = true, Message = "Team has no players, you can deactivate the team.", Id = teamId };
    }

    public async Task<RepositoryResponse> DeactivateTeam(long id)
    {
        var checkValidTeam = await IsValidDeactivation(id);
        if (checkValidTeam.IsSuccess)
        {
            return await teamsRepository.DeactivateTeam(currentUser.CompanyId, id);
        }

        return checkValidTeam;
    }

    public async Task<RepositoryResponse> IsTeamNameValid(Team teamInput)
    {
        var dbteams = await teamsRepository.GetAllTeams(currentUser.CompanyId, true);
        if (teamInput.Id != 0)
        {
            dbteams = dbteams.Where(p => p.Id != teamInput.Id).ToList();
        }

        var dbteamNames = dbteams.Select(p => p.Name.NormalizeCaps()).ToList();

        if (!string.IsNullOrEmpty(teamInput.Name))
        {
            if (dbteamNames.Contains(teamInput.Name.NormalizeCaps()))
            {
                return GetRejectResponse("Team Name already exists!!");
            }
        }

        return GetSuccessResponse(teamInput.Id, "Team Name is unique!!");
    }

    public async Task<RepositoryResponse> CreateUpdateTeam(Team teamInput)
    {
        var checkValidTeamName = await IsTeamNameValid(teamInput);
        if (!checkValidTeamName.IsSuccess)
        {
            return checkValidTeamName;
        }

        if (teamInput.Id == 0)
        {
            return await teamsRepository.CreateTeam(currentUser.CompanyId, teamInput);
        }

        return await teamsRepository.UpdateTeam(currentUser.CompanyId, teamInput);
    }

    public async Task<RepositoryResponse> CreateUpdateTeamUserMapping(TeamUserMappingsView teamUserMappingsView) => await teamUserMappingsRepository.CreateUpdateTeamUserMapping(currentUser.CompanyId, teamUserMappingsView);

    public async Task<List<TeamPlayerWithPosition>> GetTeamPlayers(long teamId)
    {
        var teamPlayers = await teamUserMappingsRepository.GetTeamPlayers(currentUser.CompanyId, teamId);
        return teamPlayers.TeamPlayers;
    }

    public async Task<List<TeamPlayerWithPosition>> GetPlayersForTeamMapping(string teamModuleType)
    {
        var allTeams = await teamsRepository.GetAllTeams(currentUser.CompanyId,false);
        var teamDict = allTeams.ToDictionary(team => team.Id, team => team.TeamType);
        var mappedEmployees = await teamUserMappingsRepository.GetTeamPlayerMappingsDict(currentUser.CompanyId);
        List<long> filteredTeamPlayerIds;
        if (teamModuleType == "Both")
        {
            filteredTeamPlayerIds = mappedEmployees.Values.SelectMany(ids => ids).ToList();
        }
        else
        {
            filteredTeamPlayerIds = teamDict.Where(kvp => mappedEmployees.ContainsKey(kvp.Key) && (kvp.Value == teamModuleType || kvp.Value == "Both"))
           .SelectMany(kvp => mappedEmployees[kvp.Key]).ToList();
        }
        var allEmployees = await employeeRepository.GetAllEmployees(currentUser.CompanyId);
        var employeeIdList = allEmployees.Select(e => e.Id).Except(filteredTeamPlayerIds).ToList();
        var employees = allEmployees.Where(a => employeeIdList.Contains(a.Id)).ToList();
        var positionsEmployee = (await positionCodeRepository.GetPositionByEmployee(currentUser.CompanyId, employeeIdList)).ToLookup(p => p.EntityId);
        var employeesWithPosition = employees.Select(e => new TeamPlayerWithPosition
        {
            TeamPlayerId = e.Id, TeamPlayerName = e.Name, TeamPlayerErpId = e.ErpId, Positions = positionsEmployee.Contains(e.Id) ? positionsEmployee[e.Id].ToList() : null,
        }).ToList();
        return employeesWithPosition;
    }
}
