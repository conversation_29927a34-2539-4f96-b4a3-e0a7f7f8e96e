﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;
public class OrderSummaryPDFConfigService(IOrderSummaryPDFConfigRepository orderSummaryPDFConfigRepository, ICompanySettingsRepository companySettingsRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task UpdateFields(List<OrderSummPdfConfig> fields, PdfType pdfType)
    {
        var fieldstoUpdate = fields.Where(f => f.Id != 0).ToList();
        if (fieldstoUpdate.Count > 0)
        {
            await orderSummaryPDFConfigRepository.UpdateFields(fieldstoUpdate, currentUser, pdfType);
        }
    }
    public async Task<List<OrderSummPdfConfig>> GetFields(PdfType pdfType)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);

        var fields = await orderSummaryPDFConfigRepository.GetFields(currentUser.CompanyId, pdfType, companySettings);
        return fields;
    }

}
