﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class OutletInput
{
    [StringLength(12), DisplayName("Aadhar No")]
    public string <PERSON><PERSON>har { set; get; }

    [DisplayName("Account Holder's Name")]
    public string AccountHoldersName { get; set; }

    public string Address { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1Id { get; set; }
    public string AttributeImage2Id { get; set; }
    public string AttributeImage3Id { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }

    [StringLength(18), DisplayName("Bank Account Number")]
    public string BankAccountNumber { get; set; }

    public long BeatId { get; set; }
    public long ChannelId { get; set; }
    public long? TerritoryId { get; set; }

    [StringLength(50)]
    public string City { set; get; }

    [StringLength(50)]
    [RegularExpression("^[0-9]{7,10}$", ErrorMessage = "Invalid Contact No.")]
    public string ContactNo { get; set; }

    [StringLength(50)]
    public string Email { get; set; }

    public long? EntityMarginSlabId { get; set; }

    [StringLength(50)]
    public string ErpId { get; set; }
    public ISRAvailability? IsrAvailability { get; set; }

    public string Franchise { set; get; }

    [StringLength(15)]
    public string GSTIN { get; set; }

    public bool GSTRegistered { get; set; }
    public long Id { get; set; }

    [StringLength(11), DisplayName("IFSC Code")]
    public string IFSCCode { get; set; }

    public string ImageId { set; get; }

    public bool IsBlocked { set; get; }

    public bool IsFocused { set; get; }

    [StringLength(100)]
    public string MarketName { set; get; }

    public OutletChannel OutletChannel { set; get; }

    [DisplayName("Shop Name"), Required(ErrorMessage = "Please provide a name")]
    public string OutletName { get; set; }

    [StringLength(50)]
    public string OwnersName { get; set; }

    [StringLength(10), DisplayName("PAN No")]
    public string PAN { set; get; }

    [StringLength(6)]
    [RegularExpression(@"^\d{4,6}$", ErrorMessage = "Pincode should Contain only Digits")]
    public string PinCode { get; set; }

    public string PlaceOfDelivery { set; get; }

    // public string BeatName { get; set; }
    public List<long> Routes { get; set; }

    [StringLength(50), DisplayName("Secondary Email")]
    public string SecondaryEmail { get; set; }

    public OutletSegmentation Segmentation { set; get; }

    public string SegmentationScope { set; get; }

    //public string ShopType { set; get; }
    public long? ShopTypeId { get; set; }

    [StringLength(50)]
    public string State { set; get; }

    //  public Segmentation CompanySegmentation { set; get; }
    public decimal? Latitude { get; set; }

    public decimal? Longitude { get; set; }
    public string FSSAINumber { get; set; }
    public DateTime? FSSAIExpiryDate { get; set; }
    public bool IsAssetPresent { get; set; }
    public string AssetDescription { set; get; }
    public string AssetCode { set; get; }
    public string AssetType { set; get; }
    public string AssetSize { set; get; }
    public LocationConsumerType? ConsumerType { get; set; }
    public string CustomTags { get; set; }
    public long? GeographicalMappingId { get; set; }
    public double? GeoAccuracy { get; set; }

    [StringLength(50)]
    public string TIN { get; set; }
    public long? SubShopTypeId { get; set; }
}
