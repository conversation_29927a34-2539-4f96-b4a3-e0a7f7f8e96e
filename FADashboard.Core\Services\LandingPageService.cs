﻿using System.Dynamic;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.ResilientHttpClient;
using MTDDateHelper = FADashboard.Core.Helper.MTDDateHelper;

namespace FADashboard.Core.Services;

public class LandingPageService(NumberSystemHelper numberSystemService, ICurrentUser currentUser, ICompanySettingsRepository companySettingsRepository, FAResilientHttpClient resilientHttpClient, AppConfigSettings appConfigSettings, MTDDateHelper mtdDateHelper)
    : RepositoryResponse
{
    private async Task<AttendanceSummary> UserCallSummaryData(DateTime date, List<long> positionIds, string level,SaleType saleType = SaleType.All)
    {
        var positionParams = positionIds !=null && positionIds.Count != 0 ? string.Join("&", positionIds.Select(id => $"positionCodeIds={id}")) : null;
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/call/callSummaryUsingPositionsNew?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&userId={currentUser.LocalId}&userRole={currentUser.UserRole}&{positionParams}&level={level}&saleType={saleType}";
        var data = await resilientHttpClient.GetJsonAsync<AttendanceSummary>(dataUrl, appConfigSettings.reportApiToken);

        data.OrderInRevenueDisplay = numberSystemService.GetFormattedValue(data.OrderInRevenue);
        data.OrderInStdUnitsDisplay = numberSystemService.GetFormattedValue(data.OrderInStdUnits);
        data.OrderInSuperUnitsDisplay = numberSystemService.GetFormattedValue(data.OrderInSuperUnits, isSales: true);
        data.NetOrderInRevenueDisplay = numberSystemService.GetFormattedValue(data.NetOrderInRevenue);
        return data;
    }

    public async Task<MTDLMTD> GetMTDLMTDSummary(DateTime date, List<long> positionIds, string level, SaleType saleType = SaleType.All, PortalUserRole role = PortalUserRole.Unknown)
    {
        var data = new MTDLMTD();
        try
        {
            var user = currentUser.LocalId;
            if (role == PortalUserRole.Unknown)
            {
                role = currentUser.UserRole;
            }
            var positionParams = positionIds.Count != 0 ? string.Join("&", positionIds.Select(id => $"positionCodeIds={id}")) : null;
            var dataUrl = "";
            if (saleType == SaleType.All)
            {
                dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/ds/mtdLmtdUsingPositions?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&userId={user}&userRole={role}&{positionParams}&level={level}&saleType={saleType}";
            }
            else
            {
                dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/ds/mtdLmtdUsingPositionsSaleType?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&userId={user}&userRole={role}&{positionParams}&level={level}&saleType={saleType}";
            }
            var dataTask = resilientHttpClient.GetJsonAsync<MTDLMTD>(dataUrl, appConfigSettings.reportApiToken);
            var companySettingsDictTask = companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            await Task.WhenAll(dataTask, companySettingsDictTask);
            data = await dataTask;
            var companySettingsDict = await companySettingsDictTask;
            var companySettings = new CompanySettings(companySettingsDict);
            var usesSalesInAmount = companySettings.UsesSalesInAmount;
            var CurrencySymbol = companySettings.CurrencySymbol;

            var result = new MTDLMTD { MTDSalesSummary = data.MTDSalesSummary };
            result.MTDSalesSummaryDisplay = numberSystemService.GetFormattedValue(result.MTDSalesSummary);
            result.LMTDSalesSummary = data.LMTDSalesSummary;
            result.LMTDSalesSummaryDisplay = numberSystemService.GetFormattedValue(result.LMTDSalesSummary);
            result.ValueHeader = usesSalesInAmount ? $"Net Value({CurrencySymbol})" : "Qty(Std. Unit)";
            return result;
        }
        catch (Exception ex)
        {
            data.ErrorResponse = new RepositoryResponse { Id = 0, ExceptionMessage = ex.GetBaseException().Message, Message = "An Error Occurred while calling MTD LMTD summary API", IsSuccess = false };
            return data;
        }
    }

    //TODO : Need new reporting API based on new userId
    public async Task<MTDCategorySalesTotal> GetMTDPCSales(DateTime date, List<long> positionCodeIds, string level, SaleType saleType = SaleType.All)
    {
        string positionParams = positionCodeIds != null &&  positionCodeIds.Count != 0 ? string.Join("&", positionCodeIds.Select(id => $"positionCodeIds={id}")) : null;
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/sales/pcMTDWiseSalesWithTotalUsingPositions?companyId={currentUser.CompanyId}&date={date:MM /dd/yyyy}&userId={currentUser.LocalId}&userRole={currentUser.UserRole}&{positionParams}&level={level}&saleType={saleType}";
        var data = await resilientHttpClient.GetJsonAsync<MTDCategorySalesTotal>(dataUrl, appConfigSettings.reportApiToken);

        data.MTDSalesDisplay = numberSystemService.GetFormattedValue(data.MTDSales, data.Unit, data.IsAmount);
        data.MTDCategorySales?.ForEach(d => d.MTDSalesDisplay = numberSystemService.GetFormattedValue(d.MTDSales, d.Unit, d.IsAmount));
        data.Others = (data?.MTDCategorySales?.Any() == true) ? data.MTDCategorySales.Select(d => d.MTDSales).Sum() - data.MTDCategorySales.Select(x => x.MTDSales).OrderByDescending(x => x).Take(5).Sum() : 0;
        data.OthersDisplay = numberSystemService.GetFormattedValue(data.Others, data.Unit, data.IsAmount);

        return data;
    }

    public async Task<MTDOutletSummary> GetOutletSummary(DateTime date, List<long> positionIds, string level, SaleType saleType = SaleType.All)
    {
        var positionParams = positionIds != null && positionIds.Count != 0 ? string.Join("&", positionIds.Select(id => $"positionCodeIds={id}")) : null;
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/outlet/mtdSummaryUsingPositionsNew?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&userId={currentUser.LocalId}&userRole={currentUser.UserRole}&{positionParams}&level={level}&saleType={saleType}";
        var data = await resilientHttpClient.GetJsonAsync<MTDOutletSummary>(dataUrl, appConfigSettings.reportApiToken);
        data.UPCDisplay = numberSystemService.FormatDataValue(data.UPC);
        data.UTCDisplay = numberSystemService.GetFormattedValue(data.UTC);
        data.NoSalesOutletDisplay = numberSystemService.GetFormattedValue(data.NoSalesOutlet);
        data.UnAttendedOutletsDisplay = numberSystemService.GetFormattedValue(data.UnAttendedOutlets);
        data.TotalOutletsDisplay = numberSystemService.GetFormattedValue(data.TotalOutlets);
        data.PCDisplay = numberSystemService.GetFormattedValue(data.PC);
        data.TCDisplay = numberSystemService.GetFormattedValue(data.TC);
        data.CoveredDisplay = numberSystemService.GetFormattedValue(data.Covered);
        data.OrderedDisplay = numberSystemService.GetFormattedValue(data.Ordered);
        data.ProductivityDisplay = numberSystemService.GetFormattedValue(data.Productivity, "%");
        return data;
    }

    public async Task<SecondaryOrderValidation> UnderUserSalesPosition(DateTime date, List<long> positionIds, string level,SaleType saleType = SaleType.All)
    {
        var positionParams = positionIds.Count != 0 ? string.Join("&", positionIds.Select(id => $"positionCodeIds={id}")) : null;
        var dataUrl =
            $"{appConfigSettings.reportApiBaseUrl}api/ordervalidation/UnderUserSecondaryOrderAndValidationMTDWithTotalUsingPositionNew?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&userId={currentUser.LocalId}&userRole={currentUser.UserRole}&{positionParams}&level={level}&saleType={saleType}";
        var data = await resilientHttpClient.GetJsonAsync<SecondaryOrderValidation>(dataUrl, appConfigSettings.reportApiToken);

        data.SecondaryOrderValidationsPositions.PositionOrderDisplay =
            numberSystemService.GetFormattedValue(data.SecondaryOrderValidationsPositions.Order, data.SecondaryOrderValidationsPositions.Unit, data.SecondaryOrderValidationsPositions.IsAmount);
        data.SecondaryOrderValidationsPositions.PositionValidationDisplay =
            numberSystemService.GetFormattedValue(data.SecondaryOrderValidationsPositions.Validation, data.SecondaryOrderValidationsPositions.Unit, data.SecondaryOrderValidationsPositions.IsAmount);
        data.SecondaryOrderValidationGeography.GeographyOrderDisplay = numberSystemService.GetFormattedValue(data.SecondaryOrderValidationGeography.Order, data.SecondaryOrderValidationGeography.Unit, data.SecondaryOrderValidationGeography.IsAmount);
        data.SecondaryOrderValidationGeography.GeographyValidationDisplay =
            numberSystemService.GetFormattedValue(data.SecondaryOrderValidationGeography.Validation, data.SecondaryOrderValidationGeography.Unit, data.SecondaryOrderValidationGeography.IsAmount);
        data.SecondaryOrderValidationsPositions.SecondaryOrderValidations?.ForEach(s =>
        {
            s.OrderDisplay = numberSystemService.GetFormattedValue(s.Order, s.Unit, s.IsAmount);
            s.ValidationDisplay = numberSystemService.GetFormattedValue(s.Validation, s.Unit, s.IsAmount);
        });

        data.SecondaryOrderValidationGeography.SecondaryOrderValidations?.ForEach(s =>
        {
            s.OrderDisplay = numberSystemService.GetFormattedValue(s.Order, s.Unit, s.IsAmount);
            s.ValidationDisplay = numberSystemService.GetFormattedValue(s.Validation, s.Unit, s.IsAmount);
        });

        return data;
    }

    public async Task<dynamic> UserCallSummary(DateTime date, List<long> positionIds, string level,SaleType saleType = SaleType.All)
    {
        var dataTask = UserCallSummaryData(date, positionIds, level, saleType);
        var companySettingsDictTask = companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        await Task.WhenAll(dataTask, companySettingsDictTask);
        var companySettingsDict = await companySettingsDictTask;
        var companySettings = new CompanySettings(companySettingsDict);
        var data = await dataTask;
        var companySeesDataIn = companySettings.CompanySeesDataIn;
        var currencySymbol = companySettings.CurrencySymbol;
        var companySeeDataValue = companySeesDataIn == CompanySeesDataInEnum.Value;
        var companySeeDataStdUnit = companySeesDataIn == CompanySeesDataInEnum.StandardUnit;
        dynamic dyCallSummaryObj = new ExpandoObject();
        dyCallSummaryObj.Productivity = numberSystemService.GetFormattedValue(data.Productivity);
        dyCallSummaryObj.PCDisplay = numberSystemService.GetFormattedValue(data.PC);
        dyCallSummaryObj.PC = data.PC;
        dyCallSummaryObj.TCDisplay = numberSystemService.GetFormattedValue(data.TC);
        dyCallSummaryObj.TC = data.TC;
        dyCallSummaryObj.OVC = data.OVC;
        dyCallSummaryObj.OVCDisplay = numberSystemService.GetFormattedValue(data.OVC);
        dyCallSummaryObj.ValueHeader = companySeeDataValue ? $"Net Value({currencySymbol})" : companySeeDataStdUnit ? "Qty( StdUnit )" : "Qty( SuperUnit )";
        dyCallSummaryObj.StdUnitOrNetValue = companySeeDataValue ? data.NetOrderInRevenue : companySeeDataStdUnit ? data.OrderInStdUnits : data.OrderInSuperUnits;
        dyCallSummaryObj.StdUnitOrNetValueDisplay = companySeeDataValue ? data.NetOrderInRevenueDisplay :
            companySeeDataStdUnit ? data.OrderInStdUnitsDisplay : data.OrderInSuperUnitsDisplay;
        return dyCallSummaryObj;
    }

    public async Task<DayStartTypeSummary> UserDaySummary(DateTime date, List<long> positionCodeIds, string level, SaleType saleType = SaleType.All)
    {
        var user = currentUser.LocalId;
        var role = currentUser.UserRole;
        var positionParams = positionCodeIds != null && positionCodeIds.Count != 0 ? string.Join("&", positionCodeIds.Select(id => $"positionCodeIds={id}")): null;
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/ds/dayUserSummaryUsingPositionsNew?companyId={currentUser.CompanyId}&date={date:MM/dd/yyyy}&userId={user}&userRole={role}&{positionParams}&level={level}&saleType={saleType}";
        var data = await resilientHttpClient.GetJsonAsync<DayStartTypeSummary>(dataUrl, appConfigSettings.reportApiToken);
        return data;
    }

    public async Task<MTDLMTDTarget> GetMTDLMTDTarget(DateTime date, long userId, PortalUserRole userRole)
    {
        var data = new MTDLMTDTarget();
        try
        {
            var user = userId > 0 ? userId : currentUser.LocalId;
            var role = userId > 0 ? userRole : currentUser.UserRole;
            var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/EmployeeTarget/GetTarget?companyId={currentUser.CompanyId}&userId={user}&userRole={role}&date={date:MM/dd/yyyy}";
            data = await resilientHttpClient.GetJsonAsync<MTDLMTDTarget>(dataUrl, appConfigSettings.reportApiToken);

            var result = new MTDLMTDTarget
            {
                mtdTarget = data.mtdTarget,
                mtdTargetDisplay = numberSystemService.GetFormattedValue(data.mtdTarget),
                lmtdTarget = data.lmtdTarget,
                lmtdTargetDisplay = numberSystemService.GetFormattedValue(data.lmtdTarget)
            };
            return result;
        }
        catch (Exception ex)
        {
            data.ErrorResponse = new RepositoryResponse { Id = 0, ExceptionMessage = ex.GetBaseException().Message, Message = "An Error Occurred while calling MTD LMTD Target summary API", IsSuccess = false };
            return data;
        }
    }

    public async Task<List<SecondaryOrderDmsDTO>> GetOTIFSummaryForGivenUserId(DateTime date, long userId, PortalUserRole userRole)
    {
        var data = new List<SecondaryOrderDmsDTO>();
        try
        {
            var mtdData = await mtdDateHelper.GetMTDDateData(date);
            var startDate = mtdData.MTD.StartDate;
            var endDate = date;
            var user = userId > 0 ? userId : currentUser.LocalId;
            var role = userId > 0 ? userRole : currentUser.UserRole;
            var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/dms-clickhouse/order-metrics?companyId={currentUser.CompanyId}&startDate={startDate.Value.Date:yyyy-MM-dd}&endDate={endDate.Date:yyyy-MM-dd}&userId={user}&userRole={role}";
            data = await resilientHttpClient.GetJsonAsync<List<SecondaryOrderDmsDTO>>(dataUrl, appConfigSettings.reportApiToken);

            return data;
        }
        catch (Exception ex)
        {
            return  [];
        }
    }

}
