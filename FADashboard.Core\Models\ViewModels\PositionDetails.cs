﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Models.ViewModels;

public class PositionDetails : PositionMin
{
    public string CodeId { get; set; }
    public string LevelName { get; set; }
    public string ParentCodeId { get; set; }
    public string ParentEntityName { get; set; }
    public long? ParentEntityId { get; set; }
    public long? ParentId { get; set; }
    public PositionCodeLevel? ParentLevel { get; set; }
    public string ParentLevelName { get; set; }
    public string ParentName { get; set; }
    public string AttachedTo { get; set; }
}

public class PositionMinParent
{
    public long EntityId { get; set; }
    public List<PositionDetails> Positions { get; set; }
}
