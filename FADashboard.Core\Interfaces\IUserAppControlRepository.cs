﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IUserAppControlRespository
{
    Task<RepositoryResponse> ActivateDeactivateRule(long ruleId, long companyId, bool action, CancellationToken ct = default);
    Task<RepositoryResponse> CreateRule(UserAppControlInput rule, long companyId, CancellationToken ct = default);
    Task<UserAppControlInput> GetRuleById(long ruleId, long companyId, CancellationToken ct = default);
    Task<List<UserAppControl>> GetRules(long companyId, bool includeDeactivate, CancellationToken ct = default);
    Task<RepositoryResponse> UpdateRule(UserAppControlInput rule, long companyId, CancellationToken ct = default);
}
