﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class CohortV2Service(ICurrentUser currentUser, ICohortV2Repository CohortV2Repository, IShopTypeRepository shopTypeRepository, IOutletMasterRepository outletMasterRepository) : RepositoryResponse
{
    private async Task<List<string>> GetChannelEnumsStringFromIds(List<long> channelIds)
    {
        if (channelIds.Count != 0)
        {
            var channelDict = (await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => ((int)s.Enum).ToString());
            return channelIds.Select(cId => channelDict.GetValueOrDefault(cId)).Where(p => p != null).ToList();
        }

        return [];
    }

    private async Task<List<string>> GetShopNamesFromShopTypeIds(List<long> shopTypeIds)
    {
        if (shopTypeIds.Count != 0)
        {
            var shopTypeDict = (await shopTypeRepository.GetShopTypes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.ShopTypeName);
            return shopTypeIds.Select(Id => shopTypeDict.GetValueOrDefault(Id)).Where(p => p != null).ToList();
        }

        return [];
    }

    private async Task<List<string>> GetSegmentationEnumsStringFromIds(List<long> segmentationIds)
    {
        if (segmentationIds.Count != 0)
        {
            var segDict = (await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.Segmentation.ToString());
            return segmentationIds.Select(segId => segDict.GetValueOrDefault(segId)).Where(p => p != null).ToList();
        }
        return [];
    }

    public async Task<RepositoryResponse> ActivateDeactivateCohort(long requestId, bool action, CancellationToken ct) => await CohortV2Repository.ActivateDeactivateCohort(requestId, action, currentUser.CompanyId, ct);
    public async Task<RepositoryResponse> CreateUpdateCohort(FilterConstraintDetailInput cohort, CancellationToken ct)
    {
        if (cohort.EntityType == 0)
        {
            if (cohort.Id == 0)
            {
                return await CohortV2Repository.CreateUserCohort(cohort, currentUser.CompanyId, ct);
            }
            else
            {
                return await CohortV2Repository.UpdateUserCohort(currentUser.CompanyId, cohort, ct);
            }
        }

        if (cohort.EntityType == FilterConstraintEntityType.Outlet)
        {
            cohort.OutletCohortConstraints.Channels = cohort.OutletCohortConstraints.ChannelsList != null ? await GetChannelEnumsStringFromIds(cohort.OutletCohortConstraints.ChannelsList) : [];
            cohort.OutletCohortConstraints.ShopTypes = cohort.OutletCohortConstraints.ShopTypesList != null ? cohort.OutletCohortConstraints.ShopTypesList : [];
            cohort.OutletCohortConstraints.Segmentations = cohort.OutletCohortConstraints.SegmentationsList != null ? await GetSegmentationEnumsStringFromIds(cohort.OutletCohortConstraints.SegmentationsList) : [];

            if (cohort.Distributor.DistributorIds.Count == 0)
            {
                cohort.DistributorConstraints.Channels = cohort.DistributorConstraints.ChannelsList?.Select(l => l.ToString()).ToList();
                cohort.DistributorConstraints.Segmentations = cohort.DistributorConstraints.SegmentationsList?.Select(l => l.ToString()).ToList();
            }

            if (cohort.Id == 0)
            {
                return await CohortV2Repository.CreateOutletCohort(cohort, currentUser.CompanyId, ct);
            }
            else
            {
                return await CohortV2Repository.UpdateOutletCohort(currentUser.CompanyId, cohort, ct);
            }

        }

        if (cohort.EntityType == FilterConstraintEntityType.Distributor)
        {
            cohort.DistributorConstraints.Channels = cohort.DistributorConstraints.ChannelsList?.Select(l => l.ToString()).ToList();
            cohort.DistributorConstraints.Segmentations = cohort.DistributorConstraints.SegmentationsList?.Select(l => l.ToString()).ToList();

            if (cohort.Id == 0)
            {
                return await CohortV2Repository.CreateDistributorCohort(cohort, currentUser.CompanyId, ct);
            }
            else
            {
                return await CohortV2Repository.UpdateDistributorCohort(currentUser.CompanyId, cohort, ct);
            }
        }

        throw new NotImplementedException();
    }

    public async Task<List<CohortV2View>> GetAllCohorts(bool showDeactive)
    {
        var result = await CohortV2Repository.GetAllCohorts(currentUser.CompanyId, showDeactive);
        return result;
    }

    public async Task<FilterConstraintById> GetById(long id, CancellationToken ct)
    {
        var result = await CohortV2Repository.GetById(id, currentUser.CompanyId, ct);
        return result;
    }

}
