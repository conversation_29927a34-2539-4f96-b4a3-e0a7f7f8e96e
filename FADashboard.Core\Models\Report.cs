﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class FlexibleReports
{
    public bool Deleted { get; set; }
    public long Id { get; set; }

    public string Link => $"FADashboard/ExcelData/FlexibleReport/{Id}";

    public string Name { get; set; }
}

public class Report
{
    public bool CanEmail { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string Description { get; set; }
    public DateTime EmailLocalDeliveryTime { get; set; }
    public Guid EncryptionKey { get; set; }
    public ReportFrequency Frequency { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string Link { get; set; }
    public string Name { get; set; }
    public bool OnlyProductTeamCanAttach { get; set; }
    public ReportCategory ReportCategory { get; set; }
    public ReportSectionHeader ReportSectionEnum { get; set; }
    public string ReportSectionName { get; set; }
    public EnumForReportAssembly ReportType { get; set; }
}

public class FlexibleReportsMin
{
    public long Id { get; set; }
    public string Name { get; set; }
    public int? ReportPerspective { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public bool IsDeactive { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string SubscribedRoles { get; set; }
    public string EmailSubscribedRoles { get; set; }
    public string EmailSubscribedToAdmins { get; set; }
    public EmailFrequency? EmailFrequency { get; set; }
    public bool IsEmailSubscriptionIsActive { get; set; }
}
