﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class CompanyKPIService(ICompanyKPIRepository companyKPIRepository, IKPIRepository kPIRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<CompanyKPIsList>> GetCompanyKPIList(bool showDeactive)
    {
        var companyKPI = await companyKPIRepository.GetCompanyKPIList(currentUser.CompanyId, showDeactive);
        return companyKPI;
    }

    public async Task<CompanyKPIs> GetCompanyKPIById(long id)
    {
        var companyKPI = await companyKPIRepository.GetCompanyKPIById(currentUser.CompanyId, id);
        return companyKPI;
    }

    public async Task<RepositoryResponse> ActivateDeactivateCompanyKPI(long id, bool action)
    {
        var companyKPI = await companyKPIRepository.ActivateDeactivateCompanyKPI(id, currentUser.CompanyId, action);
        return companyKPI;
    }

    private async Task<RepositoryResponse> IsValidCompanyKPI(CompanyKPIRequestInput companyKPI)
    {
        var allCompanyKPIs = await companyKPIRepository.GetCompanyKPIList(currentUser.CompanyId, false);
        if (companyKPI.Id != 0)
        {
            allCompanyKPIs = allCompanyKPIs.Where(p => p.Id != companyKPI.Id).ToList();
        }

        var companyKPINameList = allCompanyKPIs.Select(p => p.Name.NormalizeCaps()).ToList();

        if (companyKPINameList.Contains(companyKPI.Name.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = companyKPI.Id, ExceptionMessage = "CompanyKPI name is not unique", Message = "CompanyKPI creation/updation Failed!", IsSuccess = false,
            };
        }

        return GetSuccessResponse(companyKPI.Id, "CompanyKPI unique");
    }

    public async Task<RepositoryResponse> CreateUpdateCompanyKPI(CompanyKPIRequestInput companyKPI)
    {
        var checkValid = await IsValidCompanyKPI(companyKPI);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        var globalKPI = await kPIRepository.GetKPIById(companyKPI.KpiId);
        if (companyKPI.Id == 0)
        {
            return await companyKPIRepository.CreateCompanyKPI(companyKPI, globalKPI, currentUser.CompanyId);
        }

        return await companyKPIRepository.UpdateCompanyKPI(companyKPI, currentUser.CompanyId);
    }
}
