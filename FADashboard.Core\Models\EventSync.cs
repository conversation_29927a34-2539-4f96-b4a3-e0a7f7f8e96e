﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class EventSync
{
    public string Source { get; set; }
    public GeoLocation GeoLocation { get; set; }
    public long StartTime { get; set; }
    public long EndTime { get; set; }
    public Guid EventId { set; get; }
    public FAEventType Type { set; get; }
    public string SessionId { set; get; }
    public string Description { set; get; }
    public long? FAUnifyUserId { get; set; }
    public string FAUnifySource { get; set; }
    public DistributorStockDto DistributorStock { set; get; }
    public PrimaryOrderDto PrimaryOrder { get; set; }
    public bool IsUpdateOutletGeoLocation { get; set; }
    public long? PositionCodeId { get; set; }
    public ApprovalEngineRequesType? RequestType { get; set; }
}
