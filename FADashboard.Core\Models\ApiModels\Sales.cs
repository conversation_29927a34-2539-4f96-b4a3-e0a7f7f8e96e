﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class PCSale
{
    public long PrimaryCatId { get; set; }
    public string PrimaryCatName { get; set; }
    public decimal SaleInUnits { get; set; }
    public decimal SalePrice { get; set; }
}

public class PCSCSale
{
    public long PrimaryCatId { get; set; }
    public string PrimaryCatName { get; set; }
    public decimal SaleInUnits { get; set; }
    public decimal SalePrice { get; set; }
    public long SecondaryCatId { get; set; }
    public string SecondaryCatName { get; set; }
}

public class PCSum
{
    public long PrimaryCatId { get; set; }
    public string PrimaryCatName { get; set; }
    public decimal SaleInUnitsSum { get; set; }
    public decimal SalePriceSum { get; set; }
}

public class PositionCodeAndPCSCWiseSales
{
    public List<PCSCSale> PCSCSales { get; set; }
    public long? PositionId { get; set; }
    public PositionCodeLevel? PositionLevel { get; set; }
    public string PositionName { get; set; }
    public long? PositionUserId { get; set; }
    public string PositionUserName { get; set; }
}

public class PositionCodeAndPCSCWiseSalesWithSum
{
    public List<PositionCodeAndPCSCWiseSales> PositionCodeAndPCSCWiseSales { get; set; }
    public List<SCSum> SCSums { get; set; }
}

public class PositionCodeAndPCWiseSales
{
    public List<PCSale> PCSales { get; set; }
    public long? PositionId { get; set; }
    public PositionCodeLevel? PositionLevel { get; set; }
    public string PositionName { get; set; }
    public long? PositionUserId { get; set; }
    public string PositionUserName { get; set; }
}

public class PositionCodeAndPCWiseSalesWithSum
{
    public List<PCSum> PCSums { get; set; }
    public List<PositionCodeAndPCWiseSales> PositionCodeAndPCWiseSales { get; set; }
}

public class PositionManagerAndPCSCWiseSales
{
    public long? PositionId { get; set; }
    public PositionCodeLevel? PositionLevel { get; set; }
    public string PositionName { get; set; }
    public long? PositionUserId { get; set; }
    public string PositionUserName { get; set; }
    public long PrimaryCatId { get; set; }
    public string PrimaryCatName { get; set; }
    public decimal SaleCF { get; set; }
    public decimal SaleInSuperUnits { get; set; }
    public decimal SalePrice { get; set; }
    public double? SaleQuantity { get; set; }
    public long SecondaryCatId { get; set; }
    public string SecondaryCatName { get; set; }
    public string StandardUnit { get; set; }
}

public class SalesQtyAndValue
{
    public double OrderInRevenue { get; set; }
    public double OrderInStdUnits { get; set; }
    public double? OrderInSuperUnits { get; set; }
    public double OrderInUnits { get; set; }
    public double ProductWiseDiscount { get; set; }
    public decimal SchemeCashDiscount { get; set; }
}

public class SCSum
{
    public decimal SaleInUnitsSum { get; set; }
    public decimal SalePriceSum { get; set; }
    public long SecondaryCatId { get; set; }
    public string SecondaryCatName { get; set; }
}

public class UserAndPCSCWiseSalesQtyAndValue : SalesQtyAndValue
{
    public long PositionCodeId { get; set; }
    public long PrimaryCategoryId { get; set; }
    public long ProductId { get; set; }
    public long SecondaryCategoryId { get; set; }
    public long UserId { get; set; }
}
