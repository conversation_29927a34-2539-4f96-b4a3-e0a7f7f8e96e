﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs;

public class DistributorDTO
{
    public string Address { set; get; }
    public long? ChannelId { get; set; }
    public string ClientSideId { get; set; }
    public long CompanyId { get; set; }
    public string ContactNo { get; set; }
    public string DistributorCategory { get; set; }
    public int DistributorGrade { get; set; }
    public string DistributorType { get; set; }
    public string EmailId { get; set; }
    public string ErpId { get; set; }
    public string FSSAINumber { get; set; }
    public string GSTIN { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { set; get; }
    public bool IsGSTRegistered { get; set; }
    public string LocalName { get; set; }
    public string ManagerName { set; get; }
    public string Name { get; set; }
    public string PANNumber { get; set; }
    public string Aadhar { get; set; }
    public virtual DistributorDTO Parent { get; set; }
    public long? ParentId { get; set; }
    public string PinCode { set; get; }
    public string Place { get; set; }
    public string PlaceOfSupply { get; set; }
    public List<long> ProductDivisionIds { get; set; }
    public virtual Regions Region { get; set; }
    public long? RegionId { get; set; }
    public string RegionName { get; set; }
    public string SecondaryEmailId { get; set; }
    public string State { set; get; }
    public StockistType StockistType { get; set; }
    public long? SuperStockistId { get; set; }

    public string Zone { set; get; }
    public string WarehouseAddress { get; set; }
    public string WarehouseCity { get; set; }
    public string WarehouseState { get; set; }
    public string WarehouseId { get; set; }
    public string WarehouseName { get; set; }
    public int? CreditDuration { set; get; }

    public double? CreditValue { set; get; }
    public int? CreditInvoiceCount { set; get; }
    public int? IntransitPeriod { set; get; }
    public string SecurityDeposit { set; get; }

    public string DDNumber { set; get; }
    public string PaymentMode { set; get; }
    public DateTime? DateOfPayment { get; set; }

    public bool HaveSeperateProducts { get; set; }

    public long? DistributorSegmentationId { get; set; }

    public long? DistributorChannelId { get; set; }
    public bool IsAppUser { get; set; }
    public bool IsIDT { get; set; }
    public string IDTIds { get; set; }
    public DateTime? FSSAIExpiryDate { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public List<DistributorKycDetails> KycDocumentList { get; set; }
    public long? MappedBeats { get; set; }
    public decimal? CashDiscountLimit { get; set; }
    public string CityName { get; set; }

    public string MsmeNumber { get; set; }

    public List<WarehouseNamesWithErpId> MappedWarehouseNamesWithErpId { get; set; }
}

public class WarehouseNamesWithErpId
{
    public string WarehouseName { get; set; }
    public string WarehouseErpId { get; set; }
}
public class DistributorKycDetails
{
    public string KycDocumentType { get; set; }
    public string DocumentGuid { get; set; }
}

public class KycDocumentDetails
{
    public KycDocumentType KycDocumentType { get; set; }
    public Guid DocumentGuid { get; set; }
}
