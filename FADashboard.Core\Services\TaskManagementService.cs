﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class TaskManagementService(ICurrentUser currentUser,ITaskManagementRepository taskManagementRepository) : RepositoryResponse
{
    public async Task<List<EntityMin>> GetTaskFocusArea()
    {
        return await taskManagementRepository.GetTaskFocusArea(currentUser.CompanyId);
    }    
}
