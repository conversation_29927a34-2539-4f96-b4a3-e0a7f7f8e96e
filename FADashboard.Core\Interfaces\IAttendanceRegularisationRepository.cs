﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IAttendanceRegularisationRepository
{
    Task<RepositoryResponse> ApproveRejectAttendanceRegularisationRequest(long requestId, bool action);

    Task<AttendanceRegulariseRequest> GetAttendanceRegularizationRequestById(long id, long companyId);

    Task<List<Requests>> GetAttendanceRegularisationRequests(long userId, PortalUserRole userRole, long companyId, bool includeArchieved = false);
}
