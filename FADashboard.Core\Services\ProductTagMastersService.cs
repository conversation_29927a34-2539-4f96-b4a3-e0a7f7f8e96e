﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;
public class ProductTagMastersService(IProductTagMastersRepository productTagMastersRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<List<ProductTagMasterList>> GetProductTagList(bool showDeactive)
    {
        var productTagList = await productTagMastersRepository.GetProductTagList(currentUser.CompanyId, showDeactive);
        return productTagList;
    }
}
