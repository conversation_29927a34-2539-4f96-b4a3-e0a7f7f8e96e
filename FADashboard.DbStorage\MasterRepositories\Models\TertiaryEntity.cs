﻿using System.ComponentModel.DataAnnotations.Schema;
using AuditHelper;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("TertiaryEntities")]
public class TertiaryEntity : IAuditedEntity, IERPEntity
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string ErpId { get; set; }
    public long CompanyId { get; set; }
    [Audited]
    public string OwnersName { get; set; }
    [Audited]
    public string OwnersNo { get; set; }
    [Column("EmailId")]
    public string Email { get; set; }
    public string Address { get; set; }
    public string City { set; get; }
    public string State { get; set; }
    public string PinCode { get; set; }
    public string ImageId { set; get; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool IsDeleted { get; set; }
    public bool IsDeactive { get; set; }
}
