﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ProductList
{
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { set; get; }
    public int MBQ { get; set; }
    public string Name { get; set; }
    public string PcName { get; set; }
    public bool IsSaleable { get; set; }
    public double Price { get; set; }
    public string DisplayMRP { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public string ScName { get; set; }
    public long SecondaryCategoryId { get; set; }
    public string Unit { get; set; }
    public string VariantName { get; set; }
    public long? ProductGSTCategoryId { get; set; }
    public long? ProductCESSCategoryId { get; set; }
    public bool IsMarkDownMarginLogic { get; set; }
    public string AlternateCategory { get; set; }
    public string DisplayCategory { get; set; }
    public string ProductDivision { get; set; }
    public double? SuperUnitConversionFactor { get; set; }
    public ProductVisibilityTag VisibilityTag { get; set; }
    public string GSTCategoryName { get; set; }
}

public class ProductTotal
{
    public int Total { get; set; }
    public List<ProductList> ProductRecords { get; set; }
}
