using System;
using System.ComponentModel.DataAnnotations;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadSourceDto
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        [Required(ErrorMessage = "Source Name is required.")]
        [StringLength(255)]
        public string SourceName { get; set; }

        public string Description { get; set; }
        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        // Auditing fields
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public string CreatedByName { get; set; }
        public string UpdatedByName { get; set; }
    }

    public class LMSLeadSourceInput
    {
        public long Id { get; set; }
        [Required(ErrorMessage = "Source Name is required.")]
        [StringLength(255)]
        public string SourceName { get; set; }

        public string Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
    }
}
