﻿using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;

namespace FADashboard.DbStorage.Helpers;
public class SfaToFloSyncHelperSwastik(AppConfigSettings appConfigSettings)
{
    public async Task AddToFloMasterSyncQueueContinous(long companyId, List<long> ids, FloQueueEntityType entityType)
    {
        var queueHandler = new QueueHandler<SfaFloSync>(QueueType.FloMasterSyncQueueContinous, appConfigSettings.StorageConnectionString);
        await queueHandler.AddToQueue(new SfaFloSync { CompanyId = companyId, Ids = ids, EntityType = entityType });
    }

    public class SfaFloSync
    {
        public long CompanyId { get; set; }
        public List<long> Ids { get; set; }
        public FloQueueEntityType EntityType { get; set; }
    }
}
