﻿namespace FADashboard.Core.Models.ApiModels;

public class BeatPositionMapInfo
{
    public long Id { get; set; }
    public string Name { get; set; }
    public long? RegionId { get; set; }
    public string DistributorNames { get; set; }
    public long? TerritoryId { get; set; }
    public List<long?> PDIdsAttachedToBeats { get; set; }
    public List<long> PositionIdsList { get; set; }
    public List<long> SecondaryPositionIdsList { get; set; }
    public bool IsSecondaryMapping { get; set; }
    public long? MappedOutlets { get; set; }
    public bool IsTemporaryAttachment { get; set; }
}
