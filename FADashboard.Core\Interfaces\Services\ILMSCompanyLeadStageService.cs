﻿using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSCompanyLeadStageService
    {
        Task<LMSCompanyLeadStageDto> GetCompanyLeadStageByIdAsync(long id);
        Task<List<LMSCompanyLeadStageDto>> GetCompanyLeadStageByTemplateIdAsync(long leadTemplateId);
        Task<IEnumerable<LMSCompanyLeadStageDto>> GetAllCompanyLeadStagesAsync(long companyId);
        Task<IEnumerable<LMSCompanyLeadStageDto>> BulkUpsertStagesAsync(LMSCompanyLeadStageBulkInputDto inputDto, long companyId, long userId);
    }
}
