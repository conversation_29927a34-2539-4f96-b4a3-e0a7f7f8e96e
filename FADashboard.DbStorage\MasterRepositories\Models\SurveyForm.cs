﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("NewSurvey_JsonForm")]
public class Survey(long companyId, long createdBy)
{
    public Company Company { get; set; }

    public long CompanyId { get; set; } = companyId;

    [ForeignKey("Admin")] public long CreatedBy { get; set; } = createdBy;

    [Column(TypeName = "datetime2")] public DateTime CreatedOn { get; set; }

    public bool? Deleted { set; get; }

    [StringLength(500)] public string Description { get; set; }
    [StringLength(500)] public string DynamicDescription { get; set; }

    public bool Disable { get; set; }

    public DateTime? EditedOn { get; set; }

    //Database fields
    [Column("id")] public long ID { get; set; }

    [Column("isSkippable")] public bool IsSkippable { get; set; }

    [NotMapped] public IEnumerable<string> Outlets { get; set; }

    public List<QuestionGroup> QuestionGroups { get; set; }
    public bool SingleTime { get; set; }

    public SurveyType? SurveyType { get; set; }

    [Obsolete] public SurveyVisitType SurveyVisitType { set; get; }

    [StringLength(200)] public string Title { get; set; }
    public string SurveyConstraints { set; get; }

    public static Survey CreateDbModel(SurveyInput form, long companyId, long createdAt)
    {
        var dbSurvey = new Survey(companyId, createdAt)
        {
            ID = form.Id,
            Title = form.Title,
            Description = form.Description,
            DynamicDescription = form.DynamicDescription,
            Deleted = null,
            Disable = form.Disable,
            IsSkippable = form.IsSkippable,
            SingleTime = form.SingleTime,
            SurveyType = form.SurveyType,
            Outlets = form.Outlets,
            QuestionGroups = form.QuestionGroups.Select(f => new QuestionGroup
            {
                Deactivated = f.Deactivated,
                Deleted = f.Deleted,
                Description = f.Description,
                DynamicDescription = f.DynamicDescription,
                DisplayOrder = f.DisplayOrder,
                FormId = f.FormId,
                GroupType = f.GroupType,
                ID = f.ID,
                JsonFormId = f.JsonFormId,
                ShowCondition = f.ShowCondition,
                Title = f.Title,
                Questions = f.Questions.Select(q => new Question
                {
                    ID = q.ID,
                    Title = q.Title,
                    Hint = q.Hint,
                    Deactivated = q.Deactivated,
                    DefaultValue = q.DefaultValue,
                    Deleted = q.Deleted,
                    Description = q.Description,
                    DynamicDescription = q.DynamicDescription,
                    DisplayOrder = q.DisplayOrder,
                    DisplayWidth = q.DisplayWidth,
                    OutletMetadata = q.OutletMetadata,
                    QuestionGroupId = q.QuestionGroupId,
                    QuestionType = q.QuestionType,
                    Required = q.Required,
                    ValidationErrorMessage = q.ValidationErrorMessage,
                    ValidationRule = q.ValidationRule,
                    Choices = q.Choices.Select(c => new Choice
                    {
                        ID = c.ID,
                        Text = c.Text,
                        Value = c.Value,
                        Condition = c.Condition,
                        Deleted = c.Deleted,
                        IsSelected = c.IsSelected,
                        QuestionId = c.QuestionId
                    }).ToList()
                }).ToList(),
            }).ToList()
        };
        return dbSurvey;
    }
}
