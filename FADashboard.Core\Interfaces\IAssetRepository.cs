﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IAssetRepository
{
    Task<long?> GetSurveyResponseIdByAssetId(long assetId, long companyId);
    Task<List<AssetDetailModel>> GetAllAssetsInCompany(long companyId, bool includeDeactive);
    Task<Dictionary<long, AssetDetailModel>> GetAssetData(long companyId, List<long> ids);

    Task<RepositoryResponse> UpdateReferenceNumber(long companyId, long AssetOutletId, string ReferenceNumber);

    Task<RepositoryResponse> DeleteAssetMapping(long companyId, long AssetOutletId);

    Task<Dictionary<long, List<AssetDetailModel>>> GetAssetOutletData(long companyId, List<long> outletids);
    Task<RepositoryResponse> UpdateAssetOutletMapping(long companyId, long currentOutletId, long? newOutletId, long equipmentId, long assetOutletMappingId);
   Task<long> GetAssetOutletMappingCount(long companyId, PaginationFilter validFilter);
}
