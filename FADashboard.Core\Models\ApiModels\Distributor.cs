﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class DistributorList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string RegionName { get; set; }
    public string ErpId { get; set; }
    public StockistType StockistType { get; set; }
    public bool IsDeactive { get; set; }
    public long? ParentId { get; set; }
    public string ParentName { get; set; }
    public List<string> ProductDivision { get; set; }
    public virtual DistributorList Parent { get; set; }
    public Guid? LoginGuid { get; set; }
    public string EmailId { get; set; }
    public long? MappedBeats { get; set; }
    public long? MappedProductDivisions { get; set; }
    public string ContactNo { get; set; }

    public string MsmeNumber { get; set; }

}
public class DistributorOptionsList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public long? RegionId { get; set; }
    public StockistType StockistType { get; set; }
    public long? ParentId { get; set; }
    public string EmailId { get; set; }
    public string ErpId { get; set; }
    public List<WarehouseNamesWithErpIds> WarehouseNamesWithErpIds { get; set; }
}

public class WarehouseNamesWithErpIds
{
    public string WarehouseName { get; set; }
    public string WarehouseErpId { get; set; }
}
public class DistributorTotal
{
    public int Total { get; set; }
    public List<DistributorList> DistributorRecords { get; set; }
}
public class DistChannels
{
    public string Name { get; set; }
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string ErpId { get; set; }
    public bool Deleted { get; set; }
    public bool IsDeactive { get; set; }
}
public class DistSegmentations
{
    public string Name { get; set; }
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string ErpId { get; set; }
    public bool Deleted { get; set; }
    public bool IsDeactive { get; set; }
}
