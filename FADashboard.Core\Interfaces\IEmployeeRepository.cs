﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IEmployeeRepository
{
    Task<List<EmployeeDTO>> GetAllEmployeeMin();

    Task<List<EntityMin>> GetEmployeeMin(long companyId, bool isincludeDeactivateUsers = false);

    Task<RepositoryResponse> ActionEmployee(long employeeId, long companyId, bool action, string redirectUrl, CompanySettings companySettings);

    Task<RepositoryResponse> CreateEmployee(EmployeeInput employee, long companyId, CompanySettings companySettings, CancellationToken ct = default);

    Task<Employee> GetEmployee(long id, long companyId);

    Task<RepositoryResponse> GetActivationCode(long employeeId);

    Task<Employee> GetActiveEmployeeById(long Id, long companyIds);

    Task<Employee> GetActiveEmployeeByErpId(string erpId, long companyIds);

    Task<List<long>> GetActiveFieldUsersListUnderManager(long companyId, long userId, PortalUserRole userRole);

    Task<List<UserHierarchyModel>> GetActiveUserHierarchyUnder(long companyId, long userId, PortalUserRole userRole);

    Task<List<Employee>> GetAllEmployees(long companyId, bool includeDeactivate = false);
    Task<List<Employee>> GetAllEmployeesMin(long companyId, bool includeDeactivate = false);

    Task<List<Employee>> GetEmployeesForIds(long companyId, List<long> employeeIds, bool includeDeactivate = false);

    Task<List<long>> GetAllLevel1EmployeeIds(long companyId);

    Task<List<EntityMinUserWithStatus>> GetAllManagers(long companyId, bool includeDeactivate);

    Task<List<UserWithManagers>> GetAllUsersOfRole(long companyId, PortalUserRole globalSalesManager);

    Task<Dictionary<long, Employee>> GetDesignationForIds(long companyId, List<long> oldIds);

    Task<List<EntityMin>> GetDesignationList(long companyId);

    Task<List<EmployeeList>> GetEmployeeBySearch(long companyId, string searchString);

    Task<Dictionary<long, Employee>> GetEmployeeDictionary(long companyId, bool includeDeactivate = false);

    Task<Dictionary<long, Employee>> GetEmployeeDictionary(long companyId, List<long> empIds);

    Task<Dictionary<long, string>> GetEmployeeERPDict(List<long> employeeIds, long companyId);

    Task<List<EmployeeGeoHierarchy>> GetEmployeeGeoHierarchyNew(long companyId, long userId, PortalUserRole userRole, bool isInclueDeactiveUsers = false);

    Task<Dictionary<long, string>> GetEmployeeGUID(List<long> employeeIds, long companyId);

    Task<List<long>> GetEmployeeidListForGeoHierarchy(long companyId, long userId, PortalUserRole userRole, long geoHierarchyLevelId, GeographicalHierarchyLevel geoHierarchyLevel);

    Task<EntityMinWithRankRole> GetEmployeeMinById(long Id, long companyId);

    Task<List<EntityMin>> GetEmployeesForProductDivision(long productDivisionId, long companyId);

    Task<List<UserWithManagers>> GetEmployeesUnderUsersWithParents(long companyId, List<long> parentIds, PortalUserRole employeeRole);

    Task<List<long>> GetFieldUserIdsUnderManager(long companyId, PortalUserRole userRole, long userId);

    Task<Dictionary<long, long>> GetNewEmployeeId(List<long> ids, long companyId);

    Task<long> GetNewId(long companyId, PortalUserRole userRole, long userId);

    Task<long> GetNewIdForManager(long id, PortalUserRole userRole);

    Task<Dictionary<long, long>> GetNewIdsForOldTableIds(long companyId, List<long> oldIds, PortalUserRole userRole);

    Task<List<string>> GetOfficialWorkTypesForUserRole(long companyId, PortalUserRole portalUserRole);

    Task<long> GetOldIdForManager(long id, long companyId);

    Task<long?> GetOldIdForManagerNullable(long id, long companyId);

    Task<string> GetUserClientSideId(long id, long companyId);

    Task<Dictionary<long, string>> GetUserDesignation(long companyId);

    Task<string> GetUserName(long companyId, long userId);

    Task<Dictionary<long, PortalUserRole>> GetUserRoleForIds(List<long> userIds, long companyId);

    Task<List<UserWithManagers>> GetUsersUnderManagerWithNewManagerId(long companyId, long managerId, PortalUserRole portalUserRole, bool isincludeDeactivateUsers = false);

    Task<bool> IsActive(long Id, long companyId);

    Task<RepositoryResponse> RegisterUserDashboard(long employeeId, bool action, long companyId, EmployeeRoleIds employeeRoleIds, string redirectUrl, CompanySettings companySettings, string userName, bool isRoleBasedModules = false, bool twoFactorEnabled = false);

    Task<RepositoryResponse> UpdateEmployee(EmployeeInput employee, long companyId, string redirectUrl, CompanySettings companySettings, CancellationToken ct = default);

    Task<long> GetEmployeeCount(long companyId, PaginationFilter validFilter);

    Task<List<Employee>> GetAllEmployeesSQL(long companyId, PaginationFilter validFilter, int? positionLevel, int? userType, bool isDormant, int? dormantDays);

    Task<int> GetEmployeesCountSQL(long companyId, PaginationFilter validFilter, int? positionLevel, int? userType, bool isDormant, int? dormantDays);

    Task<List<Employee>> GetAllEmployeesSQLWithAppVersion(long companyId, PaginationFilter validFilter, int? positionLevel, int? userType, bool isDormant, int? dormantDays);

    Task<int> GetEmployeesCountSQLWithAppVersion(long companyId, PaginationFilter validFilter, int? positionLevel, int? userType, bool isDormant, int? dormantDays);
    Task<List<EntityMin>> GetTokenForEmp(long userId, long companyId);

    Task<EntityMin> GetLatestEmployeeToken(long userId, long companyId);

    Task<UserSummary> GetEmployeeSummary(long companyId, bool isDormant, int? dormantDays);

    Task<List<EntityMinWithStatus>> GetDSREmployeeMin(long companyId);

    Task<int> GetTotalTrainingUsers(long companyId);
    Task<List<EmployeeSummaryWithPositions>> GetEmployeeWithPosition(long companyId, int? dormantDays);
    Task<PagedResponse<List<Employee>>> GetAllEmployeesV2(long companyId, PaginationFilter validFilter, int? positionLevel, List<long> positionCodeIds);
    Task<Dictionary<long, string>> GetUserNames(List<long> userIds);
}
