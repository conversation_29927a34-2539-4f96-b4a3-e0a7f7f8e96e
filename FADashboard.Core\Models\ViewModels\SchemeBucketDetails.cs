﻿using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Models.ViewModels;

public class SchemeBucketDetails
{
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public List<EntityMin> Products { get; set; }
    public string ProductsNameString { get; set; }
    public List<SchemeMin> Schemes { get; set; }
}
