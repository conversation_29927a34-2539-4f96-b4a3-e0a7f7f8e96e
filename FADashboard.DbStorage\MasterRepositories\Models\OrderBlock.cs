﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class OrderBlock : IAuditedEntity
{
    public OrderBlock()
    {
        OrderBlockItems = [];
    }

    [Key] public long Id { get; set; }

    [Required] [MaxLength(1024)] public string Name { get; set; }

    [MaxLength(2000)] public string GeographyConstraints { get; set; }

    [MaxLength(2000)] public string OutletConstraints { get; set; }

    [Required] public DateTime CreatedAt { get; set; }

    [Required] public DateTime LastUpdatedAt { get; set; }

    [Required] [MaxLength(255)] public string CreationContext { get; set; }

    [Required] public long CompanyId { get; set; }

    [Required] public bool IsDeactive { get; set; }

    [ForeignKey("CompanyId")] public virtual Company Company { get; set; }
    public virtual ICollection<OrderBlockItems> OrderBlockItems { set; get; }
}

public class OrderBlockItems
{
    [Key] public long Id { get; set; }

    [Required] public DateTime ItemDate { get; set; }

    [Required] public bool Deleted { get; set; }

    public Guid Guid { get; set; }

    [Required] public long OrderBlockId { get; set; }

    [Required] public DateTime CreatedAt { get; set; }

    [Required] public DateTime LastUpdatedAt { get; set; }

    [ForeignKey("OrderBlockId")] public virtual OrderBlock OrderBlock { get; set; }
}
