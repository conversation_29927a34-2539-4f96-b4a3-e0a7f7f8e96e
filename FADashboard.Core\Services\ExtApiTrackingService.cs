﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class ExtApiTrackingService(IExternalApiTrackingRepository externalApiTrackingRepository, ICurrentUser currentUser)
{
    public static long ConvertDateTimeToLong(DateTime date) => long.Parse(date.ToString("yyyyMMdd"));

    public async Task<List<ExternalAPITrackingModel>> GetAPILogs(DateTime startDate, DateTime endDate)
    {
        var companyId = currentUser.CompanyId;
        var startDateKey = ConvertDateTimeToLong(startDate);
        var endDateKey = ConvertDateTimeToLong(endDate);
        var logs = await externalApiTrackingRepository.GetExternalAPITrackings(companyId, startDateKey, endDateKey);
        return logs;
    }
}
