﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.DateTimeHelpers;
using Library.ResilientHttpClient;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class EmployeeService(
    IEmployeeRepository employeeRepository,
    IPositionCodeRepository positionCodeRepository,
    IProductDivRepository productDivRepository,
    IDeviceRepository deviceRepository,
    ICurrentUser currentUser,
    IDayStartRepository dayStartRepository,
    ICompanySettingsRepository companySettingsRepository,
    IIdsRepository idsRepository,
    FAResilientHttpClient resilientHttpClient,
    ITeamUserMappingsRepository teamUserMappingsRepository,
    AppConfigSettings appConfigSettings = null) : RepositoryResponse
{
    public async Task<long> GetEmployeeCount(long companyId, PaginationFilter validFilter)
    {
        return await employeeRepository.GetEmployeeCount(companyId, validFilter);
    }

    private async Task<RepositoryResponse> IsValidEmployee(EmployeeInput employee, CompanySettings companySettings)
    {
        var trainingUserCount = await employeeRepository.GetTotalTrainingUsers(currentUser.CompanyId);
        var definedTrainingUsers = companySettings.MaximumNumberOfTrainingUsersAllowed;
        if (employee.IsTrainingUser && trainingUserCount >= definedTrainingUsers)
        {
            return new RepositoryResponse
            {
                Id = employee.Id, ExceptionMessage = "Training User Limit Exceeded", Message = "Employee Creation/Updation Failed!", IsSuccess = false,
            };
        }

        var employees = await employeeRepository.GetAllEmployeeMin();
        if (employee.Id != 0)
        {
            employees = employees.Where(p => p.Id != employee.Id).ToList();
        }

        var employeeERPIDs = employees.Where(p => p.CompanyId == currentUser.CompanyId).Select(p => p.ERPID.NormalizeCaps()).ToList();
        var employeePhoneNumbers = employees.Select(p => p.PhoneNo.NormalizeCaps()).ToList();
        if (!string.IsNullOrEmpty(employee.ContactNo))
        {
            if (employeePhoneNumbers.Contains(employee.ContactNo.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = employee.Id, ExceptionMessage = "Employee Mobile Number is not unique", Message = "Employee Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        if (!string.IsNullOrEmpty(employee.ErpId)) // Asana : Add Duplicate ERP check. Date: May 12,2023. link: https://app.asana.com/0/1203071525101825/1204571050943784/f
        {
            if (employeeERPIDs.Contains(employee.ErpId.NormalizeCaps()))
            {
                return new RepositoryResponse
                {
                    Id = employee.Id, ExceptionMessage = "Employee ERPID is not unique", Message = "Employee Creation/Updation Failed!", IsSuccess = false,
                };
            }
        }

        return new RepositoryResponse { Id = employee.Id, Message = "Employee Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateEmployee(long employeeId, long companyId, bool action, string redirectUrl)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        return await employeeRepository.ActionEmployee(employeeId, companyId, action, redirectUrl, companySettings);
    }

    public async Task<List<RepositoryResponse>> ActivateDeactivateListOfEmployees(List<long> employeeIds, string redirectUrl)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var responses = new List<RepositoryResponse>();
        foreach (var employeeId in employeeIds)
        {
            var response = await employeeRepository.ActionEmployee(employeeId, currentUser.CompanyId, false, redirectUrl, companySettings);
            responses.Add(response);
        }

        return responses;
    }

    public async Task<RepositoryResponse> CreateUpdateEmployee(EmployeeInput employee, string redirectUrl, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var checkValid = await IsValidEmployee(employee, companySettings);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (employee.Id == 0)
        {
            return await employeeRepository.CreateEmployee(employee, currentUser.CompanyId, companySettings, ct);
        }

        await idsRepository.UpdateRoleIds(employee.RoleIds, currentUser.CompanyId, employee.Id);
        return await employeeRepository.UpdateEmployee(employee, currentUser.CompanyId, redirectUrl, companySettings, ct);
    }

    public async Task<RepositoryResponse> GetActivationCode(long employeeId) => await employeeRepository.GetActivationCode(employeeId);

    public async Task<List<EmployeeList>> GetAllEmployees(long companyId)
    {
        var allEmps = await employeeRepository.GetAllEmployees(companyId, true);
        var allEmpDict = allEmps.ToDictionary(e => e.Id, e => e.Name);
        var employeeIdList = allEmps.Select(e => e.Id).ToList();
        var positionsEmployee = (await positionCodeRepository.GetPositionByEmployee(companyId, employeeIdList)).GroupBy(p => p.EntityId).ToDictionary(p => p.Key, p => p.ToList());
        var appVersionNumberDict = (await deviceRepository.GetDeviceForEmployeeList(companyId, [.. allEmpDict.Keys])).ToDictionary(d => d.EmployeeId, d => d.AppVersionNumber);
        return allEmps.Select(g => new EmployeeList
        {
            Id = g.Id,
            Name = g.Name,
            ErpId = g.ErpId,
            Rank = g.Rank,
            EmailId = g.EmailId,
            RegionName = g.Region,
            ContactNo = g.ContactNo,
            IsActive = g.IsActive,
            LoginGuid = g.LoginGuid,
            AppVersionNumber = appVersionNumberDict.TryGetValue(g.Id, out var value) ? value : null,
            PositionsWithParent = positionsEmployee.GetValueOrDefault(g.Id),
            ZoneName = g.Zone,
            UserType = g.UserType,
            IsFieldAppuser = g.IsFieldAppuser,
            IsBillable = g.IsBillable,
            IsOrderBookingDisabled = g.IsOrderBookingDisabled,
            IsVacant = g.IsVacant
        }).ToList();
    }

    public async Task<List<EmployeeListMin>> GetAllEmployeesMin(long companyId)
    {
        var allEmps = await employeeRepository.GetAllEmployeesMin(companyId);
        var employeeIdList = allEmps.Select(e => e.Id).ToList();
        var positionsEmployee = (await positionCodeRepository.GetPositionByEmployee(companyId, employeeIdList)).GroupBy(p => p.EntityId).ToDictionary(p => p.Key, p => p.ToList());
        return allEmps.Select(g => new EmployeeListMin
        {
            UserId = g.Id,
            UserName = g.Name,
            CodeId = positionsEmployee.GetValueOrDefault(g.Id)?.First()?.Positions.First()?.CodeId,
            LevelName = positionsEmployee.GetValueOrDefault(g.Id)?.First()?.Positions.First()?.LevelName
        }).ToList();
    }

    public async Task<List<EmployeeList>> GetEmployeesInfoByIds(long companyId, List<long> employeeIds)
    {
        var allEmps = await employeeRepository.GetEmployeesForIds(companyId, employeeIds);
        var employeeIdList = allEmps.Select(e => e.Id).ToList();
        var positionsEmployee = (await positionCodeRepository.GetPositionByEmployee(companyId, employeeIdList)).GroupBy(p => p.EntityId).ToDictionary(p => p.Key, p => p.ToList());
        return allEmps.Select(g => new EmployeeList
        {
            Id = g.Id,
            Name = g.Name,
            ErpId = g.ErpId,
            Rank = g.Rank,
            EmailId = g.EmailId,
            RegionName = g.Region,
            ContactNo = g.ContactNo,
            IsActive = g.IsActive,
            LoginGuid = g.LoginGuid,
            PositionsWithParent = positionsEmployee.GetValueOrDefault(g.Id),
            ZoneName = g.Zone,
            UserType = g.UserType,
            IsFieldAppuser = g.IsFieldAppuser,
            IsBillable = g.IsBillable,
            IsOrderBookingDisabled = g.IsOrderBookingDisabled,
            IsVacant = g.IsVacant
        }).ToList();
    }

    public async Task<PagedResponse<List<EmployeeList>>> GetAllEmployees(PaginationFilter validFilter, int? positionLevel, int? userType, bool isDormant, int? dormantDays)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var compaySeeAppVersionInUserManagement = companySettings.CompaySeeAppVersionInUserManagement;
        var allEmps = compaySeeAppVersionInUserManagement
            ? await employeeRepository.GetAllEmployeesSQLWithAppVersion(currentUser.CompanyId, validFilter, positionLevel, userType, isDormant, dormantDays)
            : await employeeRepository.GetAllEmployeesSQL(currentUser.CompanyId, validFilter, positionLevel, userType, isDormant, dormantDays);
        var totalRecords = compaySeeAppVersionInUserManagement
            ? await employeeRepository.GetEmployeesCountSQLWithAppVersion(currentUser.CompanyId, validFilter, positionLevel, userType, isDormant, dormantDays)
            : await employeeRepository.GetEmployeesCountSQL(currentUser.CompanyId, validFilter, positionLevel, userType, isDormant, dormantDays);
        if (validFilter.ShowDeactive)
        {
            var deactiveData = allEmps.Select(g => new EmployeeList { Id = g.Id, Name = g.Name, ErpId = g.ErpId, RegionName = g.Region }).ToList();
            var deactiveEmployeeReponse = PaginationHelper.CreatePagedReponse(deactiveData, validFilter, totalRecords);
            return deactiveEmployeeReponse;
        }

        //TODO: App Version and Postion should be fetched in sql only
        var employeeIdList = allEmps.Select(e => e.Id).ToList();
        var positionsEmployee = (await positionCodeRepository.GetPositionByEmployee(currentUser.CompanyId, employeeIdList)).GroupBy(p => p.EntityId).ToDictionary(p => p.Key, p => p.ToList());
        var employees = allEmps.Select(g => new EmployeeList
        {
            Id = g.Id,
            Name = g.Name,
            ErpId = g.ErpId,
            Rank = g.Rank,
            EmailId = g.EmailId,
            RegionName = g.Region,
            ContactNo = EncryptionHelper.EncryptString(g.ContactNo),
            IsActive = g.IsActive,
            LoginGuid = g.LoginGuid,
            AppVersionNumber = g.AppVersionNumber,
            PositionsWithParent = positionsEmployee.GetValueOrDefault(g.Id),
            ZoneName = g.Zone,
            UserType = g.UserType,
            IsFieldAppuser = g.IsFieldAppuser,
            IsBillable = g.IsBillable,
            IsOrderBookingDisabled = g.IsOrderBookingDisabled,
            IsVacant = g.IsVacant,
            MappedBeats = g.MappedBeats != null ? g.MappedBeats : 0,
            IsTrainingUser = g.IsTrainingUser,
            AlertSource = g.AlertSource
        }).ToList();
        var pagedReponse = PaginationHelper.CreatePagedReponse(employees, validFilter, totalRecords);
        return pagedReponse;
    }
    public async Task<PagedResponse<List<Employee>>> GetAllEmployeesV2(PaginationFilter validFilter, int? positionLevel, List<long> positionCodeIds)
    {
        var employees = await employeeRepository.GetAllEmployeesV2(currentUser.CompanyId, validFilter, positionLevel, positionCodeIds);
        return employees;
    }

    public async Task<List<EntityMinIncludeParent>> GetAllEmployeesOfRole(PortalUserRole userRole)
    {
        var allEmps = await employeeRepository.GetAllUsersOfRole(currentUser.CompanyId, userRole);
        return allEmps.Select(g => new EntityMinIncludeParent { Id = g.Id, Name = g.Name, ParentId = g.ParentId, ParentName = g.ParentName }).ToList();
    }


    public async Task<EmployeeView> GetEmployeeById(long employeeId, long companyId)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var empDetails = await employeeRepository.GetEmployee(employeeId, companyId);
        var roleIds = await idsRepository.GetRoleIdsForUser(employeeId, companyId);
        var empTeamDict = await teamUserMappingsRepository.GetTeamPlayerTeamIDDict(companyId, employeeId);
        var empListfake = new List<long> { employeeId };
        var productDivisionDict = new Dictionary<long, List<long>>();
        var pcIds = new List<long>();

        productDivisionDict = await productDivRepository.GetEmployeeWiseProductDiv(companyId, empListfake);

        if (companySettings.CompanyUsesPrimaryPositionCodeOnly)
        {
            pcIds = (await positionCodeRepository.GetUserPositionCodeMin(employeeId, companyId)).Select(p => p.Id).ToList();

            if (pcIds.Any())
            {
                productDivisionDict = await positionCodeRepository.GetPositionWiseProductDiv(companyId, pcIds);
            }
        }

        var positionsEmployeeList = await positionCodeRepository.GetPositionByEmployee(companyId, empListfake);
        var positionsEmployee = positionsEmployeeList.FirstOrDefault();

        var retunModel = new EmployeeView
        {
            Id = empDetails.Id,
            Name = empDetails.Name,
            ErpId = empDetails.ErpId,
            DesignationId = empDetails.DesignationId,
            ContactNo = empDetails.ContactNo,
            DateOfJoining = empDetails.DateOfJoining,
            EmailId = empDetails.EmailId,
            LocalName = empDetails.LocalName,
            IsFieldAppuser = empDetails.IsFieldAppuser,
            UserType = empDetails.UserType,
            IsActive = empDetails.IsActive,
            ProductDivisionMapping = !companySettings.CompanyUsesPrimaryPositionCodeOnly
                                         ? productDivisionDict.GetValueOrDefault(empDetails.Id)
                                         : (pcIds.Any() ? productDivisionDict.GetValueOrDefault(pcIds.First()) : null),
            RegionId = empDetails.RegionId,
            AllowOrderBooking = !empDetails.IsOrderBookingDisabled,
            PositionsWithParent = positionsEmployee,
            CompanyZoneId = empDetails.CompanyZoneId,
            EmployeeAttributeText1 = empDetails.EmployeeAttributeText1,
            EmployeeAttributeText2 = empDetails.EmployeeAttributeText2,
            EmployeeAttributeBoolean1 = empDetails.EmployeeAttributeBoolean1,
            EmployeeAttributeBoolean2 = empDetails.EmployeeAttributeBoolean2,
            EmployeeAttributeNumber1 = empDetails.EmployeeAttributeNumber1,
            EmployeeAttributeNumber2 = empDetails.EmployeeAttributeNumber2,
            EmployeeAttributeDate = empDetails.EmployeeAttributeDate,
            UserRole = empDetails.UserRole,
            LoginGuid = empDetails.LoginGuid,
            RoleIds = roleIds,
            IsTrainingUser = empDetails.IsTrainingUser,
            JourneyPlanEntity = empDetails.JourneyPlanEntity.ToString(),
            //TeamId = empTeamDict.GetValueOrDefault(empDetails.Id),
            AttendanceNormsTeamId = empTeamDict.TryGetValue(TeamModuleType.AttendanceNorms, out var norm)? norm: (empTeamDict.TryGetValue(TeamModuleType.Both, out var both) ? both : null),
            BattleGroundTeamId = empTeamDict.TryGetValue(TeamModuleType.FABattleground,out var game) ? game: (empTeamDict.TryGetValue(TeamModuleType.Both,out var both1)? both1: null),};

        return retunModel;
    }


    public async Task<List<EmployeeList>> GetEmployeeBySearch(long companyId, string searchString)
    {
        var employees = await employeeRepository.GetEmployeeBySearch(companyId, searchString);
        return employees;
    }

    public async Task<List<EntityMin>> GetEmployeeDesignationList(long companyId)
    {
        var empDesignation = await employeeRepository.GetDesignationList(companyId);
        return empDesignation;
    }

    public async Task<List<EntityMin>> GetEmployeesForProductDivision(long productDivisionId, long companyId)
    {
        var users = await employeeRepository.GetEmployeesForProductDivision(productDivisionId, companyId);
        return users;
    }

    public async Task<List<EntityMinIncludeParent>> GetEmployeesUnderUsers(long companyId, List<long> parentIds, PortalUserRole employeeRole)
    {
        var employees = await employeeRepository.GetEmployeesUnderUsersWithParents(companyId, parentIds, employeeRole);
        return employees.Select(e => new EntityMinIncludeParent { Id = e.Id, Name = e.Name, ParentId = e.ParentId, ParentName = e.ParentName }).ToList();
    }

    public async Task<List<string>> GetOfficialWorkTypesForUserRole(long companyId, PortalUserRole portalUserRole)
    {
        var officialWorkTypes = await employeeRepository.GetOfficialWorkTypesForUserRole(companyId, portalUserRole);
        return officialWorkTypes;
    }

    public async Task<RepositoryResponse> RegisterUserDashboard(long userId, bool action, EmployeeRoleIds employeeRoleIds, string redirectUrl, bool twoFactorEnabled, string userName)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var isRoleBasedModules = companySettings.CompanyUsesRoleBasedAccessRightsModule;
        var userInfo = await employeeRepository.GetEmployeeMinById(userId, currentUser.CompanyId);
        var users = await employeeRepository.RegisterUserDashboard(userId, action, currentUser.CompanyId, employeeRoleIds, redirectUrl, companySettings, userName, isRoleBasedModules, twoFactorEnabled);
        return users;

    }

    public async Task<List<EntityMin>> GetTokenForEmp(long userId)
    {
        var empTokens = await employeeRepository.GetTokenForEmp(userId, currentUser.CompanyId);
        return empTokens;
    }

    public async Task<List<Fedns>> MarkEndDayForAllSessionsNS(long userId)
    {
        var allOpenSessionForEmployee = await dayStartRepository.GetAllOpenDayStartForEmployee(currentUser.CompanyId, userId);
        var empLatestToken = await employeeRepository.GetLatestEmployeeToken(userId, currentUser.CompanyId);
        var rows = new List<Fedns>();
        if (allOpenSessionForEmployee.Count != 0)
        {
            foreach (var session in allOpenSessionForEmployee)
            {
                if (empLatestToken == null)
                {
                    return default;
                }

                rows.Add(await ForceEndDayNS(session.SessionId, empLatestToken.Name));
            }
        }

        return rows;
    }

    private async Task<Fedns> ForceEndDayNS(Guid? sessionId, string employeeToken)
    {
        var sessionIdString = sessionId?.ToString().Replace("{", "").Replace("}", "");
        if (sessionIdString == null)
        {
            return new Fedns { SessionId = null, Error = "App Api error", Message = "Session Not Found" };
        }

        var dayEndTime = DateTime.UtcNow;

        //creating model for NS DayEnd.
        var model = new
        {
            SessionId = sessionIdString,
            IsForcedDayEnd = true,
            Time = dayEndTime.ToUnixTime(),
            DayEndType = (int)DayEndType.ForcedViaURL,
            IgnoreNSErrors = true
        };
        try
        {
            var dataUrl = $"{appConfigSettings.NsAppApiBaseUrl}/api/Day/forceEnd";
            await resilientHttpClient.PostJsonAsync<RepositoryResponse>(dataUrl, employeeToken, model);

            return new Fedns { SessionId = sessionIdString, Error = "", Message = "SuccessFully Marked end Day" };
        }
        catch (Exception ex)
        {
            return new Fedns { SessionId = sessionIdString, Error = "App Api Error", Message = ex.Message, };
        }
    }

    public async Task<UserSummary> GetEmployeeSummary(bool isDormant, int? dormantDays) => await employeeRepository.GetEmployeeSummary(currentUser.CompanyId, isDormant, dormantDays);
    public async Task<List<EmployeeSummaryWithPositions>> GetDormantUserList(int? dormantDays)
    {
        var empolyeeWithPosition = await employeeRepository.GetEmployeeWithPosition(currentUser.CompanyId, dormantDays);
        var dayRecords = await dayStartRepository.GetDayStartsData(currentUser.CompanyId, dormantDays);
        var dayRecordEmployeeIds = new HashSet<long>(dayRecords);
        var dormantUsers = empolyeeWithPosition
                                  .Where(r => !dayRecordEmployeeIds.Contains(r.EmployeeId))
                                  .Select(r => new EmployeeSummaryWithPositions
                                  {
                                      EmployeeId = r.EmployeeId,
                                      EmployeeName = r.EmployeeName,
                                      UserType = r.UserType,
                                      ErpId = r.ErpId,
                                      RegionName = r.RegionName,
                                      ContactNo = r.ContactNo,
                                      PositionName = r.PositionName,
                                      PositionLevel = r.PositionLevel,
                                      IsActive = r.IsActive
                                  }).ToList();

        return dormantUsers;
    }

}
