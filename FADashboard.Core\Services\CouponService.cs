﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class CouponService(ICouponRepository couponRepository) : RepositoryResponse
{
    public async Task<OrderWithCoupons> GetAllCouponsForOrder(long companyId, long orderId)
    {
        var managers = await couponRepository.GetAllCouponsForOrder(companyId, orderId);
        return managers;
    }

    public async Task<RepositoryResponse> UpdateCouponStatus(long companyId, long orderId, long couponId) => await couponRepository.UpdateCouponStatus(companyId, orderId, couponId);
}
