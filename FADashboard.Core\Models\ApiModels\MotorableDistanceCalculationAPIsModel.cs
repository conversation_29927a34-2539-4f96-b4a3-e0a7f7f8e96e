﻿namespace FADashboard.Core.Models.ApiModels;

public class MotorableDistanceCalculationAPIsModel
{
    public long Id { get; set; }
    public string ApiType { get; set; }
    public string ApiLink { get; set; }
    public string ApiUserName { get; set; }
    public string Api<PERSON>ey { get; set; }
    public string Activity { get; set; }
    public string PositionLevel { get; set; }
    public string UserLevel { get; set; }
    public string FloAPIToken { get; set; }
    public string WorkFlowDetails { get; set; }
    public List<WorkFlowDetail> WorkFlowDetailsValues { get; set; }
    public List<long> PositionLevelValues { get; set; }
    public List<long>? UserLevelValues { get; set; }
    public List<string> ActivityValues { get; set; }
}

public class WorkFlowDetail
{
    public string ReportId { get; set; }
    public string WorkFlow { get; set; }
}
