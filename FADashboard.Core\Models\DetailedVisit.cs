﻿// Copyright (c) FieldAssist.All Rights Reserved.


using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace FADashboard.Core.Models
{
    public class DetailedVisit
    {
        public Guid VisitGuid { get; set; }
        public long VisitId { get; set; }
        public string InvoiceNumber { get; set; }
        public string EmployeeName { get; set; }
        public Guid EmployeeGuid { get; set; }
        public string DistributorErpId { get; set; }
        public string SuperStockistName { get; set; }
        public string SuperStockistErpId { get; set; }
        public string EmployeeDesignation { get; set; }
        public string EmpoyeeERPId { get; set; }
        public string EmployeePhoneNumber { get; set; }
        public string UserType { get; set; }
        public string EmployeeType { get; set; }
        public string OutletName { get; set; }
        public string OutletERPId { get; set; }
        public bool IsFocused { get; set; }
        public string BeatName { get; set; }
        public string BeatERPId { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool IsCallReviewed { get; set; }
        public bool? Productive { get; set; }
        public bool? Valid { get; set; }
        public string NoSalesReason { get; set; }
        public string RemarksManagement { set; get; }
        public string RemarksDistributor { set; get; }
        public string RemarksOther { set; get; }
        public DateTimeOffset Time { get; set; }
        public DateTimeOffset SyncTime { get; set; }
        public double Discount { get; set; }
        public double OrderInUnits { get; set; }
        public double? OrderInValue { get; set; }
        public decimal? TotalGST { get; set; }
        public bool IsVerified { get; set; }
        public string CompanyName { get; set; }

        public decimal? Vat { get; set; }
        public double? TotalDiscount { get; set; }
        public double? NetValue { get; set; }
        public double? GrossValue { get; set; }
        public IEnumerable<DetailedSale> Sales { get; set; }
        public bool OutOfTurn { get; set; }
        public bool IsTelephonic { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public VisitOutlet Outlet { get; set; }

        public string EmployeeLocalName { get; set; }
        public string RouteErpId { get; set; }
        public DateTime CallStartTime { get; set; }
        public DateTime CallEndTime { get; set; }
        public bool IsNewOutlet { get; set; }
        public string ModeOfPayment { get; set; }
        public string Position { get; set; }
        public string PositionCode { get; set; }
        public string VanName { get; set; }

        public IEnumerable<VisitInternationDiscount> InternationDiscounts { get; set; }
        public string SecondarySalesType { get; set; }
        public bool IsOVC { get; set; }
        public string NoSaleReasonCategory { get; set; }
        public string VanChasisNumber { get; set; }
        public string WarehouseErpId { get; set; }
        public decimal Excise { get; set; }
        public string OrderSource { get; set; }
        public string AttributeKey1 { get; set; }
        public string AttributeKey2 { get; set; }
        public string AttributeKey3 { get; set; }
        public long? CompanyId { get; set; }
    }

    public class VisitInternationDiscount
    {
        public string ProductERPId { get; set; }
        public string ProductName { get; set; }
        public string Variant { get; set; }
        public string Material { get; set; }

        public double? FirstLevelDiscountAmount { set; get; }
        public double? SecondLevelDiscountAmount { set; get; }

        public double FOCQty { get; set; }
        public double? FOCSaleValue { get; set; }
    }

    public abstract class VistOutletBase
    {
        public string OutletName { get; set; }
        public string ShopType { set; get; }
        public string ShopTypeErpId { get; set; }
        public string ChannelErpId { get; set; }
        public string OutletChannelName { get; set; }
        public string Address { get; set; }
        public string FormattedAddress { get; set; }
        public string OwnersName { get; set; }
        public string OwnersNo { get; set; }
        public string TIN { get; set; }
        public string GSTIN { get; set; }
        public string PAN { set; get; }
        public string Aadhar { get; set; }
        public string Email { get; set; }
        public string PinCode { get; set; }
        public string MarketName { get; set; }
        public string City { get; set; }
        public string SubCity { get; set; }
        public string State { get; set; }
        public bool GSTRegistered { get; set; }
        public string BankAccountNumber { get; set; }
        public string AccountHoldersName { get; set; }
        public string IFSCCode { get; set; }
        public string AttributeText1 { get; set; }
        public string AttributeText2 { get; set; }
        public string AttributeText3 { get; set; }
        public string SegmentationDisplayName { get; set; }
        public int Segmentation { get; set; }
        public string SegmentationErpId { get; set; }
        public string AttributeText4 { get; set; }

        [Range(typeof(decimal), "0", "50")] public double? AttributeNumber1 { get; set; }

        [Range(typeof(decimal), "0", "50")] public double? AttributeNumber2 { get; set; }

        [Range(typeof(decimal), "0", "50")] public double? AttributeNumber3 { get; set; }

        [Range(typeof(decimal), "0", "50")] public double? AttributeNumber4 { get; set; }

        public bool? AttributeBoolean1 { get; set; }
        public bool? AttributeBoolean2 { get; set; }
        public DateTime? AttributeDate1 { get; set; }
        public DateTime? AttributeDate2 { get; set; }
        public long? BillToId { get; set; }
        public string BillingAlternateAddress { get; set; }
        public string BillingAlternateAddressErp { get; set; }

        public long? ShippedToId { get; set; }
        public string ShippingAlternateAddress { get; set; }
        public string ShippingAlternateAddressErp { get; set; }
    }

    public class VisitOutlet : VistOutletBase
    {
        public string OutletGuid { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string PlaceOfDelivery { get; set; }
    }

    public class ErpIdUpdateModel
    {
        public string OutletErpId { get; set; }
    }

    public class GuidBlockModel
    {
        public string OutletGuid { get; set; }
        public bool IsBlocked { get; set; }
    }

    public class DetailedSale
    {
        public string ProductERPId { get; set; }
        public string ProductName { get; set; }
        public string ProductDivision { get; set; }
        public string Variant { get; set; }
        public string Material { get; set; }
        public double Quantity { get; set; }
        public double? Price { get; set; }
        public double? PTD { get; set; }
        public string OrderType { get; set; }
        public double? Discount_Product { get; set; }
        public decimal SchemeCashDiscount { get; set; }
        public double SchemeQuantity { get; set; }
        public double? SuggestiveQuantity { get; set; }
        public string SchemeErpId { get; set; }
        public string SchemeName { get; set; }
        public string SchemeDescription { get; set; }
        public long VisitId { get; set; }
        public string AlternateCategory { get; set; }
        public string DistributorErpId { get; set; }
        public double? OriginalPTR { get; set; }
        public string DistributorType { get; set; }
        public string ProductUnit { get; set; }
        public decimal? CGST { get; set; }
        public decimal? SGST { get; set; }
        public decimal? IGST { get; set; }
        public decimal? VAT { get; set; }
        public long? ProductId { get; set; }
        public string SchemeCategory { get; set; }
        public string FAUnifySource { get; set; }
        public bool IsProductMustSell { get; set; }
        public bool IsFocused { get; set; }
        public bool IsAssorted { get; set; }
        public double? FirstLevelDiscountAmount { set; get; }
        public double? SecondLevelDiscountAmount { set; get; }
        public decimal Excise { get; set; }

        public List<DetailedScheme> Schemes { get; set; }
        public double StandardUnitConversionFactor { get; set; }
        public double Pieces { get; set; }
        public double Cases { get; set; }
        public double? CasePrice { get; set; }
        public double? ProductWiseTotalSales { get; set; }
        public AdditionalProductAttributes AdditionalProductAttributes { get; set; }
    }

    public class DetailedScheme
    {
        public string ErpId { get; set; }
        public string ProductErpId { get; set; }
        public string Category { get; set; }
        public double Quantity { get; set; }
        public decimal Value { get; set; }
        public string SchemeName { get; set; }
        public string SchemeDescription { get; set; }
    }

    public class AdditionalProductAttributes
    {
        public double StandardUnitConversionFactor { get; set; }
        public double StandardUnit { get; set; }
        public double? CasePrice { get; set; }
        public double? ProductWiseTotalSales { get; set; }
    }
}
