﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IPerfectStoreRepository
{
    Task<List<PerfectStoreList>> GetPerfectStoreList(long companyId, bool showDeactive);

    Task<PerfectStoreModel> GetPerfectStoreById(long companyId, long id);

    Task<RepositoryResponse> ActivateDeactivatePerfectStore(long id, long companyId, bool action);

    Task<RepositoryResponse> CreatePerfectStore(PerfectStoreModel perfectStore, long companyId);

    Task<List<PerfectStoreList>> GetPerfectCallList(long companyId, bool showDeactive);
}
