﻿using Libraries.CommonEnums;
using Library.DateTimeHelpers;

namespace FADashboard.Core.Models.ApiModels.QuickViz;

public class ChartDataInput
{
    public long CompanyId { get; set; }
    public List<long> PositionIds { get; set; }
    public int PositionCodeLevel { get; set; }
    public int? PositionLevelFilter { get; set; }
    public long ChartId { get; set; }
    public DateRangeModel DateRangeModel { get; set; }
    public long GeoId { get; set; }
    public string GeoType { get; set; }
    public bool IsNewDashboard { get; set; }
    public List<long> PositionUserIds { get; set; }
    public int? PositionUserLevel { get; set; }
    public string FilterName { get; set; }
    public long FilterId { get; set; }
    public string BaseFilterName { get; set; }
    public long BaseFilterId { get; set; }
    public string DrillDownDimension { get; set; }
    public string DrillDownDimensionDisplay { get; set; }
    public List<long> GeoIds { get; set; }
    public long UserId { get; set; }
    public List<long> ProductIds { get; set; }
    public ProductHierarchy ProductType { get; set; }
    public bool usePositionFilter { get; set; }
    public bool usePositionUserFilter { get; set; }
}
