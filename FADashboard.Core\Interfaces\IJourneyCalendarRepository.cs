﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IJourneyCalendarRepository
{
    Task<List<JourneyCalendarModel>> GetJourneyCalendars();
    Task<RepositoryResponse> CreateJourneyCalendar(JourneyCalendarModel journeyCalendar);
    Task<JourneyCalendarModel> GetJourneyCalendarById(long id);
    Task<RepositoryResponse> ActivateDeactivateJourneyCalendar(long id, bool action);
    Task<RepositoryResponse> UpdateJourneyCalendar(JourneyCalendarModel journeyCalendar);
    Task<RepositoryResponse> DeleteJourneyWeeksForCalendar(long journeyCalenderId);
    Task<RepositoryResponse> CreateJourneyWeeksForCalendar(JourneyCalendarModel journeyCalendar);
    Task<JourneyCalendarModel> GetJourneyCalendarByYear(long companyId, int year);
}
