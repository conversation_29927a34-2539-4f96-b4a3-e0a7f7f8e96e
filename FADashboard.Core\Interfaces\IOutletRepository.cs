﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IOutletRepository
{
    Task<List<LocationDTO>> GetAllActiveOutlets(long companyId, GeographyLevel geographyLevel, List<long> levelIds);

    Task<List<LocationModel>> GetAllOutlet(long companyId, bool includeDeactivate = false);

    Task<List<LocationModel>> GetAllOutletByRegion(long companyId, List<long> regionIds);

    Task<List<LocationModel>> GetAllOutletWithRegion(long companyId);

    List<LocationModel> GetLocationsUnderBeats(long companyId, List<long> beatIds);

    Task<List<LocationModel>> GetOutletBySearch(long companyId, string searchString, int leave, int take, long positionId);

    //Task<List<Location>> GetOutletBySearch(long companyId, string searchString, int leave, int take, long positionId);
    Task<List<LocationModel>> GetOutletBySearchAndRegion(long companyId, string searchString, int leave, int take, List<long> regionFilter, long positionId);

    Task<long> GetOutletCountBySearch(long companyId, string searchString, long positionId);

    Task<long> GetOutletCountBySearchRegion(long companyId, string searchString, List<long> regionFilter, long positionId);

    Task<List<LocationModel>> GetOutletOfPosition(long companyId, long postionId);

    Task<List<EntityMinWithIsDeactive>> GetOutletMin(long companyId, List<long> outletIds = null, bool includeBlocked = false);

    Task<Dictionary<long, LocationModel>> GetLocations(long companyId, List<long> OutletIds, bool includeDeactive);
    Task<Dictionary<long, int>> GetBeatOutletCountDict(long companyId);
    Task<Dictionary<long, int>> GetOutletForCompanyWithSegmentation(long companyId, bool includeDeactive = false);
    Task<long> GetOutletCount(long companyId, PaginationFilter validFilter);
    Task<LocationInfoDto> GetAllLocationInfoAsync(long companyId, string outletErpId);
    Task<LocationModel> GetOutlets(long companyId, long id);
    Task ApproveorReject(OutletVerification VerificationDetails,
    long companyId, long LocationId, bool IsApproved, OutletVerificationRequestInput kYCOutletEditModel = null);
}
