﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class ProductVisibilityRuleService(ICurrentUser currentUser,
    IProductVisibilityRuleRepository productVisibilityRuleRepository,
    IShopTypeRepository shopTypeRepository,
    IOutletMasterRepository outletMasterRepository) : RepositoryResponse
{
    private async Task<List<long>> GetChannelIdsFromEnums(List<string> channelEnums)
    {
        if (channelEnums.Count != 0)
        {
            var channelDict = (await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, false)).ToDictionary(s => ((int)s.Enum).ToString(), s => s.Id);
            return channelEnums.Select(cEnum => channelDict.GetValueOrDefault(cEnum, 0)).ToList();
        }

        return [];
    }
    private async Task<List<string>> GetChannelEnumsStringFromIds(List<long> channelIds)
    {
        if (channelIds.Count != 0)
        {
            var channelDict = (await shopTypeRepository.GetOutletChannels(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => ((int)s.Enum).ToString());
            return channelIds.Select(cId => channelDict.GetValueOrDefault(cId)).Where(p => p != null).ToList();
        }

        return [];
    }
    private async Task<List<long>> GetShopTypeIdsFromShopNames(List<string> shopNames)
    {
        if (shopNames.Count != 0)
        {
            var shopTypeDict = (await shopTypeRepository.GetShopTypes(currentUser.CompanyId, false)).ToDictionary(s => s.ShopTypeName, s => s.Id);
            return shopNames.Select(name => shopTypeDict.GetValueOrDefault(name, 0)).ToList();
        }

        return [];
    }
    private async Task<List<string>> GetShopNamesFromShopTypeIds(List<long> shopTypeIds)
    {
        if (shopTypeIds.Count != 0)
        {
            var shopTypeDict = (await shopTypeRepository.GetShopTypes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.ShopTypeName);
            return shopTypeIds.Select(Id => shopTypeDict.GetValueOrDefault(Id)).Where(p => p != null).ToList();
        }

        return [];
    }
    private async Task<List<long>> GetSegmentationIdsFromEnums(List<string> segmentationEnums)
    {
        if (segmentationEnums.Count != 0)
        {
            var segDict = (await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, false)).ToDictionary(s => s.Segmentation.ToString(), s => s.Id);
            return segmentationEnums.Select(segEnum => segDict.GetValueOrDefault(segEnum, 0)).ToList();
        }

        return [];
    }
    private async Task<List<string>> GetSegmentationEnumsStringFromIds(List<long> segmentationIds)
    {
        if (segmentationIds.Count != 0)
        {
            var segDict = (await outletMasterRepository.GetOutletSegmentationAttributes(currentUser.CompanyId, false)).ToDictionary(s => s.Id, s => s.Segmentation.ToString());
            return segmentationIds.Select(segId => segDict.GetValueOrDefault(segId)).Where(p => p != null).ToList();
        }

        return [];
    }

    public async Task<List<ProductVisibilityRuleList>> GetAllProductVisibilityRules(bool showDeactive)
    {
        var productVisibilityRuleList = await productVisibilityRuleRepository.GetAllProductVisibilityRule(currentUser.CompanyId, showDeactive);
        return productVisibilityRuleList;
    }

    public async Task<ProductVisibilityRuleInput> GetProductVisibilityRuleById(long id)
    {
        var rule = await productVisibilityRuleRepository.GetProductVisibilityRuleById(currentUser.CompanyId, id);
        if (rule.IsGeographyConstraint)
        {
            rule.GeographyConstraint.RequiredZonesList = rule.GeographyConstraint.RequiredZones != null ? rule.GeographyConstraint.RequiredZones.Select(long.Parse).ToList() : [];
            rule.GeographyConstraint.RequiredRegionsList = rule.GeographyConstraint.RequiredRegions != null ? rule.GeographyConstraint.RequiredRegions.Select(long.Parse).ToList() : [];
            rule.GeographyConstraint.RequiredTerritoriesList = rule.GeographyConstraint.RequiredTerritories != null ? rule.GeographyConstraint.RequiredTerritories.Select(long.Parse).ToList() : [];
        }
        if (rule.IsDistributorConstraint)
        {
            rule.DistributorConstraint.RequiredDistributorsList = rule.DistributorConstraint.RequiredDistributors != null ? rule.DistributorConstraint.RequiredDistributors.Select(long.Parse).ToList() : [];
        }
        if (rule.IsOutletConstraint)
        {
            rule.OutletConstraint.RequiredChannelsList = rule.OutletConstraint.RequiredChannels != null ? await GetChannelIdsFromEnums(rule.OutletConstraint.RequiredChannels) : [];
            rule.OutletConstraint.RequiredShopTypesList = rule.OutletConstraint.RequiredShopTypes != null ? await GetShopTypeIdsFromShopNames(rule.OutletConstraint.RequiredShopTypes) : [];
            rule.OutletConstraint.RequiredSegmentationsList = rule.OutletConstraint.RequiredSegmentations != null ? await GetSegmentationIdsFromEnums(rule.OutletConstraint.RequiredSegmentations) : [];
            rule.OutletConstraint.OutletIdsList = rule.OutletConstraint.OutletIds != null ? rule.OutletConstraint.OutletIds.Select(long.Parse).ToList() : [];
        }

        if (rule.IsTertiaryConstraint)
        {
            rule.TertiaryConstraint.RequiredTertiaryEntitiesList = rule.TertiaryConstraint.RequiredTertiaryEntityIds != null ? rule.TertiaryConstraint.RequiredTertiaryEntityIds.Select(long.Parse).ToList() : [];
            rule.TertiaryConstraint.RequiredAttributeText1List = rule.TertiaryConstraint.RequiredAttributeText1 ?? [];
            rule.TertiaryConstraint.RequiredAttributeText2List = rule.TertiaryConstraint.RequiredAttributeText2 ?? [];
            rule.TertiaryConstraint.RequiredAttributeText3List = rule.TertiaryConstraint.RequiredAttributeText3 ?? [];
        }

        if (rule.IsProductConstraint)
        {
            rule.ProductConstraint.RequiredProductAttributeNumber1List = rule.ProductConstraint.RequiredProductAttributeNumber1 != null && rule.ProductConstraint.RequiredProductAttributeNumber1.Count != 0 ? rule.ProductConstraint.RequiredProductAttributeNumber1.Select(double.Parse).ToList() : [];
            rule.ProductConstraint.RequiredProductAttributeNumber2List = rule.ProductConstraint.RequiredProductAttributeNumber2 != null && rule.ProductConstraint.RequiredProductAttributeNumber2.Count != 0 ? rule.ProductConstraint.RequiredProductAttributeNumber2.Select(double.Parse).ToList() : [];
            rule.ProductConstraint.RequiredProductAttributeText1List = rule.ProductConstraint.RequiredProductAttributeText1 ?? [];
            rule.ProductConstraint.RequiredProductAttributeText2List = rule.ProductConstraint.RequiredProductAttributeText2 ?? [];
            rule.ProductConstraint.RequiredMRPsList = rule.ProductConstraint.RequiredMRPs != null && rule.ProductConstraint.RequiredMRPs.Any() ? rule.ProductConstraint.RequiredMRPs.Select(long.Parse).ToList() : [];
        }
        rule.ProductConstraint.RequiredProductDivisionsList = rule.ProductConstraint.RequiredProductDivisionIds != null && rule.ProductConstraint.RequiredProductDivisionIds.Any() ? rule.ProductConstraint.RequiredProductDivisionIds.Select(long.Parse).ToList() : [];
        rule.ProductConstraint.RequiredSecondaryCategoriesList = rule.ProductConstraint.RequiredSecondaryCategoryIds != null && rule.ProductConstraint.RequiredSecondaryCategoryIds.Any() ? rule.ProductConstraint.RequiredSecondaryCategoryIds.Select(long.Parse).ToList() : [];
        rule.ProductConstraint.RequiredPrimaryCategoriesList = rule.ProductConstraint.RequiredPrimaryCategoryIds != null && rule.ProductConstraint.RequiredPrimaryCategoryIds.Any() ? rule.ProductConstraint.RequiredPrimaryCategoryIds.Select(long.Parse).ToList() : [];
        rule.ProductConstraint.RequiredProductsList = rule.ProductConstraint.RequiredProductIds != null && rule.ProductConstraint.RequiredProductIds.Any() ? rule.ProductConstraint.RequiredProductIds.Select(long.Parse).ToList() : [];

        return rule;
    }

    public async Task<RepositoryResponse> DeactivateProductVisibilityRule(long id, bool action)
    {
        var equipmentMaster = await productVisibilityRuleRepository.DeactivateProductVisibilityRule(currentUser.CompanyId, id, action);
        return equipmentMaster;
    }
    public async Task<RepositoryResponse> CreateUpdateProductVisibilityRule(ProductVisibilityRuleInput pvcr)
    {
        if (pvcr.Id == 0)
        {
            var activeProductVisibilityRule = (await productVisibilityRuleRepository.GetAllRuleMin(currentUser.CompanyId, false)).Where(n => n.Name == pvcr.Name).ToList();

            if (activeProductVisibilityRule.Count != 0)

            {
                return new RepositoryResponse
                {
                    Id = 0,
                    ExceptionMessage = "Product Visibility Rule with same name already exists",
                    Message = "Product Visibility Rule Creation Failed!",
                    IsSuccess = false,
                };
            }
            var pvcrRule = await CreatePvcrModel(pvcr);
            return await productVisibilityRuleRepository.CreateProductVisibilityRule(currentUser.CompanyId, pvcrRule);
        }
        else
        {
            var pvcrRule = await CreatePvcrModel(pvcr);
            return await productVisibilityRuleRepository.UpdateProductVisibilityRule(currentUser.CompanyId, pvcrRule);
        }

        throw new NotImplementedException();
    }
    private async Task<ProductVisibilityRuleInput> CreatePvcrModel(ProductVisibilityRuleInput pvcr)
    {
        if (pvcr.IsGeographyConstraint)
        {
            pvcr.GeographyConstraint.RequiredZones = pvcr.GeographyConstraint.RequiredZonesList != null ? pvcr.GeographyConstraint.RequiredZonesList.Select(l => l.ToString()).ToList() : [];
            pvcr.GeographyConstraint.RequiredRegions = pvcr.GeographyConstraint.RequiredRegionsList != null ? pvcr.GeographyConstraint.RequiredRegionsList.Select(l => l.ToString()).ToList() : [];
            pvcr.GeographyConstraint.RequiredTerritories = pvcr.GeographyConstraint.RequiredTerritoriesList != null ? pvcr.GeographyConstraint.RequiredTerritoriesList.Select(l => l.ToString()).ToList() : [];
        }

        if (pvcr.IsDistributorConstraint)
        {
            pvcr.DistributorConstraint.RequiredDistributors = pvcr.DistributorConstraint.RequiredDistributorsList != null ? pvcr.DistributorConstraint.RequiredDistributorsList.Select(l => l.ToString()).ToList() : [];
        }
        if (pvcr.IsFactoryConstraint)
        {
            pvcr.FactoryConstraint.RequiredFactories = pvcr.FactoryConstraint.RequiredFactoriesList != null ? pvcr.FactoryConstraint.RequiredFactoriesList.Select(l => l.ToString()).ToList() : [];
        }

        if (pvcr.IsOutletConstraint)
        {
            pvcr.OutletConstraint.RequiredChannels = pvcr.OutletConstraint.RequiredChannelsList != null ? await GetChannelEnumsStringFromIds(pvcr.OutletConstraint.RequiredChannelsList) : [];
            pvcr.OutletConstraint.RequiredShopTypes = pvcr.OutletConstraint.RequiredShopTypesList != null ? await GetShopNamesFromShopTypeIds(pvcr.OutletConstraint.RequiredShopTypesList) : [];
            pvcr.OutletConstraint.RequiredSegmentations = pvcr.OutletConstraint.RequiredSegmentationsList != null ? await GetSegmentationEnumsStringFromIds(pvcr.OutletConstraint.RequiredSegmentationsList) : [];
            pvcr.OutletConstraint.OutletIds = pvcr.OutletConstraint.OutletIdsList != null ? pvcr.OutletConstraint.OutletIdsList.Select(l => l.ToString()).ToList() : [];
        }

        if (pvcr.IsTertiaryConstraint)
        {
            pvcr.TertiaryConstraint.RequiredTertiaryEntityIds = pvcr.TertiaryConstraint.RequiredTertiaryEntitiesList != null ? pvcr.TertiaryConstraint.RequiredTertiaryEntitiesList.Select(l => l.ToString()).ToList() : [];
            pvcr.TertiaryConstraint.RequiredAttributeText1 = pvcr.TertiaryConstraint.RequiredAttributeText1List != null ? pvcr.TertiaryConstraint.RequiredAttributeText1List.Select(l => l.ToString()).ToList() : [];
            pvcr.TertiaryConstraint.RequiredAttributeText2 = pvcr.TertiaryConstraint.RequiredAttributeText2List != null ? pvcr.TertiaryConstraint.RequiredAttributeText2List.Select(l => l.ToString()).ToList() : [];
            pvcr.TertiaryConstraint.RequiredAttributeText3 = pvcr.TertiaryConstraint.RequiredAttributeText3List != null ? pvcr.TertiaryConstraint.RequiredAttributeText3List.Select(l => l.ToString()).ToList() : [];
        }

        pvcr.ProductConstraint.RequiredProductAttributeNumber1 = pvcr.ProductConstraint.RequiredProductAttributeNumber1List != null && pvcr.ProductConstraint.RequiredProductAttributeNumber1List.Any() ? pvcr.ProductConstraint.RequiredProductAttributeNumber1List.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredProductAttributeNumber2 = pvcr.ProductConstraint.RequiredProductAttributeNumber2List != null && pvcr.ProductConstraint.RequiredProductAttributeNumber2List.Any() ? pvcr.ProductConstraint.RequiredProductAttributeNumber2List.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredProductAttributeText1 = pvcr.ProductConstraint.RequiredProductAttributeText1List != null && pvcr.ProductConstraint.RequiredProductAttributeText1List.Any() ? pvcr.ProductConstraint.RequiredProductAttributeText1List.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredProductAttributeText2 = pvcr.ProductConstraint.RequiredProductAttributeText2List != null && pvcr.ProductConstraint.RequiredProductAttributeText2List.Any() ? pvcr.ProductConstraint.RequiredProductAttributeText2List.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredMRPs = pvcr.ProductConstraint.RequiredMRPsList != null && pvcr.ProductConstraint.RequiredMRPsList.Any() ? pvcr.ProductConstraint.RequiredMRPsList.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredProductDivisionIds = pvcr.ProductConstraint.RequiredProductDivisionsList != null && pvcr.ProductConstraint.RequiredProductDivisionsList.Count != 0 ? pvcr.ProductConstraint.RequiredProductDivisionsList.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredPrimaryCategoryIds = pvcr.ProductConstraint.RequiredPrimaryCategoriesList != null && pvcr.ProductConstraint.RequiredPrimaryCategoriesList.Any() ? pvcr.ProductConstraint.RequiredPrimaryCategoriesList.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredSecondaryCategoryIds = pvcr.ProductConstraint.RequiredSecondaryCategoriesList != null && pvcr.ProductConstraint.RequiredSecondaryCategoriesList.Any() ? pvcr.ProductConstraint.RequiredSecondaryCategoriesList.Select(l => l.ToString()).ToList() : [];
        pvcr.ProductConstraint.RequiredProductIds = pvcr.ProductConstraint.RequiredProductsList != null && pvcr.ProductConstraint.RequiredProductsList.Any() ? pvcr.ProductConstraint.RequiredProductsList.Select(l => l.ToString()).ToList() : [];
        return pvcr;
    }
}
