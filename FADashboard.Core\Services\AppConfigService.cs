﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class AppConfigService(
    ICurrentUser currentUser,
    IAppConfigRepository appConfigsRepository) : RepositoryResponse
{
    public async Task<List<AppConfigView>> GetAllAppConfig() => await appConfigsRepository.GetAllAppConfig(currentUser.CompanyId);

    public async Task<AppConfigView> GetAppConfigById() => await appConfigsRepository.GetAppConfigById(currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateUpdateAppConfig(AppConfigView appConfig)
    {
        if (appConfig.Id > 0)
        {
            return await appConfigsRepository.UpdateAppConfig(currentUser.CompanyId, appConfig);
        }

        return await appConfigsRepository.CreateAppConfig(currentUser.CompanyId, appConfig);
    }
}
