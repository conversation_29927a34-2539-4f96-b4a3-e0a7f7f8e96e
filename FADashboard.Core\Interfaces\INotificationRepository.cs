﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface INotificationRepository
{
    Task<List<NotificationList>> GetNotifications(long companyId, bool showDeactive);
    Task<NotificationDetails> GetNotificationById(long companyId, long Id);
    Task<RepositoryResponse> CreateNotification(NotificationCreateInput notification, long companyId);
    Task<RepositoryResponse> ActionNotifications(long companyId, long Id, bool action);
}
