﻿using FADashboard.DbStorage.TransactionRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace FADashboard.DbStorage.DbContexts;

public class TransactionDbContext(DbContextOptions<TransactionDbContext> options) : DbContext(options)
{
    public DbSet<AttendanceRegulariseRequest> AttendanceRegulariseRequest { get; set; }
    public DbSet<Attendances> Attendances { get; set; }
    public DbSet<DbAssetAudit> AssetAudits { get; set; }
    public DbSet<DayRecord> DayRecords { get; set; }
    public DbSet<DeadOutletRequestItem> DeadOutletRequestItems { get; set; }
    public DbSet<DeadOutletRequest> DeadOutletRequests { get; set; }
    public DbSet<OpenMarketDayStock> OpenMarketDayStocks { get; set; }
    public DbSet<OpenMarketDayStart> OpenMarketDayStarts { get; set; }
    public DbSet<OpenMarketDayEnd> OpenMarketDayEnds { get; set; }
    public DbSet<UserCredentials> UserCredentials { get; set; }
    public DbSet<OutletCreationRequestManagerEdit> OutletCreationRequestManagerEdits { get; set; }
    public DbSet<OutletCreationRequest> OutletCreationRequests { get; set; }
    public DbSet<Sales> Sales { get; set; }
    public DbSet<SchemeSaleItems> SchemeSaleItems { get; set; }
    public DbSet<SchemeSales> SchemeSales { get; set; }
    public DbSet<SMSRecipientDetails> SMSRecipientDetails { get; set; }
    public DbSet<SurveyResponses> SurveyResponses { get; set; }
    public DbSet<SurveyResponseItem> SurveyResponseItems { get; set; }
    public DbSet<RewardRequest> FAMonetaryTransactionRequests { get; set; }
    public DbSet<AssetAllocations> AssetAllocations { get; set; }
    public DbSet<DistributorCreationRequests> DistributorCreationRequests { get; set; }
    public DbSet<DistributorCreationRequestManagerEdits> DistributorCreationRequestManagerEdits { get; set; }
    public DbSet<FieldEvent> FieldEvents { get; set; }
    public DbSet<AssetAgreements> AssetAgreements { get; set; }
    public DbSet<AssetReallocation> AssetReallocationRequests { get; set; }
    public DbSet<OutletVerification> OutletVerifications { get; set; }
    //public DbSet<EmployeeLocationDump> EmployeeLocationDumps { get; set; }
    public DbSet<LMSLeadActivityMaster> LMSLeadActivitiesMaster { get; set; }
    public DbSet<LMSLeadActivityTransactions> LMSLeadActivitiesTransactions { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder) => modelBuilder.Entity<DayRecord>()
        .HasKey(d => d.SessionId);

    public void RejectChanges()
    {
        var entries = ChangeTracker.Entries().ToList();
        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                case EntityState.Deleted:
                    entry.State = EntityState.Modified; //Revert changes made to deleted entity.
                    entry.State = EntityState.Unchanged;
                    break;

                case EntityState.Added:
                    entry.State = EntityState.Detached;
                    break;
                case EntityState.Detached:
                    break;
                case EntityState.Unchanged:
                    break;
            }
        }
    }

    [Obsolete("SaveChanges not allowed on ReadOnlyContext", true)]
    public override int SaveChanges(bool acceptAllChangesOnSuccess) => throw new InvalidOperationException("SaveChanges not allowed on ReadOnlyContext");

    [Obsolete("SaveChangesAsync not allowed on ReadOnlyContext", true)]
    public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default) => throw new InvalidOperationException("SaveChanges not allowed on ReadOnlyContext");
}
