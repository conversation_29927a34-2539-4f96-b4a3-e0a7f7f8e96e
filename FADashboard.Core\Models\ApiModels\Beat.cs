﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class Beat
{
    public string DistributorNames { get; set; }
    public List<long> Distributors { get; set; }
    public string ErpId { set; get; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public List<long> PositionIdsList { get; set; } // Primary Positions
    public List<long> SecondaryPositionIdsList { get; set; }
    public string PositionIds { get; set; }
    public string PositionNames { get; set; }
    public string SecondaryPositionNames { get; set; }
    public long? RegionId { get; set; }
    public string RegionName { get; set; }
    public long? TerritoryId { get; set; }
    public string TerritoryName { get; set; }
    public List<long?> PDIdsAttachedToBeats { get; set; }
    public List<long> PDIdsAttachedToDistributorsOfBeat { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }
    public BeatGrade BeatGrade { get; set; }
    public long? MappedOutlets { get; set; }
    public bool IsPrimaryMapping { get; set; }
}

public class BeatMinModel
{
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public string Name { get; set; }
    public long? ParentId{ get; set; }
}

public class BeatOutlets
{
    public long BeatId { get; set; }
    public List<long> OutletIds { get; set; }
}



public class BeatList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string ErpId { set; get; }
    public long? TerritoryId { get; set; }
    public string TerritoryName { get; set; }
    public long? RegionId { get; set; }
    public string RegionName { get; set; }
    public string DistributorNames { get; set; }
    public string PositionNames { get; set; }
    public List<long> PositionIds { get; set; }
    public string SecondaryPositionNames { get; set; }
    public bool IsActive { get; set; }
    public long OutletCount { get; set; }

}

public class BeatTotal
{
    public int Total { get; set; }
    public List<BeatList> BeatRecords { get; set; }
}
