﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IOutletMetricRepository
{
    Task<List<ExternalOutletMetricInput>> GetExternalOutletMetrics(long companyId, bool includeDeactive = false);

    Task<RepositoryResponse> CreateExternalOutletMetric(ExternalOutletMetricInput externalOutletMetric, long companyId);

    Task<RepositoryResponse> UpdateExternalOutletMetric(ExternalOutletMetricInput externalOutletMetric);

    Task<RepositoryResponse> ActivateDeactivateExternalOutletMetric(long externalOutletMetricId, bool action);

    Task<ExternalOutletMetricInput> GetExternalOutletMetricById(long externalOutletMetricId);
}
