using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSCustomFieldValueDto
    {
        public long Id { get; set; }
        public int EntityType { get; set; }
        public long EntityId { get; set; }
        public long CustomFieldId { get; set; }
        public string Value { get; set; }

        // Custom Field Definition Properties
        public string Label { get; set; }
        public LMSCustomFieldType Type { get; set; }
        public string Placeholder { get; set; }
        public List<string> Options { get; set; }
        public string MinValue { get; set; }
        public string MaxValue { get; set; }
        public bool Required { get; set; }
        public bool IsActive { get; set; }
        public int Sequence { get; set; }

        // Auditing fields
        public DateTime CreatedAt { get; set; }
        public long? CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public long? UpdatedBy { get; set; }
    }
}
