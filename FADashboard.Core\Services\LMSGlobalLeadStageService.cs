using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Services
{
    public class LMSGlobalLeadStageService : ILMSGlobalLeadStageService
    {
        private readonly ILMSGlobalLeadStageRepository _lmsGlobalLeadStageRepository;

        public LMSGlobalLeadStageService(ILMSGlobalLeadStageRepository lmsGlobalLeadStageRepository)
        {
            _lmsGlobalLeadStageRepository = lmsGlobalLeadStageRepository ?? throw new ArgumentNullException(nameof(lmsGlobalLeadStageRepository));
        }

        public async Task<LMSGlobalLeadStageDto> GetGlobalLeadStageByIdAsync(long id)
        {
            return await _lmsGlobalLeadStageRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<LMSGlobalLeadStageDto>> GetAllGlobalLeadStagesAsync()
        {
            return await _lmsGlobalLeadStageRepository.GetAllAsync();
        }

        public async Task<IEnumerable<LMSGlobalLeadStageDto>> GetGlobalLeadStagesByCompanyIdAsync(long companyId)
        {
            return await _lmsGlobalLeadStageRepository.GetByCompanyIdAsync(companyId);
        }

        public async Task<LMSGlobalLeadStageDto> CreateGlobalLeadStageAsync(LMSGlobalLeadStageDto stageDto, long createdByUserId)
        {
            if (stageDto == null) throw new ArgumentNullException(nameof(stageDto));

            stageDto.CreatedBy = createdByUserId;

            return await _lmsGlobalLeadStageRepository.AddAsync(stageDto);
        }

        public async Task<LMSGlobalLeadStageDto> UpdateGlobalLeadStageAsync(long id, LMSGlobalLeadStageDto stageDto, long? updatedByUserId)
        {
            if (stageDto == null) throw new ArgumentNullException(nameof(stageDto));

            stageDto.Id = id;
            stageDto.UpdatedBy = updatedByUserId;

            return await _lmsGlobalLeadStageRepository.UpdateAsync(stageDto);
        }

        public async Task<bool> DeleteGlobalLeadStageAsync(long id, long? updatedByUserId)
        {
            return await _lmsGlobalLeadStageRepository.DeleteAsync(id, updatedByUserId);
        }
    }
}
