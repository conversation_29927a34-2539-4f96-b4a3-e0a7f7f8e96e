﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Library.StringHelpers;

namespace FADashboard.Core.Services;

public class EquipmentMasterService(IEquipmentMasterRepository equipmentMasterRepository,
    ICurrentUser currentUser,
    IAssetRepository assetRepository,
    IAssetAllocationTransactionRepository assetAllocationTransactionRepository) : RepositoryResponse
{
    public async Task<List<EquipmentMasterListView>> GetAllEquipmentMaster(bool showDeactive)
    {
        var equipmentMaster = await equipmentMasterRepository.GetAllEquipmentMaster(currentUser.CompanyId, showDeactive);
        return equipmentMaster;
    }

    public async Task<EquipmentMasterView> GetEquipmentById(long id)
    {
        var equipmentMaster = await equipmentMasterRepository.GetEquipmentById(currentUser.CompanyId, id);
        return equipmentMaster;
    }

    public async Task<RepositoryResponse> ActivateDeactivateEquipmentMaster(long id, bool action)
    {
        var equipmentMaster = await equipmentMasterRepository.ActivateDeactivateEquipmentMaster(currentUser.CompanyId, id, action);
        return equipmentMaster;
    }

    private async Task<RepositoryResponse> IsValidEquipmentMaster(EquipmentMasterView equipmentMaster)
    {
        var allEquipmentMasters = await equipmentMasterRepository.GetAllEquipmentMaster(currentUser.CompanyId, false);
        if (equipmentMaster.Id != 0)
        {
            allEquipmentMasters = allEquipmentMasters.Where(p => p.Id != equipmentMaster.Id).ToList();
        }

        var equipmentMasterErpIdList = allEquipmentMasters.Select(p => p.ErpId.NormalizeCaps()).ToList();
        var equipmentMasterReferNumberList = allEquipmentMasters.Select(p => p.ReferenceNumber).ToList();

        if (!string.IsNullOrEmpty(equipmentMaster.ErpId.NormalizeCaps()) && equipmentMasterErpIdList.Contains(equipmentMaster.ErpId.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = equipmentMaster.Id, ExceptionMessage = "EquipmentMaster ErpId is not unique", Message = "EquipmentMaster creation/updation Failed!", IsSuccess = false,
            };
        }

        if (!string.IsNullOrEmpty(equipmentMaster.ReferenceNumber) && equipmentMasterReferNumberList.Contains(equipmentMaster.ReferenceNumber))
        {
            return new RepositoryResponse
            {
                Id = equipmentMaster.Id,
                ExceptionMessage = "EquipmentMaster Reference Number is not Unique",
                Message = "EquipmentMaster creation/updation Failed!",
                IsSuccess = false,
            };
        }

        return GetSuccessResponse(equipmentMaster.Id, "EquipmentMaster unique");
    }

    public async Task<RepositoryResponse> CreateUpdateEquipmentMaster(EquipmentMasterView equipmentMaster)
    {
        var checkValid = await IsValidEquipmentMaster(equipmentMaster);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (equipmentMaster.Id == 0)
        {
            return await equipmentMasterRepository.CreateEquipmentMaster(currentUser.CompanyId, equipmentMaster);
        }

        return await equipmentMasterRepository.UpdateEquipmentMaster(currentUser.CompanyId, equipmentMaster);
    }
    public async Task<List<EquipmentMasterListView>> GetAllUnmappedEquipmentMaster(bool showDeactive)
    {
        var equipmentMaster = await equipmentMasterRepository.GetAllEquipmentMaster(currentUser.CompanyId, showDeactive);
        var assetMappings = await assetRepository.GetAllAssetsInCompany(currentUser.CompanyId, showDeactive);
        var pendingRequests = await assetAllocationTransactionRepository.GetAssetAllocationPendingRequest(currentUser.CompanyId);

        // already mapped equipment
        var mappedEquipmentIds = assetMappings
            .Where(a => a.EquipmentId != null)
            .Select(a => a.EquipmentId)
            .ToHashSet();

        if (pendingRequests != null)
        {
            foreach (var id in pendingRequests.Where(p => p.EquipmentId != null && p.Status != AssetManagementRequestStatus.BackToWarehouse).Select(p => p.EquipmentId))
            {
                mappedEquipmentIds.Add(id);
            }
        }

        var unmappedEquipment = equipmentMaster
            .Where(e => !mappedEquipmentIds.Contains(e.Id))
            .ToList();

        return unmappedEquipment;
    }
}
