﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class OutletOnboardingDetails : IEntity, ICompanyEntity, IAuditedEntity
{
    public long Id { get; set; }
    public PortalUserRole AssigneeRole { get; set; }
    public long AssigneeId { get; set; }
    public long? AssignedToUserId { get; set; }
    public Status Status { get; set; } = Status.Pending;

    [StringLength(512)]
    public string ShopName { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public long? Market { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }

    [StringLength(1024)]
    public string Address { get; set; }
    [StringLength(256)]
    public string OwnersName { get; set; }

    [StringLength(64)]
    public string OwnersNo { get; set; }

    [StringLength(64)]
    public string TIN { get; set; }

    [StringLength(512)]
    public string Email { get; set; }

    [StringLength(8)]
    public string PinCode { get; set; }

    [StringLength(128)]
    public string MarketName { get; set; }

    [StringLength(64)]
    public string City { get; set; }

    [StringLength(64)]
    public string State { get; set; }

    [StringLength(128)]
    public string ImageId { get; set; }

    public long CompanyId { get; set; }

    [StringLength(32)]
    public string GSTIN { get; set; }

    [StringLength(16)]
    public string PAN { get; set; }

    [StringLength(16)]
    public string Aadhar { get; set; }

    [StringLength(64)]
    public string AttributeText1 { get; set; }

    [StringLength(64)]
    public string AttributeText2 { get; set; }

    [StringLength(64)]
    public string AttributeText3 { get; set; }

    public bool GSTRegistered { get; set; }

    [StringLength(16)]
    public string BankAccountNumber { get; set; }

    [StringLength(128)]
    public string AccountHoldersName { get; set; }

    [StringLength(16)]
    public string IFSCCode { get; set; }

    [StringLength(1024)]
    public string LandMark { get; set; }

    [StringLength(64)]
    public string LandlineNumber { get; set; }

    [StringLength(128)]
    public string AlternateImageId { get; set; }

    [StringLength(128)]
    public string PhotoProofId { get; set; }

    [StringLength(128)]
    public string TypeOfIdProof { get; set; }

    [StringLength(512)]
    public string ModeOfDataCollection { get; set; }

    [StringLength(64)]
    public string AttributeText4 { get; set; }

    public float? AttributeNumber1 { get; set; }
    public float? AttributeNumber2 { get; set; }
    public float? AttributeNumber3 { get; set; }
    public float? AttributeNumber4 { get; set; }

    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }

    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }

    [StringLength(64)]
    public string AttributeImage1 { get; set; }

    [StringLength(64)]
    public string AttributeImage2 { get; set; }

    [StringLength(64)]
    public string AttributeImage3 { get; set; }

    [StringLength(128)]
    public string District { get; set; }

    [StringLength(64)]
    public string FSSAINumber { get; set; }
    public DateTime? FSSAIExpiryDate { get; set; }
    [StringLength(128)]
    public string CreationContext { get; set; }
    public long? AssigneePositionId { get; set; }
    public long? AssignedToPositionId { get; set; }
    [StringLength(1024)]
    public string SuggestedUsersId { get; set; }
    [StringLength(1024)]
    public string SuggestedUsersPositionId { get; set; }
}
