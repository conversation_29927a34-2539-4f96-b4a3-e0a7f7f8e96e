﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class WhiteListedIPsService(
    ICurrentUser currentUser,
    IWhiteListedIPsRepository whiteListedIPsRepository) : RepositoryResponse
{
    public async Task<List<WhiteListedIPList>> GetAllWhiteListedIPs(bool includeDeactive, CancellationToken ct) => await whiteListedIPsRepository.GetAllWhiteListedIPs(currentUser.CompanyId, includeDeactive, ct);
    public async Task<RepositoryResponse> ActivateDeactivateWhiteListedIP(long id, bool action, CancellationToken ct) => await whiteListedIPsRepository.ActivateDeactivateWhiteListedIP(currentUser.CompanyId, id, action, ct);

    public async Task<RepositoryResponse> CreateWhiteListedIPs(CreateWhiteListedIPsRequest request, CancellationToken ct)
    {
        return await whiteListedIPsRepository.CreateWhiteListedIPs(currentUser.CompanyId, request.RuleName, request.IpAdresses, ct);
    }

}
