﻿namespace FADashboard.Core.Models;
public class HCCBIntegrationLog
{
    public long Id { get; set; }
    public string IntegrationName { get; set; }
    public string EntityName { get; set; }
    public long TotalReceived { get; set; }
    public long TotalSuccess { get; set; }
    public long TotalFailure { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }

}

public enum HCCBIntegrationEnum
{
    CustomerMaster = 1
}

public enum HCCBEntityEnum
{
    Geographies = 1,
    Position = 2,
    Distributor = 3,
    PositionBeatMapping = 4,
    DistributorBeatMappings = 5,
    OutletsCreate = 6,
    OutletUpdate = 7,
    Employees = 8,
    AssetMappings = 9,
    ProcessUserMaster_Deactivation = 10,
    ProcessUserMaster_Activation = 11,
}

