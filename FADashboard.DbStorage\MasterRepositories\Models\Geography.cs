﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Geographies : IAuditedEntity
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string ErpId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public GeographyLevel Level { get; set; }
    public string Name { get; set; }
    public long? ParentId { get; set; }
}
