﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Microsoft.AspNetCore.Http;


namespace FADashboard.Core.Interfaces;

public interface IProductRepository
{
    Task<RepositoryResponse> ActivateDeactivateProduct(long productId, long companyId, bool action);

    Task<RepositoryResponse> CreateProduct(ProductInput product, long companyId);

    Task<string> GetFirstStdUnit(long companyId, TargetValueType targetValueType);

    Task<List<EntityMin>> GetPCsForProducts(long companyId, List<long> productIds);

    Task<List<EntityMinIncludeParent>> GetPrimaryCategories(long companyId);

    Task<List<EntityMin>> GetPrimaryCategories(long companyId, List<long> ids);

    Task<ProductInput> GetProductById(long productId, long companyId);

    Task<List<ProductDisplayCategoryList>> GetProductDisplayCategories(long companyId, bool includeDeactivate);

    Task<List<EntityMin>> GetProductForPCMin(long companyId, List<long> pcids);

    Task<List<EntityMin>> GetProductForSCMin(long companyId, List<long> scids);

    Task<List<EntityMin>> GetProductMin(long companyId);

    Task<List<EntityMin>> GetProductMin(long companyId, List<long> ids);

    Task<ProductTotal> GetProductsList(long companyId, PaginationFilter validFilter);

    Task<List<ProductList>> GetProducts(long companyId, bool includeDeactivate);

    Task<List<EntityMin>> GetSCsForProducts(long companyId, List<long> productIds);

    Task<List<EntityMinIncludeParent>> GetSecondaryCategories(long companyId);

    Task<List<EntityMin>> GetSecondaryCategories(long companyId, List<long> ids);

    Task<RepositoryResponse> UpdateProduct(ProductInput product, long companyId);

    Task<ApiResponse> BulkUploadProductImages(long companyId, IFormFile file, string containerName);
    Task<ApiResponse> BulkUploadProductThumbnails(long companyId, IFormFile file, string containerName);
    Task<ProductBatchNumberResponse> GetProductBatchNumbers(long companyId, List<long> productIds);
    Task<List<ProductGroupList>> GetProductGroups(long companyId, bool includeDeactivate);
    Task<RepositoryResponse> CreateProductGroup(ProductGroupInput pg, long companyId);
    Task<RepositoryResponse> UpdateProductGroup(ProductGroupInput pg, long companyId);
    Task<ProductGroupInput> GetProductGroupById(long pgId, long companyId);
    Task<RepositoryResponse> ActivateDeactivateProductGroup(long pgId, long companyId, bool action);
    Task<List<ProductBatchNumberDto>> GetProductsBatchNumber(long companyId, List<long> productIds);

}
