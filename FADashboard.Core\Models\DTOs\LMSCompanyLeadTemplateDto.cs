﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSCompanyLeadTemplateDto
    {
        public long Id { get; set; }

        [Required]
        public long CompanyId { get; set; }

        [Required]
        [StringLength(255)]
        public string TemplateName { get; set; }

        public string Description { get; set; }

        public bool IsActive { get; set; }

        public bool IsDefault { get; set; }

        public List<TemplateCustomFields> CustomFields { get; set; }

        public int LeadsCount { get; set; }

        public IEnumerable<LMSCompanyLeadStageDto> LeadStages { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; }

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string CreatedByName { get; set; }

        public string UpdatedByName { get; set; }
    }

    public class LMSCompanyLeadTemplateInput
    {
        [Required]
        [StringLength(255)]
        public string TemplateName { get; set; }

        public string Description { get; set; }

        public bool IsDefault { get; set; } = true;

        public List<TemplateCustomFields> CustomFields { get; set; }

        public bool IsActive { get; set; }
    }

    public class TemplateCustomFields
    {
        public long CustomFieldId { get; set; }
        public LMSCustomFieldsEntityType EntityType { get; set; }
        public long EntityId { get; set; }
        public string Label { get; set; }
        public string Description { get; set; }
        public LMSCustomFieldType Type { get; set; }
        public string Placeholder { get; set; }
        public List<string> Options { get; set; }
        public bool Required { get; set; }
        public bool IsActive { get; set; }
        public int Sequence { get; set; }
        public string MinValue { get; set; }
        public string MaxValue { get; set; }
    }
}
