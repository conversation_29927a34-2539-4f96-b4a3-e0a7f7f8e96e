﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class OutletTags : IAuditedEntity
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long CompanyId { get; set; }
    public string? IconImage { get; set; }
    [StringLength(14)]
    public string TagColor { get; set; }
    [StringLength(14)]
    public string TextColor { get; set; }
    public bool ShowInApp { get; set; }
    public bool ShowIconwithText { get; set; }
}
