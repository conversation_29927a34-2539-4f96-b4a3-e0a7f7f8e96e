﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSGlobalLeadStageRepository
    {
        Task<LMSGlobalLeadStageDto> GetByIdAsync(long id);
        Task<IEnumerable<LMSGlobalLeadStageDto>> GetAllAsync();
        Task<IEnumerable<LMSGlobalLeadStageDto>> GetByCompanyIdAsync(long companyId);
        Task<LMSGlobalLeadStageDto> AddAsync(LMSGlobalLeadStageDto entity);
        Task<LMSGlobalLeadStageDto> UpdateAsync(LMSGlobalLeadStageDto entity);
        Task<bool> DeleteAsync(long id, long? updatedByUserId);
    }
}
