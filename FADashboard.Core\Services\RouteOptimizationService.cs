﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Services;

public class RouteOptimizationService(IRouteOptimizationRepository routeOptimizationRepository, ICurrentUser currentUser,
    IAssetDefinitionsRepository assetDefinitionsRepository,IAssetRepository assetRepository,IOutletRepository outletRepository) : RepositoryResponse
{
    public async Task<List<RouteOptimizationList>> GetRouteOptimization(bool includeDeactivate)
    {
        var routeOptimization = await routeOptimizationRepository.GetRouteOptimization(currentUser.CompanyId, includeDeactivate);
        return routeOptimization;
    }

    public async Task<List<WeeklyRouteOptimizationList>> GetWeeklyRouteOptimization(bool includeDeactivate)
    {
        var weeklyRouteOptimization = await routeOptimizationRepository.GetWeeklyRouteOptimization(currentUser.CompanyId, includeDeactivate);
        return weeklyRouteOptimization;
    }

    public async Task<RouteOptimizationInput> GetRouteOptimizationById(long id)
    {
        var routeOptimization = await routeOptimizationRepository.GetRouteOptimizationById(id, currentUser.CompanyId);
        return routeOptimization;
    }

    public async Task<RepositoryResponse> ActivateDeactivateRouteOptimization(long id, bool action) => await routeOptimizationRepository.ActivateDeactivateRouteOptimization(id, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> ActivateDeactivateWeeklyRouteOptimization(long id, bool action) => await routeOptimizationRepository.ActivateDeactivateWeeklyRouteOptimization(id, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> UpdateRouteOptimization(RouteOptimizationInput routeOptimization) => await routeOptimizationRepository.UpdateRouteOptimization(routeOptimization, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateRouteOptimization(RouteOptimizationInput routeOptimization) => await routeOptimizationRepository.CreateRouteOptimization(routeOptimization, currentUser.CompanyId);

    public async Task<RepositoryResponse> CreateWeeklyRouteOptimization(WeeklyRouteOptimizationInput routeOptimization)
    {
            var retailTimeDetailsModel = new RetailTimeDetailsModel();
            var requiredVisitInputModel = new VisitSegmentationModel();
            if (routeOptimization.RetailTimeConfiguration == Libraries.CommonEnums.RequiredRetailTime.TaskSpecificRetailTime)
            {
                var visitModelList = new List<VisitModel>();
                var segmentationList = new List<SegmentationTimeModel>();
                var assetDefinitions = await assetDefinitionsRepository.GetAllAssets(currentUser.CompanyId, false);
                var assetDataDict = (await assetRepository.GetAllAssetsInCompany(currentUser.CompanyId, false)).GroupBy(s => s.AssetId).ToDictionary(s => s.Key, s => s.ToList());
                var locations = (await outletRepository.GetOutletForCompanyWithSegmentation(currentUser.CompanyId, false));
                foreach (var obj in routeOptimization.OutletConstraints)
                {
                    var visitModel = new VisitModel();
                    var segModel = new SegmentationTimeModel();
                    segModel.Segmentation = (int)obj.Segmentation;
                    visitModel.EntityValue = segModel.Segmentation;
                    visitModel.Max = obj.Visit.MaxVisits;
                    visitModel.Min = obj.Visit.MinVisits;
                    visitModel.Avg = obj.Visit.IdealVisits;
                    visitModelList.Add(visitModel);
                    var focusAreaList = new List<FocusAreaDetails>();
                    foreach (var task in obj.Tasks)
                    {
                        var focusAreaModel = new FocusAreaDetails();
                        var time = 0d;
                        if (task.AssetRetailTime?.Count > 0)
                        {
                            var assetList = new List<AssetTimeDetails>();
                            var outletIdsForSeg = locations.Where(s => s.Value == segModel.Segmentation).Select(s => s.Key).ToList();
                            foreach (var asset in task.AssetRetailTime)
                            {
                                var assetTimeModel = new AssetTimeDetails();
                                var definitionIds = assetDefinitions.Where(s => s.VolumeCapacity >= asset.MinVol && s.VolumeCapacity <= asset.MaxVol).Select(s => s.Id).ToList();
                                var filteredAssetIds = definitionIds.SelectMany(s => assetDataDict.ContainsKey(s) ? assetDataDict[s] : new List<AssetDetailModel>()).Where(s => outletIdsForSeg.Contains(s.OutletId)).Select(s => s.AssetOutletId).ToList();
                                assetTimeModel.Ids = filteredAssetIds;
                                assetTimeModel.Time = asset.RetailTime ?? 0;
                                time += assetTimeModel.Time;
                                assetList.Add(assetTimeModel);
                            }
                            focusAreaModel.Asset = assetList;
                        }
                        else
                        {
                            focusAreaModel.Asset = null;
                            time += task.RetailTime ?? 0;
                        }
                        focusAreaModel.Id = task.TaskId ?? 0;
                        focusAreaModel.Time = time;
                        focusAreaList.Add(focusAreaModel);
                    }
                    segModel.FocusAreas = focusAreaList;
                    segmentationList.Add(segModel);
                }
                retailTimeDetailsModel.Segmentations = segmentationList;
                requiredVisitInputModel.Entity = visitModelList;
            }
            return await routeOptimizationRepository.CreateWeeklyRouteOptimization(routeOptimization, currentUser.CompanyId, retailTimeDetailsModel, requiredVisitInputModel);
    }
}
