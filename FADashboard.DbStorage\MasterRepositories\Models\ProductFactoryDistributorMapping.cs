﻿using System.ComponentModel.DataAnnotations;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class ProductFactoryDistributorMapping : IAuditedEntity, IDeactivatable, ICompanyEntity
{
    public long Id { get; set; }
    public long FactoryId { get; set; }
    public long ProductId { get; set; }
    public long CompanyId { get; set; }
    public long DistributorId { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    [StringLength(32)]
    public string CreationContext { get; set; }
    public bool IsPrimary { get; set; }

}


