﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.RegularExpressions;
using AuditHelper;
using EntityHelper;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.DbStorage.MasterRepositories.Models;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace FADashboard.DbStorage.DbContexts;

public abstract partial class DbContextAbstract : DbContext
{
    private ICurrentUser _identity;

    protected DbContextAbstract(DbContextOptions<WritableMasterDbContext> options, ICurrentUser identity) :
        base(options)
    {
        _identity = identity;
    }

    protected DbContextAbstract(DbContextOptions<WritableMasterDbTransactionContext> options, ICurrentUser identity) :
        base(options)
    {
        _identity = identity;
    }

    protected DbContextAbstract(DbContextOptions<WritableTransactionDbContext> options, ICurrentUser identity) :
        base(options)
    {
        _identity = identity;
    }

    public virtual DbSet<DataAuditTrail> DbAuditTrails { get; set; }

    private void PrepareData()
    {
        _identity ??= Thread.CurrentPrincipal.Identity as ICurrentUser;

        //Device Entities
        var newDeviceEntities = ChangeTracker.Entries<IDeviceEntity>()
            .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
        PrepareDataForInsertion(newDeviceEntities);

        //New Entities
        var newEntities = ChangeTracker.Entries<ICreatedEntity>()
            .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
        PrepareDataForInsertion(newEntities);
        //New Transaction Entities
        var newTransactionEntities = ChangeTracker.Entries<ICreatedTransactionEntity>()
            .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
        PrepareTransactionDataForInsertion(newTransactionEntities);

        //Changed Entities
        var updatedEntities = ChangeTracker.Entries<IUpdatableEntity>()
            .Where(e => e.State is EntityState.Modified or EntityState.Added);
        SetLastUpdated(updatedEntities);

        var changedEntities = ChangeTracker.Entries<IAuditedEntity>()
            .Where(e => e.State == EntityState.Modified);
        PrepareDataForUpdation(changedEntities);

        //Data Cleaning before final saving
        //ChangeTracker is taking all Entries rather than IAuditedEntity or IUpdatableEntity or ICreatedEntity ...etc...
        //Reason: Not all tables have inherited any one of it, so want to make it generalized
        var entities = ChangeTracker.Entries()
            .Where(e => e.State is EntityState.Modified or EntityState.Added);
        TrimStrings(entities);
    }

    private void PrepareDataForUpdation(IEnumerable<EntityEntry<IAuditedEntity>> changedEntities)
    {
        var now = DateTime.UtcNow;
        var sessionId = Guid.NewGuid().ToString();
        var entryForThisChange = new List<DataAuditTrail>();

        foreach (var item in changedEntities)
        {
            item.Entity.LastUpdatedAt = now;
        }

        foreach (var changed in changedEntities)
        {
            var oldValues = changed.OriginalValues;
            var entity = changed.Entity;
            var newValues = changed.CurrentValues;
            var entityType = entity.GetType();
            var entityName = entityType.Name;

            foreach (var column in entityType.GetProperties())
            {
                // TODO: Add Audited attribute to track only those
                var attributesOfEntity = Attribute.GetCustomAttribute(column, typeof(AuditedAttribute));
                if (attributesOfEntity != null && oldValues.Properties.Select(p => p.Name).Contains(column.Name)
                                               && newValues.Properties.Select(p => p.Name).Contains(column.Name))
                {
                    var oldValue = oldValues?[column.Name];
                    var newValue = newValues?[column.Name];
                    if (!(oldValue?.Equals(newValue) ?? (newValue == null)))
                    {
                        var change = new DataAuditTrail
                        {
                            ChangedDate = now,
                            SessionId = sessionId,
                            NewValue = newValue?.ToString(),
                            OldValue = oldValue?.ToString(),
                            EntityName = entityName,
                            FieldName = column.Name,
                            EntityId = entity.Id,
                            ChangedByRole = PortalUserRole.Unknown,
                        };
                        if (_identity != null)
                        {
                            change.ChangedById = _identity.LocalId;
                            change.ChangedByRole = _identity.UserRole;
                        }

                        entryForThisChange.Add(change);
                    }
                }
            }

            DbAuditTrails.AddRange(entryForThisChange);
        }
    }

    private static void SetLastUpdated(IEnumerable<EntityEntry<IUpdatableEntity>> updatedEntities)
    {
        var now = DateTime.UtcNow;
        foreach (var item in updatedEntities)
        {
            item.Entity.LastUpdatedAt = now;
        }
    }

    private static void TrimStrings(IEnumerable<EntityEntry> changedEntities)
    {
        foreach (var changed in changedEntities)
        {
            var Entity = changed.Entity;
            var StringEntity = Entity.GetType().GetProperties().Where(p =>
                p.PropertyType == typeof(string) && !p.GetCustomAttributes(true).Any(z =>
                    z.GetType() == typeof(ObsoleteAttribute) || z.GetType() == typeof(NotMappedAttribute)) &&
                p.CanRead && p.CanWrite);
            foreach (var propertyInfo in StringEntity)
            {
                var value = propertyInfo.GetValue(Entity)?.ToString();
                if (string.IsNullOrEmpty(value))
                    continue;
                //Using Regex to remove extra spaces from between, and Trim to clear out sides
                var trimmedValue = MyRegex().Replace(value.Trim(), " ");
                propertyInfo.SetValue(Entity, trimmedValue);
            }
        }
    }

    public static void PrepareDataForInsertion(List<IDeviceEntity> newDeviceEntities)
    {
        var now = DateTime.UtcNow;
        foreach (var item in newDeviceEntities)
        {
            item.ServerTime = now;
        }
    }

    public void PrepareDataForInsertion<T>(List<T> newEntities) where T : ICreatedEntity
    {
        var now = DateTime.UtcNow;
        foreach (var item in newEntities)
        {
            item.CreatedAt = now;

            if (_identity == null)
            {
                if (string.IsNullOrEmpty(item.CreationContext))
                    item.CreationContext = "NewDashboard:Feature under research!!";
            }
            else
            {
                item.CreationContext = $"NewDashboard:{_identity.UserRole}:{_identity.LocalId}";
            }

            if (item is IAuditedEntity audited)
                audited.LastUpdatedAt = now;
        }
    }

    public static void PrepareTransactionDataForInsertion<T>(List<T> newEntities) where T : ICreatedTransactionEntity
    {
        var now = DateTime.UtcNow;
        foreach (var item in newEntities)
        {
            item.CreatedAt = now;
        }
    }

    public void SaveAndCleanEntityTrackings()
    {
        SaveChanges();
        var changedEntities = ChangeTracker.Entries();
        foreach (var item in changedEntities)
        {
            switch (item.State)
            {
                case EntityState.Unchanged:
                    item.State = EntityState.Detached;
                    break;

                case EntityState.Added:
                    item.State = EntityState.Detached;
                    break;

                case EntityState.Deleted:
                    item.State = EntityState.Detached;
                    break;

                case EntityState.Modified:
                    item.State = EntityState.Detached;
                    break;
                case EntityState.Detached:
                    break;
            }
        }
    }

    public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
        CancellationToken cancellationToken = default)
    {
        PrepareData();
        return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
    }

    [GeneratedRegex(" +")]
    private static partial Regex MyRegex();
}
