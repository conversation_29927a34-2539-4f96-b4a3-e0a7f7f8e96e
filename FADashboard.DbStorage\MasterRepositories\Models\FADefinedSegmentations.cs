﻿namespace FADashboard.DbStorage.MasterRepositories.Models;

public class FADefinedSegmentations
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool IsDeactive { get; set; }
    public long OutletId { get; set; }
    public long? ProductDivisionId { get; set; }
    public int Segmentation { get; set; }
    public string DisplayName { get; set; }
    public int MonthsConsidered { get; set; }
    public int PrescribedVisits { get; set; }
    public bool ForProductDivision { get; set; }
}
