﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs;

public class PositionCodeDTO
{
    public string CodeId { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public PositionCodeLevel Level { get; set; }
    public string Name { get; set; }
    public string AttachedTo { get; set; }
    public string ParentCode { get; set; }
    public long? ParentId { get; set; }
    public PositionCodeLevel ParentLevel { get; set; }
    public string ParentName { get; set; }
    public EmployeeType EmployeeType { get; set; }
    public List<long> OutletIds { get; set; }
    public List<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
}
