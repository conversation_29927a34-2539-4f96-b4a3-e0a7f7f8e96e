﻿using System.Collections.Concurrent;
using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.Infrastructure.QueueService;
using Library.ResilientHttpClient;
using Library.StorageWriter.Reader_Writer;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using OfficeOpenXml;

namespace FADashboard.Core.Services;

public class RoutePlaygroundService : RepositoryResponse
{
    private const string ContainerName = "ro-playground";
    private const int EstimationTimeHrs = 1;
    private readonly IRoutePlaygroundRepository routePlaygroundRepository;
    private readonly ICurrentUser currentUser;
    private readonly AppConfigSettings appConfigSettings;
    private readonly FaiDataLakeBlobWriter faiDataLakeBlobWriter;
    private readonly ICompanySettingsRepository companySettingsRepository;
    private readonly FAResilientHttpClient resilientHttpClient;

    public RoutePlaygroundService(
        IRoutePlaygroundRepository routePlaygroundRepository,
        ICurrentUser currentUser,
        AppConfigSettings appConfigSettings,
        FaiDataLakeBlobWriter faiDataLakeBlobWriter,
        FAResilientHttpClient resilientHttpClient,
        ICompanySettingsRepository companySettingsRepository)
    {
        this.routePlaygroundRepository = routePlaygroundRepository;
        this.currentUser = currentUser;
        this.appConfigSettings = appConfigSettings;
        this.faiDataLakeBlobWriter = faiDataLakeBlobWriter;
        this.companySettingsRepository = companySettingsRepository;
        this.resilientHttpClient = resilientHttpClient;
        ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
    }

    public async Task<List<RoutePlaygroundDetailDto>> GetRoutePlaygroundDetails(CancellationToken ct = default)
    {
        var data = await routePlaygroundRepository.GetRoutePlaygroundDetails(currentUser.CompanyId, currentUser.LocalId, currentUser.UserRole, ct);
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var offset = companySettings.TimeZoneOffset;
        return data.Select(s => new RoutePlaygroundDetailDto
        {
            Id = s.Id,
            InputFileName = s.InputFileName,
            ExecutedAt = s.ExecutedAt,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            StatusRemark = s.StatusRemark,
            EmailId = s.EmailId,
            InputType = s.InputType,
            EstimatedTime = (s.Status is RoutePlaygroundStatus.Executed || s.Status is RoutePlaygroundStatus.ErrorDuringExecution) ? s.CreatedAt.AddMinutes(100).ToString("HH:mm , MMM dd") : DateTime.UtcNow.Add(offset) >= s.CreatedAt.Add(offset).AddHours(EstimationTimeHrs)
                                ? DateTime.UtcNow.Add(offset).AddMinutes(30).ToString("HH:mm , MMM dd")
                                : s.CreatedAt.Add(offset).AddHours(EstimationTimeHrs).ToString("HH:mm , MMM dd")
        }).OrderByDescending(s => s.Id).ToList();
    }

    public async Task<RepositoryResponse> CreateRoutePlaygroundDetail(IFormFile file, RoutePlaygroundInputConstraints inputConstraints, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var country = companySettings.GetCountryName.ToLower();
        var response = await routePlaygroundRepository.SaveRoutePlaygroundDetails(currentUser.LocalId,
            currentUser.CompanyId, currentUser.UserRole, file?.FileName ?? $@"AutomaticMaster_{currentUser.LocalId}_{DateTime.Today.Date:dd/MM/yyyy}.xlsx", currentUser.EmailId, inputConstraints, ct);

        if (response.Id != 0)
        {
            var queueHandler = new QueueHandler<RoutePlaygroundQueueModel>(QueueType.RoPlaygroundFileSplitQueue, appConfigSettings.StorageConnectionString);
            if (inputConstraints.InputType == InputType.Automatic)
            {
                queueHandler = new QueueHandler<RoutePlaygroundQueueModel>(QueueType.RoPlaygroundAutomaticMasterQueue, appConfigSettings.StorageConnectionString);
            }
            else
            {
                var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                await faiDataLakeBlobWriter.UploadFileAsync(ContainerName, file, $"{response.Id}/{file.FileName}", contentType);
            }
            var queueObj = new RoutePlaygroundQueueModel
            {
                Id = response.Id ?? 0,
                FileName = file?.FileName ?? $@"AutomaticMaster_{currentUser.LocalId}_{DateTime.Today.Date:dd/MM/yyyy}.xlsx",
                MaximumDailyDistance = inputConstraints.MaximumDailyDistance,
                MaximumDailyVisits = inputConstraints.MaximumDailyVisits,
                SpectralCoefficient = inputConstraints.SpectralCoefficient.ToString(),
                OutlierAddition = inputConstraints.OutlierAddition.ToString(),
                JourneyType = inputConstraints.JourneyType.ToString(),
                Country = country
            };
            await queueHandler.AddToQueue(queueObj);
            return response;
        }

        return response;
    }

    public async Task<Stream> DownloadFile(string filePath, CancellationToken ct = default)
    {
        var stream = await faiDataLakeBlobWriter.OpenFileReadStreamAsync(ContainerName, filePath, ct);
        return stream;
    }

    public async Task<RouteDataModel> GetRouteData(long id, CancellationToken ct)
    {
        var requestDetail = await routePlaygroundRepository.GetRoutePlaygroundDetails(id, ct);
        if (requestDetail.Count > 0 && requestDetail.First().CompanyId == currentUser.CompanyId && requestDetail.First().Status == RoutePlaygroundStatus.Executed)
        {
            var employeeListPath = $"{id}/employee_list.json";
            var dayListPath = $"{id}/days_list.json";
            var employeeList = await GetDataFromBlob<List<string>>(employeeListPath, ct);
            var daysList = await GetDataFromBlob<List<string>>(dayListPath, ct);

            return new RouteDataModel{
                EmployeeList = employeeList,
                DaysList = daysList
            };
        }
        else
        {
            throw new BadHttpRequestException("You are not authorized to access this request");
        }
    }

    public async Task<T> GetDataFromBlob<T>(string filePath, CancellationToken ct)
    {
        var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync(ContainerName, filePath, ct);
        using var reader = new StreamReader(stream);
        var jsonString = await reader.ReadToEndAsync(cancellationToken: ct);
        return JsonConvert.DeserializeObject<T>(jsonString);
    }

    public async Task<List<RoutePlaygroundOutletModel>> GetRouteOutletData(long id,List<string> employeeIds,List<string> daysList, CancellationToken ct = default)
    {
        var result = new ConcurrentBag<List<RoutePlaygroundOutletModel>>();
        var maxDegreeOfParallelism = 5;
        await Parallel.ForEachAsync(employeeIds, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism,
            CancellationToken = ct
        },
        async (employee, token) =>
        {
            foreach (var day in daysList)
            {
                var fileName = $"{id}/Routes/{employee}/{(int.TryParse(day, out var d) ? d.ToString("00") : day)}/route.json";
                var routeOutletData = await GetDataFromBlob<List<RoutePlaygroundOutletModel>>(fileName, ct);

                if (routeOutletData != null)
                {
                    result.Add(routeOutletData);
                }
            }
        });

        return result.SelectMany(s => s).ToList();
    }

    public async Task SaveRoutePlaygroundData(long id, List<RoutePlaygroundOutletModel> outletsData,bool isOriginal,List<RemovedRouteModel> removedRoutes, CancellationToken ct = default)
    {
        await routePlaygroundRepository.UpdateRoutePlaygroundDetail(id, currentUser.LocalId, ct);
        var outletJsonData = JsonConvert.SerializeObject(new {
            Outlets= outletsData,
            IsOriginal= isOriginal,
            RemovedRoutes = removedRoutes
        });
        var fileName = $"change-{DateTime.UtcNow.ToString("dd-MM-yyyy HH:mm:ss")}.json";
        await faiDataLakeBlobWriter.UploadJsonToBlobAsync(ContainerName, $"{id}/{fileName}", outletJsonData,ct);
        var queueHandler = new QueueHandler<RoutePlaygroundChangeQueueModel>(QueueType.RoPlaygroundUpdationQueue, appConfigSettings.StorageConnectionString);
        var queueObj = new RoutePlaygroundChangeQueueModel
        {
            Id = id,
            FileName = fileName
        };
        await queueHandler.AddToQueue(queueObj);
    }

    public async Task<List<RoutePlaygroundOutletModel>> GetOptimizedRoutes(OptimizedRouteInputModel outletsData, CancellationToken ct = default)
    {
        var dataUrl = $"{appConfigSettings.tspApiBaseUrl}/SolveTsp";
        var result = await resilientHttpClient.PostJsonAsync<OptimizedRouteOutputModel> (dataUrl, appConfigSettings.tspApiToken, new
        {
            unoptimized_sequence = outletsData.Outlets,
            country = outletsData.Country
        });
        return result.optimized_sequence;
    }
}
