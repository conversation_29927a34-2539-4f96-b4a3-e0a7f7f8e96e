﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class DistributorTransit
{
    public long DistributorId { get; set; }
    public double? TransitTime { get; set; }
}

public class CompanyFactoryIO : CompanyFactories
{

    public string Address { get; set; }
    public string Pincode { get; set; }
    public string GSTIN { get; set; }
    public long RegionId { get; set; }
    public string RegionName { get; set; }
    public FactoryProductType ProductEntityType { get; set; }
    public List<long?> ProductEntityIds { get; set; }
    public string EmailID { get; set; }
    public string PanNumber { get; set; }
    public string FactoryUnitName { get; set; }
    public string FssaiNo { get; set; }
    public string ContactPersonName { get; set; }
    public string BankName { get; set; }
    public string BankAccountNumber { get; set; }
    public string BankAddress { get; set; }
    public string IfscCode { get; set; }
    public string BankMicrCode { get; set; }
    public string BranchName { get; set; }
    public string StateCode { get; set; }
    public List<DistributorTransit> DistributorTransits { get; set; }
}
