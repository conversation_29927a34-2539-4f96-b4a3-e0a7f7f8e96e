﻿using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;


namespace FADashboard.Core.Interfaces;

public interface IRegionRepository
{
    Task<RepositoryResponse> ActivateDeactivateRegion(long regionId, bool action, long companyId);

    Task<RepositoryResponse> CreateRegion(GeographiesListWithParent zone, long companyId);

    Task<List<EntityMinIncludeParent>> GetAllActiveRegions(long companyId);

    Task<List<EntityMin>> GetAllActiveRegions(long companyId, List<long> Ids);

    Task<List<GeographiesListWithParent>> GetAllRegions(long companyId, bool includeDeactivate);

    Task<GeographyInput> GetRegionById(long regionId, long companyId);

    Task<List<GeographyList>> GetRegionBySearch(long companyId, string searchString);

    Task<Dictionary<long, GeographyList>> GetRegionDictionary(long companyId);

    Task<List<EntityMinIncludeParent>> GetRegionsByZoneIds(long companyId, List<long> zoneIds);

    Task<List<EntityMin>> GetRegionsMinByIds(long companyId, List<long> Ids);

    Task<List<EntityMin>> GetRegionsUnderManager(long managerId, PortalUserRole managerRole, long companyId);

    Task<List<GeographiesListParent>> GetZoneByRegion(long companyId);

    Task<RepositoryResponse> UpdateRegion(GeographiesListWithParent region, long companyId);

    Task<string> GetRegionShortCodeBeat(long companyId, long beatId);
}
