﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class BeatOMeterRuleForSegmentation
{
    public long Id { get; set; }
    public BeatOMeterRule OutletReachRule { get; set; }
    public long OutletReachRuleId { get; set; }
    public OutletSegmentationAttribute OutletSegmentationAttribute { get; set; }
    public long OutletSegmentationAttributeId { get; set; }
}
