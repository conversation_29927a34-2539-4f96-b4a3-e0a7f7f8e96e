﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IAssetDefinitionsRepository
{
    Task<List<AssetDefinitionsList>> GetAllAssets(long companyId, bool includeDeactive);
    Task<AssetDefinitionsList> GetAssetById(long companyId, long id);
    Task<RepositoryResponse> DeactivateAsset(long companyId, long id);
    Task<RepositoryResponse> CreateAsset(long companyId, AssetDefinitionsList assetDefinitionsInput);
    Task<RepositoryResponse> UpdateAsset(long companyId, long id, AssetDefinitionsList assetDefinitionsInput);
    Task<List<AssetDefinitionsList>> GetActiveDisplayTypes(long companyId);
    Task<List<AssetDefinitionsList>> GetActiveDisplayTypesForEntities(long companyId, List<long> entityIds);

}
