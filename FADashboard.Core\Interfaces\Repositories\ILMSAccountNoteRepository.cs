using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Repositories
{
    public interface ILMSAccountNoteRepository
    {
        Task<LMSAccountNoteDto> GetByIdAsync(long id);
        Task<PagedResult<LMSAccountNoteDto>> GetByAccountIdAsync(long accountId, LMSAccountNoteQueryParameters queryParameters);
        Task<LMSAccountNoteDto> CreateNoteAsync(LMSAccountNoteCreateInput noteDto, long createdByUserId, long companyId);
        Task<LMSAccountNoteDto> UpdateNoteAsync(long noteId, LMSAccountNoteUpdateInput noteDto, long updatedByUserId);
        Task<bool> DeleteNoteAsync(long noteId, long deletedByUserId);
    }
}
