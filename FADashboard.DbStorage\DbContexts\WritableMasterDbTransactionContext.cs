﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.DbStorage.MasterRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace FADashboard.DbStorage.DbContexts;

// to perform transaction based saving (to remove partial api saving logic)
public class WritableMasterDbTransactionContext(DbContextOptions<WritableMasterDbTransactionContext> options, ICurrentUser identity) : DbContextAbstract(options, identity)
{
    public DbSet<GlobalOutletMetric> GlobalOutletMetrices { get; set; }
    public DbSet<OutletMetric> OutletMetrices { get; set; }
    public DbSet<CueCardsMaster> CueCards { get; set; }
    public DbSet<CompanySetting> CompanySettings { get; set; }
    public DbSet<CompanySettingValue> CompanySettingValues { get; set; }

    public DbSet<Company> Companies { get; set; }
    public DbSet<PerfectEntityRule> PerfectEntityRules { get; set; }
    public DbSet<PerfectEntityRuleCriteria> PerfectEntityRuleCriterias { get; set; }


    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
    }
}
