﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface IFocusProductRepository
{
    Task<RepositoryResponse> CreateFocusProduct(FocusProductInput focusProduct, long companyId);

    Task<RepositoryResponse> DeactivateFocusProduct(long focusProductId, long companyId);

    Task<FocusProductInput> GetFocusProductById(long focusProductId, long companyId);

    Task<List<FocusProductList>> GetFocusProducts(long companyId, bool includeDeactivate);

    Task<RepositoryResponse> UpdateFocusProduct(FocusProductInput focusProduct, long companyId);
}
