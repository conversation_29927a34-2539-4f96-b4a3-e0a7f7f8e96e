﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IFlexibleReportRepository
{
    Task<List<FlexibleReports>> GetAllFlexibleReports(long companyId, PortalUserRole userRole, long userId);

    Task<FlexibleReport> GetFlexibleReportById(long reportId, PortalUserRole userRole);

    Task<RepositoryResponse> SaveFlexibleReport(long companyId, FlexibleReport data);

    Task<RepositoryResponse> AddFlexibleReportToQueue(FlexibleReport report, RequestMode requestMode, long reportId = 1001, bool dateManipulated = false);
    Task<RepositoryResponse> AddToFlexibleReportBuilderQueue(FlexibleReportBuilderRequest report, RequestMode requestMode);

    Task<List<FlexibleReportsMin>> GetAllFlexibleReportsList(long companyId, PortalUserRole userRole, long userId);
    Task<RepositoryResponse> ActivateDeactivateFlexibleReport(long reportId, long companyId, bool action);
    Task<RepositoryResponse> UpdateFlexibleReportSubscription(long flexibleReportId, long companyId, string subscriptionRoles);
    Task<RepositoryResponse> UpdateFlexibleReportEmailSubscription(long companyId, long flexibleReportId, string subscriptionRoles, string subscriptionToAdmins, bool IsActive, EmailFrequency frequency);

    Task<FlexibleTargetMaster> GetTargetMaster(long targetId);
    Task<RepositoryResponse> AddTargetAchievementReportToQueue(FlexibleReport report, RequestMode requestMode, long reportId = 1001, bool dateManipulated = false);
}
