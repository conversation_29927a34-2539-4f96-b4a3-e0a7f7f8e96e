﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;
public class OutletVerification
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string ContactNo { get; set; }
    public bool IsContactVerified { get; set; }
    public bool IsValid { get; set; }
    public long LocationId { get; set; }
    public long EmployeeId { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string ImageId { get; set; }
    public bool IsImageVerified { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public bool IslatlongVerified { get; set; }
    public VerificationStatus VerificationStatus { get; set; }
    public long? OldTableId { get; set; }
    public long? ActionTakenBy { get; set; }
    public DateTime? ActionTakenTime { get; set; }
    public string RejectionReason { get; set; }
    public DateTime CreatedAt { get; set; }
}
