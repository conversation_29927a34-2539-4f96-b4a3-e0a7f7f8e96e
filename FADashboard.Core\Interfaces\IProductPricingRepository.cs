﻿using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;

public interface IProductPricingRepository
{
    Task<ProductPricingResponse> UpdateProductsDefaultPricing(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductGSTCategoryFlat> gstTaxesDict, Dictionary<long, ProductCESSCategoryFlat> cessTaxesDict,
        Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingResponse> UpdateProductsRegionalPricing(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductGSTCategoryFlat> gstTaxesDict, Dictionary<long, ProductCESSCategoryFlat> cessTaxesDict,
        Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingResponse> UpdateProductsStockistCategoryPricing(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductGSTCategoryFlat> gstTaxesDict, Dictionary<long, ProductCESSCategoryFlat> cessTaxesDict,
        Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingResponse> UpdateProductsStockistPricing(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductGSTCategoryFlat> gstTaxesDict, Dictionary<long, ProductCESSCategoryFlat> cessTaxesDict,
        Dictionary<long, ProductList> productDictionary);

    Task<List<decimal>> GetProductPricingMrp(List<long> productIds, long companyId);

    Task<ProductPricingResponse> UpdateProductsDefaultPrice(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingResponse> UpdateProductsRegionalPrice(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingResponse> UpdateProductsStockistCategoryPrice(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingResponse> UpdateProductsStockistPrice(ProductPricingInput productPricingInput, long companyId, Dictionary<long, ProductList> productDictionary);

    Task<ProductPricingMasterTotal> GetPricingMasterPricingTypeWise(long companyId, PaginationFilter validFilter, PricingMastertype pricingMastertype);
}
