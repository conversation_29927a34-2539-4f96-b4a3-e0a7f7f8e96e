﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace FADashboard.Core.Helper.AsanaHelpers;
public class AsanaNewTask : AsanaTask
{
    public string Workspace { get; set; }

    private AsanaNewTask()
    {
        Workspace = "34125054317482";
    }
    public static AsanaNewTask CreateFrom(IRequirement requirement)
    {
        var taskModel = new AsanaNewTask()
        {
            Name = requirement.GetTitle(),
            html_notes = requirement.GetDescription(),
            Followers = requirement.GetFollowers(),
            Assignee = AsanaCredentials.GetCredentialsForUser(requirement.GetAssigneeName()).UserId,
            Tags = requirement.GetTags()
        };

        taskModel.Followers.Add(taskModel.Assignee);
        return taskModel;
    }

    public static AsanaNewTask Attachments(IRequirement requirement)
    {
        var taskModel = new AsanaNewTask()
        {
            Attachment = requirement.GetAttachment()
        };
        return taskModel;
    }

    [JsonProperty("html_notes")]
    public string html_notes { get; set; }
    public string Assignee { get; set; }
    public bool Completed { get; set; }
    public List<string> Followers { get; set; }
    public List<string> Projects { get; set; }
    public List<string> Tags { get; set; }

    //[BindProperty]
    public List<IFormFile> Attachment { get; set; }
}
