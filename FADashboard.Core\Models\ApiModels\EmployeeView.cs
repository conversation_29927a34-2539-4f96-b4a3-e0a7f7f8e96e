﻿using FADashboard.Core.Models.ViewModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class EmployeeView
{
    public bool AllowOrderBooking { get; set; }
    public long? AreaSalesManagerId { get; set; }
    public long CompanyId { get; set; }
    public long? CompanyZoneId { get; set; }
    public string ContactNo { get; set; }
    public DateTime DateOfCreation { get; set; }
    public DateTime? DateOfJoining { get; set; }
    public DateTime? DateOfLeaving { get; set; }
    public long? DesignationId { get; set; }
    public string EmailId { get; set; }

    //public string Position { get; set; }
    //public string PositionName { get; set; }
    //public string PositionLevel { get; set; }
    public string ErpId { get; set; }

    public Guid GuId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsFieldAppuser { get; set; }
    public string LocalName { get; set; }
    public string Name { get; set; }
    public long OldTableId { get; set; }
    public PositionMinParent PositionsWithParent { get; set; }
    public List<long> ProductDivisionMapping { get; set; }
    public EmployeeRank Rank { get; set; }
    public string Region { get; set; }
    public long? RegionId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public UserActiveStatus UserStatus { get; set; }
    public EmployeeType UserType { set; get; }
    public string EmployeeAttributeText1 { get; set; }
    public string EmployeeAttributeText2 { get; set; }
    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
    public Guid? LoginGuid { get; set; }
    public List<long> RoleIds { get; set; }
    public bool IsTrainingUser { get; set; }
    public string JourneyPlanEntity { get; set; }
    public long? TeamId { get; set; }

    public long? AttendanceNormsTeamId { get; set; }

    public long ? BattleGroundTeamId { get; set; }
}
