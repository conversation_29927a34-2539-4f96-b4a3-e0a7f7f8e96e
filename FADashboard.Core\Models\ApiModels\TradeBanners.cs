﻿namespace FADashboard.Core.Models.ApiModels;
public class TradeBanners
{
    public long Id { get; set; }
    public string Title { get; set; }
    public string Image { get; set; }
    public string Link { get; set; }
    public bool IsActive { get; set; }
    public List<long> RegionIds { get; set; }
    public bool CtaEnable { get; set; }
    public string CtaUrl { get; set; }
    public string CtaName { get; set; }
    public CtaType CtaType { get; set; }
    public ScrollType CtaScrollType { get; set; }
    public AppScreenType CtaAppScreenType { get; set; }
}
public enum CtaType
{
    NotApplicable = 0,
    URL = 1,
    Scroll = 2,
    AppScreen = 3,
}
public enum ScrollType
{
    NotApplicable = 0,
    NewLaunch = 1,
    PromotionalProducts = 2,
    BuyAgain = 3,
}
public enum AppScreenType
{
    NotApplicable = 0,
    OrderBooking = 1,
    OrderStatus = 2,
    ELearning = 3,
    QPSSchemes = 4,
    Loyalty = 5
}
