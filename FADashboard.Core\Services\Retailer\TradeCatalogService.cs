﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using Microsoft.AspNetCore.Http;

namespace FADashboard.Core.Services.Retailer;

public class TradeCatalogService(ITradeCatalogRepository tradeCatalogRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<ApiResponse> BulkUploadTradeCatalogs(string containerName, IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            return ApiResponse.GetFailure("Zip File is either empty or not uploaded.");
        }

        var response = await tradeCatalogRepository.BulkUploadTradeCatalogs(currentUser.CompanyId, file, containerName);

        response.CalculateResponse();

        return response;
    }

}
