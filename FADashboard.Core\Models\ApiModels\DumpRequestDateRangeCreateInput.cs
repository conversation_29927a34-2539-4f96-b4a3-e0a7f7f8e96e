﻿using Libraries.CommonEnums;
using static Libraries.CommonEnums.TypesUsedInReports;

namespace FADashboard.Core.Models.ApiModels;

public class DumpRequestDateRangeCreateInput
{
    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }
    public List<long> PCUserIds { get; set; }
    public List<long> PCIds { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public int? PCUserLevel { get; set; }
    public int? PositionCodeLevel { get; set; }
    public List<long> ProductFilterIds { get; set; }
    public int? ProductFilterLevel { get; set; }
    public bool ShowAllColumns { get; set; }
    public Guid? SubscriptionKey { get; set; }
    public int? ShowDataFor { get; set; }
}

public class ViewReportFilter
{
    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }
    public List<long> PCUserIds { get; set; }
    public List<long> PCIds { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public int? PCUserLevel { get; set; }
    public int? PositionCodeLevel { get; set; }
    public List<long> ProductIds { get; set; }
    public int? ProductHierarchy { get; set; }
    public bool ShowAllColumns { get; set; }
    public int? ShowDataFor { get; set; }
    public long SubscriptionId { get; set; }
    public int ReportType { get; set; }
}

public class DumpRequestReportCreateInput
{
    // Required parameters
    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public string StartMonth { get; set; }
    public string EndMonth { get; set; }
    public int StartYear { get; set; }
    public int EndYear { get; set; }
    public string Month { get; set; }
    public int Year { get; set; }
    public long? FromDateTimeStamp { get; set; }
    public long? ToDateTimeStamp { get; set; }
    public List<long> PCUserIds { get; set; }
    public List<long> PCIds { get; set; }
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public PositionCodeLevel? PCUserLevel { get; set; }
    public PositionCodeLevel? PositionCodeLevel { get; set; }

    // Extra Json parameters
    public int Channel { get; set; }

    public long DistributorId { get; set; }
    public List<long> DistributorIds { get; set; }
    public List<long> GeoFilterIds { get; set; }
    public GeographicalHierarchy GeoFilter { get; set; }
    public bool UsesGeographyMultiSelect { get; set; }
    public List<long> ProductFilterIds { get; set; }
    public ProductHierarchy ProductFilterLevel { get; set; }
    public ProductHierarchyEnum? Producthierarchy { get; set; }
    public bool ShowAllColumns { get; set; }
    public bool ShowActiveDataOnly { get; set; }
    public bool DateInvoiceOrder { get; set; }

    public Guid? SubscriptionKey { get; set; }

    public bool SaveFlexibleReport { get; set; }
    public PSODShowDataForEnum? ShowDataFor { get; set; }
    public PositionCodeLevel? BasisPCLevel { get; set; }
    public PositionCodeLevel? Rank { get; set; }
    public FieldUserType? FieldUserType { get; set; }
    public List<long> UserPositionCodeIds { get; set; }
    public EnumForReportAssembly? ReportType { get; set; } // added only for report download of type master report
    public int? SurveyId { get; set; }
    public StockistType? StockistType { get; set; }
    public List<long> SchemeIds { get; set; }
    public OutletStatus? OutletStatus { get; set; }
    public string ReportName { get; set; }
    public CampaignTaskStatus CampaignTaskStatus { get; set; }
    public string CampaignName { get; set; }
    public List<long> CampaignNames { get; set; }
    public bool IsIncludeAll { get; set; }
    public long? GameId { get; set; }
    public List<string> VariantName { get; set; }
    public long? BeatometerRuleId { get; set; }
    public bool ShowHierarchy { get; set; }
    public bool ShowPosHierarchy { get; set; }
    public bool ShowBlankForLowerHierarchy { get; set; }
    public bool ViewPositionHierarchy { get; set; }
    public bool ShowLiveData { get; set; }

}
