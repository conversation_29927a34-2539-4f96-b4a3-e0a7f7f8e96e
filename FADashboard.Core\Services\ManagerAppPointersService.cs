﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums.Helpers;

namespace FADashboard.Core.Services;

public class ManagerAppPointersService(
    IDataPointerMappingsRepository dataPointerMappingsRepository,
    ICurrentUser currentUser,
    NomenclatureService nomenclatureService,
    ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    public async Task<List<ManagerAppPointers>> GetManagerAppPointers()
    {
        var nomenclatureDict = await nomenclatureService.GetCompanyNomenclatureDict();
        var displayNameDict = nomenclatureDict.ToDictionary(p => p.Key, p => p.Value.DisplayName);
        var viewModel = new ManagerAppDataPointer(displayNameDict).AllPointers;
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var CompanyUsesOpenMarketOperations = companySettings.UsesOpenMarketOperations;
        viewModel = CompanyUsesOpenMarketOperations ? viewModel : viewModel.Where(x => x.useCompanySettings != "CompanyUsesOpenMarketOperations").ToList();
        return await dataPointerMappingsRepository.GetManagerAppPointers(currentUser.CompanyId, viewModel);
    }

    public async Task<RepositoryResponse> SaveManagerAppPointers(ManagerAppPointerInput inputPointers) => await dataPointerMappingsRepository.SaveManagerAppPointers(inputPointers, currentUser.CompanyId);

    public async Task<List<ManagerAppPointers>> GetManagerDataPointers(long companyId, bool CompanyUsesOpenMarketOperations = false)
    {
        var data = await dataPointerMappingsRepository.GetManagerDataPointers(companyId);
        if (data == null)
        {
            var pointers = CompanyUsesOpenMarketOperations ? [.. ManagerAppDataPointer.Pointers] : ManagerAppDataPointer.Pointers.Where(x => x.useCompanySettings != "CompanyUsesOpenMarketOperations").ToList();
            return pointers.Where(d => d.IsForManager && (d.PointerStateForManagers == PointerState.Fixed || d.PointerStateForManagers == PointerState.Default)).Select(d => d).ToList();
        }

        return ManagerAppDataPointer.Pointers.Where(d => data.Contains(d.Name)).Select(d => d).ToList();
    }

    public async Task<List<ManagerAppPointers>> GetUserDataPointers(long companyId)
    {
        var data = await dataPointerMappingsRepository.GetUserDataPointers(companyId);
        if (data == null)
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var CompanyUsesOpenMarketOperations = companySettings.UsesOpenMarketOperations;
            var pointers = CompanyUsesOpenMarketOperations ? [.. ManagerAppDataPointer.Pointers] : ManagerAppDataPointer.Pointers.Where(x => x.useCompanySettings != "CompanyUsesOpenMarketOperations").ToList();
            return pointers.Where(d => d.IsForUser && (d.PointerStateForUsers == PointerState.Fixed || d.PointerStateForUsers == PointerState.Default)).Select(d => d).ToList();
        }

        return ManagerAppDataPointer.Pointers.Where(d => data.Contains(d.Name)).Select(d => d).ToList();
    }

    public async Task<List<ManagerAppPointers>> GetDashboardDataPointers(long companyId)
    {
        var data = await dataPointerMappingsRepository.GetDashboardDataPointers(companyId);
        if (data == null)
        {
            var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var CompanyUsesOpenMarketOperations = companySettings.UsesOpenMarketOperations;
            var pointers = CompanyUsesOpenMarketOperations ? [.. ManagerAppDataPointer.Pointers] : ManagerAppDataPointer.Pointers.Where(x => x.useCompanySettings != "CompanyUsesOpenMarketOperations").ToList();
            return pointers.Where(d => d.IsForDashboard && (d.PointerStateForDashboard == PointerState.Fixed || d.PointerStateForDashboard == PointerState.Default)).Select(d => d).ToList();
        }

        return ManagerAppDataPointer.Pointers.Where(d => data.Contains(d.Name)).Select(d => d).ToList();
    }
}
