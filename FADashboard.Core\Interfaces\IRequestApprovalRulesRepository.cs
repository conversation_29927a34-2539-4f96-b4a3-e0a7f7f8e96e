﻿using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IRequestApprovalRulesRepository
{
    Task<List<RequestApprovalRule>> GetRequestApprovalRulesList(long companyId, bool includeDeactivate, CancellationToken ct);
    Task<List<RequestApprovalRule>> GetRequestApprovalRules(ApprovalEngineRequesType approvalEngineRequestType, long companyId, bool includeDeactivate, CancellationToken ct);
    Task<RepositoryResponse> CreateRequestApprovalRule(RequestApprovalRule requestApprovalRule, long companyId, CancellationToken ct);
    Task<RepositoryResponse> UpdateRequestApprovalRule(RequestApprovalRule requestApprovalRule, long companyId, CancellationToken ct);
    Task<RepositoryResponse> ActivateDeactivateRequestApprovalRule(long requestId, bool action, long companyId, CancellationToken ct);
    Task<RequestApprovalRule> GetRequestApprovalRuleById(long requestId, long companyId, CancellationToken ct);
    Task <RepositoryResponse> UpdatePriorities(List<PriorityUpdateModel> priorityUpdates, long companyId, CancellationToken ct);
    Task<int> GetExistingRequestCountAsync(RequestApprovalRule requestApprovalRule, long companyId, CancellationToken ct);
    Task<List<ApprovalEntityApiModel>> GetApprovalEntitiesWithConfigs(ICurrentUser currentUser, CancellationToken ct);
    Task<RepositoryResponse> CreateOrUpdateApprovalEntityConfig(ApprovalEntityConfigModel model, ICurrentUser currentUser, CancellationToken ct);
}
