﻿using FADashboard.Core.Models;
using Libraries.CommonEnums;

namespace FADashboard.Core.Interfaces;
public interface IBugCreationRepository
{
    Task<List<AsanaBugModel>> GetAsanaBugs(Platform platform, string module);
    Task<RepositoryResponse> SaveAsana(AsanaBugModel data);
    Task<RepositoryResponse> AddToExistingAsana(long companyId, long asanaId);
    Task<List<BugIssueListView>> GetAllAsanaBugs(long companyId);
}
