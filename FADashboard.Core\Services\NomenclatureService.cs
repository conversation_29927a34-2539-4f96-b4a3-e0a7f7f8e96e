﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;

namespace FADashboard.Core.Services;

public class NomenclatureService(INomenclatureRepository nomenclatureRepository, ICurrentUser currentUser) : RepositoryResponse
{
    public async Task<RepositoryResponse> CompanyNomenclatureSave(List<CompanyNomenclature> nomenclatureValues)
    {
        var res = await nomenclatureRepository.UpdateNomenclatureValues(nomenclatureValues, currentUser.CompanyId);
        return res;
    }

    public async Task<RepositoryResponse> CreateDefaultNomenclatures(long companyId)
    {
        var res = await nomenclatureRepository.CreateDefaultNomenclatures(companyId);
        return res;
    }

    public async Task<Dictionary<string, CompanyNomenclature>> GetCompanyNomenclatureDict()
    {
        var nomenclatureDict = await nomenclatureRepository.GetAllNomenclature(currentUser.CompanyId);
        return nomenclatureDict.ToDictionary(n => n.Name, n => n);
    }

    public async Task<Dictionary<string, string>> GetHierarchyNomenclature()
    {
        var nomenclatureDic = new Dictionary<string, string>();
        var keys = new List<string>
        {
            "L1Position",
            "L2Position",
            "L3Position",
            "L4Position",
            "L5Position",
            "L6Position",
            "L7Position",
            "L8Position",
            "GlobalSalesManager",
            "NationalSalesManager",
            "ZonalSalesManager",
            "RegionalSalesManager",
            "AreaSalesManager",
            "Employee_test"
        };
        var nomenclatures = await nomenclatureRepository.GetCompanyNomenclature(currentUser.CompanyId, keys);
        foreach (var key in keys)
        {
            nomenclatureDic.Add(key, nomenclatures.TryGetValue(key, out var nomenclature) ? nomenclature.DisplayName : key);
        }

        return nomenclatureDic;
    }
}
