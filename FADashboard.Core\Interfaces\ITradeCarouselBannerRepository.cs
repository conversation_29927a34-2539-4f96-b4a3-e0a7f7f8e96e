﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ITradeCarouselBannerRepository
{
    Task<List<TradeBanners>> GetTradeCarouselBannersList(long companyId, bool showInactive);

    Task<RepositoryResponse> CreateTradeCarouselBanners(TradeBanners tradeBanner, long companyId);
    Task<RepositoryResponse> UpdateTradeCarouselBanners(TradeBanners tradeBanner, long companyId);

    Task<RepositoryResponse> DeactivateTradeCarouselBanner(long bannerId, long companyId);
    Task<TradeBanners> GetTradeCarouselBannerById(long id, long companyId);
}
