﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("EntityMarginSlabs")]
public class EntityMarginSlab : ICompanyEntity, IAuditedEntity, IDeletable
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    //public long LocationId { get; set; }
    public string Name { get; set; }
    public MarginEntity EntityType { get; set; }
    [NotMapped]
    public Dictionary<long, double> EntityMargins { get; set; }
    public string AttributeConstraints { get; set; }
    public string EntityMarginFlat
    {
        get => EntityMargins != null ? JsonConvert.SerializeObject(EntityMargins) : null;
        set => EntityMargins = value == null ? [] : JsonConvert.DeserializeObject<Dictionary<long, double>>(value);
    }

    public bool Deleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }

    public virtual Company Company { private set; get; }
    //public virtual Location Location { private set; get; }
}
