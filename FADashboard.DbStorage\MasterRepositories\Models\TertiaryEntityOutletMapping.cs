﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("TertiaryEntityOutletMapping")]
public class TertiaryEntityOutletMapping : ICreatedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long OutletId { get; set; }
    public long TertiaryEntityId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool IsDeleted { get; set; }
}
