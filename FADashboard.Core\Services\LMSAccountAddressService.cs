﻿using FADashboard.Core.Interfaces.Repositories;
using FADashboard.Core.Interfaces.Services;
using FADashboard.Core.Models.DTOs;

namespace FADashboard.Core.Services
{
    public class LMSAccountAddressService(ILMSAccountAddressRepository lmsAccountAddressRepository, ILMSAccountRepository lmsAccountRepository) : ILMSAccountAddressService
    {
        public async Task<LMSAccountAddressDto> GetAccountAddressByIdAsync(long id) => await lmsAccountAddressRepository.GetByIdAsync(id);

        public async Task<IEnumerable<LMSAccountAddressDto>> GetAllAccountAddressesAsync() => await lmsAccountAddressRepository.GetAllAsync();

        public async Task<IEnumerable<LMSAccountAddressDto>> GetAccountAddressesByAccountIdAsync(long accountId) =>
            await lmsAccountAddressRepository.GetByAccountIdAsync(accountId);

        public async Task<LMSAccountAddressDto> CreateAccountAddressAsync(LMSAccountAddressCreateInput accountAddressDto, long createdByUserId, long companyId)
        {
            ArgumentNullException.ThrowIfNull(accountAddressDto);

            var account = await lmsAccountRepository.GetByIdAsync(accountAddressDto.AccountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountAddressDto.AccountId} not found.");
            }

            var newAddress = new LMSAccountAddressDto
            {
                AccountId = accountAddressDto.AccountId,
                CompanyId = companyId,
                AddressName = accountAddressDto.AddressName,
                Street = accountAddressDto.Street,
                City = accountAddressDto.City,
                State = accountAddressDto.State,
                PinCode = accountAddressDto.PinCode,
                Country = accountAddressDto.Country,
                Latitude = accountAddressDto.Latitude,
                Longitude = accountAddressDto.Longitude,
                CreatedBy = createdByUserId,
                UpdatedBy = createdByUserId
            };

            return await lmsAccountAddressRepository.AddAsync(newAddress);
        }

        public async Task<LMSAccountAddressDto> UpdateAccountAddressAsync(long id, LMSAccountAddressUpdateInput accountAddressDto, long? updatedByUserId)
        {
            ArgumentNullException.ThrowIfNull(accountAddressDto);

            var existingAddress = await lmsAccountAddressRepository.GetByIdAsync(id);
            if (existingAddress == null)
            {
                throw new KeyNotFoundException($"Account address with ID {id} not found.");
            }

            existingAddress.AddressName = accountAddressDto.AddressName;
            existingAddress.Street = accountAddressDto.Street;
            existingAddress.City = accountAddressDto.City;
            existingAddress.State = accountAddressDto.State;
            existingAddress.PinCode = accountAddressDto.PinCode;
            existingAddress.Country = accountAddressDto.Country;
            existingAddress.Latitude = accountAddressDto.Latitude;
            existingAddress.Longitude = accountAddressDto.Longitude;
            existingAddress.UpdatedBy = updatedByUserId;

            return await lmsAccountAddressRepository.UpdateAsync(existingAddress);
        }

        public async Task<bool> DeleteAccountAddressAsync(long id, long? updatedByUserId)
        {
            var existingAddress = await lmsAccountAddressRepository.GetByIdAsync(id);
            if (existingAddress == null)
            {
                throw new KeyNotFoundException($"Account address with ID {id} not found.");
            }
            return await lmsAccountAddressRepository.DeleteAsync(id, updatedByUserId);
        }
    }
}
