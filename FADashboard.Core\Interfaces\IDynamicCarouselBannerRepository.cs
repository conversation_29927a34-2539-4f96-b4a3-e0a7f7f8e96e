﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;
public interface IDynamicCarouselBannerRepository
{
    Task<DynamicBanners> GetDynamicCarouselById(long dynamicBannerId, long companyId);
    Task<List<DynamicBanners>> GetDynamicCarouselInfo(long companyId, bool includeDeactive = true);
    Task<RepositoryResponse> CreateUpdateDynamicCarouselBanners(DynamicBanners dynamicBanner, long companyId);
    Task<RepositoryResponse> ActivateDeactivateDynamicCarouselBanner(long bannerId, bool action, long companyId);
}
