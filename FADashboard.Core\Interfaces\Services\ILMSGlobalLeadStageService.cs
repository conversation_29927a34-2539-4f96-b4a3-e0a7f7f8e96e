using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSGlobalLeadStageService
    {
        Task<LMSGlobalLeadStageDto> GetGlobalLeadStageByIdAsync(long id);
        Task<IEnumerable<LMSGlobalLeadStageDto>> GetAllGlobalLeadStagesAsync();
        Task<IEnumerable<LMSGlobalLeadStageDto>> GetGlobalLeadStagesByCompanyIdAsync(long companyId);
        Task<LMSGlobalLeadStageDto> CreateGlobalLeadStageAsync(LMSGlobalLeadStageDto stageDto, long createdByUserId);
        Task<LMSGlobalLeadStageDto> UpdateGlobalLeadStageAsync(long id, LMSGlobalLeadStageDto stageDto, long? updatedByUserId);
        Task<bool> DeleteGlobalLeadStageAsync(long id, long? updatedByUserId);
    }
}
