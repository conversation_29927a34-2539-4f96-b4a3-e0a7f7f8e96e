﻿using EntityHelper;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class Holidays : IAuditedEntity
{
    public long Id { get; set; }
    public DateTime Date { get; set; }
    public string Remark { get; set; }
    public long RegionId { get; set; }
    public bool IsDeleted { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public virtual Company Company { get; set; }
    public virtual Regions Region { get; set; }
}

