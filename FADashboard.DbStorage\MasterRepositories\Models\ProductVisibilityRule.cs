﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ProductVisibilityRules")]
public class ProductVisibilityRule: IAuditedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public ProductOrderType OrderType { get; set; }
    public ProductRuleType RuleType { get; set; }
    public ProductVisibilityTag ProductVisibilityTag { get; set; }
    public string GeographyConstraint { get; set; }
    public string DistributorConstraint { get; set; }
    public string FactoryConstraint { get; set; }
    public string OutletConstraint { get; set; }
    public string ProductConstraint { get; set; }
    public string TertiaryConstraint { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public string CreationContext { get; set; }
}
