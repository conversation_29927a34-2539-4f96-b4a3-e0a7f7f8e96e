namespace FADashboard.Core.Models.DTOs
{
    public class LMSAccountTemplateQueryParameters
    {
        private const int MaxPageSize = 100;
        private int _pageSize = 10;

        public string Name { get; set; } // Search filter for name

        public int PageNumber { get; set; } = 1;

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        public string SortBy { get; set; } = "UpdatedAt"; // Default sort
        public string Order { get; set; } = "desc";
    }
}
