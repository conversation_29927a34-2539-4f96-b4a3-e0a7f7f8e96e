﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class ParameterReference
{
    public string Parameter { get; set; }
    public string Reference { get; set; }
}
public class GlobalOutletMetricInput
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public string TransactionSqlQuery { get; set; }
    public string ReportSqlQuery { get; set; }
    public string MasterSqlQuery { get; set; }
    public OutletMetricQueryRelation QueriesRelation { get; set; }
    public OutletMetricDataType DataType { get; set; }
    public CueCardPerspective Perspective { get; set; }
    public List<ParameterReference> ParameterReferences { get; set; }
    public List<string> ColumnFields { get; set; }
    public string HCCBSQLQuery { get; set; }
    public string ClickHouseQuery { get; set; }
}
