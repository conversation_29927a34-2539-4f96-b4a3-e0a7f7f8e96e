﻿using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class AdvanceLeaveSubmission
{
    public ActionOnLeave ActionOnLeave { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public bool Deleted { get; set; }
    public long EmployeeId { get; set; }
    public DateTime EndDate { get; set; }
    public long Id { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long? ManagerId { get; set; }
    public int NumberOfDays { get; set; }
    public int NumberOfLeavesApproved { get; set; }

    [StringLength(1024)]
    public string Reason { get; set; }

    public DateTime StartDate { get; set; }
    public string UserName { get; set; }
    public string UserPosition { get; set; }
    public string ImageGuid { get; set; }
}
