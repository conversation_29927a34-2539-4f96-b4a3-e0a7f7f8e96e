﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.DTOs
{
    public class LMSLeadTaskQueryParameters
    {
        private const int MaxPageSize = 20;
        private int _pageSize = 5;

        public int PageNumber { get; set; } = 1;
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        public long? AssignedTo { get; set; }
        public LMSTaskPriority? Priority { get; set; }
        public LMSTaskStatus? Status { get; set; }
    }
}
