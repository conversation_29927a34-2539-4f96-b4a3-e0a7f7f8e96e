﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("IDSLogins")]
public class IdsLogins : IUpdatableEntity, IDeactivatable, ICreatedEntity
{
    public virtual Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public string EmailId { get; set; }
    public long Id { get; set; }
    public bool IsDeactive { get; set; }

    public bool IsManager =>
        UserRole is PortalUserRole.AreaSalesManager or
            PortalUserRole.RegionalSalesManager or
            PortalUserRole.ZonalSalesManager or
            PortalUserRole.NationalSalesManager or
            PortalUserRole.GlobalSalesManager;

    public DateTime LastUpdatedAt { get; set; }
    public long LocalId { get; set; }
    public Guid LoginGuid { get; set; }
    public string Name { get; set; }
    public string PhoneNo { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string RoleIds { get; set; }
    public string UserName { get; set; }
}
