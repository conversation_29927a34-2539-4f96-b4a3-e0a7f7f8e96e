﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class UserWiseCustomReport
{
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public long CustomReportId { get; set; }
    public long EmployeeId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public Dictionary<string, object> SelectedReportPreferences { get; set; }
    public PortalUserRole UserRole { get; set; }
    public List<UserWiseCustomReportItem> UserWiseCustomReportItems { get; set; }
}

public class UserWiseCustomReportItem
{
    public DateTime CreatedAt { get; set; }
    public long CustomReportItemId { get; set; }
    public long Id { get; set; }
    public bool IsSelected { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public UserWiseCustomReport UserWiseCustomReport { get; set; }
    public long UserWiseCustomReportId { get; set; }
}
