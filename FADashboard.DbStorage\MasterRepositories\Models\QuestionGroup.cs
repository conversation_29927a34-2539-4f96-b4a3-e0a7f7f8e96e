﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class QuestionGroup
{
    public bool Deactivated { get; set; }

    public bool? Deleted { set; get; }

    [StringLength(500)] public string Description { get; set; }
    [StringLength(500)] public string DynamicDescription { get; set; }

    public int DisplayOrder { get; set; }

    [ForeignKey("JsonFormId")]
    public virtual Survey Form { get; set; }

    public long FormId { get; set; }

    public GroupType GroupType { get; set; }

    public long ID { get; set; }

    [Column("JsonForm_Id")]
    public long JsonFormId { get; set; }

    public List<Question> Questions { get; set; }

    [StringLength(50)] public string ShowCondition { get; set; }

    [StringLength(200)] public string Title { get; set; }
}
