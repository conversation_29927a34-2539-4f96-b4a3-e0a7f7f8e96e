﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;
public class RoutePlanAutomationDetail : IAuditedEntity
{
    public long Id { get; set; }

    public long UserId { get; set; }

    public long CompanyId { get; set; }

    public PortalUserRole UserRole { get; set; }

    public string InputFileName { get; set; }

    public DateTime CreatedAt { get; set; }

    public RouteAutomationStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string Email { get; set; }

    public DateTime StartDate { get; set; }
}

public class RoutePlanManualDJPDetail : IAuditedEntity
{
    public long Id { get; set; }

    public string InputFileName { get; set; }

    public DateTime CreatedAt { get; set; }

    public long CompanyId { get; set; }

    public long UserId { get; set; }

    public PortalUserRole UserRole { get; set; }

    public RouteAutomationStatus Status { get; set; }

    public string StatusRemark { get; set; }

    public DateTime? ExecutedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public DateTime StartDate { get; set; }

    public string Email { get; set; }
}
