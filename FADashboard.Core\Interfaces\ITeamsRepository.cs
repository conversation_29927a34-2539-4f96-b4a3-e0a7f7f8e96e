﻿using FADashboard.Core.Models.ApiModels;

namespace FADashboard.Core.Interfaces;

public interface ITeamsRepository
{
    Task<List<SendTeamInfo>> GetAllTeams(long companyId, bool includeDeactive = false);
    Task<SendTeamInfo> GetTeamById(long companyId, long id);
    Task<RepositoryResponse> DeactivateTeam(long companyId, long id);
    Task<RepositoryResponse> CreateTeam(long companyId, Team teamInput);
    Task<RepositoryResponse> UpdateTeam(long companyId, Team teamInput);
    Task<List<long>> GetAttendanceNormActiveTeam(long companyid);
    Task<Dictionary<long, string>> GetTeamName(List<long> teamid);
}
