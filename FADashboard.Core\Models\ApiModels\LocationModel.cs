﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class LocationModel
{
    public string Address { get; set; }
    public string ErpId { get; set; }
    public string GUID { get; set; }
    public long Id { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string MarketName { get; set; }
    public string Name { get; set; }
    public long RegionId { get; set; }
    public string RegionName { get; set; }
    public string ShopName { get; set; }
    public OutletSegmentation Segmentation { get; set; }
    public long BeatId { get; set; }
    public string Aadhar { get; set; }
    public string AccountHoldersName { get; set; }
    public string BankAccountNumber { get; set; }
    public string IFSCCode { get; set; }
    public string City { get; set; }
    public string SubCity { get; set; }
    public string Email { get; set; }
    public string State { get; set; }
    public string PinCode { get; set; }
    public string GSTIN { get; set; }
    public bool IsKYC { get; set; }
    public bool GSTRegistered { get; set; }
    public long? ShopTypeId { get; set; }
    public string PAN { set; get; }
    public string OwnersNo { get; set; }
    public string OwnersName { get; set; }
    public string FormattedAddress { get; set; }
    public string PlaceOfDelivery { set; get; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public string AttributeImage3 { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }
    public string ImageId { set; get; }
    public VerificationStatus VerificationStatus { get; set; }
    public string FSSAINumber { get; set; }
    public double? GeoAccuracy { get; set; }
    public long? GeographicalMappingId { get; set; }
}
