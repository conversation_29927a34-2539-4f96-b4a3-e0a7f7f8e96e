﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;
using Library.ResilientHttpClient;

namespace FADashboard.Core.Services;

public class ViewReportService(ICurrentUser currentUser, IReportRepository reportRepository, FAResilientHttpClient resilientHttpClient) : RepositoryResponse
{
    private readonly AppConfigSettings appConfigSettings;

    public async Task<string> GetReportDataReporting(DumpRequestDateRangeCreateInput inputData)
    {
        var subscription = await reportRepository.GetReportSubscription(currentUser.CompanyId, (Guid)inputData.SubscriptionKey);
        var data = new ViewReportFilter
        {
            StartDate = inputData.StartDate,
            EndDate = inputData.EndDate,
            UserId = inputData.UserId,
            UserRole = inputData.UserRole,
            PCIds = inputData.PCIds,
            PCUserIds = inputData.PCUserIds,
            PCUserLevel = inputData.PCUserLevel,
            PositionCodeLevel = inputData.PositionCodeLevel,
            ProductIds = inputData.ProductFilterIds,
            ProductHierarchy = inputData.ProductFilterLevel,
            ShowAllColumns = inputData.ShowAllColumns,
            ShowDataFor = inputData.ShowDataFor,
            SubscriptionId = subscription.Id,
            ReportType = (int)subscription.Report.ReportType,
        };
        var dataUrl = $"{appConfigSettings.reportApiBaseUrl}api/viewreport/previewReport?companyId={currentUser.CompanyId}";
        var result = await resilientHttpClient.PostJsonAsync(dataUrl, appConfigSettings.reportApiToken, data);
        return result;
    }
}
