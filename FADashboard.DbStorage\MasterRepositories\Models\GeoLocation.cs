﻿using System.ComponentModel.DataAnnotations.Schema;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[ComplexType]
public class GeoLocation
{
    public int? Accuracy { get; set; }
    [Column("Captured_Address")]
    public string CapturedAddress { get; set; }

    public bool HasValue => Longitude.HasValue && Latitude.HasValue;

    //[NotMapped]
    public bool IsGPSOff { get; set; }

    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string Source { get; set; }
}
