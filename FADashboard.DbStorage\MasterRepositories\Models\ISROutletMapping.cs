﻿using EntityHelper;
using FADashboard.Core.Models;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class ISROutletMapping : ICreatedEntity
{
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public long Id { get; set; }

    public bool Deleted { get; set; }

    public long EmployeeId { set; get; }
    public long OutletId { set; get; }

    public virtual Employee Employee { set; get; }
    public virtual Location Outlet { set; get; }
}
