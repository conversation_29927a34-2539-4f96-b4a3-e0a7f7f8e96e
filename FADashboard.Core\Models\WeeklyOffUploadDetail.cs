﻿using Libraries.CommonEnums.HCCB;

namespace FADashboard.Core.Models;

public class WeeklyOffUploadDetail
{
    public long Id { get; set; }
    public string InputFileName { get; set; }
    public string InputFilePublicPath { get; set; }
    public WeeklyOffUploadStatus Status { get; set; }
    public string StatusDetails { get; set; }
    public DateTime? ExecutedAt { get; set; }
    public DateTime CreatedAt { get; set; }
}



public class WeeklyOffUploadQueue
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public string FilePath { get; set; }
}
