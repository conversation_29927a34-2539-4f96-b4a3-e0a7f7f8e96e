﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class AssetDetailModel
{
    public long AssetOutletId { get; set; }
    public long AssetId { get; set; }
    public string AssetName { get; set; }
    public string ImageId { get; set; }
    public string AssetType { get; set; }
    public string AssetReferenceNumber { get; set; }
    public long OutletId { get; set; }
    public long? EquipmentId { get; set; }
}

public class AttendanceAudit
{
    public long AttendanceId { get; set; }
    public long OutletId { get; set; }
    public DateTime AttendanceDate { get; set; }
    public double? ValueEfficiency { set; get; }

    public double? VolumeEfficiency { set; get; }

    //public List<long> Assetoutletid { set; get; }
    public List<AssetAuditing> AssetAuditingData { set; get; }
    public string LastVisitedFieldUser { get; set; }
}

public class AssetAuditing
{
    public long AssetMappingId { set; get; }
    public string ImageId { get; set; }
    public string Remark { get; set; }
    public AssetOutletConfidenceScore ConfidenceScore { get; set; }
    public AssetOutletHealthStatus AssetHealth { get; set; }
    public string AssetHealthRemarks { get; set; }
    public string Reason { get; set; }
    public string AuditedBy { get; set; }
}

public class AttendanceAsset
{
    public string OutletName { get; set; }
    public string OutletErpId { get; set; }
    public string AssetReferenceNumber { get; set; }
    public double? ValueEfficiency { set; get; }
    public double? VolumeEfficiency { set; get; }
    public string AssetName { get; set; }
    public string AssetType { get; set; }
    public long? AssetOutletId { get; set; }
    public string ImageId { get; set; }
    public string Remark { get; set; }
    public string ConfidenceScore { get; set; }
    public string Reason { get; set; }
    public DateTime AttendanceDate { get; set; }
    public string AssetAuditingStatus { get; set; }
    public string AssetHealth { get; set; }
    public string AssetHealthRemarks { get; set; }
    public string FieldUserName { get; set; } // last audited by user if assetAudit data present else last visited user for the outlet
}
public class AssetAllocationRequest
{
    public long RequestId { set; get; }
    public long? AssetDefinitionTypeId { get; set; }
    public long? EquipmentId { get; set; }
}
