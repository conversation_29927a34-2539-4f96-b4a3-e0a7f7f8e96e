﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class NewJourneyPlanService(IEmployeeTourPlanRepository employeeTourPlanRepository, IPositionCodeRepository positionCodeRepository, ICurrentUser currentUser, ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    public async Task<List<NewJourneyPlan>> GetNewJourneyPlans()
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var companyCreatesPlanOnPositionCodes = companySettings.CompanyCreatesPlanOnPositionCodes;

        var newJourneyPlans = await employeeTourPlanRepository.GetNewJourneyPlans();

        var positionsEmployee = new Dictionary<long, PositionCodeDetails>();

        if (companyCreatesPlanOnPositionCodes)
        {
            var positionsIdList = newJourneyPlans
                .Where(e => e.PositionCodeId.HasValue)
                .Select(e => e.PositionCodeId.Value)
                .Distinct()
                .ToList();

            if (positionsIdList.Any())
            {
                positionsEmployee = (await positionCodeRepository
                    .GetAllPositionCodesIncludeUserInfo(positionsIdList, currentUser.CompanyId, SaleType.All))
                    .ToDictionary(p => p.Id, p => p);
            }

            foreach (var journeyPlan in newJourneyPlans)
            {
                if (journeyPlan.PositionCodeId != null)
                {
                    var positionId = journeyPlan.PositionCodeId;
                    if (positionsEmployee.TryGetValue((long)positionId, out var positionDetails))
                    {
                        journeyPlan.PositionCodeLevel = string.IsNullOrEmpty(journeyPlan.PositionCodeLevel)
                            ? positionDetails.LevelName ?? ""
                            : journeyPlan.PositionCodeLevel;

                        journeyPlan.EmployeeName = positionDetails.EmployeeName;
                        journeyPlan.EmployeeErpId = positionDetails.EmployeeErpId;
                    }
                    else
                    {
                        journeyPlan.PositionCodeLevel = string.IsNullOrEmpty(journeyPlan.PositionCodeLevel) ? "" : journeyPlan.PositionCodeLevel;
                        journeyPlan.EmployeeName = null;
                        journeyPlan.EmployeeErpId = null;
                    }
                }
                else
                {
                    journeyPlan.PositionCodeLevel = string.IsNullOrEmpty(journeyPlan.PositionCodeLevel) ? "" : journeyPlan.PositionCodeLevel;
                    journeyPlan.EmployeeName = null;
                    journeyPlan.EmployeeErpId = null;
                }
            }
        }
        else
        {
            var employeeIdList = newJourneyPlans
                .Where(e => e.EmployeeId != null)
                .Select(e => e.EmployeeId)
                .Distinct()
                .ToList();

            if (employeeIdList.Any())
            {
                var positionsEmployeeList = (await positionCodeRepository
                    .GetPositionByEmployee(currentUser.CompanyId, employeeIdList))
                    .ToDictionary(p => p.EntityId, p => p);

                foreach (var journeyPlan in newJourneyPlans)
                {
                    if (positionsEmployeeList.TryGetValue(journeyPlan.EmployeeId, out var positionDetails))
                    {
                        journeyPlan.PositionCodeLevel = string.IsNullOrEmpty(journeyPlan.PositionCodeLevel)
                            ? positionDetails.Positions.FirstOrDefault()?.Level.ToString() ?? ""
                            : journeyPlan.PositionCodeLevel;
                    }
                    else
                    {
                        journeyPlan.PositionCodeLevel = string.IsNullOrEmpty(journeyPlan.PositionCodeLevel) ? "" : journeyPlan.PositionCodeLevel;
                    }
                }
            }
        }
        return newJourneyPlans;
    }




    public async Task<RepositoryResponse> PlanAlreadyExist(NewJourneyPlan newJourneyPlan)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var companyCreatesPlanOnPositionCodes = companySettings.CompanyCreatesPlanOnPositionCodes;
        return await employeeTourPlanRepository.PlanAlreadyExist(newJourneyPlan, companyCreatesPlanOnPositionCodes);
    }

    public async Task<RepositoryResponse> CreateUpdateNewJourneyPlan(NewJourneyPlan newJourneyPlan)
    {
        if (newJourneyPlan.Id == 0)
        {
            return await employeeTourPlanRepository.CreateNewJourney(newJourneyPlan);
        }

        return await employeeTourPlanRepository.UpdateNewJourney(newJourneyPlan);
    }

    public async Task<NewJourneyPlan> GetNewJourneyPlanById(long id)
    {
        var newJourneyPlan = await employeeTourPlanRepository.GetNewJourneyPlanById(id);
        return newJourneyPlan;
    }

    public async Task<RepositoryResponse> ForceEndNewJourneyPlan(long id) => await employeeTourPlanRepository.ForceEndNewJourneyPlan(id);
    public async Task<RepositoryResponse> UpdateJourneyPlanConfiguration(List<NewJourneyPlanConfig> newJourneyConfig) => await employeeTourPlanRepository.UpdateJourneyPlanConfiguration(newJourneyConfig);
    public async Task<RepositoryResponse> UpdateJourneyPlanEntityConfiguration(List<NewJourneyPlanEntityConfig> newConfigs) => await employeeTourPlanRepository.UpdateJourneyPlanEntityConfiguration(newConfigs);
    public async Task<List<NewJourneyPlanEntityConfig>> GetJourneyPlanEntityConfigurations() => await employeeTourPlanRepository.GetJourneyPlanEntityConfigurations();
    public async Task<List<NewJourneyPlanConfig>> GetJourneyPlanConfiguration()
    {
        var newJourneyPlanConfig = await employeeTourPlanRepository.GetJourneyPlanConfiguration();
        return newJourneyPlanConfig;
    }

    public async Task<RepositoryResponse> GetJourneyPlanId(long employeeId) => await employeeTourPlanRepository.GetJourneyPlanId(employeeId);
}
