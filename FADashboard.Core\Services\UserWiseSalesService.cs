﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Library.DateTimeHelpers;

namespace FADashboard.Core.Services;

public class UserWiseSalesService(IUserWiseSalesRepository userWiseSalesRepository, ICurrentUser currentUser, ICompanySettingsRepository companySettingsRepository) : RepositoryResponse
{
    public async Task<PositionCodeAndPCWiseSalesWithSum> GetPositionLevelWisePCSales(PositionCodeLevel level, List<long> positionCodeIds, DateTime startDate, DateTime endDate, SaleType saleType = SaleType.All)
    {
        var startDateKey = startDate.GetDateKey();
        var endDateKey = endDate.GetDateKey();
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var companySeesInInStdUnit = companySettings.CompanySeesDataIn == CompanySeesDataInEnum.StandardUnit;
        var companyUsesDMS = companySettings.CompanyUsesDms;

        var sales = await userWiseSalesRepository.GetPositionLevelWiseSales(currentUser.CompanyId, level, positionCodeIds, startDateKey, endDateKey, saleType: saleType, companyUsesDMS);
        //// Doing Group By User Id because a position can have an older employee in transaction DB but may be detached now.
        var getSales = sales.GroupBy(p => new { p.PositionId, p.PositionUserId }).Select(sc => new PositionCodeAndPCWiseSales
        {
            PositionId = sc.Key.PositionId,
            PositionLevel = sc.FirstOrDefault().PositionLevel,
            PositionName = sc.FirstOrDefault().PositionName,
            PositionUserId = sc.Key.PositionUserId,
            PositionUserName = sc.FirstOrDefault().PositionUserName,
            PCSales = sc.GroupBy(p => p.PrimaryCatId).Select(s => new PCSale
            {
                PrimaryCatId = s.Key,
                PrimaryCatName = s.FirstOrDefault()?.PrimaryCatName,
                SalePrice = Math.Round(Convert.ToDecimal(s.Sum(d => d.SalePrice)), 1),
                SaleInUnits = companySeesInInStdUnit ? Math.Round(s.Sum(g => g.SaleCF), 1) : Math.Round(s.Sum(g => g.SaleInSuperUnits), 1)
            }).ToList()
        }).ToList();

        var pcSums = sales.GroupBy(s => s.PrimaryCatId).Select(pc => new PCSum
        {
            PrimaryCatId = pc.Key,
            PrimaryCatName = pc.FirstOrDefault().PrimaryCatName,
            SalePriceSum = Math.Round(Convert.ToDecimal(pc.Sum(d => d.SalePrice)), 1),
            SaleInUnitsSum = companySeesInInStdUnit ? Math.Round(Convert.ToDecimal(pc.Sum(d => d.SaleCF)), 1) : Math.Round(Convert.ToDecimal(pc.Sum(d => d.SaleInSuperUnits)), 1)
        }).ToList();

        var getSalesWithSum = new PositionCodeAndPCWiseSalesWithSum { PositionCodeAndPCWiseSales = getSales, PCSums = pcSums };

        return getSalesWithSum;
    }

    public async Task<PositionCodeAndPCSCWiseSalesWithSum> GetPositionLevelWiseSCSales(PositionCodeLevel level, List<long> positionCodeIds, DateTime startDate, DateTime endDate, SaleType saleType = SaleType.All)
    {
        var startDateKey = startDate.GetDateKey();
        var endDateKey = endDate.GetDateKey();
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var companySeesInInStdUnit = companySettings.CompanySeesDataIn == CompanySeesDataInEnum.StandardUnit;
        var companyUsesDMS = companySettings.CompanyUsesDms;

        var sales = await userWiseSalesRepository.GetPositionLevelWiseSales(currentUser.CompanyId, level, positionCodeIds, startDateKey, endDateKey, saleType: saleType, companyUsesDMS);
        // Doing Group By User Id because a position can have an older employee in transaction DB but may be detached now.
        var getSales = sales.GroupBy(p => new { p.PositionId, p.PositionUserId }).Select(sc => new PositionCodeAndPCSCWiseSales
        {
            PositionId = sc.Key.PositionId,
            PositionLevel = sc.FirstOrDefault().PositionLevel,
            PositionName = sc.FirstOrDefault().PositionName,
            PositionUserId = sc.Key.PositionUserId,
            PositionUserName = sc.FirstOrDefault().PositionUserName,
            PCSCSales = sc.GroupBy(p => p.SecondaryCatId).Select(s => new PCSCSale
            {
                SecondaryCatId = s.Key,
                SecondaryCatName = s.FirstOrDefault()?.SecondaryCatName,
                PrimaryCatId = s.FirstOrDefault().PrimaryCatId,
                PrimaryCatName = s.FirstOrDefault()?.PrimaryCatName,
                SalePrice = Math.Round(Convert.ToDecimal(s.Sum(d => d.SalePrice)), 2),
                SaleInUnits = companySeesInInStdUnit ? Math.Round(Convert.ToDecimal(s.Sum(d => d.SaleCF)), 2) : Math.Round(Convert.ToDecimal(s.Sum(d => d.SaleInSuperUnits)), 2)
            }).OrderBy(p => p.PrimaryCatId).ToList()
        }).ToList();

        var scSums = sales.GroupBy(s => s.SecondaryCatId).Select(sc => new SCSum
        {
            SecondaryCatId = sc.Key,
            SecondaryCatName = sc.FirstOrDefault().SecondaryCatName,
            SalePriceSum = Math.Round(Convert.ToDecimal(sc.Sum(d => d.SalePrice)), 2),
            SaleInUnitsSum = companySeesInInStdUnit ? Math.Round(Convert.ToDecimal(sc.Sum(d => d.SaleCF)), 2) : Math.Round(Convert.ToDecimal(sc.Sum(d => d.SaleInSuperUnits)), 2)
        }).ToList();

        var getSalesWithSum = new PositionCodeAndPCSCWiseSalesWithSum { PositionCodeAndPCSCWiseSales = getSales, SCSums = scSums };

        return getSalesWithSum;
    }
}
