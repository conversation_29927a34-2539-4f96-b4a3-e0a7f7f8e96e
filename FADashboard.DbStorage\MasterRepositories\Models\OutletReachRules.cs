﻿using EntityHelper;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

public class BeatOMeterRule : IAuditedEntity, IDeletable
{
    public BeatOMeterRule()
    {
        BeatOMeterRuleForSegmentations = [];
    }

    public long Id { get; set; }
    public string RuleName { get; set; }
    public string Description { get; set; }
    public bool Deleted { get; set; }
    public string CreationContext { get; set; }
    public DateTime CreatedAt { get; set; }
    public Company Company { get; set; }
    public long CompanyId { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public int ActiveMonthLimit { get; set; }
    public int ToBeDormantMonthLimit { get; set; }
    public int DormantMonthLimit { get; set; }
    public int NewActivationMonthLimit { get; set; }
    public List<BeatOMeterRuleForSegmentation> BeatOMeterRuleForSegmentations { get; set; }
    public OutletFocusType FocusType { get; set; }
    public RuleOn RuleOn { get; set; }
    public long? PositionId { get; set; }
    public PositionCodeLevel? PositionLevel { get; set; }
}
