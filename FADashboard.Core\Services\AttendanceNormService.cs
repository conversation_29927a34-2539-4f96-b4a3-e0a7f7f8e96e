﻿using FADashboard.Core.Interfaces;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.Core.Services;

public class AttendanceNormService(IAttendanceNormRepository attendanceNormRepository, ITeamsRepository teamsRepository) : RepositoryResponse
{
    public async Task<List<Teamlist>> GetTeamList(long companyId)
    {
        var usedTeams = await attendanceNormRepository.GetActiveNormTeam(companyId);
        var createTeamIds = await teamsRepository.GetAttendanceNormActiveTeam(companyId);
        var teamNameDict = await teamsRepository.GetTeamName(createTeamIds);
        var actlist = createTeamIds.Except(usedTeams).ToList();
        var ans = new List<Teamlist>();
        foreach (var items in actlist)
        {
            var name = teamNameDict.GetValueOrDefault(items);
            if (!string.IsNullOrEmpty(name))
            {
                var sing = new Teamlist { Id = items, Name = name };
                ans.Add(sing);
            }
        }

        return ans;
    }

    public async Task<List<string>> GetAttendanceNormName(long companyid)
    {
        var res = await attendanceNormRepository.GetUsedAttendanceNormName(companyid);
        return res;
    }

    public async Task DeactivateNorm(long normId, long companyId) => await attendanceNormRepository.DeactivateNorm(companyId, normId);

    public async Task<int> GetNormsCount(long companyId, string searchString = null, bool includeDeactive = false) => await attendanceNormRepository.GetNormsCount(companyId, searchString, includeDeactive);

    public async Task<List<NormsList>> GetNorms(long companyId, string searchString = "", bool includeDeactive = false)
    {
        var norms = new List<NormsList>();
        if (string.IsNullOrEmpty(searchString))
        {
            norms = await attendanceNormRepository.GetNormsList(companyId, includeDeactive);
        }
        else
        {
            norms = await attendanceNormRepository.GetNormsBySearch(companyId, searchString, includeDeactive);
        }

        return norms;
    }

    public async Task<AttendanceNorms> GetNormDetailsById(long normId, long companyId)
    {
        var createTeamIds = await teamsRepository.GetAttendanceNormActiveTeam(companyId);
        var teamNameDict = await teamsRepository.GetTeamName(createTeamIds);
        var data = await attendanceNormRepository.GetNormDetailsById(normId, companyId, teamNameDict);
        return data;
    }

    public async Task Createpolicy(AttendanceNorms policy, long companyId)
    {
        foreach (var items in policy.AttendanceNormPolicies)
        {
            foreach (var kratype in items.AttendanceTargetForTeam)
            {
                // asana July17,2024. link: https://app.asana.com/0/1201015423448718/1207726567317203/f reason: appending ":00" causing app side issues
                // if (kratype.KRAType is KRAAttendanceNorm.DayStartTime or KRAAttendanceNorm.FirstCallTime)
                //  {
                //Date: Aug 02,2024  Asana: https://app.asana.com/0/1201671189368484/1207941963312058/f Reason: there are KRAs whose input value is a number and not dateTime values thats why this condition is required
                if (kratype.KRAType is KRAAttendanceNorm.DayStartTime or KRAAttendanceNorm.FirstCallTime or KRAAttendanceNorm.RetailingTime or KRAAttendanceNorm.TotalTime or KRAAttendanceNorm.JointWorkingTime or KRAAttendanceNorm.CallTime)
                {
                    var ts = DateTime.Parse(kratype.FullDayTarget).TimeOfDay;
                    kratype.FullDayTarget = ts.ToString();

                    if (!string.IsNullOrEmpty(kratype.HalfDayTarget))
                    {
                        var ts1 = DateTime.Parse(kratype.HalfDayTarget).TimeOfDay;
                        kratype.HalfDayTarget = ts1.ToString();
                    }
                }
                // }
                //else if (kratype.KRAType is KRAAttendanceNorm.RetailingTime or KRAAttendanceNorm.TotalTime or KRAAttendanceNorm.JointWorkingTime)
                //{
                //    kratype.FullDayTarget += ":00";
                //    if (!string.IsNullOrEmpty(kratype.HalfDayTarget))
                //    {
                //        kratype.HalfDayTarget += ":00";
                //    }
                //}
            }
        }

        ;

        await attendanceNormRepository.CreatePolicy(policy, companyId);
    }
}
