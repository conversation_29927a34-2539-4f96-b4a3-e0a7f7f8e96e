using FADashboard.Core.Models.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FADashboard.Core.Interfaces.Services
{
    public interface ILMSAccountContactService
    {
        Task<LMSAccountContactDto> GetAccountContactByIdAsync(long accountId, long contactId);
        Task<IEnumerable<LMSAccountContactDto>> GetAccountContactsByAccountIdAsync(long accountId);
        Task<LMSAccountContactDto> CreateAccountContactAsync(LMSAccountContactCreateInput contactDto, long createdByUserId, long companyId);
        Task<LMSAccountContactDto> UpdateAccountContactAsync(long contactId, LMSAccountContactUpdateInput contactDto, long updatedByUserId);
        Task<bool> DeleteAccountContactAsync(long contactId, long deletedByUserId);
    }
}
