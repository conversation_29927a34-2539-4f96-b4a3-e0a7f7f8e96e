﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models.ApiModels;

public class CompanyKPIs
{
    public long Id { get; set; }
    public long KpiId { get; set; }
    public KpiFrequency Frequency { get; set; }
    public KpiUserType UserType { get; set; }
    public KpiObjective Objective { get; set; }
    public KpiCalculation Calculation { get; set; }
    public KpiMeasure Measure { get; set; }
    public bool IsQualifier { get; set; }
    public string Description { get; set; }
    public KpiType KPIType { get; set; }
    public string SQLQuery { get; set; }
    public bool IsDeactivated { get; set; }
    public string Name { get; set; }
    public string UIName { get; set; }
    public int Sequence { get; set; }
    public Relation Relation { get; set; }
    public string MasterSQLQuery { get; set; }
    public string TargetSQLQuery { get; set; }
    public double KPIConstraint { get; set; }
    public string UnifySQLQuery { get; set; }
    public string TransactionSQLQuery { get; set; }
    public DayOfWeek? WeekOfDay { get; set; }
}

public class CompanyKPIsList
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string UIName { get; set; }
    public string Description { get; set; }
    public bool IsDeactivated { get; set; }
}

public class CompanyKPIRequestInput : CompanyKPIs
{
    public List<ParameterReferenceValues> ParameterReferenceValues { get; set; }
}


public class ParameterReferenceValues
{
    public string Parameter { get; set; }
    public string Reference { get; set; }
    public List<long> Values { get; set; }
}
