﻿using System.Runtime.InteropServices;
using FADashboard.Core.Helper;
using FADashboard.Core.Models.PowerBI;
using Microsoft.PowerBI.Api;
using Microsoft.PowerBI.Api.Models;
using Microsoft.Rest;
using Report = Microsoft.PowerBI.Api.Models.Report;

namespace FADashboard.Core.Services.Internal;

public class PbiEmbedService
{
    private readonly EntraService aadService;
    private readonly string urlPowerBiServiceApiRoot = "https://api.powerbi.com";
    private readonly AzureAd azureAd;
    private readonly PowerBI powerBI;

    public PbiEmbedService(string azureAdAuthenticationMode, string azureAdAuthorityUri, string azureClientId, string azureTenantId, string azureScope, string azureClientSecret, string powerBiworkspaceId)
    {
        powerBI = new PowerBI();
        azureAd = new AzureAd
        {
            AuthenticationMode = azureAdAuthenticationMode,
            AuthorityUri = azureAdAuthorityUri,
            ClientId = azureClientId,
            TenantId = azureTenantId,
            Scope = [azureScope],
            ClientSecret = azureClientSecret
        };
        powerBI.WorkspaceId = powerBiworkspaceId;
        aadService = new EntraService(azureAd);
    }

    private async Task<PowerBIClient> GetPowerBIClient()
    {
        var accessToken = await aadService.GetAccessTokenAsync();
        var tokenCredentials = new TokenCredentials(accessToken, "Bearer");
        return new PowerBIClient(new Uri(urlPowerBiServiceApiRoot), tokenCredentials);
    }

    public async Task<IList<Report>> GetReportList()
    {
        var pbiClient = await GetPowerBIClient();
        var report = await pbiClient.Reports.GetReportsAsync(new Guid(powerBI.WorkspaceId));
        return report.Value;
    }

    public async Task<EmbedParams> GetEmbedParams(Guid reportId, string UserName, string UserRole, [Optional] Guid additionalDatasetId)
    {
        var pbiClient = await GetPowerBIClient();

        // Get report info
        var pbiReport = await pbiClient.Reports.GetReportInGroupAsync(new Guid(powerBI.WorkspaceId), reportId);

        //  Check if dataset is present for the corresponding report
        //  If isRDLReport is true then it is a RDL Report
        var isRDLReport = string.IsNullOrEmpty(pbiReport.DatasetId);

        EmbedToken embedToken;

        // Generate embed token for RDL report if dataset is not present
        if (isRDLReport)
            // Get Embed token for RDL Report
            embedToken = await GetEmbedTokenForRDLReport(new Guid(powerBI.WorkspaceId), reportId);
        else
        {
            // Create list of datasets
            var datasetIds = new List<Guid>
            {
                // Add dataset associated to the report
                Guid.Parse(pbiReport.DatasetId)
            };

            // Append additional dataset to the list to achieve dynamic binding later
            if (additionalDatasetId != Guid.Empty)
                datasetIds.Add(additionalDatasetId);

            // Get Embed token multiple resources
            embedToken = await GetEmbedToken(reportId, datasetIds, new Guid(powerBI.WorkspaceId), UserName, UserRole);
        }

        // Capture embed params
        var embedParams = new EmbedParams { EmbedReport = pbiReport, EmbedToken = embedToken };
        return embedParams;
    }

    private async Task<EmbedToken> GetEmbedTokenForRDLReport(Guid targetWorkspaceId, Guid reportId, string accessLevel = "view")
    {
        var pbiClient = await GetPowerBIClient();

        // Generate token request for RDL Report
        var generateTokenRequestParameters = new GenerateTokenRequest(
            accessLevel: accessLevel);

        // Generate Embed token
        var embedToken = await pbiClient.Reports.GenerateTokenInGroupAsync(targetWorkspaceId, reportId, generateTokenRequestParameters);

        return embedToken;
    }

    private async Task<EmbedToken> GetEmbedToken(Guid reportId, IList<Guid> datasetIds, [Optional] Guid targetWorkspaceId, string UserName, string UserRole)
    {
        var pbiClient = await GetPowerBIClient();

        // Create a request for getting Embed token
        // This method works only with new Power BI V2 workspace experience
        var rls = new EffectiveIdentity(username: UserName, datasets: [datasetIds[0].ToString()], roles: [UserRole]);

        var tokenRequest = new GenerateTokenRequestV2(
            reports: [new(reportId)],
            datasets: datasetIds.Select(datasetId => new GenerateTokenRequestV2Dataset(datasetId.ToString())).ToList(),
            targetWorkspaces: targetWorkspaceId != Guid.Empty ? new List<GenerateTokenRequestV2TargetWorkspace> { new(targetWorkspaceId) } : null,
            identities: [rls]
        );

        // Generate Embed token
        var embedToken = await pbiClient.EmbedToken.GenerateTokenAsync(tokenRequest);

        return embedToken;
    }
}
