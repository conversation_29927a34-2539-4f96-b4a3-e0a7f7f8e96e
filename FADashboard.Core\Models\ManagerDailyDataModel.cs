﻿namespace FADashboard.Core.Models;

public class ManagerDailyAttendanceModel
{
    public int CAP { get; set; }
    public double LineWiseDiscount { get; set; }
    public double NewOutletOrderInRevenue { get; set; }
    public int NewOutletsCreated { get; set; }
    public double OrderInRevenue { get; set; }
    public double OrderInStdUnits { get; set; }
    public int OVC { get; set; }
    public int OVT { get; set; }
    public int PC { get; set; }
    public int SPC { get; set; }
    public int TC { get; set; }
    public int TelephonicOrders { get; set; }
}
public class ManagerDailyDataModel
{
    public int Absent { get; set; }
    public int CAP { get; set; }
    public int JWCalls { get; set; }
    public int Leave { get; set; }
    public double LineWiseDiscount { get; set; }
    public double MTD { get; set; }
    public double NetValue { get; set; }
    public double NewOutletSalesInRevenue { get; set; }
    public int NewOutletsCreated { get; set; }
    public int NoOfOtherActivities { get; set; }
    public int OfficialWork { get; set; }
    public double OrderInRevenue { get; set; }
    public double OrderInStdUnits { get; set; }
    public double OVC { get; set; }
    public double OVT { get; set; }
    public int PC { get; set; }
    public int PhycialCalls { get; set; }
    public int PlannedLeave { get; set; }
    public double Productivity { get; set; }
    public int Retailing { get; set; }
    public long SC { get; set; }
    public double SchemeCashDiscount { get; set; }
    public double SchProductivity { get; set; }
    public int TC { get; set; }
    public int TelephonicOrders { get; set; }
    public double TotalSchemeQty { get; set; }
    public int TotalUsers { get; set; }

    public double FillratePer { get; set; }
    public int DC { get; set; }
}

public class SchemeDiscount
{
    public decimal? SchemeCashDiscount { get; set; }
    public decimal? TotalSchemeQty { get; set; }
}
