﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class Employee
{
    public long? AreaSalesManagerId { get; set; }
    public long CompanyId { get; set; }
    public long? CompanyZoneId { get; set; }
    public string ContactNo { get; set; }
    public DateTime DateOfCreation { get; set; }
    public DateTime? DateOfJoining { get; set; }
    public DateTime? DateOfLeaving { get; set; }
    public long? DesignationId { get; set; }
    public string EmailId { get; set; }
    public string ErpId { get; set; }
    public Guid GuId { get; set; }
    public long Id { get; set; }
    public bool IsActive { get; set; }
    public bool IsBillable { set; get; }
    public bool IsFieldAppuser { get; set; }
    public bool IsOrderBookingDisabled { get; set; }
    public bool IsVacant { set; get; }
    public string LocalName { get; set; }
    public Guid? LoginGuid { get; set; }
    public string Name { get; set; }
    public EmployeeRank Rank { get; set; }
    public string Region { get; set; }
    public long? RegionId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public UserActiveStatus UserStatus { get; set; }
    public EmployeeType UserType { set; get; }
    public string Zone { get; set; }
    public string EmployeeAttributeText1 { get; set; }
    public string EmployeeAttributeText2 { get; set; }
    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
    public int? AppVersionNumber { get; set; }
    public DateTime? LastSeenAt { get; set; }
    public int? MappedBeats { get; set; }
    public bool IsTrainingUser { get; set; }
    public AlertSource? AlertSource {  get; set; }
    public List<string> PositionNames { get; set; }
    public string PositionLevelName { get; set; }
    public List<string> ParentPositionName { get; set; }
    public PositionCodeLevel PositionLevel { get; set; }
    public UserJourneyPlanningEntity JourneyPlanEntity { get; set; }
    public string UserProfilePicture { get; set; }
}

public class EmployeeCount
{
    public int TotalCount { get; set; }
}

public class EmployeeGcmIdModel
{
    public List<string> GcmId { get; set; }
    public int AppVersionNumber { get; set; }
}

public class DeviceInfoModel
{
    public int AppVersionNumber { get; set; }
    public long EmployeeId { get; set; }
    public string GcmId { get; set; }
    public DateTime LastSeenAt { get; set; }
    public AlertSource? AlertSource { get; set; }
}
