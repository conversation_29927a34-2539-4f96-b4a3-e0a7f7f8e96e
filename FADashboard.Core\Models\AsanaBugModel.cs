﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class AsanaBugModel
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string? ClientCategory { get; set; }
    public long UserId { get; set; }
    public Platform Platform { get; set; }
    public string Module { get; set; }
    public string Type { get; set; }
    public string ListOfCompany { get; set; }
    public string AsanaTitle { get; set; }
    public string IssueDescription { get; set; }
    public string? AsanaLink { get; set; }
    public string CreatedBy { get; set; }
    public string RaisedSeverity { get; set; }
    public string ActualSeverity { get; set; }
    public bool IsSLAAdhere { get; set; }
    public DateTime CreatedAt { get; set; }
    public string ClosedBy { get; set; }
    public DateTime CloserDate { get; set; }
    public string CreationContext { get; set; }
}

public class BugIssueListView
{
    public long AsanaId { get; set; }
    public string IssueTitle { get; set; }
    public string Platform { get; set; }
    public string Module { get; set; }
    public string Severity { get; set; }
    public string Status { get; set; }
    public string AsanaLink { get; set; }
}

