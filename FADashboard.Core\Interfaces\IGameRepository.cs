﻿using FADashboard.Core.Models;

using Libraries.CommonModels;
namespace FADashboard.Core.Interfaces;

public interface IGameRepository
{
    Task ActivateGame(long companyId, long GameId);

    Task DeactivateGame(long companyId, long GameId);

    Task<List<GameList>> GetGames(long companyId);

    Task<List<GameList>> GetGamesBySearch(long companyId, string searchString);

    Task<int> GetGamesCount(long companyId, string searchString = null, bool includeDeactivate = false);

    Task<List<GameList>> GetInactiveGames(long companyId, string searchString = "");
    Task<RepositoryResponse> CreateGame(long companyId, CreateGame gameDetails);
    Task<List<EntityMin>> GetGamesForDateRange(long companyId, DateTime startdate);
    Task<CreateGame> GetGameById(long companyId, long id);

}
