﻿using FADashboard.Core.Helper;
using FADashboard.Core.Helper.PaginationHelper;
using FADashboard.Core.Helper.PaginationHelper.Filter;
using FADashboard.Core.Helper.PaginationHelper.Wrappers;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FADashboard.Core.Services;

public class BeatService(
    IBeatRepository beatRepository,
    IPositionBeatRepository positionBeatRepository,
    IDistributorBeatRepository distributorBeatRepository,
    ICompanySettingsRepository companySettingsRepository,
    ICurrentUser currentUser,
    IPositionCodeRepository positionCodeRepository,
    IOutletMasterRepository outletMasterRepository) : RepositoryResponse
{
    public async Task<RepositoryResponse> ActivateDeactivateBeats(long beatId, bool action)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var mappingType = companySettings.TypeofDistributorMapping == TypeofDistributorMapping.BeatDistributor;
        var beatIdList = new List<long> { beatId };
        var errorList = new ErrorList
        {
            Count1 = (await distributorBeatRepository.GetDistributorForBeat(beatId, currentUser.CompanyId))?.Count ?? 0,
            Count2 = mappingType ? (await positionBeatRepository.GetPositionForBeats(beatId, currentUser.CompanyId))?.Count ?? 0 : 0,
            Count3 = (await outletMasterRepository.GetOutletsOfBeats(currentUser.CompanyId, beatIdList, false))?.Count ?? 0,
        };
        if (errorList.Count1 == 0 && errorList.Count2 == 0 && errorList.Count3 == 0)
            return await beatRepository.ActivateDeactivateBeats(beatId, currentUser.CompanyId, action, companySettings);
        return new RepositoryResponse { Id = beatId, Message = $"{errorList.Count1} Distributor attached , {errorList.Count2} Positions attached,{errorList.Count3} Outlets attached, Please detach them before deactivating", IsSuccess = false };
    }

    public async Task<RepositoryResponse> AttachDistributors(long beatId, List<long> distributorIds) => await distributorBeatRepository.AttachDistributorsandBeat(currentUser.CompanyId, beatId, distributorIds);

    public async Task<RepositoryResponse> CreateUpdateBeat(Beat beat, CancellationToken ct = default)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        if (beat.Id == 0)
        {
            return await beatRepository.CreateBeat(beat, currentUser.CompanyId, companySettings, ct);
        }

        return await beatRepository.UpdateBeat(beat, currentUser.CompanyId, companySettings, ct);
    }

    public async Task<Beat> GetBeatId(long beatId) => await beatRepository.GetBeatId(beatId, currentUser.CompanyId);

    public async Task<PagedResponse<List<BeatList>>> GetBeats(PaginationFilter validFilter, List<long> positionIds)
    {
        var regionIds = new List<long>();

        if (currentUser.UserRole is PortalUserRole.AccountManager or
            PortalUserRole.GlobalAdmin or
            PortalUserRole.CompanyAdmin)
        {
            var beats = await beatRepository.GetBeats(currentUser.CompanyId, validFilter, regionIds, currentUser.UserRole);
            return PaginationHelper.CreatePagedReponse(beats.BeatRecords, validFilter, beats.Total);
        }

        var childPosCodes = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionIds, currentUser.CompanyId);
        regionIds = childPosCodes.Where(r => r.RegionId.HasValue).Select(p => p.RegionId!.Value).ToList();
        var beatsFromRegions = await beatRepository.GetBeats(currentUser.CompanyId, validFilter, regionIds, currentUser.UserRole);
        return PaginationHelper.CreatePagedReponse(beatsFromRegions.BeatRecords, validFilter, beatsFromRegions.Total);
    }

    public async Task<long> GetBeatCount(long companyId, PaginationFilter validFilter)
    {
        return await beatRepository.GetBeatCount(companyId, validFilter);
    }

    public async Task<List<Beat>> GetBeatList(bool includeDeactivate)
    {
        var beatList = await beatRepository.GetBeatsList(currentUser.CompanyId, includeDeactivate);
        var postionList = beatList.SelectMany(b => b.PositionIdsList).Distinct().ToList();
        postionList.AddRange(beatList.SelectMany(b => b.SecondaryPositionIdsList).Distinct().ToList()); // to fetch sec. position info as well
        var positionDict = (await positionCodeRepository.GetPositionsMin(currentUser.CompanyId)).ToDictionary(p => p.Id, p => p.Name);
        beatList.ForEach(b =>
        {
            b.PositionIds = string.Join(", ", b.PositionIdsList);
            b.PositionNames = string.Join(", ", b.PositionIdsList.Select(p => positionDict.GetValueOrDefault(p, "")).ToList());
            b.SecondaryPositionNames = string.Join(", ", b.SecondaryPositionIdsList.Select(p => positionDict.GetValueOrDefault(p, "")).ToList());
        });
        // TODO : optimise taking more time than rest of the api
        //var outletCountDict = await outletRepository.GetBeatOutletCountDict(currentUser.CompanyId);
        //beatList.ForEach(b =>
        //{
        //    b.MappedOutlets = outletCountDict.ContainsKey(b.Id) ? outletCountDict[b.Id] : 0;
        //});
        return beatList;
    }

    public async Task<List<BeatPositionMapInfo>> GetDistinctBeatsOfPosition(long positionId) => await beatRepository.GetBeatsOfPosition(currentUser.CompanyId, positionId);

    public async Task<List<Beat>> GetDistinctBeatsUnderAndOfPositions(List<long> positionIds)
    {
        var childPosCodes = await positionCodeRepository.GetAllUserUnderPositionCodesIncludeUserInfo(positionIds, currentUser.CompanyId);
        var childPosCodeIds = childPosCodes.Select(p => p.Id).ToList();
        positionIds.AddRange(childPosCodeIds);
        return (await beatRepository.GetBeatsOfPositions(currentUser.CompanyId, positionIds)).DistinctBy(b => b.Id).ToList();
    }

    public async Task<List<Beat>> GetBeatsUnderGeographyList(string geographyName, List<long> geographyid) => await beatRepository.GetBeatsUnderGeographyList(currentUser.CompanyId, geographyName, geographyid);

    public async Task<RepositoryResponse> ModifyBeatOutlets(List<BeatOutlets> beatOutlets)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        return await outletMasterRepository.ModifyBeatOutlets(beatOutlets, currentUser.CompanyId, companySettings);
    }
}
