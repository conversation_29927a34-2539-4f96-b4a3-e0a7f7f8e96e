﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;

public class PositionCodeHierarchy
{
    public long? Level1Id { get; set; }
    public long? Level1UserId { get; set; }
    public long? Level2Id { get; set; }
    public long? Level2UserId { get; set; }
    public long? Level3Id { get; set; }
    public long? Level3UserId { get; set; }
    public long? Level4Id { get; set; }
    public long? Level4UserId { get; set; }
    public long? Level5Id { get; set; }
    public long? Level5UserId { get; set; }
    public long? Level6Id { get; set; }
    public long? Level6UserId { get; set; }
    public long? Level7Id { get; set; }
    public long? Level7UserId { get; set; }
    public long? Level8Id { get; set; }
    public long? Level8UserId { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }

    public PositionCodeHierarchy GetUpdatedPositionCodeLevels()
    {
        switch (PositionCodeLevel)
        {
            case PositionCodeLevel.L8Position:
                Level8Id = Level1Id;

                Level8UserId = Level1UserId;

                break;

            case PositionCodeLevel.L7Position:
                Level8Id = Level2Id;
                Level7Id = Level1Id;

                Level8UserId = Level2UserId;
                Level7UserId = Level1UserId;

                break;

            case PositionCodeLevel.L6Position:
                Level8Id = Level3Id;
                Level7Id = Level2Id;
                Level6Id = Level1Id;

                Level8UserId = Level3UserId;
                Level7UserId = Level2UserId;
                Level6UserId = Level1UserId;

                break;

            case PositionCodeLevel.L5Position:
                Level8Id = Level4Id;
                Level7Id = Level3Id;
                Level6Id = Level2Id;
                Level5Id = Level1Id;

                Level8UserId = Level4UserId;
                Level7UserId = Level3UserId;
                Level6UserId = Level2UserId;
                Level5UserId = Level1UserId;

                break;

            case PositionCodeLevel.L4Position:
                Level8Id = Level5Id;
                Level7Id = Level4Id;
                Level6Id = Level3Id;
                Level5Id = Level2Id;
                Level4Id = Level1Id;

                Level8UserId = Level5UserId;
                Level7UserId = Level4UserId;
                Level6UserId = Level3UserId;
                Level5UserId = Level2UserId;
                Level4UserId = Level1UserId;

                break;

            case PositionCodeLevel.L3Position:
                Level8Id = Level6Id;
                Level7Id = Level5Id;
                Level6Id = Level4Id;
                Level5Id = Level3Id;
                Level4Id = Level2Id;
                Level3Id = Level1Id;

                Level8UserId = Level6UserId;
                Level7UserId = Level5UserId;
                Level6UserId = Level4UserId;
                Level5UserId = Level3UserId;
                Level4UserId = Level2UserId;
                Level3UserId = Level1UserId;

                break;

            case PositionCodeLevel.L2Position:
                Level8Id = Level7Id;
                Level7Id = Level6Id;
                Level6Id = Level5Id;
                Level5Id = Level4Id;
                Level4Id = Level3Id;
                Level3Id = Level2Id;
                Level2Id = Level1Id;

                Level8UserId = Level7UserId;
                Level7UserId = Level6UserId;
                Level6UserId = Level5UserId;
                Level5UserId = Level4UserId;
                Level4UserId = Level3UserId;
                Level3UserId = Level2UserId;
                Level2UserId = Level1UserId;

                break;
            case PositionCodeLevel.L1Position:
                break;
        }

        return this;
    }
}

public class PositionCodeHierarchyWithManager
{
    public long? Level1Id { get; set; }
    public long? Level1UserId { get; set; }
    public long? Level2Id { get; set; }
    public long? Level2UserId { get; set; }
    public long? Level3Id { get; set; }
    public long? Level3UserId { get; set; }
    public long? Level4Id { get; set; }
    public long? Level4UserId { get; set; }
    public long? Level5Id { get; set; }
    public long? Level5UserId { get; set; }
    public long? Level6Id { get; set; }
    public long? Level6UserId { get; set; }
    public long? Level7Id { get; set; }
    public long? Level7UserId { get; set; }
    public long? Level8Id { get; set; }
    public long? Level8UserId { get; set; }
    public long? LevelId { get; set; }
    public long? LevelUserId { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }
}
