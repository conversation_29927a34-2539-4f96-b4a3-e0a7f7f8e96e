﻿using Libraries.CommonEnums;

namespace FADashboard.Core.Models;
public class SecondaryOrderDmsDTO
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    public string SellerName { get; set; }
    public int LeadTime { get; set; }
    public long SellerId { get; set; }
    public StockistType? SellerType { get; set; }
    public int OnTime { get; set; }
    public int InFull { get; set; }
    public int TotalOrders { get; set; }
}
