﻿using FADashboard.Core.Helper;
using FADashboard.Core.Interfaces;
using FADashboard.Core.Interfaces.Authentication;
using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using FADashboard.Core.Models.DTOs;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.StringHelpers;
using Microsoft.AspNetCore.Mvc;

namespace FADashboard.Core.Services;

public class VanSalesService(IVanSalesRepository vanRepository, ICurrentUser currentUser, IEmployeeRepository employeeRepository,
    ICompanySettingsRepository companySettingsRepository, IDistributorRepository distributorRepository, IDistributorAddressRepository distributorAddressRepository, IBeatRepository beatRepository, IPositionCodeRepository positionCodeRepository) : RepositoryResponse
{
    private async Task<RepositoryResponse> IsValidVan(VanInput van)
    {
        var vans = await <PERSON><PERSON>(true);
        if (van.Id != 0)
        {
            vans = vans.Where(p => p.Id != van.Id).ToList();
        }

        var vanNameList = vans.Select(p => p.VanName.NormalizeCaps()).ToList();
        var vanInvoicePrefixList = vans.Select(p => p.VanInvoicePrefix.NormalizeCaps()).ToList();
        var vanRegNoList = vans.Select(p => p.VanRegistrationNumber.NormalizeCaps()).ToList();
        var vanChassisNoList = vans.Select(p => p.ChassisNumber.NormalizeCaps()).ToList();

        if (vanNameList.Contains(van.VanName.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = van.Id, ExceptionMessage = "Van Name is not unique", Message = "Van Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (vanRegNoList.Contains(van.VanRegistrationNumber.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = van.Id, ExceptionMessage = "Van Registeration number is not unique", Message = "Van Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (vanInvoicePrefixList.Contains(van.Vaninvoiceprefix.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = van.Id, ExceptionMessage = "Van Invoice Prefix number is not unique", Message = "Van Creation/Updation Failed!", IsSuccess = false,
            };
        }

        if (!string.IsNullOrEmpty(van.ChassisNumber) && vanChassisNoList.Contains(van.ChassisNumber.NormalizeCaps()))
        {
            return new RepositoryResponse
            {
                Id = van.Id, ExceptionMessage = "Van Chassis number is not unique", Message = "Van Creation/Updation Failed!", IsSuccess = false,
            };
        }

        return new RepositoryResponse { Id = van.Id, Message = "Van Unique", IsSuccess = true, };
    }

    public async Task<RepositoryResponse> ActivateDeactivateVan(long vanId, bool action) => await vanRepository.ActivateDeactivateVan(vanId, currentUser.CompanyId, action);

    public async Task<RepositoryResponse> CreateUpdateVan(VanInput van)
    {
        var checkValid = await IsValidVan(van);
        if (!checkValid.IsSuccess)
        {
            return checkValid;
        }

        if (van.Id == 0)
        {
            return await vanRepository.CreateVan(van, currentUser.CompanyId);
        }

        return await vanRepository.UpdateVan(van, currentUser.CompanyId);
    }

    public async Task<VanInput> GetVanById(long vanId)
    {
        var van = await vanRepository.GetVanById(vanId, currentUser.CompanyId);
        return van;
    }

    public async Task<List<VanSales>> GetVans(bool includeDeactivate)
    {
        var vans = await vanRepository.GetVans(currentUser.CompanyId, includeDeactivate);
        return vans;
    }

    public async Task<List<EntityMinWithStatus>> GetEmployeesWithVanMapping()
    {
        var mappedEmployees = await vanRepository.GetEmployeesMappedToVans(currentUser.CompanyId);
        var allEmployees = await employeeRepository.GetDSREmployeeMin(currentUser.CompanyId);
        var dsrEmployees = allEmployees.Select(employee => new EntityMinWithStatus { Id = employee.Id, Name = employee.Name, IsActive = !mappedEmployees.Contains(employee.Id), }).ToList();
        return dsrEmployees;
    }

    public async Task<List<VanSales>> GetVansForPositionIds([FromBody] List<long> positionIds)
    {
        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var distributorMapping = companySettings.TypeofDistributorMapping;
        List<long> distributorIds = new List<long>();
        if (distributorMapping == TypeofDistributorMapping.UserDistributor)
        {
            var getPositionsUnderPosition = (await positionCodeRepository.GetPositionIdsUnderPosition(positionIds, currentUser.CompanyId)).Select(p => p.Id).ToList();
            var distributors = await distributorRepository.GetDistributorsForPositions(currentUser.CompanyId, getPositionsUnderPosition);
            distributorIds = distributors.Select(x => x.Id).ToList();
        }
        else if (distributorMapping == TypeofDistributorMapping.BeatDistributor)
        {
            var getPositionsUnderPosition = (await positionCodeRepository.GetPositionIdsUnderPosition(positionIds, currentUser.CompanyId)).Select(p => p.Id).ToList();
            var beats = await beatRepository.GetBeatsOfPositions(currentUser.CompanyId, getPositionsUnderPosition);
            var DistinctBeat = beats.DistinctBy(b => b.Id).ToList();
            var beatIds = DistinctBeat.Select(x => x.Id).ToList();
            var distributors = await distributorRepository.GetDistributorsForBeats(currentUser.CompanyId, beatIds);
            distributorIds = distributors.Select(x => x.Id).ToList();
        }

        var VansList = await vanRepository.GetVansForDistributors(currentUser.CompanyId, distributorIds);
        return VansList;

    }

    public async Task<List<EntityMinWithStatus>> GetEmployessforPositionIds([FromBody] List<long> positionIds)
    {
        var EmployeesForPositionIds = await vanRepository.GetEmployeesForPositionIds(currentUser.CompanyId, positionIds);
        var mappedEmployees = await vanRepository.GetEmployeesMappedToVans(currentUser.CompanyId);

        var allEmployees = await employeeRepository.GetDSREmployeeMin(currentUser.CompanyId);
        var filteredEmployees = allEmployees
            .Where(employee => EmployeesForPositionIds.Contains(employee.Id))
            .Select(employee => new EntityMinWithStatus
            {
                Id = employee.Id,
                Name = employee.Name,
                IsActive = !mappedEmployees.Contains(employee.Id)
            })
            .ToList();
        return filteredEmployees;
    }

    public async Task<List<DistributorDTO>> GetdistributorsForPositionsWithWarehouse([FromBody] List<long> positionIds)
    {

        var companySettingsDict = await companySettingsRepository.GetSettingsCompany(currentUser.CompanyId);
        var companySettings = new CompanySettings(companySettingsDict);
        var distributorMapping = companySettings.TypeofDistributorMapping;
        List<DistributorDTO> distributors = new List<DistributorDTO>();
        if (distributorMapping == TypeofDistributorMapping.UserDistributor)
        {
            distributors = await distributorRepository.GetDistributorsForPositions(currentUser.CompanyId, positionIds);
        }
        else if (distributorMapping == TypeofDistributorMapping.BeatDistributor)
        {
            var beats = await beatRepository.GetBeatsOfPositions(currentUser.CompanyId, positionIds);
            var DistinctBeat = beats.DistinctBy(b => b.Id).ToList();
            var beatIds = DistinctBeat.Select(x => x.Id).ToList();
            distributors = await distributorRepository.GetDistributorsForBeats(currentUser.CompanyId, beatIds);
        }

        var distributorAddresses = await distributorAddressRepository.getDistributorAddressViaDistributorIds(distributors.Select(i => i.Id).ToList(), currentUser.CompanyId);
        distributors = distributors.Select(distributor =>
        {
            var filteredAddresses = distributorAddresses.Where(address => address.EntityId == distributor.Id);
            distributor.MappedWarehouseNamesWithErpId = filteredAddresses.Select(address => new WarehouseNamesWithErpId
            {
                WarehouseErpId = address.WarehouseERPID,
                WarehouseName = address.WareHouseName,
            }).ToList();
            return distributor;
        }).ToList();
        return distributors;
    }
}
