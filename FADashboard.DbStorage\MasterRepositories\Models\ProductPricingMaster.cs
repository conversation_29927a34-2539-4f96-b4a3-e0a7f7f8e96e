﻿using System.ComponentModel.DataAnnotations.Schema;
using EntityHelper;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonEnums;

namespace FADashboard.DbStorage.MasterRepositories.Models;

[Table("ProductPricingMasters")]
public class ProductPricingMaster : IEntity, IDeactivatable, IAuditedEntity, ICreatedEntity, ICompanyEntity
{
    public ProductPricingMaster()
    {
    }

    public ProductPricingMaster(ProductList product, decimal gst, decimal cess,
        decimal mrp, bool isUsingMargin,
        decimal? pRTMargin,
        decimal? pTSubStockistMargin,
        decimal? pTDMargin,
        decimal? pTSuperStockistMargin)
    {
        MRPBatch = $"{product.Id}_{mrp:0.00}";
        MRP = mrp;
        ProductId = product.Id;
        if (isUsingMargin)
        {
            if (product.IsMarkDownMarginLogic)
            {
                PTR = mrp - (mrp * (pRTMargin / 100)) - ((mrp - (mrp * (pRTMargin / 100))) * Math.Round(1 - (1 / (1 + ((gst / 100) + (cess / 100)))), 4)) ?? 0;
                PTRMargin = pRTMargin;
                PTD = PTR - (PTR * (pTDMargin / 100));
                PTDMargin = pTDMargin;
                PTSubStockist = PTR - (PTR * (pTSubStockistMargin / 100));
                PTSubStockistMargin = pTSubStockistMargin;
                PTSuperStockist = PTSubStockist - (PTSubStockist * (pTSuperStockistMargin / 100));
                PTSuperStockistMargin = pTSuperStockistMargin;
            }
            else
            {
                PTR = (mrp / (1 + (pRTMargin / 100)) / (1 + ((gst + cess) / 100))) ?? 0;
                PTRMargin = pRTMargin;
                PTD = PTR / (1 + (pTDMargin / 100));
                PTDMargin = pTDMargin;
                PTSubStockist = PTR / (1 + (pTSubStockistMargin / 100));
                PTSubStockistMargin = pTSubStockistMargin;
                PTSuperStockist = PTSubStockist / (1 + (pTSuperStockistMargin / 100));
                PTSuperStockistMargin = pTSuperStockistMargin;
            }
        }

        ProductErpId = product.ErpId;
    }

    public long Id { get; set; }
    public bool IsDeactive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }


    public long CompanyId { get; set; }

    public Company Company { get; set; }

    public long ProductId { get; set; }

    public CompanyProduct Product { get; set; }

    public long? BatchId { get; set; }

    public ProductBatchMaster Batch { get; set; }

    public long? RegionId { get; set; }

    public Regions Region { get; set; }

    public long? DistributorId { get; set; }

    public Distributor Distributor { get; set; }

    public long? SubStockistId { get; set; }

    public Distributor SubStockist { get; set; }

    public long? SuperStockistId { get; set; }

    public Distributor SuperStockist { get; set; }

    public long? DistributorSegmentationId { get; set; }

    public DistributorSegmentations DistributorSegmentation { get; set; }
    public long? DistributorChannelId { get; set; }

    public DistributorChannels DistributorChannel { get; set; }

    public decimal MRP { get; set; }
    [Column("PTR_MT")]
    public decimal? PTRMT { get; set; }

    public decimal? PTRT { get; set; }

    public decimal PTR { get; set; }

    public decimal? PTRMargin { get; set; }

    public decimal? PTSubStockist { get; set; }

    public decimal? PTSubStockistMargin { get; set; }

    public decimal? PTD { get; set; }

    public decimal? PTDMargin { get; set; }

    public decimal? PTSuperStockist { get; set; }
    public decimal? PTSuperStockistMargin { get; set; }


    public decimal? SuperStockistTradeDiscount { get; set; }
    public decimal? DistributorTradeDiscount { get; set; }
    public decimal? SubStockistTradeDiscount { get; set; }
    public decimal? RetailerTradeDiscount { get; set; }
    public string MRPBatch { get; set; }
    public string BatchNumber { get; set; }
    public PricingMastertype PricingMasterType { get; set; }


    [NotMapped] public string UniqueProduct => MRPBatch;

    [NotMapped] public string UniqueRegion => $"{MRPBatch}-{(RegionId != null ? RegionId : 0)}";

    [NotMapped]
    public string UniqueDistributor => $"{MRPBatch}-" +
                                       $"{(SuperStockistId != null ? SuperStockistId : 0)}-" +
                                       $"{(DistributorId != null ? DistributorId : 0)}-" +
                                       $"{(SubStockistId != null ? SubStockistId : 0)}";

    [NotMapped]
    public string UniqueDistributorCategory => $"{MRPBatch}-" +
                                               $"{(DistributorChannelId != null ? DistributorChannelId : 0)}-" +
                                               $"{(DistributorSegmentationId != null ? DistributorSegmentationId : 0)}";

    [NotMapped] public string ProductErpId { get; set; }
}
