﻿using FADashboard.Core.Models;
using FADashboard.Core.Models.ApiModels;
using Libraries.CommonModels;

namespace FADashboard.Core.Interfaces;

public interface IQueryViewRepository
{
    Task<RepositoryResponse> <PERSON>lone<PERSON>hart(long chartId, long companyId = 0);

    Task<List<QuickVizList>> GetQuicViz(long companyId, bool includeDeactivate = false);

    Task<List<ChartDataMin>> GetChartsForView(long viewId);

    Task<RepositoryResponse> SaveChart(ChartVizDetailModel chartViz, long companyId);

    Task<RepositoryResponse> PinUnpinChart(long chartId, long companyId, bool action);

    Task<RepositoryResponse> DeleteChart(long chartId, long companyId);
}
